"use client";

import { useState, use<PERSON>emo, useEffect, useRef } from "react";
import { Search, Plus, Heart, Eye, Copy, Filter, Tag, User, Globe, Lock, Star, Edit3, Clock, CheckCircle, X } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogTrigger } from "./ui/dialog";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Badge } from "./ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { ScrollArea } from "./ui/scroll-area";
import { Separator } from "./ui/separator";
import { useContexts, useContextCategories, useUseContext, useToggleContextLike, useUserLikedContexts } from "@/hooks/use-contexts";
import { useAddContextToProject } from "@/hooks/use-context-to-prompt";
import { useUser } from "@/hooks/use-auth";
import { supabase } from "@/lib/supabase";
import { useQueryClient } from "@tanstack/react-query";
import { ContextCreationModal } from "./context-creation-modal";
import { ContextEditModal } from "./context-edit-modal";
import { toast } from "sonner";

// Types
export interface ContextCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
}

export interface Context {
  id: string;
  title: string;
  description: string;
  content: string;
  category: ContextCategory;
  author_id: string;
  author_name: string;
  is_public: boolean;
  is_featured: boolean;
  is_template: boolean;
  tags: string[];
  usage_count: number;
  like_count: number;
  view_count: number;
  approval_status: 'pending' | 'approved' | 'rejected';
  approved_by?: string;
  approved_at?: string;
  approval_notes?: string;
  created_at: string;
  updated_at: string;
}

// Mock data removed - using real API data from Supabase

interface ContextGalleryProps {
  onSelectContext: (context: Context) => void;
  // Optional props for external control (when used without Dialog wrapper)
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  // New prop to render content only (without Dialog wrapper)
  contentOnly?: boolean;
}

export default function ContextGallery({ onSelectContext, open, onOpenChange, contentOnly = false }: ContextGalleryProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [filterType, setFilterType] = useState<"all" | "public" | "private" | "templates" | "featured">("all");
  const [selectedContext, setSelectedContext] = useState<Context | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingContext, setEditingContext] = useState<Context | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Refs for scroll handling
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Hooks
  const { data: user } = useUser();
  const { data: categories = [] } = useContextCategories();
  const useContextMutation = useUseContext();
  const toggleLikeMutation = useToggleContextLike();
  const { addContext: addContextToProject, isLoading: isAddingToProject } = useAddContextToProject();
  const queryClient = useQueryClient();
  const { data: likedContexts = [] } = useUserLikedContexts();

  // Context filters
  const contextFilters = useMemo(() => {
    const filters: {
      category_id?: string;
      search?: string;
      is_public?: boolean;
      is_template?: boolean;
      is_featured?: boolean;
      author_id?: string;
    } = {};
    
    if (selectedCategory !== "all") {
      filters.category_id = selectedCategory;
    }
    
    if (searchTerm) {
      filters.search = searchTerm;
    }

    switch (filterType) {
      case "public":
        filters.is_public = true;
        break;
      case "private":
        filters.is_public = false;
        filters.author_id = user?.id;
        break;
      case "templates":
        filters.is_template = true;
        break;
      case "featured":
        filters.is_featured = true;
        break;
    }
    
    return filters;
  }, [searchTerm, selectedCategory, filterType, user?.id]);

  const { data: contexts = [], isLoading } = useContexts(contextFilters);

  // Realtime subscription for contexts
  useEffect(() => {
    const subscription = supabase
      .channel('context_changes')
      .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'contexts' }, 
          (payload: object) => {
        console.log('Context change received:', payload);
        queryClient.invalidateQueries({ queryKey: ['contexts'] });
        queryClient.invalidateQueries({ queryKey: ['context-categories'] });
      })
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [queryClient]);

  // Enhanced scroll handling for better user experience
  useEffect(() => {
    const scrollElement = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');

    if (!scrollElement) return;

    const handleWheel = (e: Event) => {
      // Allow natural scrolling behavior
      e.stopPropagation();
    };

    const handleTouchStart = (e: Event) => {
      // Enable touch scrolling
      e.stopPropagation();
    };

    scrollElement.addEventListener('wheel', handleWheel, { passive: true });
    scrollElement.addEventListener('touchstart', handleTouchStart, { passive: true });

    return () => {
      scrollElement.removeEventListener('wheel', handleWheel);
      scrollElement.removeEventListener('touchstart', handleTouchStart);
    };
  }, []);

  const handleSelectContext = async (context: Context) => {
    try {
      // Add context to current project as prompt
      await addContextToProject(context);

      // Also call the original onSelectContext for backward compatibility
      onSelectContext(context);
      setSelectedContext(null);
    } catch (error) {
      console.error('Failed to add context to project:', error);
      toast.error('Context projeye eklenirken hata oluştu');
    }
  };

  const handleCopyContext = async (context: Context, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(context.content);

      // Increment usage count
      await useContextMutation.mutateAsync({
        contextId: context.id,
        projectId: undefined // Context gallery'de proje ID'si yok
      });

      toast.success('Context panoya kopyalandı!');
    } catch (error) {
      console.error("Copy failed:", error);
      toast.error('Kopyalama başarısız oldu');
    }
  };

  const handleLikeContext = async (contextId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await toggleLikeMutation.mutateAsync(contextId);
      toast.success('Beğeni durumu güncellendi!');
    } catch (error) {
      console.error("Like toggle failed:", error);
      toast.error('Beğeni güncellenemedi');
    }
  };

  const handleEditContext = (context: Context, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingContext(context);
    setIsEditModalOpen(true);
  };

  // Determine if this is being used as a controlled component (external Dialog control)
  const isControlled = open !== undefined && onOpenChange !== undefined;

  // If contentOnly mode, render just the content without Dialog wrapper
  if (contentOnly) {
    return (
      <div className="space-y-4">
        {/* Header with Create Button */}
        <div className="flex items-center justify-between">
          <Button
            size="sm"
            onClick={() => setIsCreateModalOpen(true)}
            className="gap-2 ml-auto"
          >
            <Plus className="h-4 w-4" />
            Yeni Ekle
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col gap-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Context ara... (başlık, açıklama, etiketler)"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Category Filters */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedCategory === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory("all")}
              className="gap-2"
            >
              <span>📂</span>
              Tümü
            </Button>
            {categories.map((category: ContextCategory) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="gap-2"
                style={selectedCategory === category.id ? { backgroundColor: category.color } : {}}
              >
                <span>{category.icon}</span>
                {category.name}
              </Button>
            ))}
          </div>

          {/* Type Filters */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={filterType === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterType("all")}
              className="gap-2"
            >
              <Filter className="h-4 w-4" />
              Tümü
            </Button>
            <Button
              variant={filterType === "featured" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterType("featured")}
              className="gap-2"
            >
              <Star className="h-4 w-4" />
              Öne Çıkan
            </Button>
            <Button
              variant={filterType === "templates" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterType("templates")}
              className="gap-2"
            >
              <Tag className="h-4 w-4" />
              Şablonlar
            </Button>
            <Button
              variant={filterType === "public" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterType("public")}
              className="gap-2"
            >
              <Globe className="h-4 w-4" />
              Herkese Açık
            </Button>
            <Button
              variant={filterType === "private" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterType("private")}
              className="gap-2"
            >
              <Lock className="h-4 w-4" />
              Özel
            </Button>
          </div>
        </div>

        {/* Context Grid */}
        <div className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex flex-col items-center gap-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p className="text-gray-500">Contextler yükleniyor...</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-4">
              {contexts.map((context: Context) => (
              <Card
                key={context.id}
                className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] group"
                onClick={() => setSelectedContext(context)}
              >
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <span style={{ color: context.category.color }}>
                        {context.category.icon}
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {context.category.name}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-1">
                      {context.is_featured && (
                        <div title="Öne Çıkan">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        </div>
                      )}
                      {context.is_public ? (
                        <div className="flex items-center gap-1">
                          <div title="Herkese Açık">
                            <Globe className="h-4 w-4 text-green-500" />
                          </div>
                          {context.approval_status === 'approved' && (
                            <div title="Onaylanmış">
                              <CheckCircle className="h-3 w-3 text-green-500" />
                            </div>
                          )}
                          {context.approval_status === 'pending' && (
                            <div title="Onay Bekliyor">
                              <Clock className="h-3 w-3 text-yellow-500" />
                            </div>
                          )}
                          {context.approval_status === 'rejected' && (
                            <div title="Reddedildi">
                              <X className="h-3 w-3 text-red-500" />
                            </div>
                          )}
                        </div>
                      ) : (
                        <div title="Özel">
                          <Lock className="h-4 w-4 text-gray-500" />
                        </div>
                      )}
                      {context.is_template && (
                        <div title="Şablon">
                          <Tag className="h-4 w-4 text-blue-500" />
                        </div>
                      )}
                    </div>
                  </div>
                  <CardTitle className="text-lg line-clamp-2">{context.title}</CardTitle>
                  <CardDescription className="line-clamp-2">{context.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {/* Tags */}
                  <div className="flex flex-wrap gap-1">
                    {context.tags.slice(0, 3).map((tag: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {context.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{context.tags.length - 3}
                      </Badge>
                    )}
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        {context.view_count}
                      </div>
                      <div className="flex items-center gap-1">
                        <Copy className="h-3 w-3" />
                        {context.usage_count}
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="h-3 w-3" />
                        {context.like_count}
                      </div>
                    </div>
                  </div>

                  {/* Author */}
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <User className="h-3 w-3" />
                    {context.author_name}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSelectContext(context);
                      }}
                      disabled={isAddingToProject}
                      className="flex-1"
                    >
                      {isAddingToProject ? 'Ekleniyor...' : 'Projeye Ekle'}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => handleCopyContext(context, e)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    {/* Edit button - only show for user's own contexts */}
                    {user && context.author_id === user.id && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => handleEditContext(context, e)}
                        title="Düzenle"
                      >
                        <Edit3 className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => handleLikeContext(context.id, e)}
                      className={likedContexts.includes(context.id) ? "text-red-500" : ""}
                    >
                      <Heart
                        className={`h-4 w-4 ${likedContexts.includes(context.id) ? "fill-current" : ""}`}
                      />
                    </Button>
                  </div>
                </CardContent>
              </Card>
              ))}
              
              {contexts.length === 0 && (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Context bulunamadı</h3>
                  <p className="text-gray-500">
                    Arama kriterlerinizi değiştirmeyi deneyin veya yeni bir context oluşturun.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Context Detail Modal */}
        {selectedContext && (
          <Dialog open={!!selectedContext} onOpenChange={() => setSelectedContext(null)}>
            <DialogContent className="max-w-4xl max-h-[90vh]">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <span style={{ color: selectedContext.category.color }}>
                    {selectedContext.category.icon}
                  </span>
                  {selectedContext.title}
                </DialogTitle>
                <div className="flex items-start justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="secondary">{selectedContext.category.name}</Badge>
                      {selectedContext.is_featured && (
                        <Badge className="bg-yellow-100 text-yellow-800">
                          <Star className="h-3 w-3 mr-1" />
                          Öne Çıkan
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </DialogHeader>
            </DialogContent>
          </Dialog>
        )}

        {/* Context Creation Modal */}
        <ContextCreationModal
          open={isCreateModalOpen}
          onOpenChange={setIsCreateModalOpen}
          onSuccess={() => {
            // Refresh contexts list
            queryClient.invalidateQueries({ queryKey: ['contexts'] });
          }}
        />

        {/* Context Edit Modal */}
        <ContextEditModal
          open={isEditModalOpen}
          onOpenChange={setIsEditModalOpen}
          context={editingContext}
          onSuccess={() => {
            // Refresh contexts list
            queryClient.invalidateQueries({ queryKey: ['contexts'] });
          }}
        />
      </div>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {!isControlled && (
        <DialogTrigger asChild>
          <Button variant="outline" className="gap-2 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 hover:from-blue-100 hover:to-indigo-100">
            <Search className="h-4 w-4" />
            Context Galerisi
          </Button>
        </DialogTrigger>
      )}
      <DialogContent className="max-w-6xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Context Galerisi
            </DialogTitle>
            <Button
              size="sm"
              onClick={() => setIsCreateModalOpen(true)}
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              Yeni Ekle
            </Button>
          </div>
        </DialogHeader>

        {/* Search and Filters */}
        <div className="flex flex-col gap-4 p-2 flex-shrink-0">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Context ara... (başlık, açıklama, etiketler)"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Category Filters */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedCategory === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory("all")}
              className="gap-2"
            >
              <span>📂</span>
              Tümü
            </Button>
            {categories.map((category: ContextCategory) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="gap-2"
                style={selectedCategory === category.id ? { backgroundColor: category.color } : {}}
              >
                <span>{category.icon}</span>
                {category.name}
              </Button>
            ))}
          </div>

          {/* Type Filters */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={filterType === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterType("all")}
              className="gap-2"
            >
              <Filter className="h-4 w-4" />
              Tümü
            </Button>
            <Button
              variant={filterType === "featured" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterType("featured")}
              className="gap-2"
            >
              <Star className="h-4 w-4" />
              Öne Çıkan
            </Button>
            <Button
              variant={filterType === "templates" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterType("templates")}
              className="gap-2"
            >
              <Tag className="h-4 w-4" />
              Şablonlar
            </Button>
            <Button
              variant={filterType === "public" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterType("public")}
              className="gap-2"
            >
              <Globe className="h-4 w-4" />
              Herkese Açık
            </Button>
            <Button
              variant={filterType === "private" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterType("private")}
              className="gap-2"
            >
              <Lock className="h-4 w-4" />
              Özel
            </Button>
          </div>
        </div>

        <Separator className="flex-shrink-0" />

        {/* Context Grid - Improved scrolling container */}
        <div className="flex-1 min-h-0">
          <ScrollArea
            ref={scrollAreaRef}
            className="h-full w-full"
            style={{
              scrollBehavior: 'smooth',
              WebkitOverflowScrolling: 'touch' // Enable momentum scrolling on iOS
            }}
          >
            <div className="p-4 space-y-4">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="flex flex-col items-center gap-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p className="text-gray-500">Contextler yükleniyor...</p>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-4">
                  {contexts.map((context: Context) => (
                    <Card
                      key={context.id}
                      className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] group"
                      onClick={() => setSelectedContext(context)}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-2">
                            <span style={{ color: context.category.color }}>
                              {context.category.icon}
                            </span>
                            <Badge variant="secondary" className="text-xs">
                              {context.category.name}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-1">
                            {context.is_featured && (
                              <div title="Öne Çıkan">
                                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                              </div>
                            )}
                            {context.is_public ? (
                              <div className="flex items-center gap-1">
                                <div title="Herkese Açık">
                                  <Globe className="h-4 w-4 text-green-500" />
                                </div>
                                {context.approval_status === 'approved' && (
                                  <div title="Onaylanmış">
                                    <CheckCircle className="h-3 w-3 text-green-500" />
                                  </div>
                                )}
                                {context.approval_status === 'pending' && (
                                  <div title="Onay Bekliyor">
                                    <Clock className="h-3 w-3 text-yellow-500" />
                                  </div>
                                )}
                                {context.approval_status === 'rejected' && (
                                  <div title="Reddedildi">
                                    <X className="h-3 w-3 text-red-500" />
                                  </div>
                                )}
                              </div>
                            ) : (
                              <div title="Özel">
                                <Lock className="h-4 w-4 text-gray-500" />
                              </div>
                            )}
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}

                  {contexts.length === 0 && (
                    <div className="text-center py-12">
                      <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Context bulunamadı</h3>
                      <p className="text-gray-500">
                        Arama kriterlerinizi değiştirmeyi deneyin veya yeni bir context oluşturun.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </DialogContent>

      {/* Context Detail Modal */}
      {selectedContext && (
        <Dialog open={!!selectedContext} onOpenChange={() => setSelectedContext(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <span style={{ color: selectedContext.category.color }}>
                  {selectedContext.category.icon}
                </span>
                {selectedContext.title}
              </DialogTitle>
              <div className="flex items-start justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="secondary">{selectedContext.category.name}</Badge>
                    {selectedContext.is_featured && (
                      <Badge className="bg-yellow-100 text-yellow-800">
                        <Star className="h-3 w-3 mr-1" />
                        Öne Çıkan
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      )}

      {/* Context Creation Modal */}
      <ContextCreationModal
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        onSuccess={() => {
          // Refresh contexts list
          queryClient.invalidateQueries({ queryKey: ['contexts'] });
        }}
      />

      {/* Context Edit Modal */}
      <ContextEditModal
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        context={editingContext}
        onSuccess={() => {
          // Refresh contexts list
          queryClient.invalidateQueries({ queryKey: ['contexts'] });
        }}
      />
    </Dialog>
  );
}