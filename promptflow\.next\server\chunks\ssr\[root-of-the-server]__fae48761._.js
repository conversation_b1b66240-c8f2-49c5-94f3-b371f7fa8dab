module.exports = {

"[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_59fa4ecd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@supabase/node-fetch/lib/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@supabase/node-fetch/lib/index.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/src/components/context-gallery.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_151a8565._.js",
  "server/chunks/ssr/node_modules_7d33768d._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/context-gallery.tsx [app-ssr] (ecmascript)");
    });
});
}),
"[project]/src/components/prompt-workspace.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_enhanced-context-gallery-modal_tsx_e934f118._.js",
  "server/chunks/ssr/src_6f9ab7a2._.js",
  "server/chunks/ssr/node_modules_120cba7e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/prompt-workspace.tsx [app-ssr] (ecmascript)");
    });
});
}),
"[project]/src/components/context-sidebar.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_8acabf02._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/context-sidebar.tsx [app-ssr] (ecmascript)");
    });
});
}),
"[project]/src/components/project-name-editor.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/components/project-name-editor.tsx [app-ssr] (ecmascript)");
    });
});
}),
"[project]/src/components/context-creation-modal.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_41655d2b._.js",
  "server/chunks/ssr/node_modules_57527e7f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/context-creation-modal.tsx [app-ssr] (ecmascript)");
    });
});
}),
"[project]/src/components/context-edit-modal.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_03d27541._.js",
  "server/chunks/ssr/src_e38143be._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/context-edit-modal.tsx [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/recharts/es6/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_recharts_es6_util_d20f1e75._.js",
  "server/chunks/ssr/node_modules_recharts_es6_component_0a115df4._.js",
  "server/chunks/ssr/node_modules_recharts_es6_state_4055506d._.js",
  "server/chunks/ssr/node_modules_recharts_es6_polar_1d49ca75._.js",
  "server/chunks/ssr/node_modules_recharts_es6_cartesian_7c490cdb._.js",
  "server/chunks/ssr/node_modules_recharts_es6_chart_8f0abe20._.js",
  "server/chunks/ssr/node_modules_recharts_es6_3b053125._.js",
  "server/chunks/ssr/node_modules_12006e12._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/recharts/es6/index.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/date-fns/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_date-fns_a6e2724e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/date-fns/index.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/date-fns/locale.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_date-fns_locale_29902f1b._.js",
  "server/chunks/ssr/node_modules_date-fns_98103547._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/date-fns/locale.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/zod/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_zod_556aa4af._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/zod/index.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/papaparse/papaparse.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_papaparse_papaparse_52d14f54.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/papaparse/papaparse.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/react-syntax-highlighter/dist/esm/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_b7c34607._.js",
  "server/chunks/ssr/node_modules_react-syntax-highlighter_dist_esm_295be47f._.js",
  "server/chunks/ssr/node_modules_highlight_js_lib_languages_mathematica_30ae9920.js",
  "server/chunks/ssr/node_modules_highlight_js_lib_languages_ced67849._.js",
  "server/chunks/ssr/node_modules_highlight_js_lib_core_3e9d5726.js",
  "server/chunks/ssr/node_modules_refractor_dd228bab._.js",
  "server/chunks/ssr/node_modules_2da3525e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/react-syntax-highlighter/dist/esm/index.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_react-syntax-highlighter_dist_esm_styles_prism_5a6d4e48._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/framer-motion/dist/es/index.mjs [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_framer-motion_dist_es_e4e80cf2._.js",
  "server/chunks/ssr/node_modules_motion-dom_dist_es_2a012ac2._.js",
  "server/chunks/ssr/node_modules_motion-utils_dist_es_c5c34ebb._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/framer-motion/dist/es/index.mjs [app-ssr] (ecmascript)");
    });
});
}),
"[project]/src/app/dashboard/page.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/app/dashboard/page.tsx [app-ssr] (ecmascript)");
    });
});
}),
"[project]/src/app/profile/page.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_32d4de43._.js",
  "server/chunks/ssr/node_modules_5c376b90._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/app/profile/page.tsx [app-ssr] (ecmascript)");
    });
});
}),
"[project]/src/app/auth/page.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_bd339054._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/app/auth/page.tsx [app-ssr] (ecmascript)");
    });
});
}),

};