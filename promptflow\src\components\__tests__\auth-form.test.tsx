/**
 * AuthForm Component Tests
 * Comprehensive testing for authentication form
 */

import { screen, waitFor } from '@testing-library/react'
import { renderWithProviders, mockSupabase, createMockEvent } from '@/lib/test-utils'
import { AuthForm } from '@/components/auth-form'

// Mock the auth hooks
jest.mock('@/hooks/use-auth', () => ({
  useSignInWithEmail: () => ({
    mutateAsync: jest.fn(),
    isPending: false,
    error: null
  }),
  useSignUpWithEmail: () => ({
    mutateAsync: jest.fn(),
    isPending: false,
    error: null
  })
}))

// Mock the validation library
jest.mock('@/lib/advanced-validation', () => ({
  AdvancedValidator: {
    validate: jest.fn((value: string, rules: any) => ({
      isValid: value.length > 0,
      errors: value.length > 0 ? [] : ['Field is required'],
      sanitizedValue: value
    }))
  },
  COMMON_VALIDATION_RULES: {
    email: { required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
    password: { required: true, minLength: 8 }
  }
}))

// Mock accessibility hooks
jest.mock('@/hooks/use-accessibility', () => ({
  useAccessibility: () => ({
    announce: jest.fn(),
    generateId: jest.fn(() => 'test-id')
  }),
  useFocusManagement: () => ({
    focusFirst: jest.fn()
  })
}))

describe('AuthForm', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders sign in form by default', () => {
    renderWithProviders(<AuthForm />)
    
    expect(screen.getByRole('heading', { name: /giriş yap/i })).toBeInTheDocument()
    expect(screen.getByLabelText(/e-posta/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/şifre/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /giriş yap/i })).toBeInTheDocument()
  })

  it('switches to sign up form when toggle is clicked', async () => {
    const { user } = renderWithProviders(<AuthForm />)
    
    const toggleButton = screen.getByText(/hesap oluştur/i)
    await user.click(toggleButton)
    
    expect(screen.getByRole('heading', { name: /hesap oluştur/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /hesap oluştur/i })).toBeInTheDocument()
  })

  it('validates email field on input', async () => {
    const { user } = renderWithProviders(<AuthForm />)
    
    const emailInput = screen.getByLabelText(/e-posta/i)
    
    // Test invalid email
    await user.type(emailInput, 'invalid-email')
    await user.tab() // Trigger blur
    
    await waitFor(() => {
      expect(screen.getByText(/geçerli e-posta adresi/i)).toBeInTheDocument()
    })
  })

  it('validates password field on input', async () => {
    const { user } = renderWithProviders(<AuthForm />)
    
    // Switch to sign up to test password validation
    const toggleButton = screen.getByText(/hesap oluştur/i)
    await user.click(toggleButton)
    
    const passwordInput = screen.getByLabelText(/şifre/i)
    
    // Test short password
    await user.type(passwordInput, '123')
    await user.tab()
    
    await waitFor(() => {
      expect(screen.getByText(/field is required/i)).toBeInTheDocument()
    })
  })

  it('shows loading state during form submission', async () => {
    const mockSignIn = jest.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))
    
    jest.doMock('@/hooks/use-auth', () => ({
      useSignInWithEmail: () => ({
        mutateAsync: mockSignIn,
        isPending: true,
        error: null
      }),
      useSignUpWithEmail: () => ({
        mutateAsync: jest.fn(),
        isPending: false,
        error: null
      })
    }))

    const { user } = renderWithProviders(<AuthForm />)
    
    const emailInput = screen.getByLabelText(/e-posta/i)
    const passwordInput = screen.getByLabelText(/şifre/i)
    const submitButton = screen.getByRole('button', { name: /giriş yap/i })
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')
    await user.click(submitButton)
    
    expect(submitButton).toBeDisabled()
    expect(screen.getByText(/yükleniyor/i)).toBeInTheDocument()
  })

  it('displays error message on form submission failure', async () => {
    const mockSignIn = jest.fn().mockRejectedValue(new Error('Invalid credentials'))
    
    jest.doMock('@/hooks/use-auth', () => ({
      useSignInWithEmail: () => ({
        mutateAsync: mockSignIn,
        isPending: false,
        error: null
      }),
      useSignUpWithEmail: () => ({
        mutateAsync: jest.fn(),
        isPending: false,
        error: null
      })
    }))

    const { user } = renderWithProviders(<AuthForm />)
    
    const emailInput = screen.getByLabelText(/e-posta/i)
    const passwordInput = screen.getByLabelText(/şifre/i)
    const submitButton = screen.getByRole('button', { name: /giriş yap/i })
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument()
    })
  })

  it('prevents form submission with invalid data', async () => {
    const mockSignIn = jest.fn()
    
    jest.doMock('@/hooks/use-auth', () => ({
      useSignInWithEmail: () => ({
        mutateAsync: mockSignIn,
        isPending: false,
        error: null
      }),
      useSignUpWithEmail: () => ({
        mutateAsync: jest.fn(),
        isPending: false,
        error: null
      })
    }))

    const { user } = renderWithProviders(<AuthForm />)
    
    const submitButton = screen.getByRole('button', { name: /giriş yap/i })
    
    // Try to submit without filling fields
    await user.click(submitButton)
    
    expect(mockSignIn).not.toHaveBeenCalled()
    expect(screen.getByText(/lütfen form hatalarını düzeltin/i)).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    renderWithProviders(<AuthForm />)
    
    const emailInput = screen.getByLabelText(/e-posta/i)
    const passwordInput = screen.getByLabelText(/şifre/i)
    
    expect(emailInput).toHaveAttribute('type', 'email')
    expect(emailInput).toHaveAttribute('autoComplete', 'email')
    expect(passwordInput).toHaveAttribute('type', 'password')
    expect(passwordInput).toHaveAttribute('autoComplete', 'current-password')
  })

  it('supports keyboard navigation', async () => {
    const { user } = renderWithProviders(<AuthForm />)
    
    const emailInput = screen.getByLabelText(/e-posta/i)
    const passwordInput = screen.getByLabelText(/şifre/i)
    const submitButton = screen.getByRole('button', { name: /giriş yap/i })
    
    // Tab through form elements
    await user.tab()
    expect(emailInput).toHaveFocus()
    
    await user.tab()
    expect(passwordInput).toHaveFocus()
    
    await user.tab()
    expect(submitButton).toHaveFocus()
  })

  it('shows visual feedback for validation states', async () => {
    const { user } = renderWithProviders(<AuthForm />)
    
    const emailInput = screen.getByLabelText(/e-posta/i)
    
    // Type valid email
    await user.type(emailInput, '<EMAIL>')
    
    await waitFor(() => {
      expect(emailInput).toHaveClass('border-green-500')
      expect(screen.getByText(/geçerli e-posta adresi/i)).toBeInTheDocument()
    })
  })

  it('clears validation errors when switching between forms', async () => {
    const { user } = renderWithProviders(<AuthForm />)
    
    const emailInput = screen.getByLabelText(/e-posta/i)
    
    // Create validation error
    await user.type(emailInput, 'invalid')
    await user.tab()
    
    await waitFor(() => {
      expect(screen.getByText(/field is required/i)).toBeInTheDocument()
    })
    
    // Switch to sign up
    const toggleButton = screen.getByText(/hesap oluştur/i)
    await user.click(toggleButton)
    
    // Switch back to sign in
    const signInToggle = screen.getByText(/giriş yap/i)
    await user.click(signInToggle)
    
    // Error should be cleared
    expect(screen.queryByText(/field is required/i)).not.toBeInTheDocument()
  })
})
