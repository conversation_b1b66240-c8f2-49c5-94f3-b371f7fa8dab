(()=>{var a={};a.id=8974,a.ids=[8974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1193:(a,b,c)=>{"use strict";c.d(b,{LandingPage:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call LandingPage() from the server but LandingPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\components\\landing-page.tsx","LandingPage")},2995:(a,b,c)=>{"use strict";c.d(b,{LandingPage:()=>u});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(29523),i=c(62688);let j=(0,i.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var k=c(11860),l=c(12941),m=c(56085),n=c(45583),o=c(99891);let p=(0,i.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var q=c(64398),r=c(5336),s=c(25541),t=c(31568);function u(){let[a,b]=(0,e.useState)(!1),{data:c,isLoading:f}=(0,t.Jd)(),i=(0,e.useCallback)(()=>{b(!1)},[]),u={"@context":"https://schema.org","@type":"SoftwareApplication",name:"PromptFlow",alternateName:"Promptbir",description:"AI destekli prompt y\xf6netim platformu. Yapay zeka prompt&apos;larınızı organize edin, takımınızla paylaşın ve verimliliğinizi 10x artırın.",url:"https://promptbir.com",applicationCategory:"DeveloperApplication",operatingSystem:"Web Browser",browserRequirements:"Requires JavaScript. Requires HTML5.",softwareVersion:"1.0",datePublished:"2024-01-01",dateModified:new Date().toISOString(),offers:[{"@type":"Offer",name:"\xdccretsiz Plan",price:"0",priceCurrency:"TRY",description:"5 proje, 100 prompt/proje, temel \xf6zellikler",category:"Free"},{"@type":"Offer",name:"Profesyonel Plan",price:"99",priceCurrency:"TRY",description:"50 proje, 5000 prompt/proje, API erişimi, gelişmiş analitik",category:"Professional"}],aggregateRating:{"@type":"AggregateRating",ratingValue:"4.9",ratingCount:"1250",bestRating:"5",worstRating:"1"},author:{"@type":"Organization",name:"PromptFlow Team",url:"https://promptbir.com"},featureList:["AI destekli prompt y\xf6netimi","Drag & drop organizasyon","Takım \xe7alışması","Context Gallery","API erişimi","Ger\xe7ek zamanlı senkronizasyon","Enterprise g\xfcvenlik"]};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(u)}}),(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Organization",name:"PromptFlow",alternateName:"Promptbir",url:"https://promptbir.com",logo:"https://promptbir.com/logo.png",description:"AI destekli geliştirme s\xfcre\xe7lerini optimize eden minimalist prompt y\xf6netim platformu",foundingDate:"2024",contactPoint:{"@type":"ContactPoint",contactType:"customer service",availableLanguage:["Turkish","English"],url:"https://promptbir.com"},sameAs:["https://twitter.com/promptbir","https://github.com/promptbir","https://linkedin.com/company/promptbir"]})}}),(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:"PromptFlow nedir?",acceptedAnswer:{"@type":"Answer",text:"PromptFlow, AI destekli geliştirme s\xfcre\xe7lerini optimize eden minimalist prompt y\xf6netim platformudur. Prompt'larınızı organize edebilir, takımınızla paylaşabilir ve verimliliğinizi artırabilirsiniz."}},{"@type":"Question",name:"\xdccretsiz plan neler i\xe7erir?",acceptedAnswer:{"@type":"Answer",text:"\xdccretsiz plan 5 proje, proje başına 100 prompt, temel \xf6zellikler ve email destek i\xe7erir."}},{"@type":"Question",name:"Profesyonel plan neler i\xe7erir?",acceptedAnswer:{"@type":"Answer",text:"Profesyonel plan 50 proje, proje başına 5000 prompt, API erişimi, takım \xf6zellikleri, Context Gallery, gelişmiş analitik ve \xf6ncelikli destek i\xe7erir."}}]})}}),(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,d.jsxs)("nav",{className:"border-b border-gray-200 bg-white/80 backdrop-blur-sm sticky top-0 z-50",role:"navigation","aria-label":"Ana navigasyon",children:[(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)("div",{className:"flex-shrink-0 flex items-center",children:[(0,d.jsx)("img",{src:"/logo.png",alt:"Promptbir Logo",className:"h-8 w-auto mr-2"}),(0,d.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Promptbir"})]})}),(0,d.jsx)("div",{className:"hidden md:block",children:(0,d.jsxs)("div",{className:"ml-10 flex items-baseline space-x-4",children:[(0,d.jsx)(g(),{href:"#features",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"\xd6zellikler"}),(0,d.jsx)(g(),{href:"#pricing",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Fiyatlandırma"}),(0,d.jsx)(g(),{href:"/blog",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Blog"}),(0,d.jsx)(g(),{href:"/about",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Hakkımızda"}),(0,d.jsx)(g(),{href:"/contact",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"İletişim"})]})}),(0,d.jsx)("div",{className:"hidden md:flex items-center space-x-4",children:f?(0,d.jsx)("div",{className:"w-20 h-8 bg-gray-200 animate-pulse rounded"}):c?(0,d.jsx)(g(),{href:"/dashboard",children:(0,d.jsxs)(h.$,{size:"sm",className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700",children:["Dashboard",(0,d.jsx)(j,{className:"ml-2 h-4 w-4"})]})}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g(),{href:"/auth",children:(0,d.jsx)(h.$,{variant:"ghost",size:"sm",children:"Giriş Yap"})}),(0,d.jsx)(g(),{href:"/auth",children:(0,d.jsxs)(h.$,{size:"sm",className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700",children:["\xdcye Ol",(0,d.jsx)(j,{className:"ml-2 h-4 w-4"})]})})]})}),(0,d.jsx)("div",{className:"md:hidden",children:(0,d.jsx)(h.$,{variant:"ghost",size:"sm",onClick:()=>b(!a),className:"touch-target",children:a?(0,d.jsx)(k.A,{className:"h-6 w-6"}):(0,d.jsx)(l.A,{className:"h-6 w-6"})})})]})}),a&&(0,d.jsxs)("div",{className:"md:hidden border-t border-gray-200 bg-white shadow-lg",children:[(0,d.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:[(0,d.jsx)(g(),{href:"#features",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target",onClick:i,children:"\xd6zellikler"}),(0,d.jsx)(g(),{href:"#pricing",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target",onClick:i,children:"Fiyatlandırma"}),(0,d.jsx)(g(),{href:"/blog",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target",onClick:i,children:"Blog"}),(0,d.jsx)(g(),{href:"/about",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target",onClick:i,children:"Hakkımızda"}),(0,d.jsx)(g(),{href:"/contact",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target",onClick:i,children:"İletişim"})]}),(0,d.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 space-y-2 safe-area-bottom",children:f?(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"w-full h-10 bg-gray-200 animate-pulse rounded"}),(0,d.jsx)("div",{className:"w-full h-10 bg-gray-200 animate-pulse rounded"})]}):c?(0,d.jsx)(g(),{href:"/dashboard",className:"block",onClick:i,children:(0,d.jsxs)(h.$,{size:"sm",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 justify-center mobile-btn",children:["Dashboard",(0,d.jsx)(j,{className:"ml-2 h-4 w-4"})]})}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g(),{href:"/auth",className:"block",onClick:i,children:(0,d.jsx)(h.$,{variant:"ghost",size:"sm",className:"w-full justify-center mobile-btn",children:"Giriş Yap"})}),(0,d.jsx)(g(),{href:"/auth",className:"block",onClick:i,children:(0,d.jsxs)(h.$,{size:"sm",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 justify-center mobile-btn",children:["\xdcye Ol",(0,d.jsx)(j,{className:"ml-2 h-4 w-4"})]})})]})})]})]}),(0,d.jsxs)("section",{className:"relative overflow-hidden min-h-screen flex items-center","aria-labelledby":"hero-heading",children:[(0,d.jsxs)("div",{className:"absolute inset-0 -z-10",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50"}),(0,d.jsx)("div",{className:"absolute top-0 left-1/4 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"}),(0,d.jsx)("div",{className:"absolute top-0 right-1/4 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"}),(0,d.jsx)("div",{className:"absolute bottom-0 left-1/3 w-72 h-72 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"})]}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center",children:(0,d.jsxs)("div",{className:"mx-auto max-w-4xl",children:[(0,d.jsx)("div",{className:"promptbir-fade-in-up mb-8",children:(0,d.jsxs)("span",{className:"inline-flex items-center rounded-full bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:[(0,d.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"AI Destekli Prompt Y\xf6netimi"]})}),(0,d.jsxs)("h1",{id:"hero-heading",className:"promptbir-fade-in-up text-3xl sm:text-5xl lg:text-7xl font-bold tracking-tight text-gray-900 mb-6 leading-tight",children:[(0,d.jsx)("span",{className:"block",children:"AI Prompt'larınızı"}),(0,d.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent block mt-2",children:"Profesyonelce Y\xf6netin"})]}),(0,d.jsxs)("p",{className:"promptbir-fade-in-up-delay mt-6 text-lg sm:text-xl leading-7 sm:leading-8 text-gray-600 max-w-3xl mx-auto px-4 sm:px-0",children:["Yapay zeka destekli geliştirme s\xfcrecinizi hızlandırın. Prompt'larınızı organize edin, takımınızla paylaşın ve verimliliğinizi ",(0,d.jsx)("span",{className:"font-semibold text-blue-600",children:"10x artırın"}),"."]}),(0,d.jsx)("div",{className:"promptbir-fade-in-up-delay mt-10 flex flex-col sm:flex-row items-center justify-center gap-4 px-4 sm:px-0",children:f?(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 w-full sm:w-auto",children:[(0,d.jsx)("div",{className:"w-full sm:w-48 h-14 bg-gray-200 animate-pulse rounded-xl"}),(0,d.jsx)("div",{className:"w-full sm:w-40 h-14 bg-gray-200 animate-pulse rounded-xl"})]}):c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g(),{href:"/dashboard",className:"w-full sm:w-auto",children:(0,d.jsxs)(h.$,{size:"lg",className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-xl mobile-btn",children:[(0,d.jsx)(n.A,{className:"mr-2 h-5 w-5"}),"Dashboard'a Git",(0,d.jsx)(j,{className:"ml-2 h-5 w-5"})]})}),(0,d.jsxs)(h.$,{variant:"outline",size:"lg",className:"w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-xl border-2 hover:bg-gray-50 mobile-btn",children:[(0,d.jsx)("span",{className:"mr-2",children:"\uD83C\uDFA5"}),"Demo İzle"]})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g(),{href:"/auth",className:"w-full sm:w-auto",children:(0,d.jsxs)(h.$,{size:"lg",className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-xl mobile-btn",children:[(0,d.jsx)(n.A,{className:"mr-2 h-5 w-5"}),"\xdccretsiz Başla",(0,d.jsx)(j,{className:"ml-2 h-5 w-5"})]})}),(0,d.jsxs)(h.$,{variant:"outline",size:"lg",className:"w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-xl border-2 hover:bg-gray-50 mobile-btn",children:[(0,d.jsx)("span",{className:"mr-2",children:"\uD83C\uDFA5"}),"Demo İzle"]})]})}),(0,d.jsxs)("div",{className:"promptbir-fade-in-up-delay mt-16 grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 max-w-2xl mx-auto px-4 sm:px-0",children:[(0,d.jsxs)("div",{className:"text-center p-4 sm:p-0",children:[(0,d.jsx)("div",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"10,000+"}),(0,d.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Aktif Kullanıcı"})]}),(0,d.jsxs)("div",{className:"text-center p-4 sm:p-0",children:[(0,d.jsx)("div",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"1M+"}),(0,d.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Y\xf6netilen Prompt"})]}),(0,d.jsxs)("div",{className:"text-center p-4 sm:p-0",children:[(0,d.jsx)("div",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"99.9%"}),(0,d.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Uptime"})]})]})]})}),(0,d.jsx)("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce",children:(0,d.jsx)("div",{className:"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center",children:(0,d.jsx)("div",{className:"w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse"})})})]}),(0,d.jsx)("section",{id:"features",className:"py-24 bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-20",children:[(0,d.jsxs)("h2",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-4",children:["G\xfc\xe7l\xfc ",(0,d.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"\xd6zellikler"})]}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"AI destekli geliştirme s\xfcrecinizi optimize eden modern ara\xe7lar"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3",children:[(0,d.jsxs)("div",{className:"group relative",children:[(0,d.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"}),(0,d.jsxs)("div",{className:"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,d.jsx)(n.A,{className:"w-6 h-6 text-white"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Hızlı Erişim"}),(0,d.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Prompt'larınıza tek tıkla erişin. Drag & drop ile organize edin. \xc7oklu se\xe7im ile toplu işlemler yapın."})]})]}),(0,d.jsxs)("div",{className:"group relative",children:[(0,d.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"}),(0,d.jsxs)("div",{className:"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,d.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"G\xfcvenli Saklama"}),(0,d.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Enterprise d\xfczeyinde g\xfcvenlik. Row Level Security ile verileriniz tamamen korunur ve sadece size aittir."})]})]}),(0,d.jsxs)("div",{className:"group relative",children:[(0,d.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"}),(0,d.jsxs)("div",{className:"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,d.jsx)(p,{className:"w-6 h-6 text-white"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Takım \xc7alışması"}),(0,d.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Takımınızla prompt'ları paylaşın. Context galeri ile ortak şablonlar oluşturun ve verimliliği artırın."})]})]}),(0,d.jsxs)("div",{className:"group relative",children:[(0,d.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"}),(0,d.jsxs)("div",{className:"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,d.jsx)(m.A,{className:"w-6 h-6 text-white"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"AI Optimizasyonu"}),(0,d.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Prompt'larınızı AI ile optimize edin. Akıllı kategorileme ve otomatik etiketleme ile d\xfczen sağlayın."})]})]}),(0,d.jsxs)("div",{className:"group relative",children:[(0,d.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"}),(0,d.jsxs)("div",{className:"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,d.jsx)(j,{className:"w-6 h-6 text-white"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Ger\xe7ek Zamanlı"}),(0,d.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"T\xfcm değişiklikler anında senkronize olur. Takım \xfcyeleri aynı anda \xe7alışabilir, \xe7akışma olmaz."})]})]}),(0,d.jsxs)("div",{className:"group relative",children:[(0,d.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"}),(0,d.jsxs)("div",{className:"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,d.jsx)("span",{className:"text-white font-bold text-lg",children:"∞"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Sınırsız Kullanım"}),(0,d.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Sınırsız proje, prompt ve kullanıcı. B\xfcy\xfcd\xfck\xe7e \xf6l\xe7eklenen altyapı ile hi\xe7 endişelenmeyin."})]})]})]})]})}),(0,d.jsx)("section",{id:"testimonials",className:"py-24 bg-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-20",children:[(0,d.jsxs)("h2",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-4",children:["Kullanıcılarımız ",(0,d.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Ne Diyor?"})]}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Binlerce geliştirici Promptbir ile verimliliğini artırdı"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-all duration-300",children:[(0,d.jsx)("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsx)(q.A,{className:"w-5 h-5 text-yellow-400 fill-current"},b))}),(0,d.jsx)("p",{className:"text-gray-700 mb-6 leading-relaxed",children:'"Promptbir sayesinde prompt y\xf6netimi \xe7ok kolay oldu. Takımımızla paylaştığımız şablonlar sayesinde geliştirme hızımız 10x arttı."'}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4",children:(0,d.jsx)("span",{className:"text-blue-600 font-semibold",children:"AK"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-semibold text-gray-900",children:"Ahmet Ocak"}),(0,d.jsx)("div",{className:"text-gray-600 text-sm",children:"Senior Developer, TechCorp"})]})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-all duration-300",children:[(0,d.jsx)("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsx)(q.A,{className:"w-5 h-5 text-yellow-400 fill-current"},b))}),(0,d.jsx)("p",{className:"text-gray-700 mb-6 leading-relaxed",children:'"AI projelerinde prompt versiyonlama \xe7ok kritikti. Promptbir ile hem organize hem de g\xfcvenli bir şekilde y\xf6netebiliyoruz."'}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4",children:(0,d.jsx)("span",{className:"text-purple-600 font-semibold",children:"EY"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-semibold text-gray-900",children:"Elif Yılmaz"}),(0,d.jsx)("div",{className:"text-gray-600 text-sm",children:"AI Engineer, StartupX"})]})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-all duration-300",children:[(0,d.jsx)("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsx)(q.A,{className:"w-5 h-5 text-yellow-400 fill-current"},b))}),(0,d.jsx)("p",{className:"text-gray-700 mb-6 leading-relaxed",children:'"Drag & drop \xf6zelliği harika! Prompt\'ları organize etmek hi\xe7 bu kadar kolay olmamıştı. Kesinlikle tavsiye ederim."'}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4",children:(0,d.jsx)("span",{className:"text-green-600 font-semibold",children:"M\xd6"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-semibold text-gray-900",children:"Mehmet \xd6zkan"}),(0,d.jsx)("div",{className:"text-gray-600 text-sm",children:"Product Manager, InnovateLab"})]})]})]})]})]})}),(0,d.jsx)("section",{id:"pricing",className:"py-24 bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"text-center mb-20",children:[(0,d.jsxs)("h2",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-4",children:["Basit ve ",(0,d.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Şeffaf Fiyatlandırma"})]}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"İhtiyacınıza g\xf6re se\xe7in, istediğiniz zaman değiştirin"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto",children:[(0,d.jsx)("div",{className:"bg-white rounded-2xl shadow-lg p-8 border-2 border-gray-200 hover:border-blue-300 transition-all duration-300",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Başlangı\xe7"}),(0,d.jsx)("div",{className:"text-4xl font-bold text-gray-900 mb-1",children:"\xdccretsiz"}),(0,d.jsx)("p",{className:"text-gray-600 mb-8",children:"Bireysel kullanım i\xe7in"}),(0,d.jsxs)("ul",{className:"space-y-4 mb-8 text-left",children:[(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"5 proje"})]}),(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"100 prompt/proje"})]}),(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"Sınırlı Context Gallery"})]}),(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"Email destek"})]})]}),(0,d.jsx)(g(),{href:"/dashboard",children:(0,d.jsx)(h.$,{className:"w-full",variant:"outline",children:"Hemen Başla"})})]})}),(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 border-2 border-blue-500 relative transform scale-105 hover:scale-110 transition-all duration-300",children:[(0,d.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,d.jsx)("span",{className:"bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold",children:"En Pop\xfcler"})}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Profesyonel"}),(0,d.jsx)("div",{className:"text-4xl font-bold text-gray-900 mb-1",children:"₺99"}),(0,d.jsx)("p",{className:"text-gray-600 mb-2",children:"aylık"}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-6",children:"veya ₺990/yıl (%17 indirim)"}),(0,d.jsxs)("ul",{className:"space-y-4 mb-8 text-left",children:[(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"50 proje"})]}),(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"5.000 prompt/proje"})]}),(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"API erişimi"})]}),(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"Takım \xf6zellikleri"})]}),(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"Context Gallery"})]}),(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"Gelişmiş analitik"})]}),(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"\xd6ncelikli destek"})]})]}),(0,d.jsx)(g(),{href:"/dashboard",children:(0,d.jsx)(h.$,{className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700",children:"Pro'ya Ge\xe7"})})]})]}),(0,d.jsx)("div",{className:"bg-white rounded-2xl shadow-lg p-8 border-2 border-gray-200 hover:border-purple-300 transition-all duration-300",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Kurumsal"}),(0,d.jsx)("div",{className:"text-4xl font-bold text-gray-900 mb-1",children:"\xd6zel"}),(0,d.jsx)("p",{className:"text-gray-600 mb-8",children:"B\xfcy\xfck takımlar i\xe7in"}),(0,d.jsxs)("ul",{className:"space-y-4 mb-8 text-left",children:[(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"T\xfcm Pro \xf6zellikler"})]}),(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"SSO entegrasyonu"})]}),(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"\xd6zel deployment"})]}),(0,d.jsxs)("li",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,d.jsx)("span",{children:"24/7 destek"})]})]}),(0,d.jsx)(h.$,{className:"w-full",variant:"outline",children:"İletişime Ge\xe7"})]})})]}),(0,d.jsx)("div",{className:"mt-16 text-center",children:(0,d.jsx)("p",{className:"text-gray-600",children:"T\xfcm planlar 14 g\xfcn \xfccretsiz deneme i\xe7erir. İstediğiniz zaman iptal edebilirsiniz."})})]})}),(0,d.jsxs)("section",{className:"relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600"}),(0,d.jsx)("div",{className:"relative max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:py-24 lg:px-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("h2",{className:"text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl",children:[(0,d.jsx)("span",{className:"block",children:"Hazır mısınız?"}),(0,d.jsx)("span",{className:"block mt-2 text-blue-100",children:"Hemen başlayın."})]}),(0,d.jsx)("p",{className:"mt-6 text-xl text-blue-100 max-w-2xl mx-auto",children:"Binlerce geliştirici gibi siz de AI prompt'larınızı profesyonelce y\xf6netin"}),(0,d.jsxs)("div",{className:"mt-10 flex flex-col sm:flex-row items-center justify-center gap-4",children:[(0,d.jsx)(g(),{href:"/dashboard",children:(0,d.jsxs)(h.$,{size:"lg",variant:"secondary",className:"bg-white text-blue-600 hover:bg-gray-50 px-8 py-4 text-lg font-semibold rounded-xl",children:[(0,d.jsx)(r.A,{className:"mr-2 h-5 w-5"}),"\xdccretsiz Başla",(0,d.jsx)(j,{className:"ml-2 h-5 w-5"})]})}),(0,d.jsxs)("div",{className:"flex items-center text-blue-100",children:[(0,d.jsx)(r.A,{className:"w-5 h-5 mr-2"}),(0,d.jsx)("span",{children:"Kredi kartı gerektirmez"})]})]}),(0,d.jsxs)("div",{className:"mt-12 flex flex-col sm:flex-row items-center justify-center gap-8 text-blue-100",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(s.A,{className:"w-5 h-5 mr-2"}),(0,d.jsx)("span",{children:"10,000+ Aktif Kullanıcı"})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(o.A,{className:"w-5 h-5 mr-2"}),(0,d.jsx)("span",{children:"Enterprise G\xfcvenlik"})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(n.A,{className:"w-5 h-5 mr-2"}),(0,d.jsx)("span",{children:"99.9% Uptime"})]})]})]})})]}),(0,d.jsx)("footer",{className:"bg-gray-900",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,d.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)(m.A,{className:"h-8 w-8 text-blue-400"}),(0,d.jsx)("span",{className:"ml-2 text-xl font-bold text-white",children:"Promptbir"})]}),(0,d.jsx)("p",{className:"text-gray-400 mb-6 max-w-md",children:"AI prompt'larınızı profesyonelce y\xf6netin. Takımınızla paylaşın, organize edin ve verimliliğinizi artırın."}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsxs)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:[(0,d.jsx)("span",{className:"sr-only",children:"Twitter"}),(0,d.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{d:"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"})})]}),(0,d.jsxs)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:[(0,d.jsx)("span",{className:"sr-only",children:"GitHub"}),(0,d.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z",clipRule:"evenodd"})})]}),(0,d.jsxs)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:[(0,d.jsx)("span",{className:"sr-only",children:"LinkedIn"}),(0,d.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z",clipRule:"evenodd"})})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-white font-semibold mb-4",children:"\xdcr\xfcn"}),(0,d.jsxs)("ul",{className:"space-y-3",children:[(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"#features",className:"text-gray-400 hover:text-white transition-colors",children:"\xd6zellikler"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"#pricing",className:"text-gray-400 hover:text-white transition-colors",children:"Fiyatlandırma"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"/dashboard",className:"text-gray-400 hover:text-white transition-colors",children:"Demo"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"API"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Entegrasyonlar"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-white font-semibold mb-4",children:"Şirket"}),(0,d.jsxs)("ul",{className:"space-y-3",children:[(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"/about",className:"text-gray-400 hover:text-white transition-colors",children:"Hakkımızda"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"/blog",className:"text-gray-400 hover:text-white transition-colors",children:"Blog"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"/careers",className:"text-gray-400 hover:text-white transition-colors",children:"Kariyer"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"/contact",className:"text-gray-400 hover:text-white transition-colors",children:"İletişim"})}),(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:"/bug-report",className:"text-gray-400 hover:text-white transition-colors",children:"Bug Report"})})]})]})]}),(0,d.jsx)("div",{className:"mt-12 pt-8 border-t border-gray-800",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,d.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2024 Promptbir. T\xfcm hakları saklıdır."}),(0,d.jsxs)("div",{className:"mt-4 md:mt-0 flex space-x-6",children:[(0,d.jsx)("a",{href:"/privacy",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Gizlilik Politikası"}),(0,d.jsx)("a",{href:"/terms",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Kullanım Şartları"}),(0,d.jsx)("a",{href:"/cookies",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"\xc7erez Politikası"})]})]})})]})})]})]})}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8266:(a,b,c)=>{"use strict";c.d(b,{L:()=>d});let d=(0,c(59522).createBrowserClient)("https://iqehopwgrczylqliajww.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlxZWhvcHdncmN6eWxxbGlhand3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2OTQyNzksImV4cCI6MjA2ODI3MDI3OX0.9zrZ8Zot6SvsjxTGMyh3IYFqrr_QXQkKSNU4rJNz0VM",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0,flowType:"pkce",debug:!1,storageKey:"sb-iqehopwgrczylqliajww-auth-token",storage:void 0},db:{schema:"public"},realtime:{params:{eventsPerSecond:10}},global:{headers:{"X-Client-Info":"promptflow-web","X-Client-Version":"1.0.0"}}})},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},11997:a=>{"use strict";a.exports=require("punycode")},12941:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g,metadata:()=>f});var d=c(37413),e=c(1193);let f=c(51286).$o.generateMetadata({title:"PromptFlow - AI Destekli Prompt Y\xf6netim Platformu",description:"AI destekli geliştirme s\xfcre\xe7lerini optimize eden minimalist prompt y\xf6netim platformu. Prompt'larınızı organize edin, takımınızla paylaşın ve verimliliğinizi 10x artırın. 10,000+ geliştirici tarafından g\xfcvenilir.",keywords:["prompt y\xf6netimi","AI ara\xe7ları","yapay zeka","geliştirici ara\xe7ları","prompt engineering","AI destekli geliştirme","kod optimizasyonu","proje y\xf6netimi","ChatGPT prompts","AI productivity","prompt organizasyonu","takım \xe7alışması","context gallery","API erişimi","ger\xe7ek zamanlı senkronizasyon"],url:"/",type:"website"});function g(){return(0,d.jsx)(e.LandingPage,{})}},22383:(a,b,c)=>{Promise.resolve().then(c.bind(c,1193))},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30631:(a,b,c)=>{Promise.resolve().then(c.bind(c,2995))},31568:(a,b,c)=>{"use strict";c.d(b,{Jd:()=>l,Rt:()=>o,a7:()=>m,rU:()=>k,wF:()=>n});var d=c(8693),e=c(51423),f=c(54050),g=c(8266);c(43210);var h=c(16189),i=c(52581);function j(a,b){let c=a instanceof Error?a.message:String(a);return!!(c.includes("Invalid Refresh Token")||c.includes("Refresh Token Not Found")||c.includes("refresh_token_not_found"))&&(console.warn("Refresh token hatası, oturum temizleniyor:",c),g.L.auth.signOut({scope:"local"}),b.clear(),!0)}function k(){(0,d.jE)(),(0,h.useRouter)()}function l(){let a=(0,d.jE)();return(0,e.I)({queryKey:["user"],queryFn:async()=>{console.log(`🔐 [USE_USER] Getting user...`);try{let{data:{session:b},error:c}=await g.L.auth.getSession();if(c)return console.error(`❌ [USE_USER] Session error:`,c),null;if(!b)return console.log(`🔐 [USE_USER] No session found`),null;console.log(`🔐 [USE_USER] Session found, getting user...`);let{data:{user:d},error:e}=await g.L.auth.getUser();if(e){if(console.error(`❌ [USE_USER] Error getting user:`,e),e.message.includes("Auth session missing"))return console.log(`🔄 [USE_USER] Auth session missing, returning null`),null;if(j(e,a))return console.log(`🔄 [USE_USER] Handled auth error, returning null`),null;throw Error(e.message)}return console.log(`✅ [USE_USER] User retrieved:`,d?.email||"null"),d}catch(b){if(console.error(`💥 [USE_USER] Exception:`,b),(b instanceof Error?b.message:String(b)).includes("Auth session missing"))return console.log(`🔄 [USE_USER] Auth session missing exception, returning null`),null;if(j(b,a))return console.log(`🔄 [USE_USER] Handled auth error exception, returning null`),null;throw b}},staleTime:3e5,retry:(a,b)=>{let c=b instanceof Error?b.message:String(b);return(console.log(`🔄 [USE_USER] Retry attempt ${a}, error: ${c}`),c.includes("Invalid Refresh Token")||c.includes("Refresh Token Not Found")||c.includes("Auth session missing"))?(console.log(`🚫 [USE_USER] Not retrying auth error: ${c}`),!1):a<3}})}function m(){let a=(0,d.jE)();return(0,f.n)({mutationFn:async({email:a,password:b})=>{console.log(`🔑 [SIGN_IN] Attempting login for: ${a}`);let{data:c,error:d}=await g.L.auth.signInWithPassword({email:a,password:b});if(d)throw console.error(`❌ [SIGN_IN] Login failed:`,d),Error(d.message);return console.log(`✅ [SIGN_IN] Login successful:`,{userId:c.user?.id,email:c.user?.email,hasSession:!!c.session}),c},onSuccess:()=>{console.log(`🎉 [SIGN_IN] onSuccess triggered, invalidating queries`),a.invalidateQueries({queryKey:["user"]}),a.invalidateQueries({queryKey:["session"]})},onError:a=>{console.error(`💥 [SIGN_IN] onError triggered:`,a)}})}function n(){let a=(0,d.jE)();return(0,f.n)({mutationFn:async({email:a,password:b})=>{let{data:c,error:d}=await g.L.auth.signUp({email:a,password:b});if(d)throw Error(d.message);return c},onSuccess:()=>{a.invalidateQueries({queryKey:["user"]}),a.invalidateQueries({queryKey:["session"]})}})}function o(){let a=(0,d.jE)(),b=(0,h.useRouter)();return(0,f.n)({mutationFn:async()=>{console.log("Starting logout process...");let{error:a}=await g.L.auth.signOut({scope:"global"});if(a)throw console.error("Logout error:",a),Error(a.message);console.log("Logout successful")},onSuccess:()=>{console.log("Logout onSuccess triggered"),i.oR.success("Başarıyla \xe7ıkış yapıldı",{description:"Giriş sayfasına y\xf6nlendiriliyorsunuz..."}),a.clear(),localStorage.removeItem("promptflow-app-store"),sessionStorage.clear(),setTimeout(()=>{console.log("Redirecting to auth page..."),b.push("/auth"),b.refresh(),window.location.href="/auth"},1e3)},onError:c=>{console.error("Logout failed:",c),i.oR.error("\xc7ıkış yapılırken hata oluştu",{description:"Yine de oturum temizleniyor..."}),a.clear(),localStorage.removeItem("promptflow-app-store"),sessionStorage.clear(),setTimeout(()=>{b.push("/auth"),b.refresh()},1e3)}})}},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},45583:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},47990:()=>{},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},56085:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},97453:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,21204)),"C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,39998)),"C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},99891:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,904,5814,3303,1093],()=>b(b.s=97453));module.exports=c})();