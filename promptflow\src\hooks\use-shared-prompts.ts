'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabaseBrowser as supabase } from '@/lib/supabase-browser'
import { Database } from '@/lib/supabase'
import { toast } from 'sonner'

// Type definitions
export type SharedPrompt = {
  id: string
  prompt_id: string
  user_id: string
  share_token: string
  title: string | null
  description: string | null
  is_public: boolean
  password_hash: string | null
  expires_at: string | null
  view_count: number
  copy_count: number
  is_active: boolean
  metadata: any
  created_at: string
  updated_at: string
}

export type SharedPromptWithDetails = SharedPrompt & {
  prompt: {
    id: string
    prompt_text: string
    title: string | null
    description: string | null
    category: string | null
    tags: string[]
    task_code: string | null
    created_at: string
  }
  project_name: string
  author_email: string
}

export type CreateSharedPromptParams = {
  prompt_id: string
  title?: string
  description?: string
  is_public?: boolean
  password?: string
  expires_at?: string
}

export type ShareUrlResult = {
  id: string
  share_token: string
  share_url: string
  created_at: string
}

// Hook: Kullanıcının paylaştığı prompt'ları listele
export function useUserSharedPrompts() {
  return useQuery({
    queryKey: ['shared-prompts', 'user'],
    queryFn: async () => {
      const { data: user } = await supabase.auth.getUser()
      if (!user.user) throw new Error('Not authenticated')

      const { data, error } = await supabase
        .from('shared_prompts')
        .select(`
          *,
          prompt:prompts(
            id,
            prompt_text,
            title,
            description,
            category,
            tags,
            task_code,
            created_at,
            project:projects(name)
          )
        `)
        .eq('user_id', user.user.id)
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) throw error

      return data.map(item => ({
        ...item,
        project_name: item.prompt?.project?.name || 'Unknown Project'
      })) as (SharedPrompt & { 
        prompt: any
        project_name: string 
      })[]
    },
    staleTime: 5 * 60 * 1000, // 5 dakika
  })
}

// Hook: Paylaşım oluştur
export function useCreateSharedPrompt() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (params: CreateSharedPromptParams): Promise<ShareUrlResult> => {
      const { data: user } = await supabase.auth.getUser()
      if (!user.user) throw new Error('Not authenticated')

      // Önce prompt'un kullanıcıya ait olduğunu kontrol et
      const { data: prompt, error: promptError } = await supabase
        .from('prompts')
        .select('id, user_id')
        .eq('id', params.prompt_id)
        .eq('user_id', user.user.id)
        .single()

      if (promptError || !prompt) {
        throw new Error('Prompt not found or access denied')
      }

      // Paylaşım token'ı oluştur
      const shareToken = generateShareToken()
      
      // Şifre hash'le (eğer varsa)
      let passwordHash = null
      if (params.password) {
        // Client-side'da basit hash (production'da server-side yapılmalı)
        passwordHash = await hashPassword(params.password)
      }

      const { data, error } = await supabase
        .from('shared_prompts')
        .insert({
          prompt_id: params.prompt_id,
          user_id: user.user.id,
          share_token: shareToken,
          title: params.title || null,
          description: params.description || null,
          is_public: params.is_public ?? true,
          password_hash: passwordHash,
          expires_at: params.expires_at || null,
        })
        .select()
        .single()

      if (error) throw error

      const shareUrl = `${window.location.origin}/share/${shareToken}`

      return {
        id: data.id,
        share_token: shareToken,
        share_url: shareUrl,
        created_at: data.created_at
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['shared-prompts'] })
      toast.success('Paylaşım linki oluşturuldu!', {
        description: 'Link panoya kopyalandı'
      })
      
      // Linki panoya kopyala
      navigator.clipboard.writeText(data.share_url).catch(console.error)
    },
    onError: (error) => {
      console.error('Share creation error:', error)
      toast.error('Paylaşım oluşturulamadı', {
        description: error instanceof Error ? error.message : 'Bilinmeyen hata'
      })
    }
  })
}

// Hook: Paylaşımı sil/deaktive et
export function useDeleteSharedPrompt() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (sharedPromptId: string) => {
      const { data: user } = await supabase.auth.getUser()
      if (!user.user) throw new Error('Not authenticated')

      const { error } = await supabase
        .from('shared_prompts')
        .update({ is_active: false })
        .eq('id', sharedPromptId)
        .eq('user_id', user.user.id)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shared-prompts'] })
      toast.success('Paylaşım kaldırıldı')
    },
    onError: (error) => {
      console.error('Delete shared prompt error:', error)
      toast.error('Paylaşım kaldırılamadı')
    }
  })
}

// Hook: Paylaşılan prompt'u görüntüle (public)
export function useSharedPrompt(shareToken: string, password?: string) {
  return useQuery({
    queryKey: ['shared-prompt', shareToken, password],
    queryFn: async (): Promise<SharedPromptWithDetails> => {
      // Önce paylaşımı al
      const { data: sharedPrompt, error } = await supabase
        .from('shared_prompts')
        .select(`
          *,
          prompt:prompts(
            id,
            prompt_text,
            title,
            description,
            category,
            tags,
            task_code,
            created_at,
            project:projects(name)
          )
        `)
        .eq('share_token', shareToken)
        .eq('is_active', true)
        .single()

      if (error) throw new Error('Paylaşım bulunamadı veya süresi dolmuş')

      // Şifre kontrolü (basit client-side kontrol)
      if (sharedPrompt.password_hash && password) {
        const isPasswordValid = await verifyPassword(password, sharedPrompt.password_hash)
        if (!isPasswordValid) {
          throw new Error('Şifre yanlış')
        }
      } else if (sharedPrompt.password_hash && !password) {
        throw new Error('Şifre gerekli')
      }

      // Son kullanma tarihi kontrolü
      if (sharedPrompt.expires_at && new Date(sharedPrompt.expires_at) < new Date()) {
        throw new Error('Paylaşımın süresi dolmuş')
      }

      // Görüntülenme sayısını artır
      supabase
        .from('shared_prompts')
        .update({ view_count: sharedPrompt.view_count + 1 })
        .eq('id', sharedPrompt.id)
        .then(() => {
          // Analytics kaydı ekle
          recordView(shareToken)
        })

      return {
        ...sharedPrompt,
        project_name: sharedPrompt.prompt?.project?.name || 'Unknown Project',
        author_email: 'Unknown Author' // Bu bilgiyi ayrı bir query ile alacağız
      } as SharedPromptWithDetails
    },
    enabled: !!shareToken,
    staleTime: 0, // Her zaman fresh data al
    retry: false, // Hata durumunda tekrar deneme
  })
}

// Utility functions
function generateShareToken(): string {
  // Güvenli token oluştur
  const array = new Uint8Array(16)
  crypto.getRandomValues(array)
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
}

async function hashPassword(password: string): Promise<string> {
  // Basit client-side hash (production'da server-side yapılmalı)
  const encoder = new TextEncoder()
  const data = encoder.encode(password)
  const hash = await crypto.subtle.digest('SHA-256', data)
  return Array.from(new Uint8Array(hash), b => b.toString(16).padStart(2, '0')).join('')
}

async function verifyPassword(password: string, hash: string): Promise<boolean> {
  const passwordHash = await hashPassword(password)
  return passwordHash === hash
}

function recordView(shareToken: string) {
  // Analytics için view kaydı
  const viewData = {
    share_token: shareToken,
    viewer_ip: null, // Server-side'da alınmalı
    viewer_user_agent: navigator.userAgent,
    referrer: document.referrer || null,
    session_id: generateSessionId(),
    viewed_at: new Date().toISOString()
  }

  // Bu veri server-side endpoint'e gönderilmeli
  fetch('/api/shared-prompts/record-view', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(viewData)
  }).catch(console.error)
}

function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}
