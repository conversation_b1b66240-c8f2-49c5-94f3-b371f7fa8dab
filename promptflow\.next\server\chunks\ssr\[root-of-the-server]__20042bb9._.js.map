{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/app/blog/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport Link from 'next/link'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  ArrowLeft, \n  Sparkles, \n  Calendar, \n  Clock, \n  User,\n  ArrowRight,\n  BookOpen,\n  TrendingUp,\n  Zap,\n  Shield,\n  Users\n} from 'lucide-react'\n\nexport const metadata: Metadata = {\n  title: 'Blog - PromptBir | AI ve Prompt Yönetimi Hakkında Yazılar',\n  description: 'AI teknolojileri, prompt engineering, verimlilik artırma ve PromptBir platformu hakkında güncel blog yazıları.',\n  keywords: [\n    'AI blog',\n    'prompt engineering',\n    'yapay zeka',\n    'verimlilik',\n    'PromptBir blog',\n    'AI araçları'\n  ],\n  openGraph: {\n    title: 'Blog - PromptBir',\n    description: 'AI teknolojileri ve prompt yönetimi hakkında güncel yazılar',\n    type: 'website',\n    url: 'https://promptbir.com/blog'\n  }\n}\n\nconst blogPosts = [\n  {\n    id: 1,\n    title: 'PromptBir ile AI Verimliliğinizi 10 Kat Artırın',\n    slug: 'promptbir-ile-ai-verimlilik',\n    excerpt: 'AI prompt\\'larınızı organize ederek nasıl daha verimli çalışabileceğinizi öğrenin. PromptBir\\'in sunduğu özellikler ile iş akışınızı optimize edin.',\n    content: 'AI teknolojilerinin hızla geliştiği günümüzde, doğru prompt yönetimi kritik önem taşıyor...',\n    author: 'PromptBir Ekibi',\n    publishDate: '2024-01-15',\n    readTime: '8 dakika',\n    category: 'Verimlilik',\n    tags: ['AI', 'Verimlilik', 'Prompt Yönetimi'],\n    featured: true\n  },\n  {\n    id: 2,\n    title: 'Prompt Engineering: Başlangıçtan İleri Seviyeye Rehber',\n    slug: 'prompt-engineering-rehber',\n    excerpt: 'Etkili prompt yazma sanatını öğrenin. Temel prensiplerden ileri tekniklere kadar kapsamlı rehber.',\n    content: 'Prompt engineering, AI ile etkileşimde bulunmanın en önemli becerilerinden biri...',\n    author: 'Dr. Ahmet Yılmaz',\n    publishDate: '2024-01-10',\n    readTime: '12 dakika',\n    category: 'Eğitim',\n    tags: ['Prompt Engineering', 'AI', 'Eğitim'],\n    featured: true\n  },\n  {\n    id: 3,\n    title: 'Takım Çalışmasında AI Prompt Paylaşımının Önemi',\n    slug: 'takim-calismasi-prompt-paylasimi',\n    excerpt: 'Ekip halinde çalışırken prompt paylaşımının nasıl verimliliği artırdığını ve en iyi uygulamaları keşfedin.',\n    content: 'Modern iş dünyasında takım çalışması ve bilgi paylaşımı kritik önem taşıyor...',\n    author: 'Elif Kaya',\n    publishDate: '2024-01-05',\n    readTime: '6 dakika',\n    category: 'Takım Çalışması',\n    tags: ['Takım Çalışması', 'Paylaşım', 'Verimlilik'],\n    featured: false\n  },\n  {\n    id: 4,\n    title: 'AI Güvenliği ve Prompt Injection Saldırıları',\n    slug: 'ai-guvenlik-prompt-injection',\n    excerpt: 'AI sistemlerinin güvenlik açıkları ve prompt injection saldırılarından nasıl korunabileceğinizi öğrenin.',\n    content: 'AI sistemlerinin yaygınlaşmasıyla birlikte güvenlik konuları da önem kazanıyor...',\n    author: 'Mehmet Demir',\n    publishDate: '2023-12-28',\n    readTime: '10 dakika',\n    category: 'Güvenlik',\n    tags: ['AI Güvenliği', 'Prompt Injection', 'Siber Güvenlik'],\n    featured: false\n  },\n  {\n    id: 5,\n    title: 'PromptBir API: Geliştiriciler için Kapsamlı Rehber',\n    slug: 'promptbir-api-rehber',\n    excerpt: 'PromptBir API\\'sini kullanarak kendi uygulamalarınızda prompt yönetimi nasıl entegre edebileceğinizi öğrenin.',\n    content: 'PromptBir API, geliştiricilerin kendi uygulamalarında prompt yönetimi yapabilmelerini sağlar...',\n    author: 'PromptBir Geliştirici Ekibi',\n    publishDate: '2023-12-20',\n    readTime: '15 dakika',\n    category: 'Geliştirici',\n    tags: ['API', 'Geliştirici', 'Entegrasyon'],\n    featured: false\n  }\n]\n\nconst categories = ['Tümü', 'Verimlilik', 'Eğitim', 'Takım Çalışması', 'Güvenlik', 'Geliştirici']\n\nexport default function BlogPage() {\n  const featuredPosts = blogPosts.filter(post => post.featured)\n  const regularPosts = blogPosts.filter(post => !post.featured)\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      {/* Header */}\n      <header className=\"border-b border-gray-200 bg-white/80 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <Link \n              href=\"/\" \n              className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <ArrowLeft className=\"h-5 w-5\" />\n              <span>Ana Sayfaya Dön</span>\n            </Link>\n            <div className=\"flex items-center space-x-2\">\n              <Sparkles className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">PromptBir</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Hero Section */}\n        <div className=\"text-center mb-16\">\n          <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n            <BookOpen className=\"h-8 w-8 text-blue-600\" />\n          </div>\n          <h1 className=\"text-4xl sm:text-5xl font-bold text-gray-900 mb-6\">\n            PromptBir Blog\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            AI teknolojileri, prompt engineering ve verimlilik artırma konularında \n            güncel yazılar ve rehberler.\n          </p>\n        </div>\n\n        {/* Featured Posts */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-8 flex items-center gap-3\">\n            <TrendingUp className=\"h-8 w-8 text-orange-600\" />\n            Öne Çıkan Yazılar\n          </h2>\n          <div className=\"grid md:grid-cols-2 gap-8\">\n            {featuredPosts.map((post) => (\n              <Card key={post.id} className=\"shadow-lg hover:shadow-xl transition-shadow group\">\n                <CardHeader>\n                  <div className=\"flex items-center gap-2 mb-3\">\n                    <Badge variant=\"secondary\" className=\"bg-orange-100 text-orange-700\">\n                      Öne Çıkan\n                    </Badge>\n                    <Badge variant=\"outline\">\n                      {post.category}\n                    </Badge>\n                  </div>\n                  <CardTitle className=\"text-xl group-hover:text-blue-600 transition-colors\">\n                    <Link href={`/blog/${post.slug}`}>\n                      {post.title}\n                    </Link>\n                  </CardTitle>\n                  <CardDescription className=\"text-base leading-relaxed\">\n                    {post.excerpt}\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"flex items-center gap-1\">\n                        <User className=\"h-4 w-4\" />\n                        <span>{post.author}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Calendar className=\"h-4 w-4\" />\n                        <span>{new Date(post.publishDate).toLocaleDateString('tr-TR')}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Clock className=\"h-4 w-4\" />\n                        <span>{post.readTime}</span>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"flex flex-wrap gap-2 mb-4\">\n                    {post.tags.map((tag, index) => (\n                      <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                        {tag}\n                      </Badge>\n                    ))}\n                  </div>\n                  <Link \n                    href={`/blog/${post.slug}`}\n                    className=\"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium group-hover:gap-3 transition-all\"\n                  >\n                    Devamını Oku\n                    <ArrowRight className=\"h-4 w-4\" />\n                  </Link>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </section>\n\n        {/* Category Filter */}\n        <section className=\"mb-8\">\n          <div className=\"flex flex-wrap gap-3 justify-center\">\n            {categories.map((category) => (\n              <Badge \n                key={category}\n                variant={category === 'Tümü' ? 'default' : 'outline'}\n                className=\"cursor-pointer hover:bg-blue-100 hover:text-blue-700 transition-colors px-4 py-2\"\n              >\n                {category}\n              </Badge>\n            ))}\n          </div>\n        </section>\n\n        {/* Regular Posts */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-8 flex items-center gap-3\">\n            <BookOpen className=\"h-8 w-8 text-blue-600\" />\n            Tüm Yazılar\n          </h2>\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {regularPosts.map((post) => (\n              <Card key={post.id} className=\"shadow-lg hover:shadow-xl transition-shadow group\">\n                <CardHeader>\n                  <div className=\"flex items-center gap-2 mb-3\">\n                    <Badge variant=\"outline\">\n                      {post.category}\n                    </Badge>\n                  </div>\n                  <CardTitle className=\"text-lg group-hover:text-blue-600 transition-colors\">\n                    <Link href={`/blog/${post.slug}`}>\n                      {post.title}\n                    </Link>\n                  </CardTitle>\n                  <CardDescription className=\"leading-relaxed\">\n                    {post.excerpt}\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                    <div className=\"flex items-center gap-1\">\n                      <Calendar className=\"h-4 w-4\" />\n                      <span>{new Date(post.publishDate).toLocaleDateString('tr-TR')}</span>\n                    </div>\n                    <div className=\"flex items-center gap-1\">\n                      <Clock className=\"h-4 w-4\" />\n                      <span>{post.readTime}</span>\n                    </div>\n                  </div>\n                  <div className=\"flex flex-wrap gap-1 mb-4\">\n                    {post.tags.slice(0, 2).map((tag, index) => (\n                      <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                        {tag}\n                      </Badge>\n                    ))}\n                    {post.tags.length > 2 && (\n                      <Badge variant=\"secondary\" className=\"text-xs\">\n                        +{post.tags.length - 2}\n                      </Badge>\n                    )}\n                  </div>\n                  <Link \n                    href={`/blog/${post.slug}`}\n                    className=\"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium group-hover:gap-3 transition-all\"\n                  >\n                    Oku\n                    <ArrowRight className=\"h-4 w-4\" />\n                  </Link>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </section>\n\n        {/* Newsletter Signup */}\n        <section className=\"mb-16\">\n          <Card className=\"shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white\">\n            <CardContent className=\"p-12 text-center\">\n              <Zap className=\"h-16 w-16 mx-auto mb-6 text-blue-100\" />\n              <h2 className=\"text-3xl font-bold mb-4\">\n                Yeni Yazılardan Haberdar Olun\n              </h2>\n              <p className=\"text-blue-100 mb-8 max-w-2xl mx-auto\">\n                AI teknolojileri ve prompt engineering konularındaki en güncel yazılarımızı \n                e-posta ile almak için abone olun.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n                <input\n                  type=\"email\"\n                  placeholder=\"E-posta adresiniz\"\n                  className=\"flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500\"\n                />\n                <button className=\"px-6 py-3 bg-white text-blue-600 rounded-lg font-medium hover:bg-blue-50 transition-colors\">\n                  Abone Ol\n                </button>\n              </div>\n            </CardContent>\n          </Card>\n        </section>\n\n        {/* Popular Topics */}\n        <section>\n          <h2 className=\"text-3xl font-bold text-gray-900 text-center mb-12\">Popüler Konular</h2>\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <Card className=\"shadow-lg text-center\">\n              <CardContent className=\"p-6\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                  <Zap className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <h3 className=\"font-semibold text-gray-900 mb-2\">AI Verimliliği</h3>\n                <p className=\"text-gray-600 text-sm\">\n                  AI araçları ile iş süreçlerinizi optimize edin\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card className=\"shadow-lg text-center\">\n              <CardContent className=\"p-6\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                  <BookOpen className=\"h-6 w-6 text-green-600\" />\n                </div>\n                <h3 className=\"font-semibold text-gray-900 mb-2\">Prompt Engineering</h3>\n                <p className=\"text-gray-600 text-sm\">\n                  Etkili prompt yazma teknikleri\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card className=\"shadow-lg text-center\">\n              <CardContent className=\"p-6\">\n                <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                  <Users className=\"h-6 w-6 text-purple-600\" />\n                </div>\n                <h3 className=\"font-semibold text-gray-900 mb-2\">Takım Çalışması</h3>\n                <p className=\"text-gray-600 text-sm\">\n                  Ekip halinde AI kullanımı\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card className=\"shadow-lg text-center\">\n              <CardContent className=\"p-6\">\n                <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                  <Shield className=\"h-6 w-6 text-orange-600\" />\n                </div>\n                <h3 className=\"font-semibold text-gray-900 mb-2\">AI Güvenliği</h3>\n                <p className=\"text-gray-600 text-sm\">\n                  Güvenli AI kullanımı rehberleri\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n        </section>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-8 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 PromptBir. Tüm hakları saklıdır.\n          </p>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAcO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,KAAK;IACP;AACF;AAEA,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAM;YAAc;SAAkB;QAC7C,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAsB;YAAM;SAAS;QAC5C,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAmB;YAAY;SAAa;QACnD,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAgB;YAAoB;SAAiB;QAC5D,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAO;YAAe;SAAc;QAC3C,UAAU;IACZ;CACD;AAED,MAAM,aAAa;IAAC;IAAQ;IAAc;IAAU;IAAmB;IAAY;CAAc;AAElF,SAAS;IACtB,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;IAC5D,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;IAE5D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,gIAAA,CAAA,OAAI;wCAAe,WAAU;;0DAC5B,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAY,WAAU;0EAAgC;;;;;;0EAGrE,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EACZ,KAAK,QAAQ;;;;;;;;;;;;kEAGlB,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;sEAC7B,KAAK,KAAK;;;;;;;;;;;kEAGf,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEACxB,KAAK,OAAO;;;;;;;;;;;;0DAGjB,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;sFAAM,KAAK,MAAM;;;;;;;;;;;;8EAEpB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,8OAAC;sFAAM,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;8EAEvD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;sFAAM,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;kEAI1B,8OAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC,iIAAA,CAAA,QAAK;gEAAa,SAAQ;gEAAY,WAAU;0EAC9C;+DADS;;;;;;;;;;kEAKhB,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;wDAC1B,WAAU;;4DACX;0EAEC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;uCAhDjB,KAAK,EAAE;;;;;;;;;;;;;;;;kCAyDxB,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,iIAAA,CAAA,QAAK;oCAEJ,SAAS,aAAa,SAAS,YAAY;oCAC3C,WAAU;8CAET;mCAJI;;;;;;;;;;;;;;;kCAWb,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAGhD,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,gIAAA,CAAA,OAAI;wCAAe,WAAU;;0DAC5B,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEACZ,KAAK,QAAQ;;;;;;;;;;;kEAGlB,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;sEAC7B,KAAK,KAAK;;;;;;;;;;;kEAGf,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEACxB,KAAK,OAAO;;;;;;;;;;;;0DAGjB,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;kFAAM,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;0EAEvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;kFAAM,KAAK,QAAQ;;;;;;;;;;;;;;;;;;kEAGxB,8OAAC;wDAAI,WAAU;;4DACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC,iIAAA,CAAA,QAAK;oEAAa,SAAQ;oEAAY,WAAU;8EAC9C;mEADS;;;;;4DAIb,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAY,WAAU;;oEAAU;oEAC3C,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;kEAI3B,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;wDAC1B,WAAU;;4DACX;0EAEC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;uCA5CjB,KAAK,EAAE;;;;;;;;;;;;;;;;kCAqDxB,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDAGxC,8OAAC;wCAAE,WAAU;kDAAuC;;;;;;kDAIpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC;gDAAO,WAAU;0DAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASvH,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CACnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;kDAMzC,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;kDAMzC,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;kDAMzC,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU/C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}]}