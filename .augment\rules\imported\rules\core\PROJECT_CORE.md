---
type: "always_apply"
---

# PromptFlow Core Architecture & Context

## Project Overview
**PromptFlow** - AI destekli geli<PERSON>rme süreçlerini optimize eden minimalist prompt yönetim platformu.

### Dual Application Architecture
#### Ana Uygulama (/promptflow)
- **Framework**: Next.js 15.4.1 (App Router)
- **React**: 19.1.0
- **Port**: 4444
- **UI**: Tailwind CSS + shadcn/ui
- **State**: TanStack Query v5 + Zustand

#### Admin Panel (/admin)
- **Framework**: Next.js 14.2.18 (App Router)
- **React**: 18.3.1
- **Port**: 4445
- **UI**: Chakra UI + Tailwind CSS
- **Features**: User management, Analytics, System settings

### Backend Infrastructure
- **Platform**: Supabase (iqehopwgrczylqliajww)
- **Database**: PostgreSQL with RLS
- **Auth**: Supabase Auth + Admin roles
- **Realtime**: Supabase Realtime subscriptions

### Core Features
- User Plans System (Free/Professional/Enterprise)
- Context Gallery with templates
- Hashtag & Category organization
- Drag & Drop prompt management
- Real-time collaboration
- Performance optimization

## Development Rules
- Always maintain dual app compatibility
- Use TypeScript strict mode
- Implement optimistic UI updates
- Follow responsive design principles
- Maintain RLS security policies
- Update this file after major changes

## Project Structure
```
PromptFlow/
├── promptflow/          # Main application
│   ├── src/
│   │   ├── app/        # Next.js App Router
│   │   ├── components/ # React components
│   │   ├── hooks/      # Custom hooks
│   │   ├── lib/        # Utilities
│   │   └── store/      # Zustand stores
│   └── package.json
├── admin/              # Admin panel
│   ├── src/
│   │   ├── app/        # Admin pages
│   │   ├── components/ # Admin components
│   │   ├── hooks/      # Admin hooks
│   │   └── lib/        # Admin utilities
│   └── package.json
└── .augment/           # AI rules system
    └── rules/
        └── imported/
            └── rules/  # This directory
```

## Technology Compatibility
- **Node.js**: 18+ required
- **npm**: 9+ required
- **TypeScript**: 5+ strict mode
- **React**: 18.3.1 (admin) / 19.1.0 (main)
- **Next.js**: 14.2.18 (admin) / 15.4.1 (main)

## Environment Setup
```bash
# Main app development
cd promptflow
npm install
npm run dev  # Port 4444

# Admin panel development
cd admin
npm install
npm run dev  # Port 4445
```

## Key Principles
1. **Dual App Harmony**: Maintain compatibility between both applications
2. **Type Safety**: Strict TypeScript throughout
3. **Performance First**: Optimize for speed and user experience
4. **Security by Design**: RLS policies and proper authentication
5. **Scalable Architecture**: Modular and maintainable code structure
6. **Real-time Features**: Live updates and collaboration
7. **Responsive Design**: Mobile-first approach
8. **Developer Experience**: Clear patterns and documentation

## Database Schema Overview
### Core Tables
- **auth.users**: Supabase managed user authentication
- **projects**: User prompt collections
- **prompts**: Individual prompts with categorization
- **plan_types**: Plan definitions (Free/Pro/Enterprise)
- **user_plans**: User plan assignments
- **usage_stats**: Usage tracking and analytics
- **context_categories**: Template categories
- **context_templates**: Pre-built prompt templates
- **admin_users**: Admin role management

### Key Relationships
- Users → Projects (1:many)
- Projects → Prompts (1:many)
- Users → User Plans (1:1 active)
- Plan Types → User Plans (1:many)
- Context Categories → Context Templates (1:many)

## API Patterns
### Authentication Flow
1. Supabase Auth handles login/signup
2. RLS policies enforce data access
3. Admin roles checked via admin_users table
4. JWT tokens manage session state

### Data Flow
1. **Frontend** → TanStack Query → **Supabase Client**
2. **Supabase** → RLS Check → **Database**
3. **Database** → **Realtime** → **All Clients**
4. **Optimistic Updates** → **UI** → **Background Sync**

## Development History

### 2025-01-31: Landing Page Sections and Blog System Implementation

#### 🚀 MAJOR FEATURE: Complete Landing Page Ecosystem
**Problem**: Landing page needed professional company pages, legal compliance pages, and content marketing system to establish credibility and meet regulatory requirements.

**Solution Implementation**:
- **8 New Professional Pages**: About, Contact, Bug Report, Privacy Policy, Terms of Service, Cookie Policy, Careers, Blog system
- **Legal Compliance**: GDPR and KVKK compliant privacy and cookie policies for Turkish and EU markets
- **SEO Blog System**: 5 comprehensive SEO-optimized articles about AI, prompt engineering, and PromptBir platform
- **Responsive Design**: Mobile-first approach with consistent design system across all pages
- **Content Strategy**: Professional Turkish content targeting business users and developers

**Technical Implementation**:
- **Pages Created**:
  - `promptflow/src/app/about/page.tsx` - Company story, mission, vision, team
  - `promptflow/src/app/contact/page.tsx` - Contact form, FAQ, support information
  - `promptflow/src/app/bug-report/page.tsx` - Structured bug reporting system
  - `promptflow/src/app/privacy/page.tsx` - GDPR/KVKK compliant privacy policy
  - `promptflow/src/app/terms/page.tsx` - Legal terms and conditions
  - `promptflow/src/app/cookies/page.tsx` - EU/Turkish cookie law compliance
  - `promptflow/src/app/careers/page.tsx` - Job opportunities and company culture
  - `promptflow/src/app/blog/page.tsx` - Blog listing with categories and featured posts
  - `promptflow/src/app/blog/[slug]/page.tsx` - Dynamic blog post pages with Next.js 15 compatibility

**SEO & Content Optimization**:
- Comprehensive metadata and OpenGraph tags for all pages
- 5 SEO-optimized blog articles covering AI productivity, prompt engineering, team collaboration, security, and API usage
- Structured content with proper heading hierarchy and internal linking
- Mobile-responsive design with performance optimization
- Newsletter signup integration and social sharing preparation

**Legal & Compliance Features**:
- GDPR Article 13/14 compliant privacy notices
- KVKK (Turkish data protection law) compliance
- EU Cookie Law compliance with granular consent options
- Terms of service covering SaaS business model and Turkish jurisdiction
- Comprehensive data retention and user rights documentation

**Navigation & UX Improvements**:
- Updated footer links in `promptflow/src/components/landing-page.tsx`
- Consistent design system across all pages using existing shadcn/ui components
- Professional color scheme and typography
- Clear call-to-action flows from marketing pages to application

**Next.js 15 Compatibility**:
- Fixed dynamic route params handling (`params: Promise<{ slug: string }>`)
- Updated async component patterns for server components
- Resolved TypeScript compilation issues with proper imports
- Ensured build optimization and static generation

**Test Results**:
- ✅ Build successful (`npm run build`)
- ✅ All 8 pages render correctly with responsive design
- ✅ SEO metadata properly configured
- ✅ Legal compliance requirements met
- ✅ Blog system functional with dynamic routing
- ✅ Navigation and footer links working correctly

### 2025-01-31: Prompt Sharing System Implementation

#### 🚀 MAJOR FEATURE: Secure Prompt Sharing System
**Problem**: Users needed ability to share their AI prompts securely with others via unique URLs, with optional password protection and expiration dates.

**Solution Implementation**:
- **Database Schema**: New `shared_prompts` and `shared_prompt_views` tables with comprehensive security features
- **Frontend Components**: Modal-based sharing interface with form validation and URL generation
- **Public Viewing**: Clean, minimal public pages for shared prompts with password protection
- **Analytics Tracking**: View counts, copy counts, and usage analytics
- **Security Measures**: Cryptographic tokens, password hashing, rate limiting, input validation

**Technical Details**:
- **Files Created**:
  - `promptflow/src/components/share-prompt-button.tsx` - Sharing modal interface
  - `promptflow/src/app/share/[token]/page.tsx` - Public viewing page
  - `promptflow/src/hooks/use-shared-prompts.ts` - React hooks for sharing functionality
- **Database**: Added comprehensive sharing tables with RLS policies
- **Security**: bcrypt password hashing, 32-character secure tokens, IP-based rate limiting
- **Integration**: Seamless integration with existing prompt workspace

**Features Implemented**:
- Share prompt with custom title and description
- Optional password protection with bcrypt hashing
- Expiration date setting for time-limited shares
- Public/private visibility controls
- One-click URL copying with toast notifications
- View and copy analytics tracking
- Mobile-responsive sharing interface

**Security & Performance**:
- Cryptographically secure token generation
- SQL injection prevention with parameterized queries
- XSS protection with input validation and sanitization
- Rate limiting on share creation and viewing
- Database indexing for optimal query performance
- Automatic cleanup of expired shares

**Test Results**:
- ✅ Build successful with all TypeScript types resolved
- ✅ Share creation modal working with form validation
- ✅ Public viewing page functional with password protection
- ✅ Analytics tracking operational
- ✅ Security measures validated and tested
- ✅ Integration with prompt workspace seamless

### 2025-02-01: UI/Modal Z-Index Conflicts Resolution

#### 🚨 CRITICAL: Complete UI Modal Visibility Fix
**Problem**: Multiple critical UI issues were affecting user interaction and modal functionality:
1. **Prompt Input Bar Disappearing**: Share prompt modal overlay was hiding input areas
2. **Context Gallery Modal Conflicts**: "Add New" popup appearing behind gallery modal
3. **Z-Index Hierarchy Issues**: Inconsistent modal stacking causing visibility problems

**Solution Implementation**:
- **Prompt Input Z-Index Fix**: Added z-[60] to all prompt input sections (mobile, tablet, desktop)
- **Context Modal Z-Index Fix**: Increased ContextCreationModal and ContextEditModal z-index to z-[60]
- **Z-Index Hierarchy Management**: Established consistent z-index scale for modal stacking

**Technical Details**:
- **Files Modified**:
  - `promptflow/src/components/prompt-workspace.tsx` - Added z-[60] to input sections
  - `promptflow/src/components/context-creation-modal.tsx` - Increased DialogContent z-index
  - `promptflow/src/components/context-edit-modal.tsx` - Increased DialogContent z-index
  - `.augment/rules/imported/rules/fixes/UI_MODAL_FIXES.md` - New documentation file
  - `.augment/rules/imported/rules/fixes/CONTEXT_GALLERY_ISSUES.md` - Updated with z-index fixes

**Z-Index Hierarchy Established**:
- z-[60]: Nested modals (Creation, Edit, Share)
- z-[55]: Enhanced Context Gallery Modal
- z-50: Standard modals (Dialog default)
- z-40: Modal backdrops
- z-30: Fixed navigation

**Test Results**:
- ✅ Build successful (`npm run build`)
- ✅ Prompt input areas remain visible when share modal is open
- ✅ "Add New" popup appears correctly above Context Gallery
- ✅ All modal interactions working across responsive layouts
- ✅ Z-index hierarchy properly documented

### 2025-02-01: Browser Console Errors and Warnings Resolution

#### 🚨 CRITICAL: Complete Browser Console Cleanup
**Problem**: Multiple critical browser console errors and warnings were affecting user experience and PWA functionality:
1. **Multiple Supabase Client Instances**: GoTrueClient warnings causing authentication conflicts
2. **Favicon 500 Error**: Server error preventing favicon.ico from loading
3. **Web Manifest 404 Error**: Missing site.webmanifest breaking PWA functionality
4. **Deprecated Meta Tags**: apple-mobile-web-app-capable meta tags causing warnings
5. **Missing Autocomplete Attributes**: Password fields lacking security attributes
6. **Deprecated API Warnings**: Performance API usage causing repeated warnings

**Solution Implementation**:
- **Supabase Client Consolidation**: Merged multiple client instances into single browser client
- **PWA Manifest Creation**: Added comprehensive site.webmanifest with proper PWA configuration
- **Favicon Fix**: Replaced corrupted favicon.ico with working version from favicon_io
- **Meta Tags Modernization**: Added modern mobile-web-app-capable alongside legacy tags
- **Security Enhancement**: Added autocomplete="new-password" to profile page password fields
- **Performance API Modernization**: Replaced deprecated getEntriesByType with PerformanceObserver

**Technical Details**:
- **Files Modified**:
  - `promptflow/src/lib/supabase.ts` - Consolidated client instances
  - `promptflow/src/lib/supabase-browser.ts` - Single source of truth client
  - `promptflow/public/site.webmanifest` - New PWA manifest
  - `promptflow/public/favicon.ico` - Fixed corrupted favicon
  - `promptflow/src/app/layout.tsx` - Updated meta tags
  - `promptflow/src/app/profile/page.tsx` - Added autocomplete attributes
  - `promptflow/src/components/ui/performance-monitor.tsx` - Modern Performance API
  - `promptflow/src/lib/dynamic-imports.ts` - Safe performance measurement

**Security & Performance Improvements**:
- Single Supabase client prevents authentication conflicts
- Proper PWA manifest enables offline functionality
- Modern Performance API eliminates deprecated warnings
- Enhanced form security with proper autocomplete attributes
- Improved accessibility with modern meta tags

**Test Results**:
- ✅ Build successful (`npm run build`)
- ✅ Runtime successful (`npm run dev`)
- ✅ All console errors resolved
- ✅ PWA functionality restored
- ✅ Performance monitoring optimized
- ✅ Security attributes properly configured

### 2025-01-31: Critical Next.js 15 Server Component Fix

#### 🚨 ACİL DURUM: Server Component useState Hatası Çözümü
**Problem**: Layout.tsx içindeki PerformanceMonitor component'i Server Component içinde `useState` ve `useEffect` hook'larını kullanıyordu, bu da Next.js 15'te runtime hatası oluşturuyordu.

**Çözüm Implementasyonu**:
- `PerformanceMonitorWrapper` - Ayrı Client Component oluşturuldu
- `'use client'` directive ile Client Component olarak işaretlendi
- Lazy loading ile performance monitor'ü sadece development ortamında yükler
- Layout.tsx tamamen Server Component olarak korundu

**Teknik Detaylar**:
- **Dosya**: `promptflow/src/components/performance-monitor-wrapper.tsx` (yeni)
- **Güvenlik**: Client-side only execution, XSS koruması
- **Performance**: Lazy loading ile bundle size optimizasyonu
- **Accessibility**: Performance monitoring kullanıcı deneyimini etkilemez
- **Error Handling**: Performance monitor yüklenemezse sessizce devam eder

**Next.js 15 App Router Uyumluluğu**:
- Server/Client Component ayrımına kesin uyum
- Hook kullanımı sadece Client Component'lerde
- SSR uyumlu lazy loading pattern'i
- Development/Production environment ayrımı

**Test Sonuçları**:
- ✅ Build başarılı (`npm run build`)
- ✅ Runtime başarılı (`npm run dev`)
- ✅ Server Component hatası çözüldü
- ✅ Performance monitoring işlevselliği korundu

### 2025-01-31: Dashboard Runtime Hatalarının Çözümü

#### 🚨 ACİL DURUM: Dashboard Kritik Runtime Hatalarının Çözümü
**Problem**: Dashboard sayfasında 3 kritik runtime hatası uygulamanın kullanılabilirliğini engellemekteydi:
1. **Performance.measure DOMException**: `dynamic-imports.ts:144` - tanımlanmamış mark name kullanımı
2. **Font 404 Hatası**: Inter font manuel preload çakışması
3. **Favicon 500 Hatası**: Next.js 15 asset routing uyumsuzluğu

**Çözüm Implementasyonu**:
- **Dynamic Imports Performance API**: Defensive programming ile güvenli hale getirildi
- **Font Loading**: Manuel preload kaldırıldı, Next.js 15 otomatik yükleme kullanıldı
- **Dashboard Error Boundary**: Özel error handling ve graceful degradation eklendi
- **Performance Tracking**: Otomatik mark oluşturma ve hata toleransı

**Teknik Detaylar**:
- **Dosya**: `promptflow/src/lib/dynamic-imports.ts` (güncellendi)
- **Dosya**: `promptflow/src/app/layout.tsx` (font preload kaldırıldı)
- **Dosya**: `promptflow/src/app/dashboard/page.tsx` (error boundary eklendi)
- **Güvenlik**: Performance API hata toleransı, XSS koruması
- **Performance**: Otomatik performance tracking, lazy loading korundu
- **Error Handling**: Graceful degradation, silent error recovery

**Next.js 15 App Router Uyumluluğu**:
- Performance API defensive programming
- Font loading Next.js 15 best practices
- Error boundary page-level protection
- Asset routing optimization

**Test Sonuçları**:
- ✅ Build başarılı (`npm run build`)
- ✅ Runtime başarılı (`npm run dev`)
- ✅ Dashboard hataları çözüldü
- ✅ Performance monitoring güvenli hale getirildi
- ✅ Font ve favicon doğru yükleniyor

### 2025-01-29: Major Feature Implementations

#### Dynamic Height System for Prompt Text Areas
**Feature**: Responsive dynamic height management for prompt input areas
**Components Added**:
- `use-dynamic-height.ts` - Custom hook for viewport-based height calculations
- Enhanced `SmartAutocomplete` with dynamic height support
- CSS utilities for smooth height transitions and animations

**Technical Implementation**:
- Viewport-aware height calculation (1/3 viewport height max)
- Responsive breakpoint support (mobile: 1/4, tablet/desktop: 1/3)
- Hardware-accelerated animations with cubic-bezier transitions
- Visual Viewport API support for mobile browsers
- Debounced resize handling for performance

**Performance Optimizations**:
- GPU acceleration with `transform: translateZ(0)`
- Smooth transitions with `will-change` property
- Debounced viewport calculations (150ms)
- Container queries for responsive components

#### Context Gallery "Add New" Button Fix
**Feature**: Complete Context Gallery integration with project workflow
**Components Enhanced**:
- `use-context-to-prompt.ts` - Context to prompt conversion system
- Enhanced `ContextCreationModal` with "Add to Project" option
- Updated `ContextGallery` with direct project integration

**Functionality Added**:
- Context to prompt conversion with validation
- Plan limit checking during context addition
- Batch context addition support
- Comprehensive error handling and user feedback
- Usage tracking and analytics integration

**Security Measures**:
- Plan limit validation before context addition
- Project ownership verification
- Input sanitization and validation
- Rate limiting considerations

#### Profile Page Plan Management
**Feature**: Complete user plan management interface
**Components Added**:
- Enhanced `/profile` page with plan management UI
- Plan comparison and selection interface
- Plan change validation and confirmation system

**Business Logic**:
- Plan downgrade validation (usage vs limits)
- Confirmation dialogs for plan changes
- Real-time plan status display
- Usage statistics integration
- Billing cycle management

**Security & Validation**:
- Pre-change usage validation
- Data integrity checks during plan transitions
- User confirmation for downgrades
- Automatic cache invalidation after changes

### 2025-01-29: Project Name Editing Feature Implementation
**Feature**: Secure inline project name editing with comprehensive validation
**Components Added**:
- `ProjectNameEditor` - Inline editing component with security-first approach
- `project-validation.ts` - Input validation and sanitization utilities
- `useUpdateProjectNameSecure` - Secure mutation hook with optimistic updates

**Security Measures Implemented**:
- Input validation (3-50 chars, regex pattern, XSS prevention)
- Rate limiting (client + server: 10 requests/minute)
- Database constraints and RLS policies
- Secure database function `update_project_name_secure()`
- SQL injection prevention

**Performance Optimizations**:
- Debounced validation (300ms)
- React.memo for component memoization
- Optimistic updates with rollback capability
- Smart cache management with TanStack Query

**Accessibility Features**:
- ARIA labels and live regions
- Keyboard navigation (Enter/ESC)
- Screen reader support
- Touch-friendly targets (44px minimum)

**Testing Coverage**:
- Unit tests for component behavior
- Security tests for XSS/injection prevention
- Accessibility tests for WCAG compliance
- Performance tests for debouncing and memory usage
- Integration tests for API interactions

**Files Modified**:
- `promptflow/src/components/project-name-editor.tsx` (new)
- `promptflow/src/lib/project-validation.ts` (new)
- `promptflow/src/hooks/use-projects.ts` (enhanced)
- `promptflow/src/components/project-sidebar.tsx` (integrated)
- `promptflow/src/app/globals.css` (touch targets)
- Database: Added constraints, RLS policies, secure functions

## Automatic Rules System

### How the System Works
1. **Core Reading**: This file (PROJECT_CORE.md) serves as the master guide
2. **Auto-Discovery**: System automatically finds related files via `auto_read_order` and `dependencies`
3. **History Loading**: Loads `development_history` from all related files
4. **Context Awareness**: Understands previous work and current state
5. **Auto-Updates**: Changes are automatically written to `auto_update_targets`

### Example Workflow (Context Gallery Text Area)
```yaml
Step 1: Read PROJECT_CORE.md (master guide)
Step 2: Auto-discover related files:
  - features/CONTEXT_GALLERY.md (main feature)
  - features/CONTEXT_GALLERY_ADMIN.md (admin integration)
  - fixes/CONTEXT_GALLERY_ISSUES.md (troubleshooting)
  - frontend/ADMIN_PANEL.md (admin UI)
  - backend/SUPABASE.md (database)

Step 3: Load development history from all files
Step 4: Execute development with full context
Step 5: Auto-update all related files with changes
```

### File Type Hierarchy
- **core**: Master guide (this file)
- **feature**: Main feature documentation
- **admin_feature**: Admin panel specific features
- **fixes**: Troubleshooting and error resolution
- **frontend_guide**: Frontend development rules
- **backend_guide**: Backend development rules
- **deployment_guide**: Production deployment rules

## Update Requirements
- This file auto-updates when major architectural changes occur
- All related files auto-update via the dependency system
- Development history is automatically tracked across all files
- Security patterns documented in security/SECURITY.md
- Performance optimizations tracked in performance/PERFORMANCE.md
- Testing patterns maintained in testing/TESTING.md
- TypeScript patterns updated in fixes/TYPESCRIPT_PATTERNS.md
