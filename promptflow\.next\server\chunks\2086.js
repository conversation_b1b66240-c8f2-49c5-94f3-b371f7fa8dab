exports.id=2086,exports.ids=[2086],exports.modules={8266:(a,b,c)=>{"use strict";c.d(b,{L:()=>d});let d=(0,c(59522).createBrowserClient)("https://iqehopwgrczylqliajww.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlxZWhvcHdncmN6eWxxbGlhand3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2OTQyNzksImV4cCI6MjA2ODI3MDI3OX0.9zrZ8Zot6SvsjxTGMyh3IYFqrr_QXQkKSNU4rJNz0VM",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0,flowType:"pkce",debug:!1,storageKey:"sb-iqehopwgrczylqliajww-auth-token",storage:void 0},db:{schema:"public"},realtime:{params:{eventsPerSecond:10}},global:{headers:{"X-Client-Info":"promptflow-web","X-Client-Version":"1.0.0"}}})},26910:(a,b,c)=>{"use strict";c.d(b,{$3:()=>i,$A:()=>f,Br:()=>g,qj:()=>j});let d={minLength:3,maxLength:50,allowedCharsRegex:/^[a-zA-Z0-9\s\-_.]+$/,debounceMs:300};function e(a){if(!a)return"";let b=a.trim();return(b=b.replace(/\s+/g," ")).trim()}function f(a){let b=e(a);return b?b.length<d.minLength?{isValid:!1,error:`Proje adı en az ${d.minLength} karakter olmalıdır`}:b.length>d.maxLength?{isValid:!1,error:`Proje adı en fazla ${d.maxLength} karakter olabilir`}:d.allowedCharsRegex.test(b)?0===b.replace(/\s/g,"").length?{isValid:!1,error:"Proje adı sadece boşluk karakterlerinden oluşamaz"}:{isValid:!0,sanitizedValue:b}:{isValid:!1,error:"Proje adı sadece harf, rakam, boşluk ve -_. karakterlerini i\xe7erebilir"}:{isValid:!1,error:"Proje adı boş olamaz"}}function g(a,b){return e(a).toLowerCase()===e(b).toLowerCase()}async function h(a,b,c){let d=e(a),h=f(a);return h.isValid?c.some(a=>a.id!==b&&g(a.name,d))?{isValid:!1,error:"Bu isimde bir proje zaten mevcut"}:{isValid:!0,sanitizedValue:d}:h}function i(a){return({unique_violation:"Bu isimde bir proje zaten mevcut",check_violation:"Proje adı ge\xe7ersiz karakterler i\xe7eriyor",not_null_violation:"Proje adı boş olamaz",foreign_key_violation:"Ge\xe7ersiz proje referansı",RateLimitExceeded:"\xc7ok fazla g\xfcncelleme isteği. L\xfctfen bekleyin.",InvalidInput:"Ge\xe7ersiz proje adı",DuplicateName:"Bu isimde bir proje zaten mevcut",NotFound:"Proje bulunamadı",Unauthorized:"Bu işlem i\xe7in yetkiniz yok"})[a]||a}function j(a,b,c=d.debounceMs){let e;return(d,f)=>{clearTimeout(e),e=setTimeout(async()=>{f(await h(d,b,a))},c)}}},31568:(a,b,c)=>{"use strict";c.d(b,{Jd:()=>l,Rt:()=>o,a7:()=>m,rU:()=>k,wF:()=>n});var d=c(8693),e=c(51423),f=c(54050),g=c(8266);c(43210);var h=c(16189),i=c(52581);function j(a,b){let c=a instanceof Error?a.message:String(a);return!!(c.includes("Invalid Refresh Token")||c.includes("Refresh Token Not Found")||c.includes("refresh_token_not_found"))&&(console.warn("Refresh token hatası, oturum temizleniyor:",c),g.L.auth.signOut({scope:"local"}),b.clear(),!0)}function k(){(0,d.jE)(),(0,h.useRouter)()}function l(){let a=(0,d.jE)();return(0,e.I)({queryKey:["user"],queryFn:async()=>{console.log(`🔐 [USE_USER] Getting user...`);try{let{data:{session:b},error:c}=await g.L.auth.getSession();if(c)return console.error(`❌ [USE_USER] Session error:`,c),null;if(!b)return console.log(`🔐 [USE_USER] No session found`),null;console.log(`🔐 [USE_USER] Session found, getting user...`);let{data:{user:d},error:e}=await g.L.auth.getUser();if(e){if(console.error(`❌ [USE_USER] Error getting user:`,e),e.message.includes("Auth session missing"))return console.log(`🔄 [USE_USER] Auth session missing, returning null`),null;if(j(e,a))return console.log(`🔄 [USE_USER] Handled auth error, returning null`),null;throw Error(e.message)}return console.log(`✅ [USE_USER] User retrieved:`,d?.email||"null"),d}catch(b){if(console.error(`💥 [USE_USER] Exception:`,b),(b instanceof Error?b.message:String(b)).includes("Auth session missing"))return console.log(`🔄 [USE_USER] Auth session missing exception, returning null`),null;if(j(b,a))return console.log(`🔄 [USE_USER] Handled auth error exception, returning null`),null;throw b}},staleTime:3e5,retry:(a,b)=>{let c=b instanceof Error?b.message:String(b);return(console.log(`🔄 [USE_USER] Retry attempt ${a}, error: ${c}`),c.includes("Invalid Refresh Token")||c.includes("Refresh Token Not Found")||c.includes("Auth session missing"))?(console.log(`🚫 [USE_USER] Not retrying auth error: ${c}`),!1):a<3}})}function m(){let a=(0,d.jE)();return(0,f.n)({mutationFn:async({email:a,password:b})=>{console.log(`🔑 [SIGN_IN] Attempting login for: ${a}`);let{data:c,error:d}=await g.L.auth.signInWithPassword({email:a,password:b});if(d)throw console.error(`❌ [SIGN_IN] Login failed:`,d),Error(d.message);return console.log(`✅ [SIGN_IN] Login successful:`,{userId:c.user?.id,email:c.user?.email,hasSession:!!c.session}),c},onSuccess:()=>{console.log(`🎉 [SIGN_IN] onSuccess triggered, invalidating queries`),a.invalidateQueries({queryKey:["user"]}),a.invalidateQueries({queryKey:["session"]})},onError:a=>{console.error(`💥 [SIGN_IN] onError triggered:`,a)}})}function n(){let a=(0,d.jE)();return(0,f.n)({mutationFn:async({email:a,password:b})=>{let{data:c,error:d}=await g.L.auth.signUp({email:a,password:b});if(d)throw Error(d.message);return c},onSuccess:()=>{a.invalidateQueries({queryKey:["user"]}),a.invalidateQueries({queryKey:["session"]})}})}function o(){let a=(0,d.jE)(),b=(0,h.useRouter)();return(0,f.n)({mutationFn:async()=>{console.log("Starting logout process...");let{error:a}=await g.L.auth.signOut({scope:"global"});if(a)throw console.error("Logout error:",a),Error(a.message);console.log("Logout successful")},onSuccess:()=>{console.log("Logout onSuccess triggered"),i.oR.success("Başarıyla \xe7ıkış yapıldı",{description:"Giriş sayfasına y\xf6nlendiriliyorsunuz..."}),a.clear(),localStorage.removeItem("promptflow-app-store"),sessionStorage.clear(),setTimeout(()=>{console.log("Redirecting to auth page..."),b.push("/auth"),b.refresh(),window.location.href="/auth"},1e3)},onError:c=>{console.error("Logout failed:",c),i.oR.error("\xc7ıkış yapılırken hata oluştu",{description:"Yine de oturum temizleniyor..."}),a.clear(),localStorage.removeItem("promptflow-app-store"),sessionStorage.clear(),setTimeout(()=>{b.push("/auth"),b.refresh()},1e3)}})}},34248:(a,b,c)=>{"use strict";c.d(b,{C:()=>h,EU:()=>i,Jd:()=>k,P5:()=>l,ov:()=>m,p$:()=>j,pS:()=>n});var d=c(51423),e=c(8693),f=c(54050),g=c(8266);function h(){return(0,d.I)({queryKey:["plan-types"],queryFn:async()=>{console.log(`📋 [USE_PLANS] Fetching plan types...`);let{data:a,error:b}=await g.L.from("plan_types").select("*").eq("is_active",!0).order("sort_order");if(b)throw console.error(`❌ [USE_PLANS] Plan types fetch failed:`,b),Error(b.message);return console.log(`✅ [USE_PLANS] Plan types fetched:`,a?.length),a||[]},staleTime:3e5})}function i(){return(0,d.I)({queryKey:["user-active-plan"],queryFn:async()=>{console.log(`👤 [USE_PLANS] Fetching user active plan...`);let{data:a,error:b}=await g.L.rpc("get_user_active_plan",{user_uuid:(await g.L.auth.getUser()).data.user?.id});if(b)throw console.error(`❌ [USE_PLANS] User active plan fetch failed:`,b),Error(b.message);return console.log(`✅ [USE_PLANS] User active plan fetched:`,a?.[0]),a?.[0]||null},staleTime:12e4})}function j(){return(0,d.I)({queryKey:["user-limits"],queryFn:async()=>{console.log(`🔍 [USE_PLANS] Checking user limits...`);let{data:a,error:b}=await g.L.rpc("check_user_limits",{user_uuid:(await g.L.auth.getUser()).data.user?.id});if(b)throw console.error(`❌ [USE_PLANS] User limits check failed:`,b),Error(b.message);return console.log(`✅ [USE_PLANS] User limits checked:`,a?.[0]),a?.[0]||null},staleTime:3e4})}function k(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async({planName:a,billingCycle:b="monthly",paymentReference:c})=>{console.log(`🔄 [USE_PLANS] Changing plan to: ${a}`);let{data:d,error:e}=await g.L.rpc("change_user_plan",{user_uuid:(await g.L.auth.getUser()).data.user?.id,new_plan_name:a,billing_cycle_param:b,payment_reference_param:c});if(e)throw console.error(`❌ [USE_PLANS] Plan change failed:`,e),Error(e.message);return console.log(`✅ [USE_PLANS] Plan changed successfully:`,d),d},onSuccess:()=>{console.log(`🎉 [USE_PLANS] Plan change successful, invalidating queries`),a.invalidateQueries({queryKey:["user-active-plan"]}),a.invalidateQueries({queryKey:["user-limits"]}),a.invalidateQueries({queryKey:["user-plan-history"]}),a.invalidateQueries({queryKey:["usage-stats"]})},onError:a=>{console.error(`💥 [USE_PLANS] Plan change error:`,a)}})}function l(){return(0,d.I)({queryKey:["user-trial-info"],queryFn:async()=>{console.log(`🔍 [USE_PLANS] Fetching user trial info...`);let{data:a,error:b}=await g.L.rpc("get_user_trial_info",{user_uuid:(await g.L.auth.getUser()).data.user?.id});if(b)throw console.error(`❌ [USE_PLANS] User trial info fetch failed:`,b),Error(b.message);return console.log(`✅ [USE_PLANS] User trial info fetched:`,a?.[0]),a?.[0]||null},staleTime:3e4})}function m(){return(0,d.I)({queryKey:["cancellation-eligibility"],queryFn:async()=>{console.log(`🔍 [USE_PLANS] Checking cancellation eligibility...`);let{data:a,error:b}=await g.L.rpc("get_cancellation_eligibility",{user_uuid:(await g.L.auth.getUser()).data.user?.id});if(b)throw console.error(`❌ [USE_PLANS] Cancellation eligibility check failed:`,b),Error(b.message);return console.log(`✅ [USE_PLANS] Cancellation eligibility checked:`,a?.[0]),a?.[0]||null},staleTime:3e4})}function n(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async({cancellationReason:a,requestRefund:b=!1})=>{console.log(`🚫 [USE_PLANS] Cancelling plan with reason: ${a}`);let{data:c,error:d}=await g.L.rpc("cancel_user_plan",{user_uuid:(await g.L.auth.getUser()).data.user?.id,cancellation_reason_param:a,request_refund:b});if(d)throw console.error(`❌ [USE_PLANS] Plan cancellation failed:`,d),Error(d.message);return console.log(`✅ [USE_PLANS] Plan cancelled successfully:`,c?.[0]),c?.[0]},onSuccess:b=>{console.log(`🎉 [USE_PLANS] Plan cancellation successful, invalidating queries`),a.invalidateQueries({queryKey:["user-active-plan"]}),a.invalidateQueries({queryKey:["user-limits"]}),a.invalidateQueries({queryKey:["user-plan-history"]}),a.invalidateQueries({queryKey:["user-trial-info"]}),a.invalidateQueries({queryKey:["cancellation-eligibility"]}),a.invalidateQueries({queryKey:["usage-stats"]})},onError:a=>{console.error(`💥 [USE_PLANS] Plan cancellation error:`,a)}})}},35950:(a,b,c)=>{"use strict";c.d(b,{w:()=>g});var d=c(60687);c(43210);var e=c(62369),f=c(4780);function g({className:a,orientation:b="horizontal",decorative:c=!0,...g}){return(0,d.jsx)(e.b,{"data-slot":"separator",decorative:c,orientation:b,className:(0,f.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...g})}},39727:()=>{},47990:()=>{},63503:(a,b,c)=>{"use strict";c.d(b,{Cf:()=>m,Es:()=>o,L3:()=>p,c7:()=>n,lG:()=>i,rr:()=>q,zM:()=>j});var d=c(60687),e=c(43210),f=c(26134),g=c(11860),h=c(4780);let i=f.bL,j=f.l9,k=f.ZL;f.bm;let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hJ,{ref:c,className:(0,h.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...b}));l.displayName=f.hJ.displayName;let m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(k,{children:[(0,d.jsx)(l,{}),(0,d.jsxs)(f.UC,{ref:e,className:(0,h.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[b,(0,d.jsxs)(f.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,d.jsx)(g.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Kapat"})]})]})]}));m.displayName=f.UC.displayName;let n=({className:a,...b})=>(0,d.jsx)("div",{className:(0,h.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...b});n.displayName="DialogHeader";let o=({className:a,...b})=>(0,d.jsx)("div",{className:(0,h.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...b});o.displayName="DialogFooter";let p=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hE,{ref:c,className:(0,h.cn)("text-lg font-semibold leading-none tracking-tight",a),...b}));p.displayName=f.hE.displayName;let q=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.VY,{ref:c,className:(0,h.cn)("text-sm text-muted-foreground",a),...b}));q.displayName=f.VY.displayName},73475:(a,b,c)=>{"use strict";c.d(b,{O3:()=>h,UE:()=>f,vw:()=>g,x5:()=>i});var d=c(8266);async function e(){try{let{data:a}=await d.L.auth.getUser();if(!a.user)throw Error("Kullanıcı oturumu bulunamadı");let{data:b,error:c}=await d.L.rpc("check_user_limits",{user_uuid:a.user.id});if(c)throw console.error("Plan limitleri kontrol edilemedi:",c),Error(c.message);return b?.[0]||null}catch(a){return console.error("Plan limitleri kontrol hatası:",a),null}}async function f(){let a=await e();return a?a.can_create_project?{allowed:!0}:{allowed:!1,reason:`Proje limiti aşıldı (${a.current_projects}/${a.max_projects})`}:{allowed:!1,reason:"Plan bilgileri alınamadı"}}async function g(a){let b=await e();if(!b)return{allowed:!1,reason:"Plan bilgileri alınamadı"};if(!b.can_create_prompt)return{allowed:!1,reason:`Prompt limiti aşıldı (${b.current_prompts}/${b.max_prompts_per_project})`};if(a)try{let{count:c,error:e}=await d.L.from("prompts").select("*",{count:"exact",head:!0}).eq("project_id",a);if(e)return console.error("Proje prompt sayısı kontrol edilemedi:",e),{allowed:!1,reason:"Proje bilgileri alınamadı"};let f=c||0;if(-1!==b.max_prompts_per_project&&f>=b.max_prompts_per_project)return{allowed:!1,reason:`Bu proje i\xe7in prompt limiti aşıldı (${f}/${b.max_prompts_per_project})`}}catch(a){return console.error("Proje prompt sayısı kontrol hatası:",a),{allowed:!1,reason:"Proje bilgileri kontrol edilemedi"}}return{allowed:!0}}async function h(){try{let{data:a}=await d.L.auth.getUser();if(!a.user)throw Error("Kullanıcı oturumu bulunamadı");let{error:b}=await d.L.rpc("update_usage_stats",{user_uuid:a.user.id});if(b)throw console.error("Kullanım istatistikleri g\xfcncellenemedi:",b),Error(b.message)}catch(a){throw console.error("Kullanım istatistikleri g\xfcncelleme hatası:",a),a}}let i={PROJECT_LIMIT_REACHED:"Proje oluşturma limitinize ulaştınız. Daha fazla proje oluşturmak i\xe7in planınızı y\xfckseltin.",PROMPT_LIMIT_REACHED:"Prompt oluşturma limitinize ulaştınız. Daha fazla prompt oluşturmak i\xe7in planınızı y\xfckseltin.",FEATURE_NOT_AVAILABLE:"Bu \xf6zellik mevcut planınızda bulunmamaktadır. Planınızı y\xfckseltin.",PLAN_EXPIRED:"Planınızın s\xfcresi dolmuştur. L\xfctfen planınızı yenileyin.",PLAN_SUSPENDED:"Hesabınız askıya alınmıştır. L\xfctfen destek ekibi ile iletişime ge\xe7in."}},77154:(a,b,c)=>{"use strict";c.d(b,{q:()=>g});var d=c(60687);c(43210);var e=c(16189),f=c(31568);function g({children:a}){let{data:b,isLoading:c,error:g}=(0,f.Jd)();(0,e.useRouter)();let h=(0,e.usePathname)();if(console.log(`🛡️ [AUTH_GUARD] Status: loading=${c}, user=${b?.email||"null"}, error=${g?"YES":"NO"}, pathname=${h}`),(0,f.rU)(),c)return console.log(`⏳ [AUTH_GUARD] Showing loading state`),(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-gray-50",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"PromptFlow y\xfckleniyor..."})]})});if(g){let a=g instanceof Error?g.message:String(g);if(console.error(`❌ [AUTH_GUARD] Auth error:`,a),a.includes("Invalid Refresh Token")||a.includes("Refresh Token Not Found"))return console.log(`🔄 [AUTH_GUARD] Refresh token error - showing loading`),(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Oturum yenileniyor..."})]})})}return b?(console.log(`✅ [AUTH_GUARD] User authenticated - showing app`),(0,d.jsx)(d.Fragment,{children:a})):(console.log(`🚫 [AUTH_GUARD] No user - showing loading (middleware will handle redirect)`),(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Kimlik doğrulanıyor..."})]})}))}},80013:(a,b,c)=>{"use strict";c.d(b,{J:()=>g});var d=c(60687);c(43210);var e=c(78148),f=c(4780);function g({className:a,...b}){return(0,d.jsx)(e.b,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...b})}},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>f});var d=c(60687);c(43210);var e=c(4780);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}},97421:(a,b,c)=>{"use strict";c.d(b,{bL:()=>s,eW:()=>v,By:()=>r,YK:()=>q,sS:()=>t,eo:()=>u});var d=c(51423),e=c(8693),f=c(54050),g=c(8266),h=c(73475),i=c(26910);let j={login:{maxRequests:5,windowMinutes:15},signup:{maxRequests:3,windowMinutes:60},password_reset:{maxRequests:3,windowMinutes:60},project_create:{maxRequests:10,windowMinutes:60},project_update:{maxRequests:20,windowMinutes:10},project_delete:{maxRequests:5,windowMinutes:60},prompt_create:{maxRequests:50,windowMinutes:10},prompt_update:{maxRequests:100,windowMinutes:10},prompt_delete:{maxRequests:20,windowMinutes:10},context_create:{maxRequests:20,windowMinutes:10},context_batch_add:{maxRequests:5,windowMinutes:10},plan_change:{maxRequests:3,windowMinutes:60},api_general:{maxRequests:200,windowMinutes:10}};class k{getStorageKey(a,b){let c=b?`_${b}`:"";return`rate_limit_${a}${c}`}async checkLimit(a,b){try{let c,d=j[a],e=this.getStorageKey(a,b),f=Date.now(),g=60*d.windowMinutes*1e3,h=localStorage.getItem(e);if(!h||(c=JSON.parse(h),f-c.windowStart>g))return c={count:1,windowStart:f,lastRequest:f},localStorage.setItem(e,JSON.stringify(c)),{allowed:!0,remainingRequests:d.maxRequests-1,resetTime:f+g};if(c.count>=d.maxRequests){let a=c.windowStart+g,b=Math.ceil((a-f)/1e3);return{allowed:!1,remainingRequests:0,resetTime:a,retryAfter:b}}return c.count++,c.lastRequest=f,localStorage.setItem(e,JSON.stringify(c)),{allowed:!0,remainingRequests:d.maxRequests-c.count,resetTime:c.windowStart+g}}catch(b){return console.warn(`Rate limit check failed for ${a}:`,b),{allowed:!0,remainingRequests:0,resetTime:Date.now()+6e4}}}resetLimit(a,b){try{let c=this.getStorageKey(a,b);localStorage.removeItem(c)}catch(b){console.warn(`Rate limit reset failed for ${a}:`,b)}}getStatus(a,b){try{let c=j[a],d=this.getStorageKey(a,b),e=localStorage.getItem(d);if(!e)return{count:0,remaining:c.maxRequests,resetTime:Date.now()+60*c.windowMinutes*1e3};let f=JSON.parse(e),g=60*c.windowMinutes*1e3,h=Date.now();if(h-f.windowStart>g)return{count:0,remaining:c.maxRequests,resetTime:h+g};return{count:f.count,remaining:Math.max(0,c.maxRequests-f.count),resetTime:f.windowStart+g}}catch(b){return console.warn(`Rate limit status check failed for ${a}:`,b),null}}}class l{async checkLimit(a,b){try{let c=j[a],d=b;if(!d){let{data:{user:a}}=await g.L.auth.getUser();if(!a)throw Error("User not authenticated");d=a.id}let{data:e,error:f}=await g.L.rpc("check_rate_limit",{p_user_id:d,p_action_type:a,p_max_requests:c.maxRequests,p_window_minutes:c.windowMinutes});if(f)return console.error(`Server rate limit check failed for ${a}:`,f),{allowed:!0,remainingRequests:0,resetTime:Date.now()+60*c.windowMinutes*1e3};let h=60*c.windowMinutes*1e3,i=Date.now()+h;if(!0!==e)return{allowed:!1,remainingRequests:0,resetTime:i,retryAfter:Math.ceil(h/1e3)};return{allowed:!0,remainingRequests:c.maxRequests-1,resetTime:i}}catch(b){return console.error(`Server rate limit error for ${a}:`,b),{allowed:!0,remainingRequests:0,resetTime:Date.now()+6e4}}}}let m=new k,n=new l;async function o(a,b={}){let{clientOnly:c=!1,serverOnly:d=!1,userId:e}=b;try{if(!d){let b=await m.checkLimit(a,e);if(!b.allowed||c)return{...b,source:"client"}}if(!c)return{...await n.checkLimit(a,e),source:d?"server":"both"};return{allowed:!0,remainingRequests:0,resetTime:Date.now()+6e4,source:"client"}}catch(b){return console.error(`Rate limit check failed for ${a}:`,b),{allowed:!0,remainingRequests:0,resetTime:Date.now()+6e4,source:"client"}}}class p extends Error{constructor(a,b,c){super(`Rate limit exceeded for ${a}. Try again in ${b} seconds.`),this.action=a,this.retryAfter=b,this.resetTime=c,this.name="RateLimitError"}}function q(){return(0,d.I)({queryKey:["projects"],queryFn:async()=>{console.log(`📁 [USE_PROJECTS] Fetching projects...`);try{let{data:{session:a},error:b}=await g.L.auth.getSession();console.log(`📁 [USE_PROJECTS] Session check:`,{hasSession:!!a,sessionError:b?.message,userId:a?.user?.id});let{data:c,error:d}=await g.L.from("projects").select("*").order("created_at",{ascending:!1});if(d)throw console.error(`❌ [USE_PROJECTS] Error fetching projects:`,d),Error(d.message);return console.log(`✅ [USE_PROJECTS] Projects fetched:`,c?.length||0,"projects"),c||[]}catch(a){throw console.error(`💥 [USE_PROJECTS] Exception:`,a),a}}})}function r(a){return(0,d.I)({queryKey:["project",a],queryFn:async()=>{if(!a)return null;let{data:b,error:c}=await g.L.from("projects").select("*").eq("id",a).single();if(c)throw Error(c.message);return b},enabled:!!a})}function s(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async a=>{let b=await (0,h.UE)();if(!b.allowed)throw Error(b.reason||h.x5.PROJECT_LIMIT_REACHED);let{data:{user:c},error:d}=await g.L.auth.getUser();if(d||!c)throw Error("Kullanıcı oturumu bulunamadı");let{data:e,error:f}=await g.L.from("projects").insert({...a,user_id:c.id}).select().single();if(f)throw Error(f.message);return e},onSuccess:async()=>{a.invalidateQueries({queryKey:["projects"]});try{await (0,h.O3)(),a.invalidateQueries({queryKey:["user-limits"]}),a.invalidateQueries({queryKey:["usage-stats"]})}catch(a){console.warn("Kullanım istatistikleri g\xfcncellenemedi:",a)}}})}function t(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async({id:a,...b})=>{console.log("\uD83D\uDD04 [UPDATE_PROJECT] Starting update:",{id:a,updates:b});let{data:c}=await g.L.auth.getUser();if(!c.user)throw Error("Kullanıcı girişi gerekli");let{data:d,error:e}=await g.L.from("projects").update({...b,updated_at:new Date().toISOString()}).eq("id",a).eq("user_id",c.user.id).select().single();if(e)throw console.error("❌ [UPDATE_PROJECT] Database error:",e),Error(`Proje g\xfcncellenirken hata oluştu: ${e.message}`);if(!d)throw Error("Proje bulunamadı veya g\xfcncelleme yetkisi yok");return console.log("✅ [UPDATE_PROJECT] Success:",d),d},onSuccess:b=>{a.invalidateQueries({queryKey:["projects"]}),a.invalidateQueries({queryKey:["project",b.id]})},onError:a=>{console.error("❌ [UPDATE_PROJECT] Mutation error:",a)}})}function u(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async({projectId:a,newName:b})=>{console.log(`🔒 [UPDATE_PROJECT_NAME] Starting secure update:`,{projectId:a,newName:b});let c=await o("project_update");if(!c.allowed)throw new p("project_update",c.retryAfter||60,c.resetTime);let d=(0,i.$A)(b);if(!d.isValid)throw Error(d.error||"Ge\xe7ersiz proje adı");let e=d.sanitizedValue;try{let{data:b,error:c}=await g.L.rpc("update_project_name_secure",{p_project_id:a,p_new_name:e});if(c)throw console.error(`❌ [UPDATE_PROJECT_NAME] Database error:`,c),Error((0,i.$3)(c.message));if(!b?.success)throw console.error(`❌ [UPDATE_PROJECT_NAME] Function error:`,b),Error(b?.message||"Proje adı g\xfcncellenemedi");return console.log(`✅ [UPDATE_PROJECT_NAME] Success:`,b.data),b.data}catch(b){console.error(`💥 [UPDATE_PROJECT_NAME] Exception:`,b);let a=b instanceof Error?b.message:"Bilinmeyen hata";throw a.includes("unique_violation")||a.includes("DuplicateName")?a="Bu isimde bir proje zaten mevcut":a.includes("check_violation")||a.includes("InvalidInput")?a="Proje adı ge\xe7ersiz karakterler i\xe7eriyor":a.includes("RateLimitExceeded")&&(a="\xc7ok fazla g\xfcncelleme isteği. L\xfctfen bekleyin."),Error(a)}},onMutate:async({projectId:b,newName:c})=>{console.log(`🚀 [UPDATE_PROJECT_NAME] Starting optimistic update:`,{projectId:b,newName:c}),await a.cancelQueries({queryKey:["projects"]}),await a.cancelQueries({queryKey:["project",b]});let d=a.getQueryData(["projects"]),e=a.getQueryData(["project",b]);return d&&a.setQueryData(["projects"],a=>a?a.map(a=>a.id===b?{...a,name:c.trim(),updated_at:new Date().toISOString()}:a):a),e&&a.setQueryData(["project",b],{...e,name:c.trim(),updated_at:new Date().toISOString()}),{previousProjects:d,previousProject:e}},onSuccess:b=>{console.log(`✅ [UPDATE_PROJECT_NAME] Success, updating cache with server data:`,b.id),a.setQueryData(["projects"],a=>a?a.map(a=>a.id===b.id?{...a,...b}:a):a),a.setQueryData(["project",b.id],b)},onError:(b,c,d)=>{console.error(`💥 [UPDATE_PROJECT_NAME] Error, rolling back:`,b),d?.previousProjects&&a.setQueryData(["projects"],d.previousProjects),d?.previousProject&&a.setQueryData(["project",c.projectId],d.previousProject)},onSettled:()=>{a.invalidateQueries({queryKey:["projects"]})}})}function v(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async a=>{let{error:b}=await g.L.from("projects").delete().eq("id",a);if(b)throw Error(b.message)},onSuccess:async()=>{a.invalidateQueries({queryKey:["projects"]});try{await (0,h.O3)(),a.invalidateQueries({queryKey:["user-limits"]}),a.invalidateQueries({queryKey:["usage-stats"]})}catch(a){console.warn("Kullanım istatistikleri g\xfcncellenemedi:",a)}}})}}};