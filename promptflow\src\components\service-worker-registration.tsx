'use client'

import { useEffect, useState } from 'react'
import { toast } from 'sonner'

// PWA install prompt event interface
interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

interface ServiceWorkerState {
  isSupported: boolean
  isRegistered: boolean
  isUpdateAvailable: boolean
  registration: ServiceWorkerRegistration | null
}

export function ServiceWorkerRegistration() {
  const [swState, setSwState] = useState<ServiceWorkerState>({
    isSupported: false,
    isRegistered: false,
    isUpdateAvailable: false,
    registration: null
  })

  useEffect(() => {
    // Check if service workers are supported
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      setSwState(prev => ({ ...prev, isSupported: true }))
      registerServiceWorker()
    }
  }, [])

  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none' // Always check for updates
      })

      console.log('Service Worker registered successfully:', registration)
      
      setSwState(prev => ({
        ...prev,
        isRegistered: true,
        registration
      }))

      // Check for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New version available
              setSwState(prev => ({ ...prev, isUpdateAvailable: true }))
              
              toast('Yeni güncelleme mevcut!', {
                description: 'Uygulamayı yenilemek için tıklayın',
                action: {
                  label: 'Yenile',
                  onClick: () => updateServiceWorker(newWorker)
                },
                duration: 10000
              })
            }
          })
        }
      })

      // Listen for controlling service worker changes
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        window.location.reload()
      })

      // Check for waiting service worker
      if (registration.waiting) {
        setSwState(prev => ({ ...prev, isUpdateAvailable: true }))
      }

      // Periodic update check (every 30 minutes)
      setInterval(() => {
        registration.update()
      }, 30 * 60 * 1000)

    } catch (error) {
      console.error('Service Worker registration failed:', error)
      
      toast.error('Çevrimdışı özellikler kullanılamıyor', {
        description: 'Service Worker kaydedilemedi'
      })
    }
  }

  const updateServiceWorker = (newWorker: ServiceWorker) => {
    newWorker.postMessage({ type: 'SKIP_WAITING' })
  }

  const forceUpdate = () => {
    if (swState.registration?.waiting) {
      updateServiceWorker(swState.registration.waiting)
    }
  }

  // Cache management functions
  const clearCache = async () => {
    try {
      const cacheNames = await caches.keys()
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      )
      
      toast.success('Önbellek temizlendi', {
        description: 'Uygulama yeniden yüklenecek'
      })
      
      setTimeout(() => window.location.reload(), 1000)
    } catch (error) {
      console.error('Cache clear failed:', error)
      toast.error('Önbellek temizlenemedi')
    }
  }

  const getCacheSize = async () => {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate()
        return {
          used: estimate.usage || 0,
          quota: estimate.quota || 0,
          usedMB: Math.round((estimate.usage || 0) / 1024 / 1024 * 100) / 100,
          quotaMB: Math.round((estimate.quota || 0) / 1024 / 1024 * 100) / 100
        }
      }
    } catch (error) {
      console.error('Storage estimate failed:', error)
    }
    return null
  }

  // Preload critical resources
  const preloadResources = (urls: string[]) => {
    if (swState.registration) {
      swState.registration.active?.postMessage({
        type: 'CACHE_URLS',
        urls
      })
    }
  }

  // Background sync registration
  const registerBackgroundSync = async (tag: string) => {
    try {
      if (swState.registration && 'sync' in swState.registration) {
        await (swState.registration as any).sync.register(tag)
        console.log('Background sync registered:', tag)
      }
    } catch (error) {
      console.error('Background sync registration failed:', error)
    }
  }

  // Push notification subscription
  const subscribeToPushNotifications = async () => {
    try {
      if (!swState.registration) return null

      const subscription = await swState.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY
      })

      console.log('Push subscription:', subscription)
      return subscription
    } catch (error) {
      console.error('Push subscription failed:', error)
      return null
    }
  }

  // Expose utilities globally for debugging
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as unknown as { swUtils: unknown }).swUtils = {
        state: swState,
        forceUpdate,
        clearCache,
        getCacheSize,
        preloadResources,
        registerBackgroundSync,
        subscribeToPushNotifications
      }
    }
  }, [swState])

  // Don't render anything - this is just for registration
  return null
}

// Hook for using service worker features
export function useServiceWorker() {
  const [isOnline, setIsOnline] = useState(true)
  const [isInstallable, setIsInstallable] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)

  useEffect(() => {
    // Online/offline detection
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // PWA install prompt
    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      e.preventDefault()
      setDeferredPrompt(e)
      setIsInstallable(true)
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt as EventListener)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt as EventListener)
    }
  }, [])

  const installPWA = async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        toast.success('Uygulama yüklendi!')
      }
      
      setDeferredPrompt(null)
      setIsInstallable(false)
    }
  }

  const addToBackgroundSync = async (tag: string, data: unknown) => {
    try {
      // Store data in IndexedDB for background sync
      // This would integrate with a proper IndexedDB wrapper
      console.log('Adding to background sync:', tag, data)
      
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        const registration = await navigator.serviceWorker.ready
        if ('sync' in registration && registration.sync) {
          await (registration.sync as any).register(tag)
        }
      }
    } catch (error) {
      console.error('Background sync failed:', error)
    }
  }

  return {
    isOnline,
    isInstallable,
    installPWA,
    addToBackgroundSync
  }
}
