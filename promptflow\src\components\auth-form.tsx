'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useSignInWithEmail, useSignUpWithEmail } from '@/hooks/use-auth'
import { AdvancedValidator, COMMON_VALIDATION_RULES } from '@/lib/advanced-validation'
import { AlertCircle, Loader2, CheckCircle, Shield } from 'lucide-react'
import { useAccessibility, useFocusManagement } from '@/hooks/use-accessibility'

export function AuthForm() {
  const [isSignUp, setIsSignUp] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [validationErrors, setValidationErrors] = useState<{
    email?: string
    password?: string
  }>({})
  const [isValidating, setIsValidating] = useState(false)

  const signInMutation = useSignInWithEmail()
  const signUpMutation = useSignUpWithEmail()

  const isLoading = signInMutation.isPending || signUpMutation.isPending

  // Accessibility hooks
  const { announce, generateId } = useAccessibility()
  const { focusFirst } = useFocusManagement()

  // Refs for form elements
  const formRef = useRef<HTMLFormElement>(null)
  const emailRef = useRef<HTMLInputElement>(null)
  const passwordRef = useRef<HTMLInputElement>(null)

  // Generate stable IDs for ARIA relationships
  const emailErrorId = generateId('email-error')
  const passwordErrorId = generateId('password-error')
  const formDescriptionId = generateId('form-description')

  // Advanced validation functions
  const validateEmail = useCallback((value: string) => {
    const result = AdvancedValidator.validate(value, COMMON_VALIDATION_RULES.email)
    return result
  }, [])

  const validatePassword = useCallback((value: string) => {
    const rules = isSignUp ? COMMON_VALIDATION_RULES.password : { required: true, minLength: 1 }
    const result = AdvancedValidator.validate(value, rules)
    return result
  }, [isSignUp])

  // Real-time validation
  const handleEmailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setEmail(value)

    if (value) {
      const validation = validateEmail(value)
      const hasError = !validation.isValid
      const errorMessage = hasError ? validation.errors[0] : undefined

      setValidationErrors(prev => ({
        ...prev,
        email: errorMessage
      }))

      // Announce validation result to screen readers
      if (hasError) {
        announce(`E-posta hatası: ${errorMessage}`, 'polite')
      }
    } else {
      setValidationErrors(prev => ({ ...prev, email: undefined }))
    }
  }, [validateEmail])

  const handlePasswordChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setPassword(value)

    if (value) {
      const validation = validatePassword(value)
      setValidationErrors(prev => ({
        ...prev,
        password: validation.isValid ? undefined : validation.errors[0]
      }))
    } else {
      setValidationErrors(prev => ({ ...prev, password: undefined }))
    }
  }, [validatePassword])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsValidating(true)

    console.log(`📝 [AUTH_FORM] Form submitted: ${isSignUp ? 'SIGN_UP' : 'SIGN_IN'} for ${email}`)

    // Comprehensive validation
    const emailValidation = validateEmail(email)
    const passwordValidation = validatePassword(password)

    const newValidationErrors: typeof validationErrors = {}

    if (!emailValidation.isValid) {
      newValidationErrors.email = emailValidation.errors[0]
    }

    if (!passwordValidation.isValid) {
      newValidationErrors.password = passwordValidation.errors[0]
    }

    setValidationErrors(newValidationErrors)

    if (Object.keys(newValidationErrors).length > 0) {
      setError('Lütfen form hatalarını düzeltin')
      setIsValidating(false)
      return
    }

    try {
      // Use sanitized values
      const sanitizedEmail = emailValidation.sanitizedValue || email
      const sanitizedPassword = passwordValidation.sanitizedValue || password

      if (isSignUp) {
        console.log(`📝 [AUTH_FORM] Attempting sign up...`)
        await signUpMutation.mutateAsync({
          email: sanitizedEmail,
          password: sanitizedPassword
        })
        console.log(`✅ [AUTH_FORM] Sign up successful`)
      } else {
        console.log(`📝 [AUTH_FORM] Attempting sign in...`)
        const result = await signInMutation.mutateAsync({
          email: sanitizedEmail,
          password: sanitizedPassword
        })
        console.log(`✅ [AUTH_FORM] Sign in successful:`, result)
        // Redirect will be handled by auth state listener in use-auth.ts
      }
    } catch (error) {
      console.error(`❌ [AUTH_FORM] Auth failed:`, error)
      setError(error instanceof Error ? error.message : 'Bir hata oluştu')
    } finally {
      setIsValidating(false)
    }
  }

  return (
    <div className="full-height-mobile flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4 safe-area-top safe-area-bottom">
      <div className="w-full max-w-md">
        <div className="text-center mb-6 lg:mb-8">
          <div className="flex items-center justify-center mb-4">
            <img
              src="/logo.png"
              alt="Promptbir Logo"
              className="h-12 w-auto lg:h-16"
            />
          </div>
          <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2 mobile-text-base">Promptbir</h1>
          <p className="text-gray-600 mobile-text-base">AI Prompt Yönetim Platformu</p>
        </div>

        <Card className="shadow-xl border-0 mobile-card-shadow mobile-transition">
          <CardHeader className="space-y-1 p-4 lg:p-6">
            <CardTitle className="text-xl lg:text-2xl text-center mobile-text-base">
              {isSignUp ? 'Hesap Oluştur' : 'Giriş Yap'}
            </CardTitle>
            <CardDescription className="text-center mobile-text-base">
              {isSignUp
                ? 'Promptbir\'e katılın ve prompt\'larınızı yönetin'
                : 'Hesabınıza giriş yapın'
              }
            </CardDescription>
          </CardHeader>
          
          <CardContent className="p-4 lg:p-6 pt-0">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="mobile-text-base flex items-center gap-2">
                  <Shield className="h-4 w-4 text-green-600" />
                  E-posta
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={handleEmailChange}
                  disabled={isLoading || isValidating}
                  className={`mobile-text-base touch-target ${
                    validationErrors.email
                      ? 'border-red-500 focus:border-red-500'
                      : email && !validationErrors.email
                        ? 'border-green-500 focus:border-green-500'
                        : ''
                  }`}
                  autoComplete="email"
                  aria-invalid={!!validationErrors.email}
                  aria-describedby={validationErrors.email ? "email-error" : undefined}
                />
                {validationErrors.email && (
                  <div id="email-error" className="flex items-center gap-2 text-sm text-red-600">
                    <AlertCircle className="h-4 w-4" />
                    {validationErrors.email}
                  </div>
                )}
                {email && !validationErrors.email && (
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    Geçerli e-posta adresi
                  </div>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password" className="mobile-text-base flex items-center gap-2">
                  <Shield className="h-4 w-4 text-green-600" />
                  Şifre {isSignUp && <span className="text-xs text-gray-500">(En az 8 karakter, büyük/küçük harf ve rakam)</span>}
                </Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={handlePasswordChange}
                  disabled={isLoading || isValidating}
                  className={`mobile-text-base touch-target ${
                    validationErrors.password
                      ? 'border-red-500 focus:border-red-500'
                      : password && !validationErrors.password
                        ? 'border-green-500 focus:border-green-500'
                        : ''
                  }`}
                  autoComplete={isSignUp ? "new-password" : "current-password"}
                  aria-invalid={!!validationErrors.password}
                  aria-describedby={validationErrors.password ? "password-error" : undefined}
                />
                {validationErrors.password && (
                  <div id="password-error" className="flex items-center gap-2 text-sm text-red-600">
                    <AlertCircle className="h-4 w-4" />
                    {validationErrors.password}
                  </div>
                )}
                {password && !validationErrors.password && isSignUp && (
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    Güçlü şifre
                  </div>
                )}
              </div>

              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md mobile-card-spacing">
                  <AlertCircle className="h-4 w-4 text-red-600 flex-shrink-0" />
                  <p className="text-sm text-red-700 mobile-text-base">{error}</p>
                </div>
              )}

              <Button 
                type="submit" 
                className="w-full touch-target mobile-btn mobile-transition focus-visible-enhanced"
                disabled={isLoading}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSignUp ? 'Hesap Oluştur' : 'Giriş Yap'}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <button
                type="button"
                onClick={() => {
                  setIsSignUp(!isSignUp)
                  setError('')
                }}
                className="text-sm text-blue-600 hover:text-blue-700 underline mobile-text-base touch-target focus-visible-enhanced mobile-transition"
                disabled={isLoading}
              >
                {isSignUp 
                  ? 'Zaten hesabınız var mı? Giriş yapın' 
                  : 'Hesabınız yok mu? Hesap oluşturun'
                }
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 