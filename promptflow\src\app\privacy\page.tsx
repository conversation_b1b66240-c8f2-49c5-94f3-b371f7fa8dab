import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  ArrowLeft,
  Sparkles,
  Shield,
  Eye,
  Lock,
  Database,
  UserCheck,
  Globe,
  Mail,
  Calendar
} from 'lucide-react'

export const metadata: Metadata = {
  title: 'Gizlilik Politikası - PromptBir | Kişisel Verilerin Korunması',
  description: 'PromptBir gizlilik politikası. Kişisel verilerinizin nasıl toplandığı, işlendiği ve korunduğu hakkında detaylı bilgi.',
  keywords: [
    'gizlilik politikası',
    'kişisel veri korunması',
    'KVKK',
    'GDPR',
    'veri güvenliği',
    'kullanıcı hakları'
  ],
  openGraph: {
    title: 'Gizlilik Politikası - PromptBir',
    description: 'Kişisel verilerinizin nasıl korunduğu hakkında detaylı bilgi',
    type: 'website',
    url: 'https://promptbir.com/privacy'
  }
}

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link 
              href="/" 
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Ana Sayfaya Dön</span>
            </Link>
            <div className="flex items-center space-x-2">
              <Sparkles className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">PromptBir</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Shield className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
            Gizlilik Politikası
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Kişisel verilerinizin güvenliği bizim için önceliktir. Bu politika, verilerinizin 
            nasıl toplandığı, işlendiği ve korunduğu hakkında detaylı bilgi sağlar.
          </p>
          <p className="text-sm text-gray-500 mt-4">
            Son güncelleme: 1 Ocak 2024
          </p>
        </div>

        {/* Quick Overview */}
        <Card className="shadow-lg mb-8 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <Eye className="h-5 w-5 text-blue-600" />
              Özet
            </h2>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div className="flex items-start gap-2">
                <UserCheck className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>Sadece gerekli verileri topluyoruz</span>
              </div>
              <div className="flex items-start gap-2">
                <Lock className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>Verilerinizi şifreleyerek saklıyoruz</span>
              </div>
              <div className="flex items-start gap-2">
                <Globe className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>KVKK ve GDPR uyumlu çalışıyoruz</span>
              </div>
              <div className="flex items-start gap-2">
                <Mail className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>Verilerinizi satmıyoruz</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="space-y-8">
          {/* Data Collection */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Database className="h-6 w-6 text-blue-600" />
                1. Toplanan Veriler
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <h4 className="font-semibold mb-2">Hesap Bilgileri:</h4>
              <ul className="list-disc pl-6 mb-4 space-y-1">
                <li>E-posta adresi (kimlik doğrulama için)</li>
                <li>Şifre (şifrelenmiş olarak saklanır)</li>
                <li>Profil bilgileri (ad, soyad - isteğe bağlı)</li>
              </ul>

              <h4 className="font-semibold mb-2">Kullanım Verileri:</h4>
              <ul className="list-disc pl-6 mb-4 space-y-1">
                <li>Oluşturduğunuz projeler ve prompt'lar</li>
                <li>Platform kullanım istatistikleri</li>
                <li>Hata raporları ve performans verileri</li>
              </ul>

              <h4 className="font-semibold mb-2">Teknik Veriler:</h4>
              <ul className="list-disc pl-6 space-y-1">
                <li>IP adresi (güvenlik amaçlı)</li>
                <li>Tarayıcı bilgileri</li>
                <li>Cihaz bilgileri (mobil/masaüstü)</li>
              </ul>
            </CardContent>
          </Card>

          {/* Data Usage */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <UserCheck className="h-6 w-6 text-purple-600" />
                2. Verilerin Kullanımı
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p className="mb-4">Topladığımız veriler aşağıdaki amaçlarla kullanılır:</p>
              <ul className="list-disc pl-6 space-y-2">
                <li><strong>Hizmet Sunumu:</strong> Platform özelliklerinin çalışması için</li>
                <li><strong>Hesap Yönetimi:</strong> Kimlik doğrulama ve hesap güvenliği</li>
                <li><strong>İletişim:</strong> Önemli güncellemeler ve destek</li>
                <li><strong>Geliştirme:</strong> Platform iyileştirmeleri ve yeni özellikler</li>
                <li><strong>Güvenlik:</strong> Kötüye kullanım tespiti ve önleme</li>
                <li><strong>Yasal Yükümlülükler:</strong> Mevzuat gereği saklama</li>
              </ul>
            </CardContent>
          </Card>

          {/* Data Protection */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Lock className="h-6 w-6 text-green-600" />
                3. Veri Güvenliği
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p className="mb-4">Verilerinizi korumak için aldığımız önlemler:</p>
              <ul className="list-disc pl-6 space-y-2">
                <li><strong>Şifreleme:</strong> Tüm veriler transit ve rest halinde şifrelenir</li>
                <li><strong>Erişim Kontrolü:</strong> Sadece yetkili personel erişebilir</li>
                <li><strong>Güvenlik Duvarı:</strong> Gelişmiş firewall koruması</li>
                <li><strong>Düzenli Yedekleme:</strong> Veri kaybına karşı koruma</li>
                <li><strong>Güvenlik Testleri:</strong> Düzenli penetrasyon testleri</li>
                <li><strong>SSL/TLS:</strong> Güvenli veri iletimi</li>
              </ul>
            </CardContent>
          </Card>

          {/* User Rights */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Globe className="h-6 w-6 text-orange-600" />
                4. Kullanıcı Hakları (KVKK & GDPR)
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p className="mb-4">KVKK ve GDPR kapsamında sahip olduğunuz haklar:</p>
              <ul className="list-disc pl-6 space-y-2">
                <li><strong>Bilgi Alma Hakkı:</strong> Hangi verilerinizin işlendiğini öğrenme</li>
                <li><strong>Erişim Hakkı:</strong> Verilerinizin bir kopyasını talep etme</li>
                <li><strong>Düzeltme Hakkı:</strong> Yanlış verilerin düzeltilmesini isteme</li>
                <li><strong>Silme Hakkı:</strong> Verilerinizin silinmesini talep etme</li>
                <li><strong>İşleme İtiraz:</strong> Veri işlemeye itiraz etme</li>
                <li><strong>Taşınabilirlik:</strong> Verilerinizi başka platforma taşıma</li>
              </ul>
              <p className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <strong>Haklarınızı kullanmak için:</strong> 
                <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline ml-1">
                  <EMAIL>
                </a> adresine yazabilirsiniz.
              </p>
            </CardContent>
          </Card>

          {/* Data Sharing */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Mail className="h-6 w-6 text-red-600" />
                5. Veri Paylaşımı
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p className="mb-4">Verilerinizi aşağıdaki durumlar dışında üçüncü taraflarla paylaşmayız:</p>
              <ul className="list-disc pl-6 space-y-2">
                <li><strong>Yasal Zorunluluk:</strong> Mahkeme kararı veya yasal talep</li>
                <li><strong>Güvenlik:</strong> Kötüye kullanım tespiti durumunda</li>
                <li><strong>Hizmet Sağlayıcılar:</strong> Altyapı hizmetleri (şifrelenmiş)</li>
                <li><strong>İş Devri:</strong> Şirket satışı durumunda (önceden bildirim ile)</li>
              </ul>
              <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                <strong>Garanti:</strong> Verilerinizi hiçbir zaman pazarlama amaçlı satmayız veya kiraya vermeyiz.
              </div>
            </CardContent>
          </Card>

          {/* Data Retention */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Calendar className="h-6 w-6 text-purple-600" />
                6. Veri Saklama Süresi
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <ul className="list-disc pl-6 space-y-2">
                <li><strong>Hesap Verileri:</strong> Hesap aktif olduğu sürece</li>
                <li><strong>Kullanım Verileri:</strong> 2 yıl (analiz amaçlı)</li>
                <li><strong>Log Verileri:</strong> 1 yıl (güvenlik amaçlı)</li>
                <li><strong>Destek Talepleri:</strong> 3 yıl (yasal zorunluluk)</li>
                <li><strong>Silinen Hesaplar:</strong> 30 gün içinde tamamen silinir</li>
              </ul>
            </CardContent>
          </Card>

          {/* Cookies */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>7. Çerezler (Cookies)</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p className="mb-4">Platform işlevselliği için gerekli çerezler kullanıyoruz:</p>
              <ul className="list-disc pl-6 space-y-2">
                <li><strong>Zorunlu Çerezler:</strong> Oturum yönetimi ve güvenlik</li>
                <li><strong>Performans Çerezleri:</strong> Site performansı analizi</li>
                <li><strong>Tercih Çerezleri:</strong> Kullanıcı ayarları</li>
              </ul>
              <p className="mt-4">
                Detaylı bilgi için <Link href="/cookies" className="text-blue-600 hover:underline">Çerez Politikası</Link> sayfasını inceleyebilirsiniz.
              </p>
            </CardContent>
          </Card>

          {/* Contact */}
          <Card className="shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="p-8 text-center">
              <h2 className="text-2xl font-bold mb-4">Sorularınız mı var?</h2>
              <p className="mb-6 text-blue-100">
                Gizlilik politikamız hakkında sorularınız varsa bizimle iletişime geçin.
              </p>
              <div className="space-y-2">
                <p><strong>KVKK Sorumlusu:</strong> <EMAIL></p>
                <p><strong>Genel Sorular:</strong> <EMAIL></p>
                <p><strong>Adres:</strong> Türkiye</p>
              </div>
            </CardContent>
          </Card>

          {/* Updates */}
          <Card className="shadow-lg border-orange-200 bg-orange-50">
            <CardContent className="p-6">
              <h3 className="font-bold text-orange-900 mb-2">Politika Güncellemeleri</h3>
              <p className="text-orange-800 text-sm">
                Bu gizlilik politikası gerektiğinde güncellenebilir. Önemli değişiklikler 
                e-posta ile bildirilecek ve platform üzerinde duyurulacaktır. 
                Güncellemeleri düzenli olarak kontrol etmenizi öneririz.
              </p>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-400">
            © 2024 PromptBir. Tüm hakları saklıdır.
          </p>
        </div>
      </footer>
    </div>
  )
}
