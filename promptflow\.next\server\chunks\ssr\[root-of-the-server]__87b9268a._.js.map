{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/landing-page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const LandingPage = registerClientReference(\n    function() { throw new Error(\"Attempted to call LandingPage() from the server but LandingPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/landing-page.tsx <module evaluation>\",\n    \"LandingPage\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,iEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/landing-page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const LandingPage = registerClientReference(\n    function() { throw new Error(\"Attempted to call LandingPage() from the server but LandingPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/landing-page.tsx\",\n    \"LandingPage\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/app/page.tsx"], "sourcesContent": ["import { LandingPage } from \"@/components/landing-page\";\nimport { Metadata } from \"next\";\nimport { SEOGenerator } from \"@/lib/seo-utils\";\n\n// Enhanced SEO metadata for home page\nexport const metadata: Metadata = SEOGenerator.generateMetadata({\n  title: \"PromptFlow - AI Destekli Prompt Yönetim Platformu\",\n  description: \"AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformu. Prompt'larınızı organize edin, takımınızla paylaşın ve verimliliğinizi 10x artırın. 10,000+ geliştirici tarafından güvenilir.\",\n  keywords: [\n    \"prompt yönetimi\",\n    \"AI araçları\",\n    \"yapay zeka\",\n    \"geliştirici araçları\",\n    \"prompt engineering\",\n    \"AI destekli geliştirme\",\n    \"kod optimizasyonu\",\n    \"proje yönetimi\",\n    \"ChatGPT prompts\",\n    \"AI productivity\",\n    \"prompt organizasyonu\",\n    \"takım çalışması\",\n    \"context gallery\",\n    \"API erişimi\",\n    \"gerçek zamanlı senkronizasyon\"\n  ],\n  url: \"/\",\n  type: \"website\"\n});\n\nexport default function Home() {\n  return <LandingPage />;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAGO,MAAM,WAAqB,2HAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC;IAC9D,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,KAAK;IACL,MAAM;AACR;AAEe,SAAS;IACtB,qBAAO,8OAAC,qIAAA,CAAA,cAAW;;;;;AACrB", "debugId": null}}]}