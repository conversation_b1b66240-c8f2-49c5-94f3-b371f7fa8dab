'use client'

import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'

// Enhanced loading spinner with customizable styles
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'dots' | 'pulse' | 'bars' | 'ring'
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  className?: string
}

export function LoadingSpinner({ 
  size = 'md', 
  variant = 'default',
  color = 'primary',
  className 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  const colorClasses = {
    primary: 'text-blue-600',
    secondary: 'text-gray-600',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    error: 'text-red-600'
  }

  if (variant === 'dots') {
    return (
      <div className={cn('flex space-x-1', className)}>
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className={cn(
              'rounded-full animate-pulse',
              sizeClasses[size],
              colorClasses[color].replace('text-', 'bg-')
            )}
            style={{
              animationDelay: `${i * 0.2}s`,
              animationDuration: '1.4s'
            }}
          />
        ))}
      </div>
    )
  }

  if (variant === 'pulse') {
    return (
      <div
        className={cn(
          'rounded-full animate-pulse',
          sizeClasses[size],
          colorClasses[color].replace('text-', 'bg-'),
          className
        )}
      />
    )
  }

  if (variant === 'bars') {
    return (
      <div className={cn('flex space-x-1', className)}>
        {[0, 1, 2, 3].map((i) => (
          <div
            key={i}
            className={cn(
              'animate-pulse',
              'w-1',
              sizeClasses[size].split(' ')[1], // height only
              colorClasses[color].replace('text-', 'bg-')
            )}
            style={{
              animationDelay: `${i * 0.15}s`,
              animationDuration: '1.2s'
            }}
          />
        ))}
      </div>
    )
  }

  if (variant === 'ring') {
    return (
      <div
        className={cn(
          'animate-spin rounded-full border-2 border-gray-200',
          sizeClasses[size],
          `border-t-${colorClasses[color].split('-')[1]}-600`,
          className
        )}
      />
    )
  }

  // Default spinner
  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2 border-gray-200 border-t-current',
        sizeClasses[size],
        colorClasses[color],
        className
      )}
    />
  )
}

// Progressive loading with stages
interface ProgressiveLoadingProps {
  stages: string[]
  currentStage: number
  className?: string
}

export function ProgressiveLoading({ stages, currentStage, className }: ProgressiveLoadingProps) {
  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex items-center space-x-3">
        <LoadingSpinner size="sm" />
        <span className="text-sm text-gray-600">
          {stages[currentStage] || 'Yükleniyor...'}
        </span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
          style={{
            width: `${((currentStage + 1) / stages.length) * 100}%`
          }}
        />
      </div>
      
      <div className="text-xs text-gray-500 text-center">
        {currentStage + 1} / {stages.length}
      </div>
    </div>
  )
}

// Skeleton loading with shimmer effect
interface SkeletonProps {
  className?: string
  variant?: 'text' | 'rectangular' | 'circular'
  animation?: 'pulse' | 'wave' | 'none'
  lines?: number
}

export function Skeleton({ 
  className, 
  variant = 'rectangular',
  animation = 'pulse',
  lines = 1 
}: SkeletonProps) {
  const baseClasses = 'bg-gray-200'
  
  const variantClasses = {
    text: 'h-4 rounded',
    rectangular: 'rounded-md',
    circular: 'rounded-full'
  }

  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'animate-shimmer',
    none: ''
  }

  if (variant === 'text' && lines > 1) {
    return (
      <div className="space-y-2">
        {Array.from({ length: lines }).map((_, i) => (
          <div
            key={i}
            className={cn(
              baseClasses,
              variantClasses[variant],
              animationClasses[animation],
              i === lines - 1 ? 'w-3/4' : 'w-full',
              'h-4',
              className
            )}
          />
        ))}
      </div>
    )
  }

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        animationClasses[animation],
        className
      )}
    />
  )
}

// Loading overlay with backdrop
interface LoadingOverlayProps {
  isVisible: boolean
  message?: string
  progress?: number
  onCancel?: () => void
  className?: string
}

export function LoadingOverlay({ 
  isVisible, 
  message = 'Yükleniyor...', 
  progress,
  onCancel,
  className 
}: LoadingOverlayProps) {
  if (!isVisible) return null

  return (
    <div className={cn(
      'fixed inset-0 bg-black/50 flex items-center justify-center z-50',
      className
    )}>
      <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4 shadow-xl">
        <div className="text-center space-y-4">
          <LoadingSpinner size="lg" />
          
          <div>
            <p className="text-gray-900 font-medium">{message}</p>
            
            {progress !== undefined && (
              <div className="mt-3">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">{progress}%</p>
              </div>
            )}
          </div>
          
          {onCancel && (
            <button
              onClick={onCancel}
              className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
            >
              İptal
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

// Smart loading state that adapts based on loading time
interface SmartLoadingProps {
  isLoading: boolean
  children: React.ReactNode
  fallback?: React.ReactNode
  delay?: number
  timeout?: number
  className?: string
}

export function SmartLoading({ 
  isLoading, 
  children, 
  fallback,
  delay = 200,
  timeout = 10000,
  className 
}: SmartLoadingProps) {
  const [showLoading, setShowLoading] = useState(false)
  const [loadingStage, setLoadingStage] = useState<'initial' | 'loading' | 'slow' | 'timeout'>('initial')

  useEffect(() => {
    if (isLoading) {
      setLoadingStage('initial')
      
      // Show loading after delay
      const delayTimer = setTimeout(() => {
        setShowLoading(true)
        setLoadingStage('loading')
      }, delay)

      // Mark as slow loading
      const slowTimer = setTimeout(() => {
        setLoadingStage('slow')
      }, 3000)

      // Mark as timeout
      const timeoutTimer = setTimeout(() => {
        setLoadingStage('timeout')
      }, timeout)

      return () => {
        clearTimeout(delayTimer)
        clearTimeout(slowTimer)
        clearTimeout(timeoutTimer)
      }
    } else {
      setShowLoading(false)
      setLoadingStage('initial')
    }
  }, [isLoading, delay, timeout])

  if (!isLoading) {
    return <>{children}</>
  }

  if (!showLoading) {
    return null // Don't show anything during initial delay
  }

  const getLoadingMessage = () => {
    switch (loadingStage) {
      case 'loading':
        return 'Yükleniyor...'
      case 'slow':
        return 'Bu işlem beklenenden uzun sürüyor...'
      case 'timeout':
        return 'Bağlantı sorunu yaşanıyor. Lütfen tekrar deneyin.'
      default:
        return 'Yükleniyor...'
    }
  }

  const getSpinnerColor = () => {
    switch (loadingStage) {
      case 'slow':
        return 'warning'
      case 'timeout':
        return 'error'
      default:
        return 'primary'
    }
  }

  if (fallback) {
    return <div className={className}>{fallback}</div>
  }

  return (
    <div className={cn('flex flex-col items-center justify-center p-8 space-y-3', className)}>
      <LoadingSpinner 
        size="lg" 
        color={getSpinnerColor() as 'primary' | 'warning' | 'error'} 
      />
      <p className="text-sm text-gray-600 text-center">
        {getLoadingMessage()}
      </p>
      {loadingStage === 'timeout' && (
        <button
          onClick={() => window.location.reload()}
          className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
        >
          Sayfayı Yenile
        </button>
      )}
    </div>
  )
}
