# PromptFlow Prompt Sharing System

## Overview
**Feature**: Secure prompt sharing system allowing users to share prompts via unique URLs
**Status**: ✅ Completed (2025-01-31)
**Components**: Frontend UI, Backend API, Database schema, Security measures

## Architecture

### Database Schema
```sql
-- Shared prompts table
CREATE TABLE shared_prompts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  prompt_id UUID NOT NULL REFERENCES prompts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  share_token VARCHAR(32) UNIQUE NOT NULL,
  title TEXT,
  description TEXT,
  is_public BOOLEAN DEFAULT true,
  password_hash TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  view_count INTEGER DEFAULT 0,
  copy_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics table
CREATE TABLE shared_prompt_views (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  shared_prompt_id UUID NOT NULL REFERENCES shared_prompts(id) ON DELETE CASCADE,
  viewer_ip INET,
  viewer_user_agent TEXT,
  viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  action_type VARCHAR(20) DEFAULT 'view' -- 'view', 'copy'
);
```

### Security Features
- **Unique Share Tokens**: 32-character cryptographically secure tokens
- **Password Protection**: Optional bcrypt-hashed password protection
- **Expiration Dates**: Time-limited sharing with automatic cleanup
- **Rate Limiting**: IP-based rate limiting for share creation and viewing
- **Input Validation**: Comprehensive XSS and injection prevention
- **Access Control**: User ownership verification and RLS policies

## Implementation Files

### Frontend Components
- **`src/components/share-prompt-button.tsx`**: Main sharing interface
- **`src/app/share/[token]/page.tsx`**: Public viewing page
- **`src/hooks/use-shared-prompts.ts`**: React hooks for sharing functionality

### Backend Integration
- **`src/lib/supabase.ts`**: TypeScript definitions and client setup
- **Database migrations**: RLS policies and table creation
- **API routes**: Share creation and viewing endpoints

## Key Features

### 1. Share Creation Modal
```typescript
interface SharePromptModalProps {
  prompt: Prompt
  isOpen: boolean
  onClose: () => void
}

// Features:
- Title and description customization
- Password protection toggle
- Expiration date picker
- Public/private visibility
- One-click URL copying
```

### 2. Public Viewing Page
```typescript
// Route: /share/[token]
// Features:
- Clean, minimal design
- Password protection form
- Copy prompt functionality
- View analytics tracking
- Mobile-responsive layout
```

### 3. Analytics Tracking
```typescript
interface ShareAnalytics {
  viewCount: number
  copyCount: number
  viewHistory: ShareView[]
  popularityScore: number
}
```

## Security Measures

### 1. Token Generation
```typescript
// Cryptographically secure 32-character tokens
const generateShareToken = (): string => {
  return crypto.randomBytes(16).toString('hex')
}
```

### 2. Password Protection
```typescript
// bcrypt hashing for password protection
const hashPassword = async (password: string): Promise<string> => {
  return await bcrypt.hash(password, 12)
}
```

### 3. Rate Limiting
```typescript
// IP-based rate limiting
const RATE_LIMITS = {
  shareCreation: { requests: 10, window: '1h' },
  shareViewing: { requests: 100, window: '1h' }
}
```

### 4. Input Validation
```typescript
const shareValidation = z.object({
  title: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  password: z.string().min(6).max(50).optional(),
  expiresAt: z.date().min(new Date()).optional(),
  isPublic: z.boolean().default(true)
})
```

## User Experience

### 1. Sharing Flow
1. User clicks "Share" button on prompt
2. Modal opens with sharing options
3. User configures title, description, password, expiration
4. System generates unique URL
5. User copies URL and shares

### 2. Viewing Flow
1. Recipient clicks shared URL
2. System validates token and checks expiration
3. If password protected, shows password form
4. On success, displays prompt in clean interface
5. Analytics recorded (view count, IP, timestamp)

### 3. Management
- Users can view all their shared prompts
- Edit sharing settings (password, expiration)
- Deactivate shares
- View analytics (views, copies)

## Performance Optimizations

### 1. Database Indexing
```sql
-- Optimized indexes for sharing system
CREATE INDEX idx_shared_prompts_token ON shared_prompts(share_token);
CREATE INDEX idx_shared_prompts_user_active ON shared_prompts(user_id, is_active);
CREATE INDEX idx_shared_prompts_expires ON shared_prompts(expires_at) WHERE expires_at IS NOT NULL;
```

### 2. Caching Strategy
- Share token validation cached for 5 minutes
- Public shares cached at CDN level
- Analytics aggregated daily

### 3. Cleanup Jobs
```sql
-- Automatic cleanup of expired shares
DELETE FROM shared_prompts 
WHERE expires_at < NOW() AND expires_at IS NOT NULL;
```

## Integration Points

### 1. Prompt Workspace
- Share button integrated into prompt cards
- Bulk sharing for multiple prompts
- Share status indicators

### 2. Project Management
- Project-level sharing permissions
- Team sharing workflows
- Shared prompt collections

### 3. Analytics Dashboard
- Sharing statistics
- Popular shared prompts
- User engagement metrics

## Development History

### 2025-01-31: Initial Implementation
- ✅ Database schema design and migration
- ✅ TypeScript type definitions
- ✅ Share creation modal component
- ✅ Public viewing page
- ✅ Security measures implementation
- ✅ Analytics tracking system
- ✅ Integration with existing prompt workspace

### Technical Challenges Resolved
1. **TypeScript Compilation**: Fixed type definitions in supabase.ts
2. **Missing Dependencies**: Installed @radix-ui/react-switch
3. **API Route Issues**: Fixed IP address extraction in Next.js 15
4. **Button Variants**: Corrected Button component variant types
5. **Null Handling**: Added proper null checks for prompt data

### Testing Results
- ✅ Build successful (`npm run build`)
- ✅ Runtime successful (`npm run dev`)
- ✅ All sharing flows working
- ✅ Security measures validated
- ✅ Analytics tracking functional

## Future Enhancements

### Planned Features
1. **Social Sharing**: Direct integration with Twitter, LinkedIn
2. **Embed Codes**: Iframe embeds for websites
3. **QR Codes**: Generate QR codes for mobile sharing
4. **Collaboration**: Real-time collaborative editing
5. **Templates**: Share prompt templates with variables

### Performance Improvements
1. **CDN Integration**: Global content delivery
2. **Image Previews**: Social media preview cards
3. **Batch Operations**: Bulk sharing management
4. **Advanced Analytics**: Detailed usage insights

## Best Practices

### 1. Security
- Always validate share tokens server-side
- Implement proper rate limiting
- Use HTTPS for all share URLs
- Regular security audits

### 2. User Experience
- Keep sharing flow simple (max 3 steps)
- Provide clear feedback on share creation
- Mobile-first responsive design
- Accessibility compliance (WCAG 2.1)

### 3. Performance
- Optimize database queries with proper indexing
- Implement caching for frequently accessed shares
- Use CDN for static assets
- Monitor and optimize bundle size

## Monitoring and Maintenance

### Key Metrics
- Share creation rate
- Share view rate
- Password protection usage
- Expiration date usage
- Error rates and performance

### Maintenance Tasks
- Weekly cleanup of expired shares
- Monthly analytics aggregation
- Quarterly security review
- Performance optimization reviews

## Related Documentation
- `features/USER_PLANS.md` - Plan limits for sharing
- `security/SECURITY.md` - Security guidelines
- `performance/PERFORMANCE.md` - Performance optimization
- `core/PROJECT_CORE.md` - Overall architecture
