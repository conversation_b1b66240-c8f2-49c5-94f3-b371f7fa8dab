// Query Performance Monitoring and Optimization
import { QueryClient } from '@tanstack/react-query'

interface QueryMetrics {
  queryKey: string
  duration: number
  timestamp: number
  cacheHit: boolean
  dataSize: number
  error?: string
}

interface PerformanceStats {
  averageQueryTime: number
  cacheHitRate: number
  slowQueries: QueryMetrics[]
  errorRate: number
  totalQueries: number
}

class QueryPerformanceMonitor {
  private static instance: QueryPerformanceMonitor
  private metrics: QueryMetrics[] = []
  private maxMetrics = 1000 // Keep last 1000 queries
  private slowQueryThreshold = 1000 // 1 second

  static getInstance(): QueryPerformanceMonitor {
    if (!QueryPerformanceMonitor.instance) {
      QueryPerformanceMonitor.instance = new QueryPerformanceMonitor()
    }
    return QueryPerformanceMonitor.instance
  }

  recordQuery(
    queryKey: unknown[],
    duration: number,
    cacheHit: boolean,
    dataSize: number,
    error?: string
  ) {
    const metric: QueryMetrics = {
      queryKey: JSON.stringify(queryKey),
      duration,
      timestamp: Date.now(),
      cacheHit,
      dataSize,
      error,
    }

    this.metrics.push(metric)

    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }

    // Log slow queries in development
    if (process.env.NODE_ENV === 'development' && duration > this.slowQueryThreshold) {
      console.warn(`🐌 Slow query detected:`, {
        queryKey: metric.queryKey,
        duration: `${duration}ms`,
        cacheHit,
        dataSize: `${Math.round(dataSize / 1024)}KB`,
      })
    }

    // Log errors
    if (error) {
      console.error(`❌ Query error:`, {
        queryKey: metric.queryKey,
        error,
        duration: `${duration}ms`,
      })
    }
  }

  getStats(timeWindow = 300000): PerformanceStats {
    const cutoff = Date.now() - timeWindow // Last 5 minutes
    const recentMetrics = this.metrics.filter(m => m.timestamp > cutoff)

    if (recentMetrics.length === 0) {
      return {
        averageQueryTime: 0,
        cacheHitRate: 0,
        slowQueries: [],
        errorRate: 0,
        totalQueries: 0,
      }
    }

    const totalQueries = recentMetrics.length
    const cacheHits = recentMetrics.filter(m => m.cacheHit).length
    const errors = recentMetrics.filter(m => m.error).length
    const slowQueries = recentMetrics.filter(m => m.duration > this.slowQueryThreshold)

    const averageQueryTime = recentMetrics.reduce((sum, m) => sum + m.duration, 0) / totalQueries

    return {
      averageQueryTime: Math.round(averageQueryTime),
      cacheHitRate: Math.round((cacheHits / totalQueries) * 100),
      slowQueries: slowQueries.slice(-10), // Last 10 slow queries
      errorRate: Math.round((errors / totalQueries) * 100),
      totalQueries,
    }
  }

  getSlowQueries(limit = 10): QueryMetrics[] {
    return this.metrics
      .filter(m => m.duration > this.slowQueryThreshold)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit)
  }

  getQueryFrequency(): Record<string, number> {
    const frequency: Record<string, number> = {}
    
    this.metrics.forEach(metric => {
      const baseKey = metric.queryKey.split(',')[0] // Get base query type
      frequency[baseKey] = (frequency[baseKey] || 0) + 1
    })

    return frequency
  }

  clearMetrics() {
    this.metrics = []
  }

  exportMetrics() {
    return {
      metrics: this.metrics,
      stats: this.getStats(),
      timestamp: Date.now(),
    }
  }
}

// Performance monitoring wrapper for queries
export function withPerformanceMonitoring<T>(
  queryFn: () => Promise<T>,
  queryKey: unknown[]
): () => Promise<T> {
  const monitor = QueryPerformanceMonitor.getInstance()

  return async () => {
    const startTime = performance.now()
    const cacheHit = false
    let dataSize = 0
    let error: string | undefined

    try {
      const result = await queryFn()
      const endTime = performance.now()
      
      // Estimate data size
      dataSize = JSON.stringify(result).length
      
      // Record metrics
      monitor.recordQuery(
        queryKey,
        endTime - startTime,
        cacheHit,
        dataSize
      )

      return result
    } catch (err) {
      const endTime = performance.now()
      error = err instanceof Error ? err.message : 'Unknown error'
      
      monitor.recordQuery(
        queryKey,
        endTime - startTime,
        cacheHit,
        dataSize,
        error
      )

      throw err
    }
  }
}

// Query client with performance monitoring
export function createMonitoredQueryClient(): QueryClient {
  const monitor = QueryPerformanceMonitor.getInstance()

  return new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        retry: (failureCount, error) => {
          // Don't retry on auth errors
          if (error instanceof Error && (
            error.message.includes('401') ||
            error.message.includes('403')
          )) {
            return false
          }
          return failureCount < 3
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      },
    },
    // QueryCache configuration removed - not supported in current version
  })
}

// Performance dashboard data
export function useQueryPerformance() {
  const monitor = QueryPerformanceMonitor.getInstance()

  return {
    getStats: () => monitor.getStats(),
    getSlowQueries: () => monitor.getSlowQueries(),
    getQueryFrequency: () => monitor.getQueryFrequency(),
    clearMetrics: () => monitor.clearMetrics(),
    exportMetrics: () => monitor.exportMetrics(),
  }
}

// Query optimization recommendations
export function getOptimizationRecommendations(): string[] {
  const monitor = QueryPerformanceMonitor.getInstance()
  const stats = monitor.getStats()
  const recommendations: string[] = []

  if (stats.cacheHitRate < 50) {
    recommendations.push('Cache hit rate is low. Consider increasing staleTime for stable data.')
  }

  if (stats.averageQueryTime > 500) {
    recommendations.push('Average query time is high. Consider optimizing database queries or adding indexes.')
  }

  if (stats.errorRate > 5) {
    recommendations.push('Error rate is high. Check network connectivity and error handling.')
  }

  if (stats.slowQueries.length > 5) {
    recommendations.push('Multiple slow queries detected. Consider query optimization or pagination.')
  }

  const frequency = monitor.getQueryFrequency()
  const mostFrequent = Object.entries(frequency)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)

  if (mostFrequent.length > 0) {
    recommendations.push(
      `Most frequent queries: ${mostFrequent.map(([key]) => key).join(', ')}. Consider optimizing these first.`
    )
  }

  return recommendations
}

// Export singleton instance
export const queryPerformanceMonitor = QueryPerformanceMonitor.getInstance()

// Development tools
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as unknown as { queryPerformance: unknown }).queryPerformance = {
    getStats: () => queryPerformanceMonitor.getStats(),
    getSlowQueries: () => queryPerformanceMonitor.getSlowQueries(),
    getRecommendations: () => getOptimizationRecommendations(),
    clearMetrics: () => queryPerformanceMonitor.clearMetrics(),
  }
}
