"use strict";exports.id=9225,exports.ids=[9225],exports.modules={10022:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},19225:(a,b,c)=>{c.r(b),c.d(b,{ContextSidebar:()=>x,default:()=>y});var d=c(60687),e=c(43210),f=c(29523),g=c(44493),h=c(34729),i=c(96834),j=c(35950),k=c(10022),l=c(47033),m=c(14952),n=c(11860),o=c(62688);let p=(0,o.A)("toggle-right",[["circle",{cx:"15",cy:"12",r:"3",key:"1afu0r"}],["rect",{width:"20",height:"14",x:"2",y:"5",rx:"7",key:"g7kal2"}]]),q=(0,o.A)("toggle-left",[["circle",{cx:"9",cy:"12",r:"3",key:"u3jwor"}],["rect",{width:"20",height:"14",x:"2",y:"5",rx:"7",key:"g7kal2"}]]),r=(0,o.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),s=(0,o.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),t=(0,o.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var u=c(43649),v=c(28555),w=c(97421);function x({onClose:a,isCollapsed:b=!1,onToggleCollapse:c,isContextGalleryOpen:o=!1,onToggleContextGallery:x}){let[y,z]=(0,e.useState)(""),[A,B]=(0,e.useState)(!1),[C,D]=(0,e.useState)(null),[E,F]=(0,e.useState)(!1),[G,H]=(0,e.useState)(""),{activeProjectId:I,setActiveProjectId:J,isContextEnabled:K,setIsContextEnabled:L}=(0,v.C)(),{data:M}=(0,w.By)(I),N=(0,w.sS)(),O=(0,w.eW)(),P=async()=>{if(I){B(!0);try{await N.mutateAsync({id:I,context_text:y}),D(new Date)}catch(a){console.error("Context kaydetme hatası:",a)}finally{B(!1)}}},Q=async()=>{if(I&&M){if(G!==M.name)return void alert("Proje adını doğru yazın!");try{await O.mutateAsync(I),J(null),F(!1),H("")}catch(a){console.error("Proje silme hatası:",a),alert("Proje silinirken bir hata oluştu!")}}};return I?(0,d.jsxs)("div",{className:"flex flex-col h-full",children:[(0,d.jsx)("div",{className:`border-b border-gray-200 ${b?"p-2":"p-4 lg:p-6"}`,children:(0,d.jsxs)("div",{className:`flex items-center ${b?"justify-center mb-2":"justify-between mb-4"}`,children:[(0,d.jsxs)("div",{className:`flex items-center gap-2 ${b?"flex-col":""}`,children:[c&&(0,d.jsx)(f.$,{variant:"ghost",size:"sm",onClick:c,className:"hidden xl:flex",title:b?"Ayarlar panelini genişlet":"Ayarlar panelini daralt",children:b?(0,d.jsx)(l.A,{className:"h-4 w-4"}):(0,d.jsx)(m.A,{className:"h-4 w-4"})}),!b&&(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Context Ayarları"})]}),(0,d.jsx)("div",{className:`flex items-center gap-2 ${b?"flex-col":""}`,children:a&&(0,d.jsx)(f.$,{variant:"ghost",size:"sm",onClick:a,className:"xl:hidden",children:(0,d.jsx)(n.A,{className:"h-4 w-4"})})})]})}),(0,d.jsx)("div",{className:`flex-1 ${b?"p-2":"p-6"}`,children:b?(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,d.jsx)(f.$,{variant:"ghost",size:"sm",onClick:()=>L(!K),className:`w-8 h-8 p-0 rounded-md transition-all ${K?"bg-green-100 text-green-700 hover:bg-green-200":"bg-gray-100 text-gray-500 hover:bg-gray-200"}`,title:`Context ${K?"Aktif":"Pasif"}`,children:K?(0,d.jsx)(p,{className:"h-4 w-4"}):(0,d.jsx)(q,{className:"h-4 w-4"})}),(0,d.jsx)(f.$,{variant:"ghost",size:"sm",onClick:P,disabled:A,className:"w-8 h-8 p-0",title:"Context Kaydet",children:(0,d.jsx)(r,{className:"h-4 w-4"})})]}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsxs)(f.$,{variant:o?"default":"outline",size:"sm",onClick:x,className:`w-full justify-center transition-all duration-200 ${o?"bg-purple-600 hover:bg-purple-700 text-white shadow-md":"border-purple-200 text-purple-600 hover:text-purple-700 hover:border-purple-300 hover:bg-purple-50"}`,children:[(0,d.jsx)(s,{className:"h-4 w-4 mr-2"}),"Context Gallery"]})}),(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"\xd6n Tanımlı Metin"}),(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>L(!K),className:`flex items-center gap-2 px-3 py-1 rounded-md transition-all ${K?"bg-green-100 text-green-700 hover:bg-green-200":"bg-gray-100 text-gray-500 hover:bg-gray-200"}`,children:[K?(0,d.jsx)(p,{className:"h-4 w-4"}):(0,d.jsx)(q,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-xs font-medium",children:K?"Aktif":"Pasif"})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[A&&(0,d.jsx)(i.E,{variant:"secondary",className:"text-xs",children:"Kaydediliyor..."}),C&&(0,d.jsxs)("span",{className:"text-xs text-gray-500",children:["Son kaydedilme: ",C.toLocaleTimeString("tr-TR")]})]})]}),(0,d.jsx)(h.T,{placeholder:"T\xfcm promptların başına eklenecek context metninizi buraya yazın...",value:y,onChange:a=>z(a.target.value),className:"min-h-[200px] resize-none",disabled:A}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)(f.$,{variant:"default",size:"sm",onClick:()=>{P()},disabled:A||!y.trim(),className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,d.jsx)(r,{className:"h-4 w-4 mr-2"}),A?"Kaydediliyor...":"Kaydet"]}),(0,d.jsxs)(f.$,{variant:"outline",size:"sm",onClick:()=>{z(""),D(null)},disabled:A,className:"text-red-600 hover:text-red-700",children:[(0,d.jsx)(t,{className:"h-4 w-4 mr-2"}),"Temizle"]})]}),(0,d.jsx)(j.w,{className:"my-6"}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{className:"text-base",children:"Proje Ayarları"})}),(0,d.jsxs)(g.Wu,{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Karakter Sayısı"}),(0,d.jsx)(i.E,{variant:"outline",children:y.length.toLocaleString()})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Proje Adı"}),(0,d.jsx)(i.E,{variant:"outline",children:M?.name})]}),(0,d.jsx)(j.w,{}),E?(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md",children:[(0,d.jsx)(u.A,{className:"h-4 w-4 text-red-600 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"Dikkat!"}),(0,d.jsx)("p",{className:"text-xs text-red-700",children:"Bu işlem geri alınamaz. Proje ve t\xfcm prompt'lar silinecek."})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("label",{className:"text-sm font-medium text-gray-700",children:["Onaylamak i\xe7in proje adını yazın: ",(0,d.jsx)("span",{className:"text-red-600 font-semibold",children:M?.name})]}),(0,d.jsx)("input",{type:"text",value:G,onChange:a=>H(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500",placeholder:"Proje adını yazın..."})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)(f.$,{variant:"outline",size:"sm",onClick:Q,disabled:G!==M?.name||O.isPending,className:"flex-1 text-red-600 hover:text-red-700 hover:bg-red-50",children:[(0,d.jsx)(t,{className:"h-4 w-4 mr-2"}),O.isPending?"Siliniyor...":"Sil"]}),(0,d.jsx)(f.$,{variant:"outline",size:"sm",onClick:()=>{F(!1),H("")},className:"flex-1",children:"İptal"})]})]}):(0,d.jsxs)(f.$,{variant:"outline",size:"sm",className:"w-full text-red-600 hover:text-red-700 hover:bg-red-50",onClick:()=>F(!0),children:[(0,d.jsx)(t,{className:"h-4 w-4 mr-2"}),"Projeyi Sil"]})]})]})]})})]}):(0,d.jsx)("div",{className:"flex items-center justify-center h-full p-6",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(k.A,{className:`${b?"h-6 w-6":"h-12 w-12"} text-gray-400 mx-auto mb-4`}),!b&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Context Alanı"}),(0,d.jsx)("p",{className:"text-gray-500 text-sm",children:"Proje se\xe7tikten sonra context metninizi buraya yazabilirsiniz"})]})]})})}let y=x},34729:(a,b,c)=>{c.d(b,{T:()=>f});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("textarea",{"data-slot":"textarea",className:(0,e.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...b})}}};