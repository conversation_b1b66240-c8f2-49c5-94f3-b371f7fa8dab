{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/app/careers/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport Link from 'next/link'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  ArrowLeft, \n  Sparkles, \n  Users, \n  Rocket, \n  Heart, \n  Code,\n  Palette,\n  BarChart3,\n  Headphones,\n  MapPin,\n  Clock,\n  DollarSign,\n  Star,\n  Coffee,\n  Zap\n} from 'lucide-react'\n\nexport const metadata: Metadata = {\n  title: 'Kariyer - PromptBir | İş İlanları ve Kariyer Fırsatları',\n  description: 'PromptBir ekibine katılın! AI teknolojileri alanında kariyer fırsatları, açık pozisyonlar ve çalışma kültürümüz hakkında bilgi edinin.',\n  keywords: [\n    'PromptBir kariyer',\n    'iş ilanları',\n    'AI şirketi iş',\n    'yazılım geliştirici iş',\n    'startup kariyer',\n    'teknoloji işleri'\n  ],\n  openGraph: {\n    title: 'Kariyer - PromptBir',\n    description: 'AI teknolojileri alanında kariyer fırsatları ve açık pozisyonlar',\n    type: 'website',\n    url: 'https://promptbir.com/careers'\n  }\n}\n\nexport default function CareersPage() {\n  const openPositions = [\n    {\n      id: 1,\n      title: 'Senior Frontend Developer',\n      department: 'Engineering',\n      location: 'Remote / İstanbul',\n      type: 'Full-time',\n      experience: '3+ yıl',\n      skills: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS'],\n      description: 'Modern web teknolojileri ile kullanıcı deneyimini geliştiren bir frontend developer arıyoruz.'\n    },\n    {\n      id: 2,\n      title: 'Backend Developer',\n      department: 'Engineering',\n      location: 'Remote / İstanbul',\n      type: 'Full-time',\n      experience: '2+ yıl',\n      skills: ['Node.js', 'PostgreSQL', 'Supabase', 'API Design'],\n      description: 'Ölçeklenebilir backend sistemleri geliştiren deneyimli bir developer arıyoruz.'\n    },\n    {\n      id: 3,\n      title: 'Product Designer',\n      department: 'Design',\n      location: 'Remote / İstanbul',\n      type: 'Full-time',\n      experience: '2+ yıl',\n      skills: ['Figma', 'UI/UX', 'Design Systems', 'User Research'],\n      description: 'Kullanıcı odaklı tasarımlar yapan yaratıcı bir product designer arıyoruz.'\n    },\n    {\n      id: 4,\n      title: 'DevOps Engineer',\n      department: 'Engineering',\n      location: 'Remote / İstanbul',\n      type: 'Full-time',\n      experience: '3+ yıl',\n      skills: ['Docker', 'Kubernetes', 'AWS', 'CI/CD'],\n      description: 'Altyapı ve deployment süreçlerini optimize eden bir DevOps engineer arıyoruz.'\n    }\n  ]\n\n  const benefits = [\n    {\n      icon: <DollarSign className=\"h-6 w-6 text-green-600\" />,\n      title: 'Rekabetçi Maaş',\n      description: 'Sektör standartlarının üzerinde maaş ve performans bonusu'\n    },\n    {\n      icon: <MapPin className=\"h-6 w-6 text-blue-600\" />,\n      title: 'Esnek Çalışma',\n      description: 'Remote çalışma imkanı ve esnek çalışma saatleri'\n    },\n    {\n      icon: <Rocket className=\"h-6 w-6 text-purple-600\" />,\n      title: 'Gelişim Fırsatları',\n      description: 'Eğitim bütçesi, konferanslar ve sertifikasyon desteği'\n    },\n    {\n      icon: <Heart className=\"h-6 w-6 text-red-600\" />,\n      title: 'Sağlık Sigortası',\n      description: 'Kapsamlı sağlık sigortası ve wellness programları'\n    },\n    {\n      icon: <Coffee className=\"h-6 w-6 text-orange-600\" />,\n      title: 'Ofis İmkanları',\n      description: 'Modern ofis, ücretsiz yemek ve içecek'\n    },\n    {\n      icon: <Star className=\"h-6 w-6 text-yellow-600\" />,\n      title: 'Hisse Senedi',\n      description: 'Şirket büyümesinden pay alma fırsatı'\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      {/* Header */}\n      <header className=\"border-b border-gray-200 bg-white/80 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <Link \n              href=\"/\" \n              className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <ArrowLeft className=\"h-5 w-5\" />\n              <span>Ana Sayfaya Dön</span>\n            </Link>\n            <div className=\"flex items-center space-x-2\">\n              <Sparkles className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">PromptBir</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Hero Section */}\n        <div className=\"text-center mb-16\">\n          <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n            <Users className=\"h-8 w-8 text-purple-600\" />\n          </div>\n          <h1 className=\"text-4xl sm:text-5xl font-bold text-gray-900 mb-6\">\n            Ekibimize Katılın\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            AI teknolojilerinin geleceğini şekillendiren bir ekibin parçası olun. \n            PromptBir'de yenilikçi projeler üzerinde çalışın ve kariyerinizi geliştirin.\n          </p>\n        </div>\n\n        {/* Company Culture */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-bold text-gray-900 text-center mb-12\">Çalışma Kültürümüz</h2>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <Card className=\"shadow-lg text-center\">\n              <CardHeader>\n                <div className=\"mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\">\n                  <Zap className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <CardTitle className=\"text-lg\">İnovasyon Odaklı</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-gray-600\">\n                  Sürekli öğrenme ve yenilik yapma kültürü ile teknolojinin \n                  sınırlarını zorluyoruz.\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card className=\"shadow-lg text-center\">\n              <CardHeader>\n                <div className=\"mx-auto w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4\">\n                  <Users className=\"h-6 w-6 text-green-600\" />\n                </div>\n                <CardTitle className=\"text-lg\">Takım Ruhu</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-gray-600\">\n                  Birlikte başarıya ulaşan, birbirini destekleyen ve \n                  güçlü bir takım ruhu olan ekip.\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card className=\"shadow-lg text-center\">\n              <CardHeader>\n                <div className=\"mx-auto w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4\">\n                  <Heart className=\"h-6 w-6 text-purple-600\" />\n                </div>\n                <CardTitle className=\"text-lg\">İş-Yaşam Dengesi</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-gray-600\">\n                  Çalışanlarımızın mutluluğunu ve refahını önemseyen, \n                  esnek çalışma imkanları sunan ortam.\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n        </section>\n\n        {/* Benefits */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-bold text-gray-900 text-center mb-12\">Çalışan Avantajları</h2>\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {benefits.map((benefit, index) => (\n              <Card key={index} className=\"shadow-lg\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-start gap-4\">\n                    <div className=\"flex-shrink-0\">\n                      {benefit.icon}\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-gray-900 mb-2\">\n                        {benefit.title}\n                      </h3>\n                      <p className=\"text-gray-600 text-sm\">\n                        {benefit.description}\n                      </p>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </section>\n\n        {/* Open Positions */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-bold text-gray-900 text-center mb-12\">Açık Pozisyonlar</h2>\n          <div className=\"space-y-6\">\n            {openPositions.map((position) => (\n              <Card key={position.id} className=\"shadow-lg\">\n                <CardHeader>\n                  <div className=\"flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n                    <div>\n                      <CardTitle className=\"text-xl mb-2\">{position.title}</CardTitle>\n                      <CardDescription className=\"text-base\">\n                        {position.description}\n                      </CardDescription>\n                    </div>\n                    <Button className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 md:flex-shrink-0\">\n                      Başvur\n                    </Button>\n                  </div>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4\">\n                    <div className=\"flex items-center gap-2\">\n                      <Users className=\"h-4 w-4 text-gray-500\" />\n                      <span className=\"text-sm text-gray-600\">{position.department}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <MapPin className=\"h-4 w-4 text-gray-500\" />\n                      <span className=\"text-sm text-gray-600\">{position.location}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <Clock className=\"h-4 w-4 text-gray-500\" />\n                      <span className=\"text-sm text-gray-600\">{position.type}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <BarChart3 className=\"h-4 w-4 text-gray-500\" />\n                      <span className=\"text-sm text-gray-600\">{position.experience}</span>\n                    </div>\n                  </div>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {position.skills.map((skill, index) => (\n                      <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                        {skill}\n                      </Badge>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </section>\n\n        {/* No Open Position */}\n        <Card className=\"shadow-lg mb-16 bg-gradient-to-r from-gray-50 to-blue-50 border-blue-200\">\n          <CardContent className=\"p-8 text-center\">\n            <Users className=\"h-16 w-16 text-blue-600 mx-auto mb-6\" />\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Aradığınız Pozisyon Yok mu?\n            </h3>\n            <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n              Şu anda açık olmayan bir pozisyonda çalışmak istiyorsanız, \n              CV'nizi gönderin. Uygun fırsat çıktığında sizinle iletişime geçelim.\n            </p>\n            <Button \n              variant=\"outline\" \n              size=\"lg\"\n              className=\"border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white\"\n            >\n              Spontan Başvuru Yap\n            </Button>\n          </CardContent>\n        </Card>\n\n        {/* Application Process */}\n        <section className=\"mb-16\">\n          <h2 className=\"text-3xl font-bold text-gray-900 text-center mb-12\">Başvuru Süreci</h2>\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-blue-600 font-bold\">1</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Başvuru</h3>\n              <p className=\"text-gray-600 text-sm\">\n                CV ve motivasyon mektubunuzu gönderin\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-purple-600 font-bold\">2</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">İnceleme</h3>\n              <p className=\"text-gray-600 text-sm\">\n                Başvurunuzu detaylı olarak inceleriz\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-green-600 font-bold\">3</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Görüşme</h3>\n              <p className=\"text-gray-600 text-sm\">\n                Teknik ve kültürel uyum görüşmeleri\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-orange-600 font-bold\">4</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Karar</h3>\n              <p className=\"text-gray-600 text-sm\">\n                Sonucu 1 hafta içinde bildiririz\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* Team Stats */}\n        <section className=\"mb-16\">\n          <Card className=\"shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white\">\n            <CardContent className=\"py-12\">\n              <h2 className=\"text-3xl font-bold text-center mb-12\">Ekibimiz Rakamlarla</h2>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\">\n                <div>\n                  <div className=\"text-3xl font-bold mb-2\">15+</div>\n                  <div className=\"text-blue-100\">Takım Üyesi</div>\n                </div>\n                <div>\n                  <div className=\"text-3xl font-bold mb-2\">5+</div>\n                  <div className=\"text-blue-100\">Farklı Uzmanlık</div>\n                </div>\n                <div>\n                  <div className=\"text-3xl font-bold mb-2\">100%</div>\n                  <div className=\"text-blue-100\">Remote Uyumlu</div>\n                </div>\n                <div>\n                  <div className=\"text-3xl font-bold mb-2\">4.8/5</div>\n                  <div className=\"text-blue-100\">Çalışan Memnuniyeti</div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </section>\n\n        {/* Contact */}\n        <section className=\"text-center\">\n          <Card className=\"shadow-lg\">\n            <CardContent className=\"py-12\">\n              <Headphones className=\"h-16 w-16 text-blue-600 mx-auto mb-6\" />\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                Sorularınız mı var?\n              </h2>\n              <p className=\"text-gray-600 mb-8 max-w-2xl mx-auto\">\n                Kariyer fırsatları, çalışma koşulları veya başvuru süreci hakkında \n                sorularınız varsa bizimle iletişime geçin.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Link href=\"mailto:<EMAIL>\">\n                  <Button size=\"lg\" className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700\">\n                    <EMAIL>\n                  </Button>\n                </Link>\n                <Link href=\"/contact\">\n                  <Button variant=\"outline\" size=\"lg\">\n                    İletişim Formu\n                  </Button>\n                </Link>\n              </div>\n            </CardContent>\n          </Card>\n        </section>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-8 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 PromptBir. Tüm hakları saklıdır.\n          </p>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAkBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,KAAK;IACP;AACF;AAEe,SAAS;IACtB,MAAM,gBAAgB;QACpB;YACE,IAAI;YACJ,OAAO;YACP,YAAY;YACZ,UAAU;YACV,MAAM;YACN,YAAY;YACZ,QAAQ;gBAAC;gBAAS;gBAAc;gBAAW;aAAe;YAC1D,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,YAAY;YACZ,UAAU;YACV,MAAM;YACN,YAAY;YACZ,QAAQ;gBAAC;gBAAW;gBAAc;gBAAY;aAAa;YAC3D,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,YAAY;YACZ,UAAU;YACV,MAAM;YACN,YAAY;YACZ,QAAQ;gBAAC;gBAAS;gBAAS;gBAAkB;aAAgB;YAC7D,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,YAAY;YACZ,UAAU;YACV,MAAM;YACN,YAAY;YACZ,QAAQ;gBAAC;gBAAU;gBAAc;gBAAO;aAAQ;YAChD,aAAa;QACf;KACD;IAED,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CACnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAU;;;;;;;;;;;;0DAEjC,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;kDAOjC,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAU;;;;;;;;;;;;0DAEjC,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;kDAOjC,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAU;;;;;;;;;;;;0DAEjC,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUrC,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CACnE,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,gIAAA,CAAA,OAAI;wCAAa,WAAU;kDAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI;;;;;;kEAEf,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAEhB,8OAAC;gEAAE,WAAU;0EACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;uCAXnB;;;;;;;;;;;;;;;;kCAsBjB,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CACnE,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,yBAClB,8OAAC,gIAAA,CAAA,OAAI;wCAAmB,WAAU;;0DAChC,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAgB,SAAS,KAAK;;;;;;8EACnD,8OAAC,gIAAA,CAAA,kBAAe;oEAAC,WAAU;8EACxB,SAAS,WAAW;;;;;;;;;;;;sEAGzB,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;sEAAmH;;;;;;;;;;;;;;;;;0DAKzI,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;wEAAK,WAAU;kFAAyB,SAAS,UAAU;;;;;;;;;;;;0EAE9D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;wEAAK,WAAU;kFAAyB,SAAS,QAAQ;;;;;;;;;;;;0EAE5D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;wEAAK,WAAU;kFAAyB,SAAS,IAAI;;;;;;;;;;;;0EAExD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;kFACrB,8OAAC;wEAAK,WAAU;kFAAyB,SAAS,UAAU;;;;;;;;;;;;;;;;;;kEAGhE,8OAAC;wDAAI,WAAU;kEACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC,iIAAA,CAAA,QAAK;gEAAa,SAAQ;gEAAY,WAAU;0EAC9C;+DADS;;;;;;;;;;;;;;;;;uCAnCT,SAAS,EAAE;;;;;;;;;;;;;;;;kCA+C5B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;8CAIpD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CACnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;0DAE5C,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;;;;;;0DAE9C,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA2B;;;;;;;;;;;0DAE7C,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;;;;;;0DAE9C,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3C,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;;;;;;;0DAEjC,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;;;;;;;0DAEjC,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;;;;;;;0DAEjC,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,8MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAuC;;;;;;kDAIpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,WAAU;8DAAkG;;;;;;;;;;;0DAIhI,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWhD,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}]}