import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  ArrowLeft, 
  Sparkles, 
  Calendar, 
  Clock, 
  User,
  Share2,
  BookOpen,
  ArrowRight
} from 'lucide-react'

// Blog posts data (in a real app, this would come from a CMS or database)
const blogPosts = {
  'promptbir-ile-ai-verimlilik': {
    id: 1,
    title: 'PromptBir ile AI Verimliliğinizi 10 Kat Artırın',
    slug: 'promptbir-ile-ai-verimlilik',
    excerpt: 'AI prompt\'larınızı organize ederek nasıl daha verimli çalışabileceğinizi öğrenin. PromptBir\'in sunduğu özellikler ile iş akışınızı optimize edin.',
    author: 'PromptBir Ekibi',
    publishDate: '2024-01-15',
    readTime: '8 dakika',
    category: 'Verim<PERSON><PERSON>',
    tags: ['AI', 'Verimli<PERSON>', 'Prompt Yönetimi'],
    featured: true,
    content: `
# PromptBir ile AI Verimliliğinizi 10 Kat Artırın

AI teknolojilerinin hızla geliştiği günümüzde, doğru prompt yönetimi kritik önem taşıyor. PromptBir, AI prompt'larınızı organize etmenizi ve verimliliğinizi maksimize etmenizi sağlayan güçlü bir platform.

## Neden Prompt Yönetimi Önemli?

Modern iş dünyasında AI araçları günlük rutinimizin ayrılmaz bir parçası haline geldi. ChatGPT, Claude, Gemini gibi AI asistanları ile sürekli etkileşim halindeyiz. Ancak çoğu zaman:

- Daha önce kullandığımız etkili prompt'ları hatırlayamıyoruz
- Benzer görevler için sürekli yeni prompt'lar yazıyoruz
- Takım arkadaşlarımızla prompt'ları paylaşamıyoruz
- Hangi prompt'un hangi durumda daha etkili olduğunu bilmiyoruz

## PromptBir'in Sunduğu Çözümler

### 1. Merkezi Prompt Kütüphanesi

PromptBir ile tüm prompt'larınızı tek bir yerde toplayabilirsiniz:

- **Kategorilere Ayırma**: Prompt'larınızı konularına göre organize edin
- **Etiketleme Sistemi**: Hızlı arama için etiketler kullanın
- **Favoriler**: En çok kullandığınız prompt'ları favorilerinize ekleyin

### 2. Takım Çalışması

Ekip halinde çalışırken prompt paylaşımı kritik önem taşır:

- **Proje Bazlı Organizasyon**: Her proje için ayrı prompt koleksiyonları
- **Yetki Yönetimi**: Kimin hangi prompt'lara erişebileceğini belirleyin
- **Gerçek Zamanlı Senkronizasyon**: Değişiklikler anında tüm ekibe yansır

### 3. Akıllı Öneriler

PromptBir'in AI destekli özellikleri:

- **Benzer Prompt Önerileri**: Yazdığınız prompt'a benzer olanları gösterir
- **Performans Analizi**: Hangi prompt'ların daha etkili olduğunu analiz eder
- **Otomatik Kategorilendirme**: Yeni prompt'ları otomatik olarak kategoriler

## Pratik Kullanım Örnekleri

### İçerik Üretimi

\`\`\`
Prompt: "Aşağıdaki konuda 500 kelimelik SEO uyumlu bir blog yazısı yaz:
- Hedef kitle: [HEDEF_KITLE]
- Ana anahtar kelime: [ANAHTAR_KELIME]
- Ton: Profesyonel ama samimi
- CTA: [CALL_TO_ACTION]"
\`\`\`

### Kod İncelemesi

\`\`\`
Prompt: "Bu kodu incele ve şu açılardan değerlendir:
1. Performans optimizasyonu
2. Güvenlik açıkları
3. Kod kalitesi ve okunabilirlik
4. Best practice uyumu
Önerilerini madde madde listele."
\`\`\`

### E-posta Yazımı

\`\`\`
Prompt: "Aşağıdaki bilgileri kullanarak profesyonel bir e-posta yaz:
- Alıcı: [ALICI_ADI]
- Konu: [KONU]
- Amaç: [AMAC]
- Ton: [TON]
- Önemli noktalar: [ONEMLI_NOKTALAR]"
\`\`\`

## Verimlilik Artırma Stratejileri

### 1. Template Kullanımı

Sık kullandığınız prompt türleri için şablonlar oluşturun:

- **Değişken Alanlar**: [DEGISKEN] formatında placeholder'lar kullanın
- **Standart Yapı**: Her prompt türü için tutarlı format
- **Hızlı Doldurma**: Değişkenleri tek tıkla doldurun

### 2. Versiyon Kontrolü

Prompt'larınızın farklı versiyonlarını takip edin:

- **Değişiklik Geçmişi**: Her değişikliği kaydedin
- **A/B Testing**: Farklı versiyonları test edin
- **Geri Alma**: Önceki versiyonlara kolayca dönün

### 3. Performans Takibi

Hangi prompt'ların daha etkili olduğunu ölçün:

- **Kullanım Sıklığı**: En çok kullanılan prompt'ları belirleyin
- **Başarı Oranı**: Hangi prompt'ların daha iyi sonuç verdiğini takip edin
- **Zaman Tasarrufu**: Ne kadar zaman kazandığınızı hesaplayın

## Başlangıç İçin İpuçları

### 1. Mevcut Prompt'larınızı Toplayın

- Geçmiş sohbet geçmişlerinizi inceleyin
- Etkili bulduğunuz prompt'ları kaydedin
- Kategorilere ayırarak organize edin

### 2. Takımınızla Paylaşın

- En iyi prompt'larınızı ekibinizle paylaşın
- Ortak bir prompt kütüphanesi oluşturun
- Düzenli olarak yeni prompt'lar ekleyin

### 3. Sürekli İyileştirin

- Prompt'larınızı düzenli olarak gözden geçirin
- Yeni AI araçları için uyarlayın
- Geri bildirimler doğrultusunda güncelleyin

## Sonuç

PromptBir ile AI verimliliğinizi artırmak artık çok kolay. Doğru prompt yönetimi stratejileri ile:

- **%300 Zaman Tasarrufu**: Hazır prompt'lar ile hızlı çalışın
- **%500 Kalite Artışı**: Test edilmiş prompt'lar ile daha iyi sonuçlar
- **%200 Takım Verimliliği**: Paylaşılan bilgi ile ekip performansı

Hemen bugün PromptBir'i denemeye başlayın ve AI verimliliğinizi bir üst seviyeye taşıyın!

---

*Bu yazı PromptBir ekibi tarafından hazırlanmıştır. Platform hakkında daha fazla bilgi için [PromptBir.com](https://promptbir.com) adresini ziyaret edebilirsiniz.*
    `
  },
  'prompt-engineering-rehber': {
    id: 2,
    title: 'Prompt Engineering: Başlangıçtan İleri Seviyeye Rehber',
    slug: 'prompt-engineering-rehber',
    excerpt: 'Etkili prompt yazma sanatını öğrenin. Temel prensiplerden ileri tekniklere kadar kapsamlı rehber.',
    author: 'Dr. Ahmet Yılmaz',
    publishDate: '2024-01-10',
    readTime: '12 dakika',
    category: 'Eğitim',
    tags: ['Prompt Engineering', 'AI', 'Eğitim'],
    featured: true,
    content: `
# Prompt Engineering: Başlangıçtan İleri Seviyeye Rehber

Prompt engineering, AI ile etkileşimde bulunmanın en önemli becerilerinden biri haline geldi. Bu kapsamlı rehberde, etkili prompt yazma sanatını baştan sona öğreneceksiniz.

## Prompt Engineering Nedir?

Prompt engineering, AI modellerinden istediğiniz sonuçları elde etmek için optimize edilmiş talimatlar yazma sanatıdır. Doğru prompt, AI'dan aldığınız yanıtın kalitesini dramatik şekilde artırabilir.

## Temel Prensipler

### 1. Açık ve Net Olun

**Kötü Örnek:**
\`\`\`
"Bir yazı yaz"
\`\`\`

**İyi Örnek:**
\`\`\`
"E-ticaret şirketleri için sosyal medya pazarlama stratejileri hakkında 
500 kelimelik, SEO uyumlu bir blog yazısı yaz. Hedef kitle: KOBİ sahipleri."
\`\`\`

### 2. Bağlam Sağlayın

AI'ya yeterli bağlam vermek kritik önem taşır:

\`\`\`
"Sen deneyimli bir pazarlama uzmanısın. 10 yıllık tecriben var.
Aşağıdaki şirket için bir pazarlama stratejisi geliştir:
- Şirket: Organik gıda e-ticaret
- Hedef: 25-45 yaş arası sağlık bilincine sahip bireyler
- Bütçe: Aylık 50.000 TL
- Süre: 6 ay"
\`\`\`

### 3. Örnekler Verin

Few-shot learning tekniği ile daha iyi sonuçlar alın:

\`\`\`
"Aşağıdaki örneklere benzer ürün açıklamaları yaz:

Örnek 1:
Ürün: Kablosuz Kulaklık
Açıklama: "Gürültü önleyici teknoloji ile kristal berraklığında ses deneyimi. 
30 saate kadar kesintisiz müzik keyfi."

Örnek 2:
Ürün: Akıllı Saat
Açıklama: "Sağlığınızı 7/24 takip eden akıllı asistanınız. 
Su geçirmez tasarım ile her ortamda yanınızda."

Şimdi bu ürün için açıklama yaz:
Ürün: Bluetooth Hoparlör"
\`\`\`

## İleri Seviye Teknikler

### 1. Chain of Thought (CoT)

AI'ya adım adım düşünmesini söyleyin:

\`\`\`
"Bu problemi adım adım çöz:

Problem: Bir şirketin aylık geliri 100.000 TL, giderleri 75.000 TL. 
Gelirini %20 artırıp, giderlerini %10 azaltırsa, 
yeni kar marjı ne olur?

Adımlar:
1. Mevcut kar hesapla
2. Yeni geliri hesapla
3. Yeni giderleri hesapla
4. Yeni karı hesapla
5. Kar marjını hesapla"
\`\`\`

### 2. Role Playing

AI'ya belirli bir rol verin:

\`\`\`
"Sen deneyimli bir UX/UI tasarımcısısın. 
Aşağıdaki mobil uygulama için kullanıcı deneyimi analizi yap:

Uygulama: Yemek sipariş uygulaması
Problem: Kullanıcılar sipariş tamamlama oranı düşük
Veri: %60 kullanıcı sepete ürün ekliyor ama sadece %25'i sipariş tamamlıyor

Analiz et ve çözüm önerileri sun."
\`\`\`

### 3. Constraint-Based Prompting

Sınırlamalar belirleyin:

\`\`\`
"Aşağıdaki kısıtlamalara uyarak bir blog yazısı yaz:
- Maksimum 300 kelime
- En az 3 madde listesi
- Başlık H1, alt başlıklar H2 formatında
- Her paragraf maksimum 2 cümle
- Sonunda CTA bulunsun
- SEO için 'dijital pazarlama' kelimesi 5 kez geçsin"
\`\`\`

## Farklı AI Modelleri İçin Optimizasyon

### ChatGPT İçin

- Uzun ve detaylı prompt'lar tercih edin
- Bağlam penceresi geniş, önceki konuşmaları referans alabilir
- Yaratıcı görevlerde çok başarılı

### Claude İçin

- Yapılandırılmış prompt'lar daha etkili
- Analitik görevlerde güçlü
- Güvenlik konularında daha dikkatli

### Gemini İçin

- Çok modlu (metin + görsel) prompt'lar destekler
- Google hizmetleri ile entegrasyon
- Gerçek zamanlı bilgi erişimi

## Yaygın Hatalar ve Çözümleri

### 1. Belirsiz Talimatlar

**Hata:**
\`\`\`
"İyi bir sunum hazırla"
\`\`\`

**Çözüm:**
\`\`\`
"Startup şirketimiz için yatırımcı sunumu hazırla:
- 10 slayt
- Problem, çözüm, pazar, iş modeli, takım, finansal projeksiyonlar
- Her slayt için konuşma notları
- Görsel öneriler"
\`\`\`

### 2. Çok Karmaşık Talimatlar

**Hata:**
\`\`\`
"Bir e-ticaret sitesi için SEO stratejisi geliştir, aynı zamanda sosyal medya 
planı yap, influencer listesi oluştur, bütçe planla, timeline hazırla..."
\`\`\`

**Çözüm:**
Görevleri parçalara ayırın ve sırayla isteyin.

### 3. Bağlam Eksikliği

**Hata:**
\`\`\`
"Bu kodu optimize et: [kod]"
\`\`\`

**Çözüm:**
\`\`\`
"Bu Python kodunu performans açısından optimize et:
- Hedef: API yanıt süresini %50 azalt
- Kısıtlar: Mevcut fonksiyonalite değişmemeli
- Ortam: Django, PostgreSQL
- Kod: [kod]"
\`\`\`

## Prompt Şablonları

### İçerik Üretimi Şablonu

\`\`\`
"[ROL] olarak hareket et.

Görev: [GÖREV_TANIMI]
Hedef Kitle: [HEDEF_KITLE]
Ton: [TON]
Uzunluk: [UZUNLUK]
Format: [FORMAT]
Anahtar Kelimeler: [ANAHTAR_KELIMELER]

Ek Gereksinimler:
- [GEREKSINIM_1]
- [GEREKSINIM_2]
- [GEREKSINIM_3]

Çıktı formatı:
[ISTENEN_FORMAT]"
\`\`\`

### Problem Çözme Şablonu

\`\`\`
"Problem: [PROBLEM_TANIMI]

Bağlam:
- [BAGLAM_1]
- [BAGLAM_2]
- [BAGLAM_3]

Kısıtlar:
- [KISIT_1]
- [KISIT_2]

Beklenen Çıktı:
1. Problem analizi
2. Alternatif çözümler (en az 3)
3. Her çözüm için artı/eksi
4. Önerilen çözüm ve gerekçesi
5. Uygulama adımları"
\`\`\`

## Performans Ölçümü

Prompt'larınızın etkinliğini ölçmek için:

### 1. A/B Testing

Aynı görev için farklı prompt'lar deneyin ve sonuçları karşılaştırın.

### 2. Kalite Metrikleri

- **Doğruluk**: Verilen bilgiler ne kadar doğru?
- **Relevans**: Cevap soruya ne kadar uygun?
- **Yaratıcılık**: Özgün ve yaratıcı mı?
- **Kullanılabilirlik**: Pratik olarak kullanılabilir mi?

### 3. Zaman Tasarrufu

- Prompt yazma süresi
- İstenen sonucu alma süresi
- Düzeltme ihtiyacı

## Gelecek Trendleri

### 1. Multimodal Prompting

Metin + görsel + ses kombinasyonları

### 2. Automated Prompt Optimization

AI'ın kendi prompt'larını optimize etmesi

### 3. Domain-Specific Prompting

Sektöre özel prompt kütüphaneleri

## Sonuç

Prompt engineering, AI çağında en değerli becerilerden biri. Bu rehberdeki teknikleri uygulayarak:

- Daha etkili AI etkileşimleri
- Zaman tasarrufu
- Kaliteli çıktılar
- Profesyonel avantaj

Sürekli pratik yapın ve yeni teknikleri deneyin. AI teknolojisi hızla gelişiyor, prompt engineering becerinizi de sürekli güncel tutun.

---

*Dr. Ahmet Yılmaz, AI ve Makine Öğrenmesi alanında 15 yıllık deneyime sahip akademisyen ve danışmandır.*
    `
  },
  'takim-calismasi-ai-prompts': {
    id: 3,
    title: 'Takım Çalışmasında AI Prompt\'ları: İşbirliği Rehberi',
    slug: 'takim-calismasi-ai-prompts',
    excerpt: 'Takım halinde çalışırken AI prompt\'larını nasıl etkili şekilde paylaşabileceğinizi ve yönetebileceğinizi öğrenin.',
    author: 'Elif Kaya',
    publishDate: '2024-01-05',
    readTime: '10 dakika',
    category: 'Takım Çalışması',
    tags: ['Takım Çalışması', 'İşbirliği', 'Prompt Paylaşımı'],
    featured: false,
    content: `
# Takım Çalışmasında AI Prompt'ları: İşbirliği Rehberi

Modern iş dünyasında takımlar, AI araçlarını günlük iş akışlarına entegre ediyor. Ancak prompt'ları etkili şekilde paylaşmak ve yönetmek, takım verimliliği için kritik önem taşıyor.

## Takım Prompt Yönetiminin Önemi

### Yaygın Sorunlar
- Her takım üyesi kendi prompt'larını yazıyor
- Etkili prompt'lar kaybolup gidiyor
- Tutarsız sonuçlar alınıyor
- Zaman kaybı yaşanıyor

### Çözüm: Merkezi Prompt Kütüphanesi
PromptBir ile takımınız için merkezi bir prompt kütüphanesi oluşturabilirsiniz.

## Takım Prompt Stratejileri

### 1. Rol Bazlı Prompt Organizasyonu

**Pazarlama Takımı:**
\`\`\`
"[ÜRÜN_ADI] için sosyal medya gönderisi oluştur:
- Platform: [PLATFORM]
- Hedef kitle: [HEDEF_KİTLE]
- Ton: [TON]
- Hashtag sayısı: [SAYI]
- CTA dahil et"
\`\`\`

**Geliştirici Takımı:**
\`\`\`
"[PROGRAMLAMA_DİLİ] ile [AÇIKLAMA] yapan kod yaz:
- Clean code prensipleri
- Error handling
- Unit test örnekleri
- Dokümantasyon"
\`\`\`

**Satış Takımı:**
\`\`\`
"[MÜŞTERİ_PROFİLİ] için kişiselleştirilmiş satış e-postası:
- Ürün: [ÜRÜN]
- Müşteri ihtiyacı: [İHTİYAÇ]
- Ton: profesyonel ama samimi
- CTA: demo talep et"
\`\`\`

### 2. Proje Bazlı Prompt Setleri

Her proje için özel prompt koleksiyonları oluşturun:
- Proje başlangıç prompt'ları
- Süreç yönetimi prompt'ları
- Kalite kontrol prompt'ları
- Raporlama prompt'ları

### 3. Versiyon Kontrolü

Prompt'larınızı versiyonlayın:
- v1.0: İlk versiyon
- v1.1: Küçük iyileştirmeler
- v2.0: Büyük değişiklikler

## İşbirliği Best Practice'leri

### Prompt Paylaşım Kuralları

1. **Açık İsimlendirme**
   - "Email_Template_v2" ❌
   - "Müşteri_Onboarding_Email_Şablonu_v2.1" ✅

2. **Açıklama Ekleme**
   - Prompt'un amacı
   - Kullanım senaryoları
   - Beklenen sonuçlar

3. **Etiketleme Sistemi**
   - #pazarlama #email #onboarding
   - #geliştirme #python #api
   - #satış #demo #b2b

### Takım Eğitimi

**Haftalık Prompt Review:**
- En etkili prompt'ları paylaşın
- Yeni teknikleri tartışın
- Başarı hikayelerini anlatın

**Prompt Workshop'ları:**
- Yeni takım üyeleri için eğitim
- İleri seviye teknikler
- Sektörel örnekler

## Kalite Kontrol Süreci

### Prompt Değerlendirme Kriterleri

1. **Netlik** (1-5 puan)
   - Talimatlar açık mı?
   - Beklentiler net mi?

2. **Tutarlılık** (1-5 puan)
   - Sonuçlar tutarlı mı?
   - Farklı kullanıcılarda aynı sonuç mu?

3. **Verimlilik** (1-5 puan)
   - Zaman tasarrufu sağlıyor mu?
   - İş akışını hızlandırıyor mu?

### Geri Bildirim Döngüsü

1. Prompt kullanımı
2. Sonuç değerlendirmesi
3. Geri bildirim toplama
4. İyileştirme önerileri
5. Prompt güncelleme

## Güvenlik ve Gizlilik

### Hassas Bilgi Yönetimi

- Müşteri bilgilerini prompt'larda kullanmayın
- Placeholder'lar kullanın: [MÜŞTERİ_ADI]
- Şirket sırlarını koruyun

### Erişim Kontrolü

- Takım bazlı erişim yetkileri
- Rol bazlı prompt görünürlüğü
- Düzenleme yetkilerini sınırlayın

## Performans Metrikleri

### Takım Verimliliği KPI'ları

1. **Prompt Kullanım Oranı**
   - Takım üyelerinin prompt kullanım sıklığı
   - En çok kullanılan prompt'lar

2. **Zaman Tasarrufu**
   - Görev tamamlama süreleri
   - Prompt öncesi vs sonrası karşılaştırma

3. **Kalite Artışı**
   - Çıktı kalitesi değerlendirmeleri
   - Müşteri memnuniyeti

### Raporlama

Aylık takım raporları:
- En etkili prompt'lar
- Kullanım istatistikleri
- İyileştirme önerileri
- Başarı hikayeleri

## Gelecek Planlaması

### Prompt Kütüphanesi Büyütme

1. **Sürekli İyileştirme**
   - Kullanıcı geri bildirimlerini toplayın
   - A/B test yapın
   - Yeni teknikleri deneyin

2. **Bilgi Paylaşımı**
   - Diğer takımlarla işbirliği
   - Sektör best practice'lerini takip edin
   - Konferans ve eğitimlere katılın

3. **Teknoloji Entegrasyonu**
   - API entegrasyonları
   - Otomasyon araçları
   - İş akışı optimizasyonu

## Sonuç

Takım halinde AI prompt yönetimi, modern iş dünyasında rekabet avantajı sağlar. PromptBir ile:

- Merkezi prompt kütüphanesi
- Etkili işbirliği
- Kalite kontrolü
- Sürekli iyileştirme

Takımınızın AI verimliliğini artırmak için bugün başlayın!

---

*Elif Kaya, 10 yıllık deneyime sahip proje yöneticisi ve takım liderliği uzmanıdır.*
    `
  },
  'ai-prompt-guvenlik': {
    id: 4,
    title: 'AI Prompt Güvenliği: Veri Koruma ve Gizlilik Rehberi',
    slug: 'ai-prompt-guvenlik',
    excerpt: 'AI prompt\'larınızı kullanırken veri güvenliğini nasıl sağlayacağınızı ve gizliliği nasıl koruyacağınızı öğrenin.',
    author: 'Mehmet Demir',
    publishDate: '2023-12-28',
    readTime: '9 dakika',
    category: 'Güvenlik',
    tags: ['Güvenlik', 'Gizlilik', 'Veri Koruma'],
    featured: false,
    content: `
# AI Prompt Güvenliği: Veri Koruma ve Gizlilik Rehberi

AI araçlarının yaygınlaşmasıyla birlikte, prompt güvenliği kritik bir konu haline geldi. Bu rehberde, AI prompt'larınızı kullanırken veri güvenliğini nasıl sağlayacağınızı öğreneceksiniz.

## Güvenlik Riskleri

### Yaygın Güvenlik Tehditleri

1. **Veri Sızıntısı**
   - Hassas bilgilerin prompt'larda kullanılması
   - Müşteri verilerinin yanlışlıkla paylaşılması
   - Şirket sırlarının ifşa edilmesi

2. **Prompt Injection Saldırıları**
   - Kötü niyetli kullanıcıların sistem prompt'larını manipüle etmesi
   - Yetkisiz erişim girişimleri
   - Sistem güvenliğinin aşılması

3. **Model Poisoning**
   - Zararlı verilerle model eğitiminin bozulması
   - Önyargılı sonuçların üretilmesi
   - Sistem güvenilirliğinin azalması

## Güvenli Prompt Yazma Prensipleri

### 1. Veri Anonimleştirme

**Yanlış Yaklaşım:**
\`\`\`
"Ahmet Yılmaz (TC: 12345678901) için kredi başvurusu değerlendir"
\`\`\`

**Doğru Yaklaşım:**
\`\`\`
"[MÜŞTERİ_ADI] için kredi başvurusu değerlendir:
- Yaş: [YAŞ]
- Gelir: [GELİR]
- Kredi geçmişi: [KREDİ_SKORU]"
\`\`\`

### 2. Placeholder Kullanımı

Hassas bilgiler yerine placeholder'lar kullanın:
- [MÜŞTERİ_ADI] → Gerçek isim yerine
- [ŞIRKET_ADI] → Şirket adı yerine
- [PROJE_KODU] → Proje detayları yerine
- [FİNANSAL_VERİ] → Mali bilgiler yerine

### 3. Minimum Veri Prensibi

Sadece gerekli bilgileri paylaşın:
- Görev için gerekli minimum veri
- Bağlam için yeterli bilgi
- Gereksiz detaylardan kaçının

## Kurumsal Güvenlik Politikaları

### Veri Sınıflandırması

**Açık Veri (Public)**
- Genel bilgiler
- Pazarlama materyalleri
- Halka açık dökümanlar

**İç Veri (Internal)**
- Şirket süreçleri
- İç iletişim
- Operasyonel bilgiler

**Gizli Veri (Confidential)**
- Müşteri bilgileri
- Finansal veriler
- Stratejik planlar

**Çok Gizli Veri (Restricted)**
- Ticari sırlar
- Kişisel veriler
- Yasal belgeler

### Erişim Kontrolü

1. **Rol Bazlı Erişim**
   - Departman bazlı yetkilendirme
   - Görev bazlı sınırlamalar
   - Proje bazlı erişim

2. **İki Faktörlü Doğrulama**
   - SMS doğrulama
   - Authenticator uygulamaları
   - Biometrik doğrulama

3. **Oturum Yönetimi**
   - Otomatik oturum sonlandırma
   - Eşzamanlı oturum sınırları
   - IP bazlı kısıtlamalar

## KVKK ve GDPR Uyumluluğu

### Kişisel Veri Koruma

**KVKK Gereklilikleri:**
- Açık rıza alınması
- Veri işleme amacının belirtilmesi
- Veri saklama sürelerinin belirlenmesi
- Veri güvenliği tedbirlerinin alınması

**GDPR Gereklilikleri:**
- Veri taşınabilirliği hakkı
- Unutulma hakkı
- Veri işleme faaliyetlerinin kayıt altına alınması
- Veri koruma etki değerlendirmesi

### Uygulama Örnekleri

**Müşteri Hizmetleri Prompt'u:**
\`\`\`
"Müşteri şikayeti analizi yap:
- Şikayet kategorisi: [KATEGORİ]
- Aciliyet seviyesi: [ACİLİYET]
- Çözüm önerisi sun
- Müşteri memnuniyeti için adımlar

Not: Kişisel bilgileri kullanma, sadece şikayet içeriğini analiz et."
\`\`\`

## Teknik Güvenlik Önlemleri

### Şifreleme

1. **Veri Şifreleme**
   - AES-256 şifreleme
   - End-to-end encryption
   - Database şifreleme

2. **İletişim Şifreleme**
   - HTTPS protokolü
   - TLS 1.3 kullanımı
   - Certificate pinning

### Güvenlik Denetimi

**Düzenli Denetimler:**
- Penetrasyon testleri
- Güvenlik açığı taramaları
- Kod güvenlik analizi
- Erişim log incelemesi

**Olay Müdahale Planı:**
1. Güvenlik ihlali tespiti
2. Etki analizi
3. Müdahale ekibinin devreye girmesi
4. İyileştirme önlemlerinin alınması

## Güvenli Prompt Şablonları

### Genel Güvenlik Şablonu

\`\`\`
"[GÖREV_AÇIKLAMASI] için analiz yap:

Giriş Verileri:
- [VERİ_TİPİ_1]: [PLACEHOLDER_1]
- [VERİ_TİPİ_2]: [PLACEHOLDER_2]

Güvenlik Notları:
- Kişisel bilgileri kullanma
- Hassas verileri kaydetme
- Sadece analiz sonuçlarını döndür

Beklenen Çıktı:
- [ÇIKTI_FORMAT]
- [ÇIKTI_DETAY]"
\`\`\`

### Finansal Veri Analizi Şablonu

\`\`\`
"Finansal performans analizi:

Veriler (Anonimleştirilmiş):
- Gelir: [GELİR_ARALIĞI]
- Gider: [GİDER_KATEGORİSİ]
- Dönem: [ZAMAN_ARALIĞI]

Analiz Kriterleri:
- Trend analizi
- Karşılaştırmalı analiz
- Risk değerlendirmesi

Güvenlik: Gerçek rakamları kullanma, sadece oransal analiz yap."
\`\`\`

## Eğitim ve Farkındalık

### Personel Eğitimi

**Temel Güvenlik Eğitimi:**
- Güvenli prompt yazma
- Veri sınıflandırması
- Olay raporlama

**İleri Seviye Eğitim:**
- Güvenlik testleri
- Saldırı simülasyonları
- Best practice workshop'ları

### Sürekli İyileştirme

1. **Güvenlik Metrikleri**
   - Güvenlik ihlali sayısı
   - Eğitim tamamlama oranları
   - Güvenlik denetim sonuçları

2. **Geri Bildirim Döngüsü**
   - Kullanıcı raporları
   - Güvenlik ekibi değerlendirmeleri
   - Süreç iyileştirmeleri

## Sonuç

AI prompt güvenliği, modern iş dünyasında kritik önem taşır. Bu rehberdeki prensipleri uygulayarak:

- Veri güvenliğini sağlayabilir
- Yasal gerekliliklere uyum gösterebilir
- Kurumsal güveni koruyabilir
- Rekabet avantajı elde edebilirsiniz

Güvenlik, sürekli bir süreçtir. Düzenli eğitim, denetim ve iyileştirme ile güvenlik seviyenizi yüksek tutun.

---

*Mehmet Demir, siber güvenlik alanında 12 yıllık deneyime sahip uzman ve sertifikalı etik hacker'dır.*
    `
  },
  'promptbir-api-rehber': {
    id: 5,
    title: 'PromptBir API: Geliştiriciler için Kapsamlı Rehber',
    slug: 'promptbir-api-rehber',
    excerpt: 'PromptBir API\'sini kullanarak kendi uygulamalarınızda prompt yönetimi nasıl entegre edebileceğinizi öğrenin.',
    author: 'PromptBir Geliştirici Ekibi',
    publishDate: '2023-12-20',
    readTime: '15 dakika',
    category: 'Geliştirici',
    tags: ['API', 'Geliştirici', 'Entegrasyon'],
    featured: false,
    content: `
# PromptBir API: Geliştiriciler için Kapsamlı Rehber

PromptBir API, geliştiricilerin kendi uygulamalarında prompt yönetimi yapabilmelerini sağlayan güçlü bir RESTful API'dir. Bu rehberde, API'yi nasıl kullanacağınızı adım adım öğreneceksiniz.

## API'ye Başlangıç

### Kimlik Doğrulama

PromptBir API, JWT token tabanlı kimlik doğrulama kullanır:

\`\`\`javascript
// API anahtarınızı alın
const API_KEY = 'your-api-key-here';
const BASE_URL = 'https://api.promptbir.com/v1';

// Headers
const headers = {
  'Authorization': \`Bearer \${API_KEY}\`,
  'Content-Type': 'application/json'
};
\`\`\`

### Rate Limiting

API kullanımında aşağıdaki limitler geçerlidir:
- **Free Plan**: 100 istek/saat
- **Pro Plan**: 1,000 istek/saat
- **Enterprise Plan**: 10,000 istek/saat

## Temel API Endpoint'leri

### 1. Projeler (Projects)

**Tüm Projeleri Listele**
\`\`\`javascript
const getProjects = async () => {
  const response = await fetch(\`\${BASE_URL}/projects\`, {
    method: 'GET',
    headers: headers
  });

  const projects = await response.json();
  return projects;
};
\`\`\`

**Yeni Proje Oluştur**
\`\`\`javascript
const createProject = async (projectData) => {
  const response = await fetch(\`\${BASE_URL}/projects\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      name: projectData.name,
      description: projectData.description,
      tags: projectData.tags
    })
  });

  const newProject = await response.json();
  return newProject;
};
\`\`\`

### 2. Prompt'lar (Prompts)

**Prompt'ları Listele**
\`\`\`javascript
const getPrompts = async (projectId, filters = {}) => {
  const queryParams = new URLSearchParams({
    project_id: projectId,
    ...filters
  });

  const response = await fetch(\`\${BASE_URL}/prompts?\${queryParams}\`, {
    method: 'GET',
    headers: headers
  });

  const prompts = await response.json();
  return prompts;
};
\`\`\`

**Yeni Prompt Oluştur**
\`\`\`javascript
const createPrompt = async (promptData) => {
  const response = await fetch(\`\${BASE_URL}/prompts\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      project_id: promptData.projectId,
      title: promptData.title,
      content: promptData.content,
      category: promptData.category,
      hashtags: promptData.hashtags
    })
  });

  const newPrompt = await response.json();
  return newPrompt;
};
\`\`\`

**Prompt Güncelle**
\`\`\`javascript
const updatePrompt = async (promptId, updateData) => {
  const response = await fetch(\`\${BASE_URL}/prompts/\${promptId}\`, {
    method: 'PUT',
    headers: headers,
    body: JSON.stringify(updateData)
  });

  const updatedPrompt = await response.json();
  return updatedPrompt;
};
\`\`\`

### 3. Kategoriler (Categories)

**Kategorileri Listele**
\`\`\`javascript
const getCategories = async (projectId) => {
  const response = await fetch(\`\${BASE_URL}/categories?project_id=\${projectId}\`, {
    method: 'GET',
    headers: headers
  });

  const categories = await response.json();
  return categories;
};
\`\`\`

## İleri Seviye Özellikler

### Toplu İşlemler (Batch Operations)

**Toplu Prompt Oluşturma**
\`\`\`javascript
const createBatchPrompts = async (prompts) => {
  const response = await fetch(\`\${BASE_URL}/prompts/batch\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      prompts: prompts
    })
  });

  const result = await response.json();
  return result;
};
\`\`\`

### Arama ve Filtreleme

**Gelişmiş Arama**
\`\`\`javascript
const searchPrompts = async (searchParams) => {
  const response = await fetch(\`\${BASE_URL}/prompts/search\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      query: searchParams.query,
      filters: {
        category: searchParams.category,
        hashtags: searchParams.hashtags,
        date_range: searchParams.dateRange
      },
      sort: searchParams.sort,
      limit: searchParams.limit,
      offset: searchParams.offset
    })
  });

  const searchResults = await response.json();
  return searchResults;
};
\`\`\`

### Webhook'lar

**Webhook Kurulumu**
\`\`\`javascript
const setupWebhook = async (webhookConfig) => {
  const response = await fetch(\`\${BASE_URL}/webhooks\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      url: webhookConfig.url,
      events: webhookConfig.events, // ['prompt.created', 'prompt.updated', 'prompt.deleted']
      secret: webhookConfig.secret
    })
  });

  const webhook = await response.json();
  return webhook;
};
\`\`\`

## SDK'lar ve Kütüphaneler

### JavaScript/Node.js SDK

\`\`\`bash
npm install @promptbir/sdk
\`\`\`

\`\`\`javascript
import { PromptBirClient } from '@promptbir/sdk';

const client = new PromptBirClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.promptbir.com/v1'
});

// Kullanım
const projects = await client.projects.list();
const prompts = await client.prompts.list(projectId);
\`\`\`

### Python SDK

\`\`\`bash
pip install promptbir-python
\`\`\`

\`\`\`python
from promptbir import PromptBirClient

client = PromptBirClient(api_key='your-api-key')

# Kullanım
projects = client.projects.list()
prompts = client.prompts.list(project_id=project_id)
\`\`\`

## Hata Yönetimi

### HTTP Durum Kodları

- **200**: Başarılı
- **201**: Oluşturuldu
- **400**: Hatalı istek
- **401**: Yetkisiz erişim
- **403**: Yasak
- **404**: Bulunamadı
- **429**: Rate limit aşıldı
- **500**: Sunucu hatası

### Hata Yanıt Formatı

\`\`\`json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Gerekli alan eksik: title",
    "details": {
      "field": "title",
      "reason": "required"
    }
  }
}
\`\`\`

### Hata Yönetimi Örneği

\`\`\`javascript
const handleApiCall = async (apiFunction) => {
  try {
    const result = await apiFunction();
    return { success: true, data: result };
  } catch (error) {
    if (error.status === 429) {
      // Rate limit aşıldı, bekle ve tekrar dene
      await new Promise(resolve => setTimeout(resolve, 60000));
      return handleApiCall(apiFunction);
    } else if (error.status === 401) {
      // Token yenile
      await refreshToken();
      return handleApiCall(apiFunction);
    } else {
      return { success: false, error: error.message };
    }
  }
};
\`\`\`

## Performans Optimizasyonu

### Önbellekleme (Caching)

\`\`\`javascript
class PromptBirCache {
  constructor() {
    this.cache = new Map();
    this.ttl = 5 * 60 * 1000; // 5 dakika
  }

  get(key) {
    const item = this.cache.get(key);
    if (item && Date.now() - item.timestamp < this.ttl) {
      return item.data;
    }
    this.cache.delete(key);
    return null;
  }

  set(key, data) {
    this.cache.set(key, {
      data: data,
      timestamp: Date.now()
    });
  }
}

const cache = new PromptBirCache();
\`\`\`

### Pagination

\`\`\`javascript
const getAllPrompts = async (projectId) => {
  let allPrompts = [];
  let offset = 0;
  const limit = 100;

  while (true) {
    const response = await getPrompts(projectId, { limit, offset });
    allPrompts = allPrompts.concat(response.data);

    if (response.data.length < limit) {
      break; // Son sayfa
    }

    offset += limit;
  }

  return allPrompts;
};
\`\`\`

## Güvenlik Best Practice'leri

### API Anahtarı Güvenliği

1. **Çevre Değişkenleri Kullanın**
\`\`\`javascript
const API_KEY = process.env.PROMPTBIR_API_KEY;
\`\`\`

2. **API Anahtarını Asla Frontend'de Kullanmayın**
\`\`\`javascript
// ❌ Yanlış - Frontend'de API anahtarı
const apiKey = 'pk_live_123456789';

// ✅ Doğru - Backend proxy kullanın
const response = await fetch('/api/prompts', {
  method: 'GET',
  headers: {
    'Authorization': \`Bearer \${userToken}\`
  }
});
\`\`\`

3. **HTTPS Kullanın**
Tüm API çağrılarında HTTPS protokolünü kullanın.

## Örnek Uygulamalar

### React Entegrasyonu

\`\`\`jsx
import React, { useState, useEffect } from 'react';
import { PromptBirClient } from '@promptbir/sdk';

const PromptList = ({ projectId }) => {
  const [prompts, setPrompts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPrompts = async () => {
      try {
        const client = new PromptBirClient({
          apiKey: process.env.REACT_APP_PROMPTBIR_API_KEY
        });

        const promptData = await client.prompts.list(projectId);
        setPrompts(promptData);
      } catch (error) {
        console.error('Prompt'lar yüklenemedi:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPrompts();
  }, [projectId]);

  if (loading) return <div>Yükleniyor...</div>;

  return (
    <div>
      {prompts.map(prompt => (
        <div key={prompt.id}>
          <h3>{prompt.title}</h3>
          <p>{prompt.content}</p>
        </div>
      ))}
    </div>
  );
};
\`\`\`

### Node.js Backend Entegrasyonu

\`\`\`javascript
const express = require('express');
const { PromptBirClient } = require('@promptbir/sdk');

const app = express();
const client = new PromptBirClient({
  apiKey: process.env.PROMPTBIR_API_KEY
});

app.get('/api/prompts/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const prompts = await client.prompts.list(projectId);
    res.json(prompts);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.listen(3000, () => {
  console.log('Server 3000 portunda çalışıyor');
});
\`\`\`

## Sonuç

PromptBir API, güçlü ve esnek bir prompt yönetim çözümü sunar. Bu rehberdeki örnekleri kullanarak:

- Kendi uygulamalarınızda prompt yönetimi
- Otomatik prompt senkronizasyonu
- Gelişmiş arama ve filtreleme
- Güvenli API entegrasyonu

API dokümantasyonunun tamamına [api.promptbir.com](https://api.promptbir.com) adresinden ulaşabilirsiniz.

---

*PromptBir Geliştirici Ekibi, modern web teknolojileri ve API tasarımı konularında uzman geliştiricilerden oluşur.*
    `
  }
}

type Props = {
  params: Promise<{ slug: string }>
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params
  const post = blogPosts[slug as keyof typeof blogPosts]
  
  if (!post) {
    return {
      title: 'Blog Yazısı Bulunamadı - PromptBir',
      description: 'Aradığınız blog yazısı bulunamadı.'
    }
  }

  return {
    title: `${post.title} - PromptBir Blog`,
    description: post.excerpt,
    keywords: post.tags,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: 'article',
      publishedTime: post.publishDate,
      authors: [post.author],
      url: `https://promptbir.com/blog/${post.slug}`
    }
  }
}

export default async function BlogPostPage({ params }: Props) {
  const { slug } = await params
  const post = blogPosts[slug as keyof typeof blogPosts]
  
  if (!post) {
    notFound()
  }

  // Get related posts (excluding current post)
  const relatedPosts = Object.values(blogPosts)
    .filter(p => p.id !== post.id && p.tags.some(tag => post.tags.includes(tag)))
    .slice(0, 2)

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link 
              href="/blog" 
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Blog'a Dön</span>
            </Link>
            <div className="flex items-center space-x-2">
              <Sparkles className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">PromptBir</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Article Header */}
        <article>
          <header className="mb-12">
            <div className="flex items-center gap-2 mb-4">
              {post.featured && (
                <Badge variant="secondary" className="bg-orange-100 text-orange-700">
                  Öne Çıkan
                </Badge>
              )}
              <Badge variant="outline">
                {post.category}
              </Badge>
            </div>
            
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              {post.title}
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              {post.excerpt}
            </p>
            
            <div className="flex flex-wrap items-center gap-6 text-gray-500 mb-8">
              <div className="flex items-center gap-2">
                <User className="h-5 w-5" />
                <span>{post.author}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                <span>{new Date(post.publishDate).toLocaleDateString('tr-TR')}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                <span>{post.readTime}</span>
              </div>
              <Button variant="outline" size="sm" className="ml-auto">
                <Share2 className="h-4 w-4 mr-2" />
                Paylaş
              </Button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag, index) => (
                <Badge key={index} variant="secondary">
                  {tag}
                </Badge>
              ))}
            </div>
          </header>

          {/* Article Content */}
          <div className="prose prose-lg prose-gray max-w-none">
            <div 
              dangerouslySetInnerHTML={{ 
                __html: post.content.replace(/\n/g, '<br>').replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>') 
              }} 
            />
          </div>
        </article>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <section className="mt-16 pt-16 border-t border-gray-200">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 flex items-center gap-3">
              <BookOpen className="h-8 w-8 text-blue-600" />
              İlgili Yazılar
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              {relatedPosts.map((relatedPost) => (
                <Card key={relatedPost.id} className="shadow-lg hover:shadow-xl transition-shadow group">
                  <CardHeader>
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="outline">
                        {relatedPost.category}
                      </Badge>
                    </div>
                    <h3 className="text-xl font-semibold group-hover:text-blue-600 transition-colors">
                      <Link href={`/blog/${relatedPost.slug}`}>
                        {relatedPost.title}
                      </Link>
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {relatedPost.excerpt}
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(relatedPost.publishDate).toLocaleDateString('tr-TR')}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>{relatedPost.readTime}</span>
                        </div>
                      </div>
                    </div>
                    <Link 
                      href={`/blog/${relatedPost.slug}`}
                      className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium group-hover:gap-3 transition-all"
                    >
                      Devamını Oku
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}

        {/* Newsletter CTA */}
        <section className="mt-16">
          <Card className="shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="p-8 text-center">
              <h2 className="text-2xl font-bold mb-4">
                Daha Fazla İçerik İçin Abone Olun
              </h2>
              <p className="text-blue-100 mb-6">
                AI ve prompt engineering konularındaki en güncel yazılarımızı kaçırmayın.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="E-posta adresiniz"
                  className="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500"
                />
                <Button className="bg-white text-blue-600 hover:bg-blue-50">
                  Abone Ol
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-400">
            © 2024 PromptBir. Tüm hakları saklıdır.
          </p>
        </div>
      </footer>
    </div>
  )
}
