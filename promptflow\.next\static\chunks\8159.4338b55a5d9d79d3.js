"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[832,8159,8451],{7801:(e,t,s)=>{s.d(t,{X1:()=>o,Xm:()=>c,v8:()=>d});var a=s(32960),r=s(26715),l=s(5041),n=s(70478),i=s(56671);function o(){return(0,a.I)({queryKey:["shared-prompts","user"],queryFn:async()=>{let{data:e}=await n.L.auth.getUser();if(!e.user)throw Error("Not authenticated");let{data:t,error:s}=await n.L.from("shared_prompts").select("\n          *,\n          prompt:prompts(\n            id,\n            prompt_text,\n            title,\n            description,\n            category,\n            tags,\n            task_code,\n            created_at,\n            project:projects(name)\n          )\n        ").eq("user_id",e.user.id).eq("is_active",!0).order("created_at",{ascending:!1});if(s)throw s;return t.map(e=>{var t,s;return{...e,project_name:(null==(s=e.prompt)||null==(t=s.project)?void 0:t.name)||"Unknown Project"}})},staleTime:3e5})}function c(){let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>{var t;let{data:s}=await n.L.auth.getUser();if(!s.user)throw Error("Not authenticated");let{data:a,error:r}=await n.L.from("prompts").select("id, user_id").eq("id",e.prompt_id).eq("user_id",s.user.id).single();if(r||!a)throw Error("Prompt not found or access denied");let l=function(){let e=new Uint8Array(16);return crypto.getRandomValues(e),Array.from(e,e=>e.toString(16).padStart(2,"0")).join("")}(),i=null;e.password&&(i=await m(e.password));let{data:o,error:c}=await n.L.from("shared_prompts").insert({prompt_id:e.prompt_id,user_id:s.user.id,share_token:l,title:e.title||null,description:e.description||null,is_public:null==(t=e.is_public)||t,password_hash:i,expires_at:e.expires_at||null}).select().single();if(c)throw c;let d="".concat(window.location.origin,"/share/").concat(l);return{id:o.id,share_token:l,share_url:d,created_at:o.created_at}},onSuccess:t=>{e.invalidateQueries({queryKey:["shared-prompts"]}),i.oR.success("Paylaşım linki oluşturuldu!",{description:"Link panoya kopyalandı"}),navigator.clipboard.writeText(t.share_url).catch(console.error)},onError:e=>{console.error("Share creation error:",e),i.oR.error("Paylaşım oluşturulamadı",{description:e instanceof Error?e.message:"Bilinmeyen hata"})}})}function d(e,t){return(0,a.I)({queryKey:["shared-prompt",e,t],queryFn:async()=>{var s,a;let{data:r,error:l}=await n.L.from("shared_prompts").select("\n          *,\n          prompt:prompts(\n            id,\n            prompt_text,\n            title,\n            description,\n            category,\n            tags,\n            task_code,\n            created_at,\n            project:projects(name)\n          )\n        ").eq("share_token",e).eq("is_active",!0).single();if(l)throw Error("Paylaşım bulunamadı veya s\xfcresi dolmuş");if(r.password_hash&&t){if(!await u(t,r.password_hash))throw Error("Şifre yanlış")}else if(r.password_hash&&!t)throw Error("Şifre gerekli");if(r.expires_at&&new Date(r.expires_at)<new Date)throw Error("Paylaşımın s\xfcresi dolmuş");return n.L.from("shared_prompts").update({view_count:r.view_count+1}).eq("id",r.id).then(()=>{fetch("/api/shared-prompts/record-view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({share_token:e,viewer_ip:null,viewer_user_agent:navigator.userAgent,referrer:document.referrer||null,session_id:"session_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),viewed_at:new Date().toISOString()})}).catch(console.error)}),{...r,project_name:(null==(a=r.prompt)||null==(s=a.project)?void 0:s.name)||"Unknown Project",author_email:"Unknown Author"}},enabled:!!e,staleTime:0,retry:!1})}async function m(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t)),e=>e.toString(16).padStart(2,"0")).join("")}async function u(e,t){return await m(e)===t}},8159:(e,t,s)=>{s.r(t),s.d(t,{PromptWorkspace:()=>ek,default:()=>e_});var a=s(95155),r=s(12115),l=s(30285),n=s(66695),i=s(88539),o=s(62523),c=s(66424),d=s(26126),m=s(48021),u=s(19145),h=s(18979),x=s(40646),p=s(9428),g=s(5196),f=s(54416),j=s(56287),v=s(24357),y=s(21380),b=s(15448),w=s(57434),N=s(74783),k=s(381),_=s(47924),C=s(84616),A=s(42103),E=s(91788),S=s(93654),z=s(47863),D=s(66474),L=s(41978),P=s(15501),K=s(67238),$=s(32960),F=s(70478);function T(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20;return(0,$.I)({queryKey:["popular-hashtags",e,t],queryFn:async()=>{if(!e)return[];let{data:s,error:a}=await F.L.from("prompts").select("tags").eq("project_id",e);if(a)throw Error(a.message);let r={};return null==s||s.forEach(e=>{e.tags&&Array.isArray(e.tags)&&e.tags.forEach(e=>{if("string"==typeof e&&e.trim()){let t=e.startsWith("#")?e.toLowerCase():"#".concat(e.toLowerCase());r[t]=(r[t]||0)+1}})}),Object.entries(r).map(e=>{let[t,s]=e;return{hashtag:t,count:s}}).sort((e,t)=>t.count-e.count).slice(0,t)},enabled:!!e})}function q(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(0,$.I)({queryKey:["popular-categories",e,t],queryFn:async()=>{if(!e)return[];let{data:s,error:a}=await F.L.from("prompts").select("category").eq("project_id",e).not("category","is",null);if(a)throw Error(a.message);let r={};return null==s||s.forEach(e=>{if(e.category){let t=e.category.toLowerCase();r[t]=(r[t]||0)+1}}),Object.entries(r).map(e=>{let[t,s]=e;return{category:t,count:s}}).sort((e,t)=>t.count-e.count).slice(0,t)},enabled:!!e})}var R=s(74178),M=s(26042),H=s(56671),Q=s(59434);function I(e){return e.replace(/^#/,"").trim().toLowerCase()}function O(e){let t=e.trim().toLowerCase();return t.startsWith("/")||(t="/"+t),(t=t.replace(/\/+/g,"/")).length>1&&t.endsWith("/")&&(t=t.slice(0,-1)),t}function U(e){let t=function(e){let t=e.match(/#[\w\u00C0-\u017F]+/g);return t?t.map(e=>e.toLowerCase()):[]}(e),s=function(e){let t=e.match(/\/[\w\u00C0-\u017F\/]+/g);return t?t.map(e=>e.toLowerCase()):[]}(e);return{hashtags:t.map(I),folderPaths:s.map(O),originalText:e}}function B(e,t){return[...new Set([...e,...t].filter(e=>e.trim().length>0))]}function G(e){let{hashtags:t,onHashtagsChange:s,suggestions:n=[],placeholder:i="Etiket ekleyin... (\xf6rn: #frontend, #api)",className:c,maxTags:m=10,disabled:u=!1}=e,[h,x]=(0,r.useState)(""),[p,g]=(0,r.useState)(!1),[j,v]=(0,r.useState)(-1),y=(0,r.useRef)(null),w=(0,r.useRef)(null),N=(function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,a=I(e);return a?t.filter(e=>e.toLowerCase().includes(a)).slice(0,s):[]})(h,n,5).filter(e=>!t.includes(e)),k=e=>{let a=I(e);if(!a||!function(e){let t=I(e);return/^[\w\u00C0-\u017F]+$/.test(t)&&t.length>0}(a))return;let r=function(e){let t=I(e);return t?"#".concat(t):""}(a);t.includes(r)||t.length>=m||(s([...t,r]),x(""),g(!1),v(-1))},_=e=>{s(t.filter((t,s)=>s!==e))};return(0,r.useEffect)(()=>{let e=e=>{var t;!w.current||w.current.contains(e.target)||(null==(t=y.current)?void 0:t.contains(e.target))||g(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,a.jsxs)("div",{className:(0,Q.cn)("space-y-2",c),children:[t.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:t.map((e,t)=>(0,a.jsxs)(d.E,{variant:"secondary",className:"flex items-center gap-1 text-xs bg-blue-100 text-blue-800 hover:bg-blue-200",children:[(0,a.jsx)(b.A,{className:"w-3 h-3"}),e.replace("#",""),!u&&(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 w-3 h-3 hover:bg-transparent",onClick:()=>_(t),children:(0,a.jsx)(f.A,{className:"w-2 h-2"})})]},t))}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(b.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,a.jsx)(o.p,{ref:y,type:"text",value:h,onChange:e=>{let t=e.target.value;x(t),g(t.length>0),v(-1)},onKeyDown:e=>{"Enter"===e.key?(e.preventDefault(),j>=0&&N[j]?k(N[j]):h.trim()&&k(h.trim())):"ArrowDown"===e.key?(e.preventDefault(),v(e=>e<N.length-1?e+1:e)):"ArrowUp"===e.key?(e.preventDefault(),v(e=>e>0?e-1:-1)):"Escape"===e.key?(g(!1),v(-1)):"Backspace"===e.key&&!h&&t.length>0&&_(t.length-1)},onFocus:()=>g(h.length>0),placeholder:t.length>=m?"Maksimum ".concat(m," etiket"):i,disabled:u||t.length>=m,className:"pl-10"})]}),h.trim()&&(0,a.jsx)(l.$,{type:"button",size:"sm",onClick:()=>k(h.trim()),disabled:u||t.length>=m,className:"shrink-0",children:(0,a.jsx)(C.A,{className:"w-4 h-4"})})]}),p&&N.length>0&&(0,a.jsx)("div",{ref:w,className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto",children:N.map((e,t)=>(0,a.jsxs)("button",{type:"button",className:(0,Q.cn)("w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2",j===t&&"bg-blue-50"),onClick:()=>{var t;k(e),null==(t=y.current)||t.focus()},children:[(0,a.jsx)(b.A,{className:"w-3 h-3 text-gray-400"}),e.replace("#","")]},e))})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[t.length,"/",m," etiket • Eklemek i\xe7in Enter'a basın • # \xf6neki kullanın"]})]})}var W=s(13052),Z=s(57340),J=s(14395);function V(e){let{category:t,onCategoryChange:s,suggestions:n=[],placeholder:i="Klas\xf6r se\xe7in... (\xf6rn: /frontend, /admin/users)",className:c,disabled:d=!1}=e,[m,u]=(0,r.useState)(""),[h,x]=(0,r.useState)(!1),[p,g]=(0,r.useState)(-1),[f,j]=(0,r.useState)(!1),v=(0,r.useRef)(null),b=(0,r.useRef)(null),w=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,a=e.toLowerCase();return a?t.filter(e=>e.toLowerCase().includes(a)).slice(0,s):[]}(m,n,8),N=t?function(e){if(!e||"/"===e)return["/"];let t=e.split("/").filter(e=>e.length>0),s=["/"],a="";return t.forEach(e=>{a+="/"+e,s.push(a)}),s}(t):[],k=e=>{let t=O(e);t&&function(e){let t=O(e);return/^\/[\w\u00C0-\u017F\/]*$/.test(t)}(t)&&(s("/"===t?null:t),u(""),x(!1),g(-1),j(!1))},_=()=>{s(null),j(!1)};return(0,r.useEffect)(()=>{let e=e=>{var t;!b.current||b.current.contains(e.target)||(null==(t=v.current)?void 0:t.contains(e.target))||x(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,a.jsxs)("div",{className:(0,Q.cn)("space-y-2",c),children:[t&&!f&&(0,a.jsxs)("div",{className:"flex items-center gap-1 p-2 bg-gray-50 rounded-md border",children:[(0,a.jsx)("div",{className:"flex items-center gap-1 flex-1 min-w-0",children:N.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[t>0&&(0,a.jsx)(W.A,{className:"w-3 h-3 text-gray-400"}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1 text-xs hover:bg-gray-200",onClick:()=>{"/"===e?_():s(e)},children:0===t?(0,a.jsx)(Z.A,{className:"w-3 h-3"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"ml-1",children:function(e){if(!e||"/"===e)return"Root";let t=e.lastIndexOf("/");return e.substring(t+1)||"Root"}(e)})]})})]},e))}),!d&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1",onClick:()=>{j(!0),u(t||""),setTimeout(()=>{var e;return null==(e=v.current)?void 0:e.focus()},0)},children:"Edit"}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1",onClick:_,children:"Clear"})]})]}),(!t||f)&&(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,a.jsx)(o.p,{ref:v,type:"text",value:m,onChange:e=>{let t=e.target.value;u(t),x(t.length>0),g(-1)},onKeyDown:e=>{"Enter"===e.key?(e.preventDefault(),p>=0&&w[p]?k(w[p]):m.trim()&&k(m.trim())):"ArrowDown"===e.key?(e.preventDefault(),g(e=>e<w.length-1?e+1:e)):"ArrowUp"===e.key?(e.preventDefault(),g(e=>e>0?e-1:-1)):"Escape"===e.key&&(x(!1),g(-1),j(!1),u(""))},onFocus:()=>x(m.length>0),placeholder:i,disabled:d,className:"pl-10"})]}),m.trim()&&(0,a.jsx)(l.$,{type:"button",size:"sm",onClick:()=>k(m.trim()),disabled:d,className:"shrink-0",children:(0,a.jsx)(C.A,{className:"w-4 h-4"})})]}),h&&w.length>0&&(0,a.jsx)("div",{ref:b,className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-40 overflow-y-auto",children:w.map((e,t)=>(0,a.jsxs)("button",{type:"button",className:(0,Q.cn)("w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2",p===t&&"bg-blue-50"),onClick:()=>{var t;k(e),null==(t=v.current)||t.focus()},children:[(0,a.jsx)(J.A,{className:"w-3 h-3 text-gray-400"}),e]},e))})]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Klas\xf6rler i\xe7in eğik \xe7izgi kullanın (\xf6rn: /frontend/components)"})]})}var X=s(22346),Y=s(33109),ee=s(24944),et=s(78749),es=s(92657),ea=s(72713),er=s(16785);function el(e){let{projectId:t,className:s}=e,[i,o]=(0,r.useState)(!1),{data:c=[]}=(0,P.F$)(t),{data:m=[]}=T(t,10),{data:u=[]}=q(t,5),h=c.length,x=c.filter(e=>e.category||e.tags&&e.tags.length>0).length,p=h-x,g=h>0?x/h*100:0,f=c.filter(e=>e.tags&&e.tags.length>0).length,j=c.filter(e=>e.category).length;return t?(0,a.jsxs)("div",{className:s,children:[(0,a.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>o(!i),className:"mb-4",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(et.A,{className:"w-4 h-4 mr-2"}),"Analitikleri Gizle"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(es.A,{className:"w-4 h-4 mr-2"}),"Analitikleri G\xf6ster"]})}),i&&(0,a.jsxs)("div",{className:"space-y-3 w-full overflow-hidden",children:[(0,a.jsxs)(n.Zp,{className:"w-full bg-white shadow-sm border border-gray-200",children:[(0,a.jsx)(n.aR,{className:"pb-2 px-3 pt-3",children:(0,a.jsxs)(n.ZB,{className:"text-xs font-medium flex items-center gap-1.5",children:[(0,a.jsx)(ea.A,{className:"w-3 h-3 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:"Genel Bakış"})]})}),(0,a.jsxs)(n.Wu,{className:"space-y-3 px-3 pb-3",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-xs text-gray-600 truncate",children:"Kategorizasyon"}),(0,a.jsxs)("span",{className:"text-xs font-medium shrink-0",children:[g.toFixed(0),"%"]})]}),(0,a.jsx)(ee.k,{value:g,className:"h-1.5 w-full"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 leading-tight",children:[x,"/",h," kategorili"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{className:"text-center p-2 bg-blue-50 rounded",children:[(0,a.jsx)("div",{className:"text-sm font-semibold text-blue-700",children:f}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"Etiket"})]}),(0,a.jsxs)("div",{className:"text-center p-2 bg-green-50 rounded",children:[(0,a.jsx)("div",{className:"text-sm font-semibold text-green-700",children:j}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Klas\xf6r"})]})]}),p>0&&(0,a.jsx)("div",{className:"p-2 bg-orange-50 border border-orange-200 rounded",children:(0,a.jsxs)("div",{className:"flex items-start gap-1.5 text-orange-700",children:[(0,a.jsx)(er.A,{className:"w-3 h-3 shrink-0 mt-0.5"}),(0,a.jsxs)("div",{className:"min-w-0",children:[(0,a.jsxs)("div",{className:"text-xs font-medium leading-tight",children:[p," kategorisiz"]}),(0,a.jsx)("div",{className:"text-xs text-orange-600 leading-tight",children:"Etiket/klas\xf6r ekleyin"})]})]})})]})]}),m.length>0&&(0,a.jsxs)(n.Zp,{className:"w-full bg-white shadow-sm border border-gray-200",children:[(0,a.jsx)(n.aR,{className:"pb-2 px-3 pt-3",children:(0,a.jsxs)(n.ZB,{className:"text-xs font-medium flex items-center gap-1.5",children:[(0,a.jsx)(b.A,{className:"w-3 h-3 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:"Pop\xfcler Etiketler"})]})}),(0,a.jsx)(n.Wu,{className:"px-3 pb-3",children:(0,a.jsx)("div",{className:"space-y-1.5",children:m.slice(0,3).map((e,t)=>{let{hashtag:s,count:r}=e,l=h>0?r/h*100:0;return(0,a.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1.5 min-w-0 flex-1",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500 w-3 shrink-0",children:["#",t+1]}),(0,a.jsx)(d.E,{variant:"secondary",className:"text-xs px-1.5 py-0.5 truncate max-w-[80px]",children:s.replace("#","")})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1.5 shrink-0",children:[(0,a.jsx)("div",{className:"w-8 h-1 bg-gray-200 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:"h-full bg-blue-500 rounded-full",style:{width:"".concat(Math.max(l,10),"%")}})}),(0,a.jsx)("span",{className:"text-xs text-gray-600 w-4 text-right",children:r})]})]},s)})})})]}),u.length>0&&(0,a.jsxs)(n.Zp,{className:"w-full bg-white shadow-sm border border-gray-200",children:[(0,a.jsx)(n.aR,{className:"pb-2 px-3 pt-3",children:(0,a.jsxs)(n.ZB,{className:"text-xs font-medium flex items-center gap-1.5",children:[(0,a.jsx)(y.A,{className:"w-3 h-3 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:"Pop\xfcler Klas\xf6rler"})]})}),(0,a.jsx)(n.Wu,{className:"px-3 pb-3",children:(0,a.jsx)("div",{className:"space-y-1.5",children:u.slice(0,3).map((e,t)=>{let{category:s,count:r}=e,l=h>0?r/h*100:0;return(0,a.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1.5 min-w-0 flex-1",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500 w-3 shrink-0",children:["#",t+1]}),(0,a.jsxs)(d.E,{variant:"outline",className:"text-xs px-1.5 py-0.5 flex items-center gap-1 truncate max-w-[80px]",children:[(0,a.jsx)(y.A,{className:"w-2.5 h-2.5 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:s})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1.5 shrink-0",children:[(0,a.jsx)("div",{className:"w-8 h-1 bg-gray-200 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:"h-full bg-green-500 rounded-full",style:{width:"".concat(Math.max(l,10),"%")}})}),(0,a.jsx)("span",{className:"text-xs text-gray-600 w-4 text-right",children:r})]})]},s)})})})]}),h>0&&(0,a.jsxs)(n.Zp,{className:"w-full bg-white shadow-sm border border-gray-200",children:[(0,a.jsx)(n.aR,{className:"pb-2 px-3 pt-3",children:(0,a.jsxs)(n.ZB,{className:"text-xs font-medium flex items-center gap-1.5",children:[(0,a.jsx)(Y.A,{className:"w-3 h-3 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:"\xd6neriler"})]})}),(0,a.jsx)(n.Wu,{className:"px-3 pb-3",children:(0,a.jsxs)("div",{className:"space-y-1.5 text-xs text-gray-600",children:[g<50&&(0,a.jsxs)("div",{className:"flex items-start gap-1.5",children:[(0,a.jsx)("span",{className:"text-orange-500 shrink-0",children:"•"}),(0,a.jsx)("span",{className:"leading-tight",children:"Etiket ekleyin"})]}),0===j&&(0,a.jsxs)("div",{className:"flex items-start gap-1.5",children:[(0,a.jsx)("span",{className:"text-blue-500 shrink-0",children:"•"}),(0,a.jsx)("span",{className:"leading-tight",children:"Klas\xf6r kullanın"})]}),m.length>10&&(0,a.jsxs)("div",{className:"flex items-start gap-1.5",children:[(0,a.jsx)("span",{className:"text-green-500 shrink-0",children:"•"}),(0,a.jsx)("span",{className:"leading-tight",children:"İyi etiket kullanımı!"})]}),h>20&&g>80&&(0,a.jsxs)("div",{className:"flex items-start gap-1.5",children:[(0,a.jsx)("span",{className:"text-green-500 shrink-0",children:"•"}),(0,a.jsx)("span",{className:"leading-tight",children:"M\xfckemmel organizasyon!"})]})]})})]})]})]}):null}function en(e){let{projectId:t,onHashtagClick:s,onCategoryClick:n,selectedHashtags:i=[],selectedCategory:m=null,className:u}=e,[h,x]=(0,r.useState)(""),[p,g]=(0,r.useState)(!0),[j,v]=(0,r.useState)(!0),{data:w=[],isLoading:N}=T(t),{data:k=[],isLoading:A}=q(t),E=w.filter(e=>{let{hashtag:t}=e;return t.toLowerCase().includes(h.toLowerCase())}),S=k.filter(e=>{let{category:t}=e;return t.toLowerCase().includes(h.toLowerCase())});return(0,a.jsxs)("div",{className:(0,Q.cn)("w-64 bg-white border-l border-gray-200 flex flex-col relative z-50",u),children:[(0,a.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,a.jsx)(Y.A,{className:"w-4 h-4 text-blue-600"}),(0,a.jsx)("h3",{className:"font-medium text-sm",children:"AI Tags"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,a.jsx)(o.p,{type:"text",placeholder:"Etiket ve klas\xf6r ara...",value:h,onChange:e=>x(e.target.value),className:"pl-10 text-sm"}),h&&(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>{x("")},children:(0,a.jsx)(f.A,{className:"w-3 h-3"})})]})]}),(0,a.jsx)(c.F,{className:"flex-1 overflow-hidden",children:(0,a.jsxs)("div",{className:"p-4 space-y-4 max-w-full",children:[p&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"w-3 h-3 text-gray-500"}),(0,a.jsx)("span",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Klas\xf6rler"})]}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 text-xs",onClick:()=>g(!p),children:p?"Gizle":"G\xf6ster"})]}),A?(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Klas\xf6rler y\xfckleniyor..."}):S.length>0?(0,a.jsx)("div",{className:"space-y-1",children:S.map(e=>{let{category:t,count:s}=e;return(0,a.jsxs)(l.$,{type:"button",variant:"ghost",size:"sm",className:(0,Q.cn)("w-full justify-between h-auto p-2 text-xs hover:bg-gray-100",m===t&&"bg-blue-50 text-blue-700"),onClick:()=>{null==n||n(t)},children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 min-w-0",children:[(0,a.jsx)(y.A,{className:"w-3 h-3 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:t})]}),(0,a.jsx)(d.E,{variant:"secondary",className:"text-xs",children:s})]},t)})}):(0,a.jsx)("div",{className:"text-xs text-gray-500",children:h?"Eşleşen klas\xf6r bulunamadı":"Hen\xfcz klas\xf6r yok"})]}),p&&j&&(0,a.jsx)(X.w,{}),j&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"w-3 h-3 text-gray-500"}),(0,a.jsx)("span",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Pop\xfcler Etiketler"})]}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 text-xs",onClick:()=>v(!j),children:j?"Gizle":"G\xf6ster"})]}),N?(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Etiketler y\xfckleniyor..."}):E.length>0?(0,a.jsx)("div",{className:"space-y-1",children:E.map(e=>{let{hashtag:t,count:r}=e;return(0,a.jsxs)(l.$,{type:"button",variant:"ghost",size:"sm",className:(0,Q.cn)("w-full justify-between h-auto p-2 text-xs hover:bg-gray-100",i.includes(t)&&"bg-blue-50 text-blue-700"),onClick:()=>{null==s||s(t)},children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 min-w-0",children:[(0,a.jsx)(b.A,{className:"w-3 h-3 shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:t.replace("#","")})]}),(0,a.jsx)(d.E,{variant:"secondary",className:"text-xs",children:r})]},t)})}):(0,a.jsx)("div",{className:"text-xs text-gray-500",children:h?"Eşleşen etiket bulunamadı":"Hen\xfcz etiket yok"})]}),(0,a.jsx)(X.w,{}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Hızlı İşlemler"}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)(l.$,{type:"button",variant:"outline",size:"sm",className:"w-full justify-start text-xs",onClick:()=>null==s?void 0:s("#new"),children:[(0,a.jsx)(C.A,{className:"w-3 h-3 mr-2"}),"#yeni etiketi ekle"]}),(0,a.jsxs)(l.$,{type:"button",variant:"outline",size:"sm",className:"w-full justify-start text-xs",onClick:()=>null==n?void 0:n("/new"),children:[(0,a.jsx)(y.A,{className:"w-3 h-3 mr-2"}),"/yeni klas\xf6r\xfc oluştur"]})]})]}),t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(X.w,{}),(0,a.jsx)("div",{className:"w-full overflow-hidden",children:(0,a.jsx)(el,{projectId:t,className:"w-full max-w-none"})})]})]})}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,a.jsx)("div",{className:"text-xs text-gray-500 text-center",children:"Filtrelemek i\xe7in tıklayın • Sayılar kullanım sayısını g\xf6sterir"})})]})}let ei=function(e){let{value:t,onChange:s,onKeyDown:l,placeholder:n,className:i,suggestions:o={hashtags:[],folders:[]},disabled:c=!1,enableDynamicHeight:d=!0,heightConfig:m={}}=e,[u,h]=(0,r.useState)(!1),[x,p]=(0,r.useState)([]),[g,f]=(0,r.useState)(-1),[j,v]=(0,r.useState)(null),[w,N]=(0,r.useState)(-1),[,k]=(0,r.useState)(""),_=(0,r.useRef)(null),C=(0,r.useRef)(null),{heightStyle:A}=function(e,t,s){let{heightStyle:a,maxHeight:l,minHeight:n}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{minHeight:t=44,maxHeightFraction:s=.33,baseHeight:a=56,enableTransitions:l=!0}=e,[n,i]=(0,r.useState)(0),[o,c]=(0,r.useState)(200),d=(0,r.useCallback)(()=>{let e=window.innerHeight,a=Math.max(Math.floor(e*s),t+50);i(e),c(a)},[s,t]);(0,r.useEffect)(()=>{let e,t=()=>{clearTimeout(e),e=setTimeout(d,150)};return d(),window.addEventListener("resize",t),window.addEventListener("orientationchange",t),()=>{clearTimeout(e),window.removeEventListener("resize",t),window.removeEventListener("orientationchange",t)}},[d]),(0,r.useEffect)(()=>{let e=()=>{if(window.visualViewport){let e=window.visualViewport.height,a=Math.max(Math.floor(e*s),t+50);i(e),c(a)}};if(window.visualViewport)return window.visualViewport.addEventListener("resize",e),()=>{var t;null==(t=window.visualViewport)||t.removeEventListener("resize",e)}},[s,t]);let m={minHeight:"".concat(t,"px"),maxHeight:"".concat(o,"px"),height:"auto",resize:"none",...l&&{transition:"max-height 0.2s ease-out, min-height 0.2s ease-out"}};return{maxHeight:o,minHeight:t,heightStyle:m,recalculateHeight:d,viewportHeight:n}}(s),[i,o]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let t=e.current;if(!t)return;let s=t.offsetHeight;t.style.height="auto";let a=Math.min(Math.max(t.scrollHeight,n),l);Math.abs(a-s)>10&&(o(!0),t.classList.add("expanding"),setTimeout(()=>{t.classList.remove("expanding"),o(!1)},200)),t.style.height="".concat(a,"px")},[t,l,n,e]),{heightStyle:{...a,overflow:"hidden"},maxHeight:l,minHeight:n,isExpanding:i}}(_,t,d?m:void 0),E=(0,r.useCallback)((e,t)=>{if(0===t)return null;for(let s=t-1;s>=0;s--){let a=e[s];if("#"===a||"/"===a){let r=s>0?e[s-1]:" ";if(" "===r||"\n"===r||0===s){let r=e.slice(s+1,t);if(!r.includes(" ")&&!r.includes("\n"))return{char:a,position:s,searchTerm:r}}}if(" "===a||"\n"===a)break}return null},[]),S=(0,r.useCallback)((e,t)=>{let s="hashtag"===t?(null==o?void 0:o.hashtags)||[]:(null==o?void 0:o.folders)||[];return s&&0!==s.length?s.filter(t=>!!t&&"string"==typeof t&&t.replace(/^[#/]/,"").toLowerCase().includes(e.toLowerCase())).slice(0,8).map((e,s)=>({id:"".concat(t,"-").concat(s),value:e,type:t,usage_count:Math.floor(100*Math.random())})).sort((e,t)=>(t.usage_count||0)-(e.usage_count||0)):[]},[o]),z=(0,r.useCallback)(e=>{var a;if(-1===w)return;let r=t.slice(0,w),l=t.slice((null==(a=_.current)?void 0:a.selectionStart)||0),n="hashtag"===e.type?"#":"/",i=e.value.startsWith(n)?e.value:"".concat(n).concat(e.value);s(r+i+" "+l),setTimeout(()=>{if(_.current){let e=r.length+i.length+1;_.current.setSelectionRange(e,e),_.current.focus()}},0),h(!1),f(-1)},[t,w,s]),D=(0,r.useCallback)(e=>{try{if(s(e),!_.current)return;let t=_.current.selectionStart||0,a=E(e,t);if(a){v(a.char),N(a.position),k(a.searchTerm);let e="#"===a.char?"hashtag":"folder",t=S(a.searchTerm,e);p(t||[]),h(t&&t.length>0),f(-1)}else h(!1),v(null),N(-1),k(""),p([]),f(-1)}catch(e){console.error("Error in handleTextChange:",e),h(!1),v(null),N(-1),k(""),p([]),f(-1)}},[s,E,S]),L=(0,r.useCallback)(e=>{if(u&&x.length>0)switch(e.key){case"ArrowDown":e.preventDefault(),f(e=>e<x.length-1?e+1:0);break;case"ArrowUp":e.preventDefault(),f(e=>e>0?e-1:x.length-1);break;case"Enter":case"Tab":g>=0&&(e.preventDefault(),z(x[g]));break;case"Escape":e.preventDefault(),h(!1),f(-1)}null==l||l(e)},[u,x,g,l,z]);return(0,r.useEffect)(()=>{let e=e=>{C.current&&!C.current.contains(e.target)&&h(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("textarea",{ref:_,value:t||"",onChange:e=>D(e.target.value),onKeyDown:L,placeholder:n,className:"".concat(i," ").concat(d?"dynamic-textarea":""),disabled:c,style:d?A:{height:"auto"}}),u&&x&&x.length>0&&(0,a.jsxs)("div",{ref:C,className:"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"p-2 text-xs text-gray-500 border-b border-gray-100",children:["#"===j?"Hashtags":"Folders"," - Use ↑↓ to navigate, Enter to select"]}),x.map((e,t)=>e&&e.id&&e.value?(0,a.jsxs)("div",{className:"flex items-center gap-2 px-3 py-2 cursor-pointer transition-colors ".concat(t===g?"bg-blue-50 text-blue-700":"hover:bg-gray-50"),onClick:()=>z(e),children:["hashtag"===e.type?(0,a.jsx)(b.A,{className:"h-4 w-4 text-blue-500"}):(0,a.jsx)(y.A,{className:"h-4 w-4 text-orange-500"}),(0,a.jsx)("span",{className:"flex-1 text-sm",children:e.value}),e.usage_count&&(0,a.jsxs)("span",{className:"text-xs text-gray-400",children:[e.usage_count," uses"]})]},e.id):null)]})]})};function eo(){return(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 animate-pulse",children:[1,2,3,4,5,6].map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mb-3"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-5/6"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-4/6"})]}),(0,a.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,a.jsx)("div",{className:"h-5 bg-blue-100 rounded-full w-16"}),(0,a.jsx)("div",{className:"h-5 bg-purple-100 rounded-full w-20"})]})]},e))})}var ec=s(66516),ed=s(74575),em=s(34869),eu=s(32919),eh=s(54165),ex=s(85057),ep=s(4884);let eg=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(ep.bL,{className:(0,Q.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...r,ref:t,children:(0,a.jsx)(ep.zi,{className:(0,Q.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});eg.displayName=ep.bL.displayName;var ef=s(7801);function ej(e){let{promptId:t,promptTitle:s,promptText:n,taskCode:c,className:d,variant:m="ghost",size:u="sm"}=e,[h,x]=(0,r.useState)(!1),[p,g]=(0,r.useState)(s||c||""),[f,j]=(0,r.useState)(""),[y,b]=(0,r.useState)(!0),[w,N]=(0,r.useState)(""),[k,_]=(0,r.useState)(""),[C,A]=(0,r.useState)(!1),E=(0,ef.Xm)(),{data:S}=(0,ef.X1)(),z=null==S?void 0:S.find(e=>e.prompt_id===t),D=async()=>{try{await E.mutateAsync({prompt_id:t,title:p.trim()||void 0,description:f.trim()||void 0,is_public:y,password:w.trim()||void 0,expires_at:k||void 0}),x(!1),L()}catch(e){console.error("Share error:",e)}},L=()=>{g(s||c||""),j(""),b(!0),N(""),_(""),A(!1)},P=async()=>{if(z){let e="".concat(window.location.origin,"/share/").concat(z.share_token);try{await navigator.clipboard.writeText(e),H.oR.success("Link panoya kopyalandı!")}catch(e){H.oR.error("Link kopyalanamadı")}}},K=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return e.length<=t?e:e.substring(0,t)+"..."};return(0,a.jsxs)(eh.lG,{open:h,onOpenChange:x,children:[(0,a.jsx)(eh.zM,{asChild:!0,children:(0,a.jsxs)(l.$,{variant:m,size:u,className:d,onClick:()=>x(!0),children:[(0,a.jsx)(ec.A,{className:"h-4 w-4"}),"icon"!==u&&(0,a.jsx)("span",{className:"ml-1",children:z?"Paylaşıldı":"Paylaş"})]})}),(0,a.jsxs)(eh.Cf,{className:"sm:max-w-md",children:[(0,a.jsxs)(eh.c7,{children:[(0,a.jsxs)(eh.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(ec.A,{className:"h-5 w-5"}),"Prompt Paylaş"]}),(0,a.jsx)(eh.rr,{children:"Bu prompt'u başkalarıyla paylaşmak i\xe7in bir link oluşturun"})]}),z?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(ed.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Bu prompt zaten paylaşılmış"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.p,{value:"".concat(window.location.origin,"/share/").concat(z.share_token),readOnly:!0,className:"text-xs bg-white"}),(0,a.jsx)(l.$,{size:"sm",onClick:P,className:"shrink-0",children:(0,a.jsx)(v.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"mt-2 flex items-center gap-4 text-xs text-green-700",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(es.A,{className:"h-3 w-3"}),z.view_count," g\xf6r\xfcnt\xfclenme"]}),(0,a.jsx)("span",{children:new Date(z.created_at).toLocaleDateString("tr-TR")})]})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Prompt \xd6nizleme"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 font-mono",children:K(n)})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Prompt \xd6nizleme"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 font-mono",children:K(n)})]}),(0,a.jsx)(X.w,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(ex.J,{htmlFor:"share-title",children:"Başlık (Opsiyonel)"}),(0,a.jsx)(o.p,{id:"share-title",value:p,onChange:e=>g(e.target.value),placeholder:"Paylaşım i\xe7in \xf6zel başlık",className:"mt-1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ex.J,{htmlFor:"share-description",children:"A\xe7ıklama (Opsiyonel)"}),(0,a.jsx)(i.T,{id:"share-description",value:f,onChange:e=>j(e.target.value),placeholder:"Bu prompt hakkında kısa a\xe7ıklama",className:"mt-1 resize-none",rows:2})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[y?(0,a.jsx)(em.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(eu.A,{className:"h-4 w-4 text-orange-600"}),(0,a.jsx)(ex.J,{htmlFor:"is-public",children:"Herkese A\xe7ık"})]}),(0,a.jsx)(eg,{id:"is-public",checked:y,onCheckedChange:b})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ex.J,{htmlFor:"password",children:"Şifre Koruması (Opsiyonel)"}),(0,a.jsxs)("div",{className:"flex gap-2 mt-1",children:[(0,a.jsx)(o.p,{id:"password",type:C?"text":"password",value:w,onChange:e=>N(e.target.value),placeholder:"Şifre belirleyin"}),(0,a.jsx)(l.$,{type:"button",variant:"outline",size:"icon",onClick:()=>A(!C),children:C?(0,a.jsx)(et.A,{className:"h-4 w-4"}):(0,a.jsx)(es.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ex.J,{htmlFor:"expires-at",children:"Son Kullanma Tarihi (Opsiyonel)"}),(0,a.jsx)(o.p,{id:"expires-at",type:"datetime-local",value:k,onChange:e=>_(e.target.value),className:"mt-1",min:new Date().toISOString().slice(0,16)})]})]}),(0,a.jsx)(X.w,{}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(l.$,{onClick:D,disabled:E.isPending,className:"flex-1",children:E.isPending?"Oluşturuluyor...":(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ec.A,{className:"h-4 w-4 mr-2"}),"Paylaşım Linki Oluştur"]})}),(0,a.jsx)(l.$,{variant:"outline",onClick:()=>x(!1),children:"İptal"})]})]})]})]})}var ev=s(75143),ey=s(50402),eb=s(78266);let ew=(0,r.lazy)(()=>Promise.all([s.e(2123),s.e(8611),s.e(8779),s.e(8650),s.e(8523),s.e(8405),s.e(9420),s.e(4939),s.e(1486),s.e(2662),s.e(3240),s.e(8669),s.e(3189),s.e(4696),s.e(1277),s.e(7979),s.e(1899),s.e(7098),s.e(4450),s.e(9744),s.e(4495),s.e(9592),s.e(433),s.e(2652),s.e(5030),s.e(465),s.e(2903),s.e(2663),s.e(9173),s.e(408),s.e(558),s.e(1356),s.e(6475),s.e(2130),s.e(4207),s.e(4191),s.e(6489),s.e(5230),s.e(7339),s.e(957),s.e(5677),s.e(6691),s.e(1151),s.e(7114),s.e(5803),s.e(3976),s.e(3492),s.e(2608),s.e(5644),s.e(2789),s.e(9824),s.e(4075),s.e(9473),s.e(7530),s.e(6759),s.e(0),s.e(6325),s.e(6077),s.e(4409),s.e(9184),s.e(7650),s.e(1595),s.e(4532),s.e(1911),s.e(5565),s.e(1038),s.e(6772),s.e(6285)]).then(s.bind(s,96285)).then(e=>({default:e.EnhancedContextGalleryModal}))),eN=(0,r.memo)(function(e){let{prompt:t,isMultiSelectMode:s,selectedPrompts:r,editingPromptId:o,editingText:c,editTextareaRef:w,onSelectPrompt:N,onEditPrompt:k,onSaveEdit:_,onCancelEdit:C,onCopyPrompt:A,setEditingText:E,onHashtagFilter:S}=e,{attributes:z,listeners:D,setNodeRef:L,transform:P,transition:K,isDragging:$}=(0,ey.gl)({id:t.id}),F={transform:eb.Ks.Transform.toString(P),transition:K,opacity:$?.5:1};return(0,a.jsxs)(n.Zp,{ref:L,style:F,className:"transition-all duration-200 ".concat(t.is_used?"bg-gray-50 border-gray-300":"bg-white border-gray-200 hover:border-blue-300 hover:shadow-md"," ").concat(r.has(t.id)?"ring-2 ring-blue-500 border-blue-500":""),onClick:()=>s?N(t.id):void 0,children:[(0,a.jsxs)(n.aR,{className:"pb-3",children:[(0,a.jsxs)("div",{className:"block lg:hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[!s&&(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 cursor-grab active:cursor-grabbing",...z,...D,children:(0,a.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})}),s&&(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 cursor-pointer",onClick:e=>{e.stopPropagation(),N(t.id)},children:r.has(t.id)?(0,a.jsx)(u.A,{className:"h-6 w-6 text-blue-600"}):(0,a.jsx)(h.A,{className:"h-6 w-6 text-gray-400"})}),(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-full text-base font-medium ".concat(t.is_used?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"),children:t.order_index}),(0,a.jsx)("div",{className:"flex flex-col",children:(0,a.jsx)("span",{className:"text-sm font-mono px-2 py-1 rounded ".concat(t.is_used?"bg-gray-100 text-gray-500":"bg-blue-50 text-blue-600"),children:t.task_code||"task-".concat(t.order_index)})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[t.is_used?(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(p.A,{className:"h-5 w-5 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm font-medium ".concat(t.is_used?"text-gray-500":"text-gray-900"),children:t.is_used?"Kullanıldı":"Bekliyor"})]})]}),!s&&(0,a.jsx)("div",{className:"flex items-center gap-2 justify-end",children:o===t.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.$,{variant:"default",size:"sm",onClick:_,className:"bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700 min-h-[44px] px-4",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Kaydet"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:C,className:"text-red-600 hover:text-red-700 min-h-[44px] px-4",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"İptal"]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>k(t),className:"text-gray-600 hover:text-gray-700 min-h-[44px] px-4",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,a.jsx)(ej,{promptId:t.id,promptTitle:t.title||void 0,promptText:t.prompt_text,taskCode:t.task_code||void 0,variant:"outline",size:"sm",className:"min-h-[44px] px-4"}),(0,a.jsxs)(l.$,{variant:t.is_used?"secondary":"default",size:"sm",onClick:()=>A(t),className:"min-h-[44px] px-4 ".concat(t.is_used?"opacity-60":""),children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),t.is_used?"Kopyalandı":"Kopyala"]})]})})]}),(0,a.jsxs)("div",{className:"hidden lg:flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[!s&&(0,a.jsx)("div",{className:"flex items-center justify-center w-6 h-6 cursor-grab active:cursor-grabbing",...z,...D,children:(0,a.jsx)(m.A,{className:"h-4 w-4 text-gray-400"})}),s&&(0,a.jsx)("div",{className:"flex items-center justify-center w-6 h-6 cursor-pointer",onClick:e=>{e.stopPropagation(),N(t.id)},children:r.has(t.id)?(0,a.jsx)(u.A,{className:"h-5 w-5 text-blue-600"}):(0,a.jsx)(h.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ".concat(t.is_used?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"),children:t.order_index}),(0,a.jsx)("div",{className:"flex flex-col",children:(0,a.jsx)("span",{className:"text-xs font-mono px-2 py-1 rounded ".concat(t.is_used?"bg-gray-100 text-gray-500":"bg-blue-50 text-blue-600"),children:t.task_code||"task-".concat(t.order_index)})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[t.is_used?(0,a.jsx)(x.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(p.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm font-medium ".concat(t.is_used?"text-gray-500":"text-gray-900"),children:t.is_used?"Kullanıldı":"Bekliyor"})]})]}),!s&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:o===t.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.$,{variant:"default",size:"sm",onClick:_,className:"bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Kaydet"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:C,className:"text-red-600 hover:text-red-700",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"İptal"]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>k(t),className:"text-gray-600 hover:text-gray-700",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,a.jsx)(ej,{promptId:t.id,promptTitle:t.title||void 0,promptText:t.prompt_text,taskCode:t.task_code||void 0,variant:"outline",size:"sm"}),(0,a.jsxs)(l.$,{variant:t.is_used?"secondary":"default",size:"sm",onClick:()=>A(t),className:t.is_used?"opacity-60":"",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),t.is_used?"Kopyalandı":"Kopyala"]})]})})]})]}),(0,a.jsx)(n.Wu,{className:"pt-0",children:o===t.id?(0,a.jsx)(i.T,{ref:w,value:c,onChange:e=>E(e.target.value),className:"min-h-[60px] max-h-[200px] resize-none",style:{height:"auto"},onKeyDown:e=>{"Enter"===e.key&&e.ctrlKey?_():"Escape"===e.key&&C()},autoFocus:!0}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm leading-relaxed cursor-pointer whitespace-pre-wrap break-words ".concat(t.is_used?"text-gray-500":"text-gray-700"),onDoubleClick:()=>k(t),title:"D\xfczenlemek i\xe7in \xe7ift tıklayın",children:t.prompt_text}),(t.tags&&t.tags.length>0||t.category)&&(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-1 pt-2 sm:gap-1.5",children:[t.category&&(0,a.jsxs)(d.E,{variant:"outline",className:"text-xs sm:text-xs bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100 transition-colors cursor-pointer",onClick:e=>{e.preventDefault(),e.stopPropagation()},children:[(0,a.jsx)(y.A,{className:"w-3 h-3 mr-1 text-gray-500"}),t.category]}),t.tags&&Array.isArray(t.tags)&&t.tags.map((e,t)=>{let s="string"==typeof e?e:String(e);return s&&""!==s.trim()?(0,a.jsxs)(d.E,{variant:"secondary",className:"text-xs sm:text-xs bg-blue-50 text-blue-700 border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors",onClick:e=>{e.preventDefault(),e.stopPropagation(),S(s)},children:[(0,a.jsx)(b.A,{className:"w-3 h-3 mr-1 text-blue-500"}),s.replace("#","")]},t):null})]})]})})]})});function ek(){let{isContextGalleryOpen:e=!1,onToggleContextGallery:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[s,n]=(0,r.useState)(""),[i,m]=(0,r.useState)(null),[u,h]=(0,r.useState)(""),[x,g]=(0,r.useState)(new Set),[j,T]=(0,r.useState)(!1),[q,Q]=(0,r.useState)(""),[I,O]=(0,r.useState)([]),[W,Z]=(0,r.useState)(null),[J,X]=(0,r.useState)(""),[Y,ee]=(0,r.useState)(!1),[et,es]=(0,r.useState)(!0),[ea,er]=(0,r.useState)([]),[el,ec]=(0,r.useState)(null),ed=(0,r.useRef)(null),{activeProjectId:em,isContextEnabled:eu}=(0,L.C)(),{data:eh}=(0,R.p$)(),ex=(0,ev.FR)((0,ev.MS)(ev.AN),(0,ev.MS)(ev.uN,{coordinateGetter:ey.JR})),{data:ep=[]}=(0,P.F$)(em),{data:eg}=(0,K.By)(em),{data:ef=[]}=(0,$.I)({queryKey:["all-hashtags",em],queryFn:async()=>{if(!em)return[];let{data:e,error:t}=await F.L.from("prompts").select("tags").eq("project_id",em);if(t)throw Error(t.message);let s=new Set;return null==e||e.forEach(e=>{e.tags&&Array.isArray(e.tags)&&e.tags.forEach(e=>{if("string"==typeof e&&e.trim()){let t=e.startsWith("#")?e.toLowerCase():"#".concat(e.toLowerCase());s.add(t)}})}),Array.from(s).sort()},enabled:!!em}),{data:ej=[]}=(0,$.I)({queryKey:["all-categories",em],queryFn:async()=>{if(!em)return[];let{data:e,error:t}=await F.L.from("prompts").select("category").eq("project_id",em).not("category","is",null);if(t)throw Error(t.message);let s=new Set;return null==e||e.forEach(e=>{e.category&&s.add(e.category.toLowerCase())}),Array.from(s).sort()},enabled:!!em}),eb=(0,P.sW)(),ek=(0,P.$I)(),e_=(0,P.GQ)(),eC=(0,P.Qu)(),eA=ep.filter(e=>{try{let t=""===q.trim()||e.prompt_text.toLowerCase().includes(q.toLowerCase())||e.title&&e.title.toLowerCase().includes(q.toLowerCase()),s=!el||e.category===el,a=0===ea.length||e.tags&&Array.isArray(e.tags)&&ea.some(t=>e.tags.some(e=>("string"==typeof e?e:String(e)).toLowerCase()===t.toLowerCase()));return t&&s&&a}catch(t){return console.error("Error filtering prompt:",t,e),!0}});(0,r.useEffect)(()=>{var e;ed.current&&((e=ed.current).style.height="auto",e.style.height=Math.min(e.scrollHeight,200)+"px")},[u]);let eE=async()=>{if(s.trim()&&em){if(eh&&!eh.can_create_prompt)return void H.oR.error("Prompt oluşturma limitinize ulaştınız (".concat(eh.current_prompts,"/").concat(eh.max_prompts_per_project,"). Planınızı y\xfckseltin."));n(""),X(""),O([]),Z(null),ee(!1);try{let{hashtags:e,folderPaths:t,originalText:a}=U(s),r=B(I||[],e||[]),l=t&&t.length>0?t[0]:W,n=ep.length>0?Math.max(...ep.map(e=>e.order_index||0)):0;await eb.mutateAsync({project_id:em,prompt_text:a,title:(null==J?void 0:J.trim())||void 0,category:l||void 0,tags:r||[],order_index:n+1,is_used:!1}),H.oR.success("Prompt başarıyla eklendi!")}catch(e){console.error("Prompt ekleme hatası:",e),n(s),X(J),O(I),Z(W),ee(Y),H.oR.error("Prompt eklenirken bir hata oluştu. L\xfctfen tekrar deneyin.")}}},eS=(0,r.useCallback)(e=>{try{let t="string"==typeof e?e:String(e);if(!t||""===t.trim())return void console.warn("Invalid hashtag provided to handleHashtagFilter:",e);ea.includes(t)?er(ea.filter(e=>e!==t)):er([...ea,t])}catch(t){console.error("Error in handleHashtagFilter:",t,e)}},[ea]),ez=(0,r.useCallback)(e=>{ec(el===e?null:e)},[el]),eD=(0,r.useCallback)(async e=>{try{let t=eu&&(null==eg?void 0:eg.context_text)||"",s=e.task_code||"task-".concat(e.order_index),a="";a=t?"".concat(t,"\n\n").concat(s,"\n").concat(e.prompt_text):"".concat(s,"\n").concat(e.prompt_text),await navigator.clipboard.writeText(a),await eC.mutateAsync(e.id)}catch(e){console.error("Kopyalama hatası:",e)}},[eu,null==eg?void 0:eg.context_text,eC]),eL=async()=>{if(em)try{let e=ep.filter(e=>e.is_used).sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())[0];e&&await ek.mutateAsync({id:e.id,is_used:!1})}catch(e){console.error("Geri alma hatası:",e)}},eP=(0,r.useCallback)(e=>{m(e.id),h(e.prompt_text)},[]),eK=async()=>{if(i&&u.trim())try{let{hashtags:e,folderPaths:t,originalText:s}=U(u),a=ep.find(e=>e.id===i),r=(null==a?void 0:a.tags)||[],l=null==a?void 0:a.category,n=B(r,e||[]),o=t&&t.length>0?t[0]:l;await ek.mutateAsync({id:i,prompt_text:s,tags:n,category:o||void 0}),m(null),h("")}catch(e){console.error("Prompt g\xfcncelleme hatası:",e),alert("Prompt g\xfcncellenirken bir hata oluştu. L\xfctfen tekrar deneyin.")}},e$=(0,r.useCallback)(()=>{m(null),h("")},[]),eF=(0,r.useCallback)(()=>{T(!j),g(new Set)},[j]),eT=(0,r.useCallback)(e=>{let t=new Set(x);t.has(e)?t.delete(e):t.add(e),g(t)},[x]),eq=async()=>{if(0!==x.size)try{let e=ep.filter(e=>x.has(e.id)).sort((e,t)=>e.order_index-t.order_index),t=eu&&(null==eg?void 0:eg.context_text)||"",s=t?"".concat(t,"\n\n"):"";for(let t of(e.forEach((t,a)=>{let r=t.task_code||"task-".concat(t.order_index);s+="".concat(a+1,". ").concat(r,"\n").concat(t.prompt_text),a<e.length-1&&(s+="\n\n")}),await navigator.clipboard.writeText(s),x))await eC.mutateAsync(t);g(new Set),T(!1)}catch(e){console.error("\xc7oklu kopyalama hatası:",e)}},eR=()=>{let e=ep.filter(e=>!e.is_used).sort((e,t)=>e.order_index-t.order_index);if(0===e.length)return void alert("Kopyalanmamış prompt bulunamadı!");let t=eu&&(null==eg?void 0:eg.context_text)||"",s="# ".concat((null==eg?void 0:eg.name)||"Prompt Listesi","\n\n");t&&(s+="## Context\n\n".concat(t,"\n\n")),s+="## Kopyalanmamış Prompt'lar\n\n",e.forEach((e,t)=>{let a=e.task_code||"task-".concat(e.order_index);s+="### ".concat(t+1,". ").concat(a,"\n\n"),s+="".concat(e.prompt_text,"\n\n"),s+="---\n\n"});let a=new Blob([s],{type:"text/markdown"}),r=URL.createObjectURL(a),l=document.createElement("a");l.href=r,l.download="".concat((null==eg?void 0:eg.name)||"prompts","-unused.md"),document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(r)},eM=(0,r.useCallback)(async e=>{let{active:t,over:s}=e;if(!s||t.id===s.id)return;let a=ep.sort((e,t)=>e.is_used!==t.is_used?e.is_used?1:-1:e.order_index-t.order_index),r=a.findIndex(e=>e.id===t.id),l=a.findIndex(e=>e.id===s.id),n=(0,ey.be)(a,r,l),i=[];for(let e=0;e<n.length;e++){let t=n[e],s=e+1,a="task-".concat(s);(t.order_index!==s||t.task_code!==a)&&i.push({id:t.id,order_index:s,task_code:a})}if(i.length>0)try{await e_.mutateAsync(i)}catch(e){console.error("Sıralama g\xfcncelleme hatası:",e)}},[ep,e_]);return em?(0,a.jsxs)("div",{className:"flex h-full",children:[(0,a.jsxs)("div",{className:"flex flex-col flex-1",children:[(0,a.jsxs)("div",{className:"p-3 sm:p-4 lg:p-6 border-b border-gray-200 bg-white",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3 sm:mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3",children:[(0,a.jsxs)("div",{className:"flex md:hidden gap-1 sm:gap-2",children:[(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:"h-8 w-8 sm:h-10 sm:w-10 p-0 touch-manipulation",children:(0,a.jsx)(N.A,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:"h-8 w-8 sm:h-10 sm:w-10 p-0 touch-manipulation",children:(0,a.jsx)(k.A,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]}),(0,a.jsx)("h2",{className:"text-base sm:text-lg lg:text-xl font-semibold text-gray-900 truncate",children:(null==eg?void 0:eg.name)||"Prompt Listesi"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 overflow-x-auto",children:[(ea.length>0||el)&&(0,a.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[ea.slice(0,2).map(e=>(0,a.jsxs)(d.E,{variant:"outline",className:"text-xs whitespace-nowrap",children:[(0,a.jsx)(b.A,{className:"w-3 h-3 mr-1"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:e.replace("#","")}),(0,a.jsx)("span",{className:"sm:hidden",children:e.replace("#","").slice(0,3)}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 w-3 h-3 ml-1 hover:bg-transparent touch-manipulation",onClick:t=>{t.preventDefault(),t.stopPropagation(),eS(e)},children:(0,a.jsx)(f.A,{className:"w-2 h-2"})})]},e)),ea.length>2&&(0,a.jsxs)(d.E,{variant:"outline",className:"text-xs",children:["+",ea.length-2]}),el&&(0,a.jsxs)(d.E,{variant:"outline",className:"text-xs whitespace-nowrap",children:[(0,a.jsx)(y.A,{className:"w-3 h-3 mr-1"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:el}),(0,a.jsx)("span",{className:"sm:hidden",children:el.slice(0,3)}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 w-3 h-3 ml-1 hover:bg-transparent",onClick:()=>ec(null),children:(0,a.jsx)(f.A,{className:"w-2 h-2"})})]})]}),(0,a.jsxs)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>es(!et),className:"hidden lg:flex",children:[(0,a.jsx)(b.A,{className:"w-4 h-4 mr-1"}),"AI Tag ",et?"Sakla":"G\xf6ster"]}),(0,a.jsxs)(d.E,{variant:"secondary",className:"text-xs lg:text-sm",children:[q?eA.filter(e=>!e.is_used).length:ep.filter(e=>!e.is_used).length," / ",q?eA.length:ep.length]})]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Prompt'larda ara...",value:q,onChange:e=>Q(e.target.value),className:"w-full pl-10 pr-10 py-2.5 sm:py-2 border border-gray-300 rounded-lg text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 hover:bg-white transition-colors touch-manipulation"}),q&&(0,a.jsx)("button",{onClick:()=>Q(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 touch-manipulation p-1",children:(0,a.jsx)(f.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)(c.F,{className:"flex-1 p-2 sm:p-3 lg:p-6 pb-32 sm:pb-28 lg:pb-24",children:(0,a.jsx)("div",{className:"space-y-2 sm:space-y-3",children:0===ep.length?(0,a.jsxs)("div",{className:"text-center py-8 sm:py-12",children:[(0,a.jsx)(C.A,{className:"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4"}),(0,a.jsx)("h3",{className:"text-base sm:text-lg font-medium text-gray-900 mb-2",children:"Hen\xfcz Prompt Yok"}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-500 px-4",children:"Aşağıdaki alandan ilk promptunuzu ekleyin"})]}):0===eA.length?(0,a.jsxs)("div",{className:"text-center py-8 sm:py-12",children:[(0,a.jsx)(_.A,{className:"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4"}),(0,a.jsx)("h3",{className:"text-base sm:text-lg font-medium text-gray-900 mb-2",children:"Arama Sonucu Bulunamadı"}),(0,a.jsxs)("p",{className:"text-sm sm:text-base text-gray-500 px-4",children:['"',q,'" i\xe7in eşleşen prompt bulunamadı']}),(0,a.jsx)(l.$,{variant:"outline",className:"mt-3 sm:mt-4 touch-manipulation",onClick:()=>Q(""),children:"Aramayı Temizle"})]}):(0,a.jsx)(ev.Mp,{sensors:ex,collisionDetection:ev.fp,onDragEnd:eM,children:(0,a.jsx)(ey.gB,{items:eA.map(e=>e.id),strategy:ey._G,children:eA.sort((e,t)=>e.is_used!==t.is_used?e.is_used?1:-1:e.order_index-t.order_index).map(e=>(0,a.jsx)(eN,{prompt:e,isMultiSelectMode:j,selectedPrompts:x,editingPromptId:i,editingText:u,editTextareaRef:ed,onSelectPrompt:eT,onEditPrompt:eP,onSaveEdit:eK,onCancelEdit:e$,onCopyPrompt:eD,setEditingText:h,onHashtagFilter:eS},e.id))})})})}),(0,a.jsx)(r.Suspense,{fallback:e?(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden",children:(0,a.jsx)(eo,{})})}):null,children:(0,a.jsx)(ew,{open:e,onOpenChange:t||(()=>{}),onSelectContext:e=>{n(e.content),null==t||t()}})}),(0,a.jsx)("div",{className:"fixed bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white to-white/95 border-t border-gray-200 shadow-xl safe-area-bottom z-[60] backdrop-blur-sm",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"block lg:hidden",children:[(0,a.jsxs)("div",{className:"p-3 border-b border-gray-100 bg-gray-50/50",children:[(0,a.jsx)("div",{className:"flex justify-center mb-2",children:(0,a.jsxs)(l.$,{variant:j?"default":"outline",size:"sm",onClick:eF,className:"min-h-[44px] px-3 transition-all duration-200 ".concat(j?"text-white bg-blue-600 hover:bg-blue-700 shadow-md":"text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300 hover:bg-blue-50"),children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:j?"Se\xe7im İptal":"\xc7oklu Se\xe7"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[j&&x.size>0&&(0,a.jsxs)(l.$,{variant:"default",size:"sm",onClick:eq,className:"text-white bg-green-600 hover:bg-green-700 min-h-[40px] px-2 shadow-md transition-all duration-200",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-1"}),(0,a.jsxs)("span",{className:"text-xs font-medium",children:["Kopyala (",x.size,")"]})]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eR,className:"text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[40px] px-2 transition-all duration-200",children:[(0,a.jsx)(E.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Export"})]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eL,className:"text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[40px] px-2 transition-all duration-200",children:[(0,a.jsx)(S.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Geri Al"})]})]})]}),(0,a.jsxs)("div",{className:"p-2 sm:p-3 relative z-[60]",children:[(0,a.jsxs)("div",{className:"block sm:hidden space-y-2",children:[(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(ei,{value:s,onChange:n,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),eE())},placeholder:"Prompt yazın... (/ klas\xf6r, # etiket)",className:"resize-none text-base border-2 focus:border-blue-500 transition-colors duration-200 pr-16 w-full rounded-lg p-3 touch-manipulation",suggestions:{hashtags:ef||[],folders:ej||[]},enableDynamicHeight:!0,heightConfig:{minHeight:48,maxHeightFraction:.25,baseHeight:48}})}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eF,className:"flex-1 min-h-[44px] transition-all duration-200 touch-manipulation ".concat(j?"text-white bg-blue-600 hover:bg-blue-700":"text-blue-600 hover:text-blue-700 border-blue-200"),children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs",children:"Se\xe7"})]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eR,className:"flex-1 text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[44px] touch-manipulation",children:[(0,a.jsx)(E.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs",children:"Export"})]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eL,className:"flex-1 text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[44px] touch-manipulation",children:[(0,a.jsx)(S.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs",children:"Geri"})]}),(0,a.jsxs)(l.$,{onClick:eE,disabled:!s.trim(),size:"default",className:"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white min-h-[44px] shadow-lg hover:shadow-xl transition-all duration-200 font-medium touch-manipulation",children:[(0,a.jsx)(C.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{className:"text-xs",children:"Ekle"})]})]})]}),(0,a.jsxs)("div",{className:"hidden sm:flex gap-2 items-end relative z-[60]",children:[(0,a.jsx)("div",{className:"flex-1 relative",children:(0,a.jsx)(ei,{value:s,onChange:n,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),eE())},placeholder:"Prompt metninizi buraya yazın... (/ i\xe7in klas\xf6r, # i\xe7in etiket)",className:"resize-none text-base border-2 focus:border-blue-500 transition-colors duration-200 pr-16 w-full rounded-lg p-3",suggestions:{hashtags:ef||[],folders:ej||[]},enableDynamicHeight:!0,heightConfig:{minHeight:44,maxHeightFraction:.33,baseHeight:44}})}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:eF,className:"min-h-[44px] px-2 transition-all duration-200 ".concat(j?"text-white bg-blue-600 hover:bg-blue-700":"text-blue-600 hover:text-blue-700 border-blue-200"),children:(0,a.jsx)(A.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:eR,className:"text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[44px] px-2",children:(0,a.jsx)(E.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:eL,className:"text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[44px] px-2",children:(0,a.jsx)(S.A,{className:"h-4 w-4"})}),(0,a.jsxs)(l.$,{onClick:eE,disabled:!s.trim(),size:"default",className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white px-4 min-h-[44px] shrink-0 shadow-lg hover:shadow-xl transition-all duration-200 font-medium",children:[(0,a.jsx)(C.A,{className:"h-4 w-4 mr-1"}),"Ekle"]})]})]})]})]}),(0,a.jsx)("div",{className:"hidden lg:block p-4 bg-gradient-to-r from-gray-50 to-white border-t border-gray-100 relative z-[60]",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 p-4",children:[(0,a.jsxs)("div",{className:"flex gap-4 items-end",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(ei,{value:s,onChange:n,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),eE())},placeholder:"Prompt metninizi buraya yazın... (/ i\xe7in klas\xf6r, # i\xe7in etiket)",className:"resize-none border-0 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg bg-gray-50 hover:bg-white transition-all duration-200 text-base pr-20 w-full p-3",suggestions:{hashtags:ef||[],folders:ej||[]},enableDynamicHeight:!0,heightConfig:{minHeight:56,maxHeightFraction:.33,baseHeight:56}}),Y&&(0,a.jsxs)("div",{className:"space-y-4 pt-4 border-t border-gray-200 bg-gray-50 rounded-lg p-4 mt-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("div",{className:"w-1 h-4 bg-purple-500 rounded-full"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Kategorilendirme"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Başlık (İsteğe bağlı)"}),(0,a.jsx)(o.p,{type:"text",placeholder:"Bu prompt i\xe7in kısa bir başlık...",value:J,onChange:e=>X(e.target.value),className:"border-gray-300 focus:border-purple-500 focus:ring-purple-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Etiketler"}),em&&(0,a.jsx)(G,{hashtags:I,onHashtagsChange:O,suggestions:ef||[],placeholder:"Etiket ekleyin... (\xf6rn: #frontend, #api)",maxTags:5})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Klas\xf6r/Kategori"}),em&&(0,a.jsx)(V,{category:W,onCategoryChange:Z,suggestions:ej||[],placeholder:"Klas\xf6r se\xe7in... (\xf6rn: /frontend, /admin)"})]})]})]}),eh&&(0,a.jsx)(M.LD,{type:"prompt",current:eh.current_prompts,max:eh.max_prompts_per_project,onUpgrade:()=>{}}),(0,a.jsxs)(l.$,{onClick:eE,disabled:!s.trim()||eh&&!eh.can_create_prompt,size:"lg",className:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-300 disabled:to-gray-400 text-white px-8 py-3 min-h-[56px] shadow-lg hover:shadow-xl transition-all duration-200 font-semibold rounded-lg",children:[(0,a.jsx)(C.A,{className:"h-5 w-5 mr-2"}),"Prompt Ekle"]})]}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.$,{variant:j?"default":"outline",size:"sm",onClick:eF,className:"transition-all duration-200 rounded-lg font-medium ".concat(j?"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md":"border-blue-200 text-blue-600 hover:text-blue-700 hover:border-blue-300 hover:bg-blue-50"),children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"\xc7oklu Se\xe7"]}),j&&x.size>0&&(0,a.jsxs)(l.$,{variant:"default",size:"sm",onClick:eq,className:"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-md transition-all duration-200 rounded-lg font-medium",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Kopyala (",x.size,")"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eR,className:"border-purple-200 text-purple-600 hover:text-purple-700 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 rounded-lg font-medium",children:[(0,a.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Export"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eL,className:"border-orange-200 text-orange-600 hover:text-orange-700 hover:border-orange-300 hover:bg-orange-50 transition-all duration-200 rounded-lg font-medium",children:[(0,a.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Geri Al"]}),!s.trim()&&(0,a.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>ee(!Y),className:"border-gray-300 text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition-all duration-200",children:Y?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(z.A,{className:"w-4 h-4 mr-2"}),"Kategorileri Gizle"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(D.A,{className:"w-4 h-4 mr-2"}),"Kategori Ekle"]})})]})})]})})]})})]}),et&&em&&(0,a.jsx)(en,{projectId:em,onHashtagClick:eS,onCategoryClick:ez,selectedHashtags:ea,selectedCategory:el,className:"hidden lg:flex"})]}):(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)("div",{className:"h-16 sm:h-20 lg:h-24"}),(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center px-4",children:(0,a.jsx)("div",{className:"max-w-2xl w-full",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(p.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-6"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Proje Se\xe7in"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Başlamak i\xe7in sol panelden bir proje se\xe7in veya yeni bir proje oluşturun"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(w.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-6"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Context Alanı"}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Proje se\xe7tikten sonra sağ panelden context metninizi yazabilirsiniz"})]})]})})}),(0,a.jsx)("div",{className:"h-32 sm:h-40 lg:h-48"})]})}let e_=ek},15501:(e,t,s)=>{s.d(t,{$I:()=>d,F$:()=>o,GQ:()=>u,Qu:()=>m,sW:()=>c});var a=s(32960),r=s(26715),l=s(5041),n=s(70478),i=s(86489);function o(e){return(0,a.I)({queryKey:["prompts",e],queryFn:async()=>{if(!e)return console.log("\uD83D\uDCDD [USE_PROMPTS] No project ID provided"),[];console.log("\uD83D\uDCDD [USE_PROMPTS] Fetching prompts for project:",e);try{var t;let{data:{session:s},error:a}=await n.L.auth.getSession();console.log("\uD83D\uDCDD [USE_PROMPTS] Session check:",{hasSession:!!s,sessionError:null==a?void 0:a.message,userId:null==s||null==(t=s.user)?void 0:t.id});let{data:r,error:l}=await n.L.from("prompts").select("*").eq("project_id",e).order("order_index",{ascending:!0});if(l)throw console.error("❌ [USE_PROMPTS] Error fetching prompts:",l),Error(l.message);return console.log("✅ [USE_PROMPTS] Prompts fetched:",(null==r?void 0:r.length)||0,"prompts"),r||[]}catch(e){throw console.error("\uD83D\uDCA5 [USE_PROMPTS] Exception:",e),e}},enabled:!!e})}function c(){let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>{let t=await (0,i.vw)(e.project_id);if(!t.allowed)throw Error(t.reason||i.x5.PROMPT_LIMIT_REACHED);let{data:{user:s},error:a}=await n.L.auth.getUser();if(a||!s)throw Error("Kullanıcı oturumu bulunamadı");let r=e.task_code||"task-".concat(e.order_index),{data:l,error:o}=await n.L.from("prompts").insert({...e,user_id:s.id,task_code:r,tags:e.tags||[],is_favorite:e.is_favorite||!1,usage_count:e.usage_count||0}).select().single();if(o)throw Error(o.message);return l},onMutate:async t=>{await e.cancelQueries({queryKey:["prompts",t.project_id]});let s=e.getQueryData(["prompts",t.project_id]),{data:{user:a}}=await n.L.auth.getUser();if(!a)return{previousPrompts:s};let r=e.getQueryData(["prompts",t.project_id])||[],l=(r.length>0?Math.max(...r.map(e=>e.order_index||0)):0)+1,i={id:"temp-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),...t,user_id:a.id,order_index:l,task_code:t.task_code||"task-".concat(l),tags:t.tags||[],is_favorite:t.is_favorite||!1,usage_count:0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};return e.setQueryData(["prompts",t.project_id],e=>Array.isArray(e)?[i,...e]:[i]),{previousPrompts:s,optimisticPrompt:i}},onError:(t,s,a)=>{(null==a?void 0:a.previousPrompts)&&e.setQueryData(["prompts",s.project_id],a.previousPrompts)},onSuccess:async(t,s,a)=>{e.setQueryData(["prompts",t.project_id],e=>{if(!Array.isArray(e))return[t];let s=e.filter(e=>{var s;return e.id!==(null==a||null==(s=a.optimisticPrompt)?void 0:s.id)&&e.id!==t.id});return[t,...s]}),setTimeout(()=>{e.invalidateQueries({queryKey:["popular-hashtags",t.project_id]}),e.invalidateQueries({queryKey:["all-hashtags",t.project_id]}),e.invalidateQueries({queryKey:["popular-categories",t.project_id]}),e.invalidateQueries({queryKey:["all-categories",t.project_id]}),(0,i.O3)().then(()=>{e.invalidateQueries({queryKey:["user-limits"]}),e.invalidateQueries({queryKey:["usage-stats"]})}).catch(e=>{console.warn("Kullanım istatistikleri g\xfcncellenemedi:",e)})},100)}})}function d(){let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>{let{id:t,...s}=e,{data:a,error:r}=await n.L.from("prompts").update({...s,updated_at:new Date().toISOString()}).eq("id",t).select().single();if(r)throw Error(r.message);return a},onSuccess:t=>{e.invalidateQueries({queryKey:["prompts",t.project_id]}),e.invalidateQueries({queryKey:["popular-hashtags",t.project_id]}),e.invalidateQueries({queryKey:["all-hashtags",t.project_id]}),e.invalidateQueries({queryKey:["popular-categories",t.project_id]}),e.invalidateQueries({queryKey:["all-categories",t.project_id]})}})}function m(){let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>{let{data:t,error:s}=await n.L.from("prompts").update({is_used:!0}).eq("id",e).select().single();if(s)throw Error(s.message);return t},onMutate:async t=>{let s=["prompts"];await e.cancelQueries({queryKey:s});let a=e.getQueriesData({queryKey:s});return e.setQueriesData({queryKey:s},e=>e&&Array.isArray(e)?e.map(e=>e.id===t?{...e,is_used:!0}:e):e),{previousPrompts:a}},onError:(t,s,a)=>{(null==a?void 0:a.previousPrompts)&&a.previousPrompts.forEach(t=>{let[s,a]=t;e.setQueryData(s,a)})},onSettled:t=>{t&&e.invalidateQueries({queryKey:["prompts",t.project_id]})}})}function u(){let e=(0,r.jE)();return(0,l.n)({mutationFn:async e=>{let t=e.filter(e=>void 0!==e.order_index||void 0!==e.task_code),s=e.filter(e=>void 0===e.order_index&&void 0===e.task_code),a=[];if(t.length>0)try{let{data:e,error:s}=await n.L.rpc("bulk_update_prompts_order",{prompt_updates:t});if(s){console.warn("RPC function failed, falling back to individual updates:",s);let e=t.map(async e=>{let{data:t,error:s}=await n.L.from("prompts").update({...e,updated_at:new Date().toISOString()}).eq("id",e.id).select().single();if(s)throw Error("Failed to update prompt ".concat(e.id,": ").concat(s.message));return t}),r=await Promise.all(e);a.push(...r)}else if(e){let t=e.map(e=>e.id),{data:s,error:r}=await n.L.from("prompts").select("*").in("id",t);if(r)throw Error("Failed to fetch updated prompts: ".concat(r.message));a.push(...s||[])}}catch(e){throw console.error("Bulk update error:",e),e}if(s.length>0){let e=s.map(async e=>{let{data:t,error:s}=await n.L.from("prompts").update({...e,updated_at:new Date().toISOString()}).eq("id",e.id).select().single();if(s)throw Error("Failed to update prompt ".concat(e.id,": ").concat(s.message));return t}),t=await Promise.all(e);a.push(...t)}return a},onMutate:async t=>{let s=["prompts"];await e.cancelQueries({queryKey:s});let a=e.getQueriesData({queryKey:s});return e.setQueriesData({queryKey:s},e=>e&&Array.isArray(e)?e.map(e=>{let s=t.find(t=>t.id===e.id);return s?{...e,...s}:e}):e),{previousPrompts:a}},onError:(t,s,a)=>{(null==a?void 0:a.previousPrompts)&&a.previousPrompts.forEach(t=>{let[s,a]=t;e.setQueryData(s,a)})},onSettled:t=>{t&&t.length>0&&e.invalidateQueries({queryKey:["prompts",t[0].project_id]})}})}},88539:(e,t,s)=>{s.d(t,{T:()=>l});var a=s(95155);s(12115);var r=s(59434);function l(e){let{className:t,...s}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s})}}}]);