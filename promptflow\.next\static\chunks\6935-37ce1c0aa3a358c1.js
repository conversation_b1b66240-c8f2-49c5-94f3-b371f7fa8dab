"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6935],{42099:(e,s,a)=>{a.d(s,{N:()=>i.L});var i=a(70478);i.L.auth.onAuthStateChange((e,s)=>{var a,i;console.log("\uD83D\uDD10 [SUPABASE_CLIENT] Auth state change: ".concat(e),{hasSession:!!s,userId:null==s||null==(a=s.user)?void 0:a.id,email:null==s||null==(i=s.user)?void 0:i.email,expiresAt:(null==s?void 0:s.expires_at)?new Date(1e3*s.expires_at).toISOString():null})}),i.L.auth.onAuthStateChange(e=>{if("SIGNED_OUT"===e){let e="https://iqehopwgrczylqliajww.supabase.co";e&&localStorage.removeItem("sb-"+e.split("//")[1].split(".")[0]+"-auth-token")}})},66935:(e,s,a)=>{a.r(s),a.d(s,{default:()=>F});var i=a(95155),r=a(12115),l=a(30285),n=a(66695),t=a(62523),d=a(85057),c=a(26126),m=a(22346),o=a(9454),x=a(22100),u=a(67238),h=a(74178),p=a(42099),j=a(71007),g=a(17951),v=a(38564),f=a(75525),b=a(35169),N=a(34835),y=a(28883),w=a(69074),_=a(69803),z=a(40646),k=a(85339),A=a(54861),P=a(6874),S=a.n(P),C=a(54165),D=a(88539),E=a(76981),T=a(5196),L=a(59434);function B(e){let{className:s,...a}=e;return(0,i.jsx)(E.bL,{"data-slot":"checkbox",className:(0,L.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...a,children:(0,i.jsx)(E.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,i.jsx)(T.A,{className:"size-3.5"})})})}let R=(0,a(74466).F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function G(e){let{className:s,variant:a,...r}=e;return(0,i.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,L.cn)(R({variant:a}),s),...r})}function J(e){let{className:s,...a}=e;return(0,i.jsx)("div",{"data-slot":"alert-description",className:(0,L.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...a})}var Z=a(1243),Y=a(51154),q=a(56671);function $(e){let{isOpen:s,onClose:a,onSuccess:n}=e,[t,c]=(0,r.useState)(""),[m,o]=(0,r.useState)(!1),[x,u]=(0,r.useState)(!1),{data:p,isLoading:j}=(0,h.ov)(),{data:g,isLoading:v}=(0,h.P5)(),f=(0,h.pS)(),b=j||v||f.isPending,N=async()=>{if(!x)return void q.oR.error("L\xfctfen iptal işlemini onaylayın");if(!t.trim())return void q.oR.error("L\xfctfen iptal nedeninizi belirtin");try{let e=await f.mutateAsync({cancellationReason:t.trim(),requestRefund:m&&(null==g?void 0:g.is_in_trial)});(null==e?void 0:e.success)?(q.oR.success(e.message||"Plan başarıyla iptal edildi"),null==n||n(),a(),c(""),o(!1),u(!1)):q.oR.error((null==e?void 0:e.message)||"Plan iptal edilemedi")}catch(e){console.error("Plan cancellation error:",e),q.oR.error("Plan iptal edilirken bir hata oluştu")}},y=()=>{f.isPending||(c(""),o(!1),u(!1),a())};return(null==p?void 0:p.can_cancel)?(0,i.jsx)(C.lG,{open:s,onOpenChange:y,children:(0,i.jsxs)(C.Cf,{className:"sm:max-w-lg",children:[(0,i.jsxs)(C.c7,{children:[(0,i.jsxs)(C.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(Z.A,{className:"h-5 w-5 text-orange-500"}),"Plan İptal Et"]}),(0,i.jsxs)(C.rr,{children:[null==p?void 0:p.plan_display_name," planınızı iptal etmek \xfczeresiniz. Bu işlem geri alınamaz."]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(null==g?void 0:g.is_in_trial)&&(0,i.jsxs)(G,{children:[(0,i.jsx)(z.A,{className:"h-4 w-4"}),(0,i.jsxs)(J,{children:["Deneme s\xfcreniz devam ediyor (",g.days_remaining," g\xfcn kaldı). İptal ederseniz tam iade alabilirsiniz."]})]}),(null==p?void 0:p.refund_eligible)&&(0,i.jsxs)(G,{children:[(0,i.jsx)(z.A,{className:"h-4 w-4"}),(0,i.jsxs)(J,{children:["Deneme s\xfcresi i\xe7inde olduğunuz i\xe7in ",p.estimated_refund_amount," TL tam iade alabilirsiniz."]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)(d.J,{htmlFor:"cancellation-reason",children:["İptal Nedeni ",(0,i.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,i.jsx)(D.T,{id:"cancellation-reason",placeholder:"Planınızı neden iptal etmek istiyorsunuz? Geri bildiriminiz bizim i\xe7in değerli.",value:t,onChange:e=>c(e.target.value),rows:3,maxLength:500,disabled:b}),(0,i.jsxs)("p",{className:"text-xs text-muted-foreground",children:[t.length,"/500 karakter"]})]}),(null==p?void 0:p.refund_eligible)&&(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(B,{id:"request-refund",checked:m,onCheckedChange:e=>o(e),disabled:b}),(0,i.jsxs)(d.J,{htmlFor:"request-refund",className:"text-sm",children:["İade talep ediyorum (",p.estimated_refund_amount," TL)"]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(B,{id:"confirm-cancellation",checked:x,onCheckedChange:e=>u(e),disabled:b}),(0,i.jsx)(d.J,{htmlFor:"confirm-cancellation",className:"text-sm",children:"Plan iptal işlemini onaylıyorum ve bu işlemin geri alınamayacağını anlıyorum"})]}),(0,i.jsxs)(G,{children:[(0,i.jsx)(Z.A,{className:"h-4 w-4"}),(0,i.jsx)(J,{children:"Plan iptal edildikten sonra \xfccretsiz plana ge\xe7eceksiniz. Mevcut projeleriniz ve promptlarınız korunacak ancak plan limitleri ge\xe7erli olacak."})]})]}),(0,i.jsxs)(C.Es,{className:"gap-2",children:[(0,i.jsx)(l.$,{onClick:y,variant:"outline",disabled:b,children:"Vazge\xe7"}),(0,i.jsxs)(l.$,{onClick:N,variant:"destructive",disabled:b||!x||!t.trim(),children:[b&&(0,i.jsx)(Y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Planı İptal Et"]})]})]})}):(0,i.jsx)(C.lG,{open:s,onOpenChange:y,children:(0,i.jsxs)(C.Cf,{className:"sm:max-w-md",children:[(0,i.jsxs)(C.c7,{children:[(0,i.jsxs)(C.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(A.A,{className:"h-5 w-5 text-red-500"}),"Plan İptal Edilemiyor"]}),(0,i.jsx)(C.rr,{children:(null==p?void 0:p.reason)||"Bu plan iptal edilemez."})]}),(0,i.jsx)(C.Es,{children:(0,i.jsx)(l.$,{onClick:y,variant:"outline",children:"Kapat"})})]})})}function F(){let[e,s]=(0,r.useState)(""),[a,P]=(0,r.useState)(""),[C,D]=(0,r.useState)(!1),[E,T]=(0,r.useState)(""),[L,B]=(0,r.useState)(null),[R,G]=(0,r.useState)(!1),{data:J}=(0,x.Jd)(),{data:Z=[]}=(0,u.YK)(),{data:Y}=(0,h.EU)(),{data:q=[]}=(0,h.C)(),{data:F}=(0,h.ov)(),{data:H}=(0,h.P5)(),I=(0,h.Jd)(),O=(0,x.Rt)(),W=async i=>{if(i.preventDefault(),T(""),B(null),e!==a){T("Yeni şifreler eşleşmiyor!"),B("error");return}if(e.length<6){T("Yeni şifre en az 6 karakter olmalı!"),B("error");return}D(!0);try{let{error:a}=await p.N.auth.updateUser({password:e});if(a)throw a;T("Şifre başarıyla g\xfcncellendi!"),B("success"),s(""),P("")}catch(e){T(e instanceof Error?e.message:"Şifre g\xfcncellenirken bir hata oluştu"),B("error")}finally{D(!1)}},U=async e=>{try{let s=q.find(s=>s.name===e);if(!s)throw Error("Ge\xe7ersiz plan se\xe7imi");if(Y&&s.max_projects<Y.max_projects&&Z.length>s.max_projects)throw Error("Bu plana ge\xe7mek i\xe7in \xf6nce proje sayınızı ".concat(s.max_projects,"'e d\xfcş\xfcrmeniz gerekiyor. ")+"Şu anda ".concat(Z.length," projeniz var."));if(Y&&s.price_monthly<Y.price_monthly&&!window.confirm("".concat(s.display_name," planına ge\xe7mek istediğinizden emin misiniz? ")+"Bu işlem bazı \xf6zelliklerinizi kısıtlayabilir."))return;await I.mutateAsync({planName:e,billingCycle:"monthly"}),T("Plan başarıyla değiştirildi!"),B("success"),setTimeout(()=>{T(""),B(null)},5e3)}catch(e){T(e instanceof Error?e.message:"Plan değiştirilemedi"),B("error"),setTimeout(()=>{T(""),B(null)},8e3)}},K=e=>{switch(e){case"free":return(0,i.jsx)(j.A,{className:"h-5 w-5"});case"professional":return(0,i.jsx)(g.A,{className:"h-5 w-5"});case"enterprise":return(0,i.jsx)(v.A,{className:"h-5 w-5"});default:return(0,i.jsx)(f.A,{className:"h-5 w-5"})}},M=e=>{switch(e){case"free":default:return"bg-gray-100 text-gray-800";case"professional":return"bg-blue-100 text-blue-800";case"enterprise":return"bg-purple-100 text-purple-800"}};return J?(0,i.jsxs)(o.q,{children:[(0,i.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsx)(S(),{href:"/",children:(0,i.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,i.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Ana Sayfaya D\xf6n"]})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Profil Ayarları"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Hesap bilgilerinizi y\xf6netin"})]})]}),(0,i.jsxs)(l.$,{variant:"outline",onClick:()=>{O.mutate()},className:"text-red-600 hover:text-red-700",children:[(0,i.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"\xc7ıkış Yap"]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,i.jsxs)(n.Zp,{children:[(0,i.jsxs)(n.aR,{children:[(0,i.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(j.A,{className:"h-5 w-5"}),"Temel Bilgiler"]}),(0,i.jsx)(n.BT,{children:"Hesap bilgilerinizi g\xf6r\xfcnt\xfcleyin"})]}),(0,i.jsxs)(n.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(d.J,{htmlFor:"email",children:"E-posta Adresi"}),(0,i.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,i.jsx)(y.A,{className:"h-4 w-4 text-gray-400"}),(0,i.jsx)("span",{className:"text-gray-900",children:J.email})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(d.J,{children:"Kayıt Tarihi"}),(0,i.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,i.jsx)(w.A,{className:"h-4 w-4 text-gray-400"}),(0,i.jsx)("span",{className:"text-gray-900",children:new Date(J.created_at).toLocaleDateString("tr-TR")})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(d.J,{children:"Hesap Durumu"}),(0,i.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,i.jsx)(f.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)(c.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:"Aktif"})]})]})]})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsxs)(n.aR,{children:[(0,i.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(_.A,{className:"h-5 w-5"}),"Şifre Değiştir"]}),(0,i.jsx)(n.BT,{children:"G\xfcvenliğiniz i\xe7in d\xfczenli olarak şifrenizi değiştirin"})]}),(0,i.jsx)(n.Wu,{children:(0,i.jsxs)("form",{onSubmit:W,className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(d.J,{htmlFor:"newPassword",children:"Yeni Şifre"}),(0,i.jsx)(t.p,{id:"newPassword",type:"password",placeholder:"Yeni şifrenizi girin",value:e,onChange:e=>s(e.target.value),disabled:C,autoComplete:"new-password",required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(d.J,{htmlFor:"confirmPassword",children:"Yeni Şifre (Tekrar)"}),(0,i.jsx)(t.p,{id:"confirmPassword",type:"password",placeholder:"Yeni şifrenizi tekrar girin",value:a,onChange:e=>P(e.target.value),disabled:C,autoComplete:"new-password",required:!0})]}),E&&(0,i.jsxs)("div",{className:"flex items-center gap-2 text-sm p-3 rounded-md ".concat("success"===L?"bg-green-50 text-green-700":"bg-red-50 text-red-700"),children:["success"===L?(0,i.jsx)(z.A,{className:"h-4 w-4"}):(0,i.jsx)(k.A,{className:"h-4 w-4"}),E]}),(0,i.jsx)(l.$,{type:"submit",disabled:C||!e||!a,className:"w-full",children:C?"G\xfcncelleniyor...":"Şifreyi G\xfcncelle"})]})})]})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{className:"text-lg",children:"Hesap İstatistikleri"})}),(0,i.jsxs)(n.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Toplam Proje"}),(0,i.jsx)(c.E,{variant:"outline",children:Z.length})]}),(0,i.jsx)(m.w,{}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Hesap Tipi"}),(0,i.jsx)(c.E,{variant:"secondary",className:M((null==Y?void 0:Y.plan_name)||"free"),children:(null==Y?void 0:Y.display_name)||"\xdccretsiz"})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Son Giriş"}),(0,i.jsx)("span",{className:"text-sm text-gray-900",children:new Date(J.last_sign_in_at||J.created_at).toLocaleDateString("tr-TR")})]})]})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsxs)(n.aR,{children:[(0,i.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(g.A,{className:"h-5 w-5 text-blue-600"}),"Plan Y\xf6netimi"]}),(0,i.jsx)(n.BT,{children:"Mevcut planınızı g\xf6r\xfcnt\xfcleyin ve değiştirin"})]}),(0,i.jsxs)(n.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[K((null==Y?void 0:Y.plan_name)||"free"),(0,i.jsx)("span",{className:"font-medium text-blue-900",children:(null==Y?void 0:Y.display_name)||"\xdccretsiz Plan"})]}),(0,i.jsx)(c.E,{className:M((null==Y?void 0:Y.plan_name)||"free"),children:"Aktif"})]}),(0,i.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,i.jsxs)("div",{children:["• ",(null==Y?void 0:Y.max_projects)||5," proje limiti"]}),(0,i.jsxs)("div",{children:["• ",(null==Y?void 0:Y.max_prompts_per_project)||100," prompt/proje limiti"]}),(null==Y?void 0:Y.expires_at)&&(0,i.jsxs)("div",{children:["• Bitiş: ",new Date(Y.expires_at).toLocaleDateString("tr-TR")]})]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(d.J,{className:"text-sm font-medium",children:"Mevcut Planlar"}),q.map(e=>(0,i.jsx)("div",{className:"p-3 border rounded-lg transition-all ".concat(e.name===(null==Y?void 0:Y.plan_name)?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"),children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[K(e.name),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-sm",children:e.display_name}),(0,i.jsxs)("div",{className:"text-xs text-gray-600",children:[-1===e.max_projects?"Sınırsız":e.max_projects," proje, "," ",-1===e.max_prompts_per_project?"Sınırsız":e.max_prompts_per_project," prompt/proje"]})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:0===e.price_monthly?"\xdccretsiz":"₺".concat(e.price_monthly,"/ay")}),e.name!==(null==Y?void 0:Y.plan_name)&&(0,i.jsx)(l.$,{size:"sm",variant:"free"===e.name?"outline":"default",onClick:()=>U(e.name),disabled:I.isPending,className:"ml-2",children:I.isPending?"Değiştiriliyor...":"Se\xe7"})]})]})},e.id))]}),(null==H?void 0:H.is_in_trial)&&(0,i.jsxs)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,i.jsx)(z.A,{className:"h-4 w-4 text-green-600"}),(0,i.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Deneme S\xfcresi Aktif"})]}),(0,i.jsxs)("p",{className:"text-xs text-green-700",children:[H.days_remaining," g\xfcn deneme s\xfcreniz kaldı. Bu s\xfcre i\xe7inde planınızı iptal ederseniz tam iade alabilirsiniz."]})]}),(null==F?void 0:F.can_cancel)&&(null==Y?void 0:Y.plan_name)!=="free"&&(0,i.jsx)("div",{className:"pt-4 border-t border-gray-200",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Plan İptal"}),(0,i.jsx)("p",{className:"text-xs text-gray-600",children:(null==H?void 0:H.is_in_trial)?"Deneme s\xfcresi i\xe7inde tam iade alabilirsiniz":"Planınızı istediğiniz zaman iptal edebilirsiniz"})]}),(0,i.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>G(!0),className:"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300",children:[(0,i.jsx)(A.A,{className:"h-4 w-4 mr-1"}),"Planı İptal Et"]})]})}),E&&L&&(0,i.jsxs)("div",{className:"p-3 rounded-lg flex items-center gap-2 ".concat("success"===L?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"),children:["success"===L?(0,i.jsx)(z.A,{className:"h-4 w-4"}):(0,i.jsx)(k.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"text-sm",children:E})]})]})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{className:"text-lg",children:"G\xfcvenlik \xd6nerileri"})}),(0,i.jsxs)(n.Wu,{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-start gap-3",children:[(0,i.jsx)(f.A,{className:"h-4 w-4 text-blue-500 mt-0.5"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"G\xfc\xe7l\xfc Şifre"}),(0,i.jsx)("p",{className:"text-xs text-gray-600",children:"En az 8 karakter, b\xfcy\xfck/k\xfc\xe7\xfck harf ve rakam kullanın"})]})]}),(0,i.jsxs)("div",{className:"flex items-start gap-3",children:[(0,i.jsx)(f.A,{className:"h-4 w-4 text-blue-500 mt-0.5"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"D\xfczenli G\xfcncelleme"}),(0,i.jsx)("p",{className:"text-xs text-gray-600",children:"Şifrenizi 3-6 ayda bir değiştirin"})]})]})]})]})]})]})]})}),(0,i.jsx)($,{isOpen:R,onClose:()=>G(!1),onSuccess:()=>{T("Plan başarıyla iptal edildi. \xdccretsiz plana ge\xe7tiniz."),B("success")}})]}):null}},88539:(e,s,a)=>{a.d(s,{T:()=>l});var i=a(95155);a(12115);var r=a(59434);function l(e){let{className:s,...a}=e;return(0,i.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...a})}}}]);