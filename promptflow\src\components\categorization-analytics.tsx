'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import {
  <PERSON><PERSON>hart<PERSON>,
  <PERSON><PERSON>,
  Fold<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Eye,
  EyeOff,
  Target
} from 'lucide-react'
import { usePopularHashtags, usePopularCategories } from '@/hooks/use-hashtags'
import { usePrompts } from '@/hooks/use-prompts'

interface CategorizationAnalyticsProps {
  projectId: string | null
  className?: string
}

export function CategorizationAnalytics({ 
  projectId, 
  className 
}: CategorizationAnalyticsProps) {
  const [isVisible, setIsVisible] = useState(false)
  
  const { data: prompts = [] } = usePrompts(projectId)
  const { data: popularHashtags = [] } = usePopularHashtags(projectId, 10)
  const { data: popularCategories = [] } = usePopularCategories(projectId, 5)

  // Calculate statistics
  const totalPrompts = prompts.length
  const categorizedPrompts = prompts.filter(p => p.category || (p.tags && p.tags.length > 0)).length
  const uncategorizedPrompts = totalPrompts - categorizedPrompts
  const categorizationRate = totalPrompts > 0 ? (categorizedPrompts / totalPrompts) * 100 : 0

  const promptsWithHashtags = prompts.filter(p => p.tags && p.tags.length > 0).length
  const promptsWithCategories = prompts.filter(p => p.category).length

  if (!projectId) return null

  return (
    <div className={className}>
      {/* Toggle Button */}
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(!isVisible)}
        className="mb-4"
      >
        {isVisible ? (
          <>
            <EyeOff className="w-4 h-4 mr-2" />
            Analitikleri Gizle
          </>
        ) : (
          <>
            <Eye className="w-4 h-4 mr-2" />
            Analitikleri Göster
          </>
        )}
      </Button>

      {isVisible && (
        <div className="space-y-3 w-full overflow-hidden">
          {/* Overview Card - Compact */}
          <Card className="w-full bg-white shadow-sm border border-gray-200">
            <CardHeader className="pb-2 px-3 pt-3">
              <CardTitle className="text-xs font-medium flex items-center gap-1.5">
                <BarChart3 className="w-3 h-3 shrink-0" />
                <span className="truncate">Genel Bakış</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 px-3 pb-3">
              {/* Categorization Rate - Compact */}
              <div className="space-y-1">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600 truncate">Kategorizasyon</span>
                  <span className="text-xs font-medium shrink-0">{categorizationRate.toFixed(0)}%</span>
                </div>
                <Progress value={categorizationRate} className="h-1.5 w-full" />
                <div className="text-xs text-gray-500 leading-tight">
                  {categorizedPrompts}/{totalPrompts} kategorili
                </div>
              </div>

              {/* Quick Stats - Compact Grid */}
              <div className="grid grid-cols-2 gap-2">
                <div className="text-center p-2 bg-blue-50 rounded">
                  <div className="text-sm font-semibold text-blue-700">{promptsWithHashtags}</div>
                  <div className="text-xs text-blue-600">Etiket</div>
                </div>
                <div className="text-center p-2 bg-green-50 rounded">
                  <div className="text-sm font-semibold text-green-700">{promptsWithCategories}</div>
                  <div className="text-xs text-green-600">Klasör</div>
                </div>
              </div>

              {uncategorizedPrompts > 0 && (
                <div className="p-2 bg-orange-50 border border-orange-200 rounded">
                  <div className="flex items-start gap-1.5 text-orange-700">
                    <Target className="w-3 h-3 shrink-0 mt-0.5" />
                    <div className="min-w-0">
                      <div className="text-xs font-medium leading-tight">
                        {uncategorizedPrompts} kategorisiz
                      </div>
                      <div className="text-xs text-orange-600 leading-tight">
                        Etiket/klasör ekleyin
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Popular Hashtags - Compact */}
          {popularHashtags.length > 0 && (
            <Card className="w-full bg-white shadow-sm border border-gray-200">
              <CardHeader className="pb-2 px-3 pt-3">
                <CardTitle className="text-xs font-medium flex items-center gap-1.5">
                  <Hash className="w-3 h-3 shrink-0" />
                  <span className="truncate">Popüler Etiketler</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="px-3 pb-3">
                <div className="space-y-1.5">
                  {popularHashtags.slice(0, 3).map(({ hashtag, count }, index) => {
                    const percentage = totalPrompts > 0 ? (count / totalPrompts) * 100 : 0
                    return (
                      <div key={hashtag} className="flex items-center justify-between gap-2">
                        <div className="flex items-center gap-1.5 min-w-0 flex-1">
                          <span className="text-xs text-gray-500 w-3 shrink-0">#{index + 1}</span>
                          <Badge variant="secondary" className="text-xs px-1.5 py-0.5 truncate max-w-[80px]">
                            {hashtag.replace('#', '')}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1.5 shrink-0">
                          <div className="w-8 h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-blue-500 rounded-full"
                              style={{ width: `${Math.max(percentage, 10)}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-600 w-4 text-right">{count}</span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Popular Categories - Compact */}
          {popularCategories.length > 0 && (
            <Card className="w-full bg-white shadow-sm border border-gray-200">
              <CardHeader className="pb-2 px-3 pt-3">
                <CardTitle className="text-xs font-medium flex items-center gap-1.5">
                  <Folder className="w-3 h-3 shrink-0" />
                  <span className="truncate">Popüler Klasörler</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="px-3 pb-3">
                <div className="space-y-1.5">
                  {popularCategories.slice(0, 3).map(({ category, count }, index) => {
                    const percentage = totalPrompts > 0 ? (count / totalPrompts) * 100 : 0
                    return (
                      <div key={category} className="flex items-center justify-between gap-2">
                        <div className="flex items-center gap-1.5 min-w-0 flex-1">
                          <span className="text-xs text-gray-500 w-3 shrink-0">#{index + 1}</span>
                          <Badge variant="outline" className="text-xs px-1.5 py-0.5 flex items-center gap-1 truncate max-w-[80px]">
                            <Folder className="w-2.5 h-2.5 shrink-0" />
                            <span className="truncate">{category}</span>
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1.5 shrink-0">
                          <div className="w-8 h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-green-500 rounded-full"
                              style={{ width: `${Math.max(percentage, 10)}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-600 w-4 text-right">{count}</span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recommendations - Compact */}
          {totalPrompts > 0 && (
            <Card className="w-full bg-white shadow-sm border border-gray-200">
              <CardHeader className="pb-2 px-3 pt-3">
                <CardTitle className="text-xs font-medium flex items-center gap-1.5">
                  <TrendingUp className="w-3 h-3 shrink-0" />
                  <span className="truncate">Öneriler</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="px-3 pb-3">
                <div className="space-y-1.5 text-xs text-gray-600">
                  {categorizationRate < 50 && (
                    <div className="flex items-start gap-1.5">
                      <span className="text-orange-500 shrink-0">•</span>
                      <span className="leading-tight">Etiket ekleyin</span>
                    </div>
                  )}
                  {promptsWithCategories === 0 && (
                    <div className="flex items-start gap-1.5">
                      <span className="text-blue-500 shrink-0">•</span>
                      <span className="leading-tight">Klasör kullanın</span>
                    </div>
                  )}
                  {popularHashtags.length > 10 && (
                    <div className="flex items-start gap-1.5">
                      <span className="text-green-500 shrink-0">•</span>
                      <span className="leading-tight">İyi etiket kullanımı!</span>
                    </div>
                  )}
                  {totalPrompts > 20 && categorizationRate > 80 && (
                    <div className="flex items-start gap-1.5">
                      <span className="text-green-500 shrink-0">•</span>
                      <span className="leading-tight">Mükemmel organizasyon!</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  )
}
