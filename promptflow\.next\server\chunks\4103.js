"use strict";exports.id=4103,exports.ids=[4103],exports.modules={3589:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},10022:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},11437:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},33295:(a,b,c)=>{c.d(b,{$I:()=>k,F$:()=>i,GQ:()=>m,Qu:()=>l,sW:()=>j});var d=c(51423),e=c(8693),f=c(54050),g=c(8266),h=c(73475);function i(a){return(0,d.I)({queryKey:["prompts",a],queryFn:async()=>{if(!a)return console.log(`📝 [USE_PROMPTS] No project ID provided`),[];console.log(`📝 [USE_PROMPTS] Fetching prompts for project:`,a);try{let{data:{session:b},error:c}=await g.L.auth.getSession();console.log(`📝 [USE_PROMPTS] Session check:`,{hasSession:!!b,sessionError:c?.message,userId:b?.user?.id});let{data:d,error:e}=await g.L.from("prompts").select("*").eq("project_id",a).order("order_index",{ascending:!0});if(e)throw console.error(`❌ [USE_PROMPTS] Error fetching prompts:`,e),Error(e.message);return console.log(`✅ [USE_PROMPTS] Prompts fetched:`,d?.length||0,"prompts"),d||[]}catch(a){throw console.error(`💥 [USE_PROMPTS] Exception:`,a),a}},enabled:!!a})}function j(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async a=>{let b=await (0,h.vw)(a.project_id);if(!b.allowed)throw Error(b.reason||h.x5.PROMPT_LIMIT_REACHED);let{data:{user:c},error:d}=await g.L.auth.getUser();if(d||!c)throw Error("Kullanıcı oturumu bulunamadı");let e=a.task_code||`task-${a.order_index}`,{data:f,error:i}=await g.L.from("prompts").insert({...a,user_id:c.id,task_code:e,tags:a.tags||[],is_favorite:a.is_favorite||!1,usage_count:a.usage_count||0}).select().single();if(i)throw Error(i.message);return f},onMutate:async b=>{await a.cancelQueries({queryKey:["prompts",b.project_id]});let c=a.getQueryData(["prompts",b.project_id]),{data:{user:d}}=await g.L.auth.getUser();if(!d)return{previousPrompts:c};let e=a.getQueryData(["prompts",b.project_id])||[],f=(e.length>0?Math.max(...e.map(a=>a.order_index||0)):0)+1,h={id:`temp-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,...b,user_id:d.id,order_index:f,task_code:b.task_code||`task-${f}`,tags:b.tags||[],is_favorite:b.is_favorite||!1,usage_count:0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};return a.setQueryData(["prompts",b.project_id],a=>Array.isArray(a)?[h,...a]:[h]),{previousPrompts:c,optimisticPrompt:h}},onError:(b,c,d)=>{d?.previousPrompts&&a.setQueryData(["prompts",c.project_id],d.previousPrompts)},onSuccess:async(b,c,d)=>{a.setQueryData(["prompts",b.project_id],a=>{if(!Array.isArray(a))return[b];let c=a.filter(a=>a.id!==d?.optimisticPrompt?.id&&a.id!==b.id);return[b,...c]}),setTimeout(()=>{a.invalidateQueries({queryKey:["popular-hashtags",b.project_id]}),a.invalidateQueries({queryKey:["all-hashtags",b.project_id]}),a.invalidateQueries({queryKey:["popular-categories",b.project_id]}),a.invalidateQueries({queryKey:["all-categories",b.project_id]}),(0,h.O3)().then(()=>{a.invalidateQueries({queryKey:["user-limits"]}),a.invalidateQueries({queryKey:["usage-stats"]})}).catch(a=>{console.warn("Kullanım istatistikleri g\xfcncellenemedi:",a)})},100)}})}function k(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async({id:a,...b})=>{let{data:c,error:d}=await g.L.from("prompts").update({...b,updated_at:new Date().toISOString()}).eq("id",a).select().single();if(d)throw Error(d.message);return c},onSuccess:b=>{a.invalidateQueries({queryKey:["prompts",b.project_id]}),a.invalidateQueries({queryKey:["popular-hashtags",b.project_id]}),a.invalidateQueries({queryKey:["all-hashtags",b.project_id]}),a.invalidateQueries({queryKey:["popular-categories",b.project_id]}),a.invalidateQueries({queryKey:["all-categories",b.project_id]})}})}function l(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async a=>{let{data:b,error:c}=await g.L.from("prompts").update({is_used:!0}).eq("id",a).select().single();if(c)throw Error(c.message);return b},onMutate:async b=>{let c=["prompts"];await a.cancelQueries({queryKey:c});let d=a.getQueriesData({queryKey:c});return a.setQueriesData({queryKey:c},a=>a&&Array.isArray(a)?a.map(a=>a.id===b?{...a,is_used:!0}:a):a),{previousPrompts:d}},onError:(b,c,d)=>{d?.previousPrompts&&d.previousPrompts.forEach(([b,c])=>{a.setQueryData(b,c)})},onSettled:b=>{b&&a.invalidateQueries({queryKey:["prompts",b.project_id]})}})}function m(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async a=>{let b=a.filter(a=>void 0!==a.order_index||void 0!==a.task_code),c=a.filter(a=>void 0===a.order_index&&void 0===a.task_code),d=[];if(b.length>0)try{let{data:a,error:c}=await g.L.rpc("bulk_update_prompts_order",{prompt_updates:b});if(c){console.warn("RPC function failed, falling back to individual updates:",c);let a=b.map(async a=>{let{data:b,error:c}=await g.L.from("prompts").update({...a,updated_at:new Date().toISOString()}).eq("id",a.id).select().single();if(c)throw Error(`Failed to update prompt ${a.id}: ${c.message}`);return b}),e=await Promise.all(a);d.push(...e)}else if(a){let b=a.map(a=>a.id),{data:c,error:e}=await g.L.from("prompts").select("*").in("id",b);if(e)throw Error(`Failed to fetch updated prompts: ${e.message}`);d.push(...c||[])}}catch(a){throw console.error("Bulk update error:",a),a}if(c.length>0){let a=c.map(async a=>{let{data:b,error:c}=await g.L.from("prompts").update({...a,updated_at:new Date().toISOString()}).eq("id",a.id).select().single();if(c)throw Error(`Failed to update prompt ${a.id}: ${c.message}`);return b}),b=await Promise.all(a);d.push(...b)}return d},onMutate:async b=>{let c=["prompts"];await a.cancelQueries({queryKey:c});let d=a.getQueriesData({queryKey:c});return a.setQueriesData({queryKey:c},a=>a&&Array.isArray(a)?a.map(a=>{let c=b.find(b=>b.id===a.id);return c?{...a,...c}:a}):a),{previousPrompts:d}},onError:(b,c,d)=>{d?.previousPrompts&&d.previousPrompts.forEach(([b,c])=>{a.setQueryData(b,c)})},onSettled:b=>{b&&b.length>0&&a.invalidateQueries({queryKey:["prompts",b[0].project_id]})}})}},34729:(a,b,c)=>{c.d(b,{T:()=>f});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("textarea",{"data-slot":"textarea",className:(0,e.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...b})}},64021:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},78272:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])}};