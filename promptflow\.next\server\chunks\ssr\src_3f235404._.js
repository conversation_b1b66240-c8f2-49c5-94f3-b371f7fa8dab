module.exports = {

"[project]/src/components/ui/textarea.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Textarea": ()=>Textarea
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
function Textarea({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
        "data-slot": "textarea",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/textarea.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
;
}),
"[project]/src/components/ui/separator.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Separator": ()=>Separator
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$separator$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-separator/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
function Separator({ className, orientation = "horizontal", decorative = true, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$separator$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "separator",
        decorative: decorative,
        orientation: orientation,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/separator.tsx",
        lineNumber: 15,
        columnNumber: 5
    }, this);
}
;
}),
"[project]/src/store/app-store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "useAppStore": ()=>useAppStore
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
const useAppStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set)=>({
        activeProjectId: null,
        setActiveProjectId: (projectId)=>set({
                activeProjectId: projectId
            }),
        isContextEnabled: true,
        setIsContextEnabled: (enabled)=>set({
                isContextEnabled: enabled
            }),
        // Sidebar collapse states
        isProjectSidebarCollapsed: false,
        setIsProjectSidebarCollapsed: (collapsed)=>set({
                isProjectSidebarCollapsed: collapsed
            }),
        isContextSidebarCollapsed: false,
        setIsContextSidebarCollapsed: (collapsed)=>set({
                isContextSidebarCollapsed: collapsed
            })
    }), {
    name: 'promptflow-app-store'
}));
}),
"[project]/src/lib/plan-limits.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Plan Limitleri Kontrol Middleware ve Utility Fonksiyonları
__turbopack_context__.s({
    "PLAN_LIMIT_MESSAGES": ()=>PLAN_LIMIT_MESSAGES,
    "canCreateProject": ()=>canCreateProject,
    "canCreatePrompt": ()=>canCreatePrompt,
    "checkPlanStatus": ()=>checkPlanStatus,
    "checkUserLimits": ()=>checkUserLimits,
    "getPlanUpgradeSuggestion": ()=>getPlanUpgradeSuggestion,
    "getUserActivePlan": ()=>getUserActivePlan,
    "hasPlanFeature": ()=>hasPlanFeature,
    "updateUsageStats": ()=>updateUsageStats
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase-browser.ts [app-ssr] (ecmascript)");
;
async function checkUserLimits() {
    try {
        const { data: user } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].auth.getUser();
        if (!user.user) {
            throw new Error('Kullanıcı oturumu bulunamadı');
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].rpc('check_user_limits', {
            user_uuid: user.user.id
        });
        if (error) {
            console.error('Plan limitleri kontrol edilemedi:', error);
            throw new Error(error.message);
        }
        return data?.[0] || null;
    } catch (error) {
        console.error('Plan limitleri kontrol hatası:', error);
        return null;
    }
}
async function getUserActivePlan() {
    try {
        const { data: user } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].auth.getUser();
        if (!user.user) {
            throw new Error('Kullanıcı oturumu bulunamadı');
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].rpc('get_user_active_plan', {
            user_uuid: user.user.id
        });
        if (error) {
            console.error('Aktif plan getirilemedi:', error);
            throw new Error(error.message);
        }
        return data?.[0] || null;
    } catch (error) {
        console.error('Aktif plan getirme hatası:', error);
        return null;
    }
}
async function canCreateProject() {
    const limits = await checkUserLimits();
    if (!limits) {
        return {
            allowed: false,
            reason: 'Plan bilgileri alınamadı'
        };
    }
    if (!limits.can_create_project) {
        return {
            allowed: false,
            reason: `Proje limiti aşıldı (${limits.current_projects}/${limits.max_projects})`
        };
    }
    return {
        allowed: true
    };
}
async function canCreatePrompt(projectId) {
    const limits = await checkUserLimits();
    if (!limits) {
        return {
            allowed: false,
            reason: 'Plan bilgileri alınamadı'
        };
    }
    if (!limits.can_create_prompt) {
        return {
            allowed: false,
            reason: `Prompt limiti aşıldı (${limits.current_prompts}/${limits.max_prompts_per_project})`
        };
    }
    // Eğer belirli bir proje için kontrol ediliyorsa, o projenin prompt sayısını kontrol et
    if (projectId) {
        try {
            const { count, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].from('prompts').select('*', {
                count: 'exact',
                head: true
            }).eq('project_id', projectId);
            if (error) {
                console.error('Proje prompt sayısı kontrol edilemedi:', error);
                return {
                    allowed: false,
                    reason: 'Proje bilgileri alınamadı'
                };
            }
            const currentPrompts = count || 0;
            if (limits.max_prompts_per_project !== -1 && currentPrompts >= limits.max_prompts_per_project) {
                return {
                    allowed: false,
                    reason: `Bu proje için prompt limiti aşıldı (${currentPrompts}/${limits.max_prompts_per_project})`
                };
            }
        } catch (error) {
            console.error('Proje prompt sayısı kontrol hatası:', error);
            return {
                allowed: false,
                reason: 'Proje bilgileri kontrol edilemedi'
            };
        }
    }
    return {
        allowed: true
    };
}
async function hasPlanFeature(featureName) {
    const plan = await getUserActivePlan();
    return plan?.features?.[featureName] === true;
}
async function updateUsageStats() {
    try {
        const { data: user } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].auth.getUser();
        if (!user.user) {
            throw new Error('Kullanıcı oturumu bulunamadı');
        }
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].rpc('update_usage_stats', {
            user_uuid: user.user.id
        });
        if (error) {
            console.error('Kullanım istatistikleri güncellenemedi:', error);
            throw new Error(error.message);
        }
    } catch (error) {
        console.error('Kullanım istatistikleri güncelleme hatası:', error);
        throw error;
    }
}
function getPlanUpgradeSuggestion(limits) {
    // Proje limiti %80'e ulaştıysa
    if (limits.max_projects !== -1 && limits.current_projects / limits.max_projects >= 0.8) {
        return {
            shouldUpgrade: true,
            reason: 'Proje limitinizin %80\'ine ulaştınız',
            suggestedPlan: 'professional'
        };
    }
    // Prompt limiti %80'e ulaştıysa
    if (limits.max_prompts_per_project !== -1 && limits.current_prompts / limits.max_prompts_per_project >= 0.8) {
        return {
            shouldUpgrade: true,
            reason: 'Prompt limitinizin %80\'ine ulaştınız',
            suggestedPlan: 'professional'
        };
    }
    return {
        shouldUpgrade: false,
        reason: '',
        suggestedPlan: ''
    };
}
const PLAN_LIMIT_MESSAGES = {
    PROJECT_LIMIT_REACHED: 'Proje oluşturma limitinize ulaştınız. Daha fazla proje oluşturmak için planınızı yükseltin.',
    PROMPT_LIMIT_REACHED: 'Prompt oluşturma limitinize ulaştınız. Daha fazla prompt oluşturmak için planınızı yükseltin.',
    FEATURE_NOT_AVAILABLE: 'Bu özellik mevcut planınızda bulunmamaktadır. Planınızı yükseltin.',
    PLAN_EXPIRED: 'Planınızın süresi dolmuştur. Lütfen planınızı yenileyin.',
    PLAN_SUSPENDED: 'Hesabınız askıya alınmıştır. Lütfen destek ekibi ile iletişime geçin.'
};
async function checkPlanStatus() {
    const plan = await getUserActivePlan();
    if (!plan) {
        return {
            isActive: false,
            status: 'no_plan',
            message: 'Aktif plan bulunamadı'
        };
    }
    if (plan.status !== 'active') {
        return {
            isActive: false,
            status: plan.status,
            message: PLAN_LIMIT_MESSAGES.PLAN_SUSPENDED
        };
    }
    if (plan.expires_at && new Date(plan.expires_at) < new Date()) {
        return {
            isActive: false,
            status: 'expired',
            message: PLAN_LIMIT_MESSAGES.PLAN_EXPIRED
        };
    }
    return {
        isActive: true,
        status: 'active'
    };
}
}),
"[project]/src/lib/project-validation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Proje adı validation ve güvenlik utilities
 * Güvenlik odaklı input validation ve sanitization
 */ // Proje adı limitleri ve kuralları
__turbopack_context__.s({
    "PROJECT_NAME_RULES": ()=>PROJECT_NAME_RULES,
    "checkClientRateLimit": ()=>checkClientRateLimit,
    "createAdvancedDebouncedValidator": ()=>createAdvancedDebouncedValidator,
    "createDebouncedValidator": ()=>createDebouncedValidator,
    "escapeHtml": ()=>escapeHtml,
    "formatValidationError": ()=>formatValidationError,
    "isSameProjectName": ()=>isSameProjectName,
    "resetClientRateLimit": ()=>resetClientRateLimit,
    "sanitizeProjectName": ()=>sanitizeProjectName,
    "validateProjectName": ()=>validateProjectName,
    "validateProjectNameUnique": ()=>validateProjectNameUnique
});
const PROJECT_NAME_RULES = {
    minLength: 3,
    maxLength: 50,
    allowedCharsRegex: /^[a-zA-Z0-9\s\-_.]+$/,
    debounceMs: 300,
    rateLimit: {
        maxRequests: 10,
        windowMinutes: 1
    }
};
// Rate limiting için local storage key
const RATE_LIMIT_KEY = 'project_name_update_rate_limit';
function sanitizeProjectName(name) {
    if (!name) return '';
    // Trim ve normalize
    let sanitized = name.trim();
    // Çoklu boşlukları tek boşluğa çevir
    sanitized = sanitized.replace(/\s+/g, ' ');
    // Başlangıç ve bitiş boşluklarını kaldır
    sanitized = sanitized.trim();
    return sanitized;
}
function validateProjectName(name) {
    const sanitized = sanitizeProjectName(name);
    // Boş kontrol
    if (!sanitized) {
        return {
            isValid: false,
            error: 'Proje adı boş olamaz'
        };
    }
    // Uzunluk kontrol
    if (sanitized.length < PROJECT_NAME_RULES.minLength) {
        return {
            isValid: false,
            error: `Proje adı en az ${PROJECT_NAME_RULES.minLength} karakter olmalıdır`
        };
    }
    if (sanitized.length > PROJECT_NAME_RULES.maxLength) {
        return {
            isValid: false,
            error: `Proje adı en fazla ${PROJECT_NAME_RULES.maxLength} karakter olabilir`
        };
    }
    // Karakter kontrol
    if (!PROJECT_NAME_RULES.allowedCharsRegex.test(sanitized)) {
        return {
            isValid: false,
            error: 'Proje adı sadece harf, rakam, boşluk ve -_. karakterlerini içerebilir'
        };
    }
    // Sadece boşluk kontrolü
    if (sanitized.replace(/\s/g, '').length === 0) {
        return {
            isValid: false,
            error: 'Proje adı sadece boşluk karakterlerinden oluşamaz'
        };
    }
    return {
        isValid: true,
        sanitizedValue: sanitized
    };
}
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
function checkClientRateLimit() {
    try {
        const stored = localStorage.getItem(RATE_LIMIT_KEY);
        const now = Date.now();
        if (!stored) {
            // İlk istek
            localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({
                count: 1,
                windowStart: now
            }));
            return true;
        }
        const data = JSON.parse(stored);
        const windowDuration = PROJECT_NAME_RULES.rateLimit.windowMinutes * 60 * 1000; // ms
        // Pencere süresi dolmuş mu?
        if (now - data.windowStart > windowDuration) {
            // Yeni pencere başlat
            localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({
                count: 1,
                windowStart: now
            }));
            return true;
        }
        // Limit kontrolü
        if (data.count >= PROJECT_NAME_RULES.rateLimit.maxRequests) {
            return false;
        }
        // Sayacı artır
        localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({
            count: data.count + 1,
            windowStart: data.windowStart
        }));
        return true;
    } catch (error) {
        console.warn('Rate limit check failed:', error);
        return true; // Hata durumunda izin ver
    }
}
function resetClientRateLimit() {
    try {
        localStorage.removeItem(RATE_LIMIT_KEY);
    } catch (error) {
        console.warn('Rate limit reset failed:', error);
    }
}
function createDebouncedValidator(validator, delay = PROJECT_NAME_RULES.debounceMs) {
    let timeoutId;
    return (value, callback)=>{
        clearTimeout(timeoutId);
        timeoutId = setTimeout(()=>{
            const result = validator(value);
            callback(result);
        }, delay);
    };
}
function isSameProjectName(name1, name2) {
    const sanitized1 = sanitizeProjectName(name1).toLowerCase();
    const sanitized2 = sanitizeProjectName(name2).toLowerCase();
    return sanitized1 === sanitized2;
}
async function validateProjectNameUnique(name, currentProjectId, projects) {
    const sanitized = sanitizeProjectName(name);
    // Önce temel validation
    const basicValidation = validateProjectName(name);
    if (!basicValidation.isValid) {
        return basicValidation;
    }
    // Duplicate kontrolü (case-insensitive)
    const isDuplicate = projects.some((project)=>project.id !== currentProjectId && isSameProjectName(project.name, sanitized));
    if (isDuplicate) {
        return {
            isValid: false,
            error: 'Bu isimde bir proje zaten mevcut'
        };
    }
    return {
        isValid: true,
        sanitizedValue: sanitized
    };
}
function formatValidationError(error) {
    // Teknik hataları kullanıcı dostu mesajlara çevir
    const errorMap = {
        'unique_violation': 'Bu isimde bir proje zaten mevcut',
        'check_violation': 'Proje adı geçersiz karakterler içeriyor',
        'not_null_violation': 'Proje adı boş olamaz',
        'foreign_key_violation': 'Geçersiz proje referansı',
        'RateLimitExceeded': 'Çok fazla güncelleme isteği. Lütfen bekleyin.',
        'InvalidInput': 'Geçersiz proje adı',
        'DuplicateName': 'Bu isimde bir proje zaten mevcut',
        'NotFound': 'Proje bulunamadı',
        'Unauthorized': 'Bu işlem için yetkiniz yok'
    };
    return errorMap[error] || error;
}
function createAdvancedDebouncedValidator(projects, currentProjectId, delay = PROJECT_NAME_RULES.debounceMs) {
    let timeoutId;
    return (value, callback)=>{
        clearTimeout(timeoutId);
        timeoutId = setTimeout(async ()=>{
            const result = await validateProjectNameUnique(value, currentProjectId, projects);
            callback(result);
        }, delay);
    };
}
}),
"[project]/src/lib/rate-limiter.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ClientRateLimiter": ()=>ClientRateLimiter,
    "RATE_LIMITS": ()=>RATE_LIMITS,
    "RateLimitError": ()=>RateLimitError,
    "ServerRateLimiter": ()=>ServerRateLimiter,
    "checkRateLimit": ()=>checkRateLimit,
    "clientRateLimiter": ()=>clientRateLimiter,
    "serverRateLimiter": ()=>serverRateLimiter,
    "useRateLimit": ()=>useRateLimit
});
/**
 * Advanced Rate Limiting System for PromptFlow
 * Provides both client-side and server-side rate limiting
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase-browser.ts [app-ssr] (ecmascript)");
'use client';
;
const RATE_LIMITS = {
    // Authentication actions
    login: {
        maxRequests: 5,
        windowMinutes: 15
    },
    signup: {
        maxRequests: 3,
        windowMinutes: 60
    },
    password_reset: {
        maxRequests: 3,
        windowMinutes: 60
    },
    // Project actions
    project_create: {
        maxRequests: 10,
        windowMinutes: 60
    },
    project_update: {
        maxRequests: 20,
        windowMinutes: 10
    },
    project_delete: {
        maxRequests: 5,
        windowMinutes: 60
    },
    // Prompt actions
    prompt_create: {
        maxRequests: 50,
        windowMinutes: 10
    },
    prompt_update: {
        maxRequests: 100,
        windowMinutes: 10
    },
    prompt_delete: {
        maxRequests: 20,
        windowMinutes: 10
    },
    // Context actions
    context_create: {
        maxRequests: 20,
        windowMinutes: 10
    },
    context_batch_add: {
        maxRequests: 5,
        windowMinutes: 10
    },
    // Plan actions
    plan_change: {
        maxRequests: 3,
        windowMinutes: 60
    },
    // General API
    api_general: {
        maxRequests: 200,
        windowMinutes: 10
    }
};
class ClientRateLimiter {
    getStorageKey(action, userId) {
        const userSuffix = userId ? `_${userId}` : '';
        return `rate_limit_${action}${userSuffix}`;
    }
    /**
   * Check if action is allowed based on rate limits
   */ async checkLimit(action, userId) {
        try {
            const config = RATE_LIMITS[action];
            const storageKey = this.getStorageKey(action, userId);
            const now = Date.now();
            const windowMs = config.windowMinutes * 60 * 1000;
            // Get current data
            const stored = localStorage.getItem(storageKey);
            let data;
            if (!stored) {
                // First request
                data = {
                    count: 1,
                    windowStart: now,
                    lastRequest: now
                };
                localStorage.setItem(storageKey, JSON.stringify(data));
                return {
                    allowed: true,
                    remainingRequests: config.maxRequests - 1,
                    resetTime: now + windowMs
                };
            }
            data = JSON.parse(stored);
            // Check if window has expired
            if (now - data.windowStart > windowMs) {
                // Reset window
                data = {
                    count: 1,
                    windowStart: now,
                    lastRequest: now
                };
                localStorage.setItem(storageKey, JSON.stringify(data));
                return {
                    allowed: true,
                    remainingRequests: config.maxRequests - 1,
                    resetTime: now + windowMs
                };
            }
            // Check if limit exceeded
            if (data.count >= config.maxRequests) {
                const resetTime = data.windowStart + windowMs;
                const retryAfter = Math.ceil((resetTime - now) / 1000);
                return {
                    allowed: false,
                    remainingRequests: 0,
                    resetTime,
                    retryAfter
                };
            }
            // Increment counter
            data.count++;
            data.lastRequest = now;
            localStorage.setItem(storageKey, JSON.stringify(data));
            return {
                allowed: true,
                remainingRequests: config.maxRequests - data.count,
                resetTime: data.windowStart + windowMs
            };
        } catch (error) {
            console.warn(`Rate limit check failed for ${action}:`, error);
            // On error, allow the request but log it
            return {
                allowed: true,
                remainingRequests: 0,
                resetTime: Date.now() + 60000 // 1 minute fallback
            };
        }
    }
    /**
   * Reset rate limit for specific action
   */ resetLimit(action, userId) {
        try {
            const storageKey = this.getStorageKey(action, userId);
            localStorage.removeItem(storageKey);
        } catch (error) {
            console.warn(`Rate limit reset failed for ${action}:`, error);
        }
    }
    /**
   * Get current rate limit status
   */ getStatus(action, userId) {
        try {
            const config = RATE_LIMITS[action];
            const storageKey = this.getStorageKey(action, userId);
            const stored = localStorage.getItem(storageKey);
            if (!stored) {
                return {
                    count: 0,
                    remaining: config.maxRequests,
                    resetTime: Date.now() + config.windowMinutes * 60 * 1000
                };
            }
            const data = JSON.parse(stored);
            const windowMs = config.windowMinutes * 60 * 1000;
            const now = Date.now();
            // Check if window expired
            if (now - data.windowStart > windowMs) {
                return {
                    count: 0,
                    remaining: config.maxRequests,
                    resetTime: now + windowMs
                };
            }
            return {
                count: data.count,
                remaining: Math.max(0, config.maxRequests - data.count),
                resetTime: data.windowStart + windowMs
            };
        } catch (error) {
            console.warn(`Rate limit status check failed for ${action}:`, error);
            return null;
        }
    }
}
class ServerRateLimiter {
    /**
   * Check server-side rate limit using Supabase function
   */ async checkLimit(action, userId) {
        try {
            const config = RATE_LIMITS[action];
            // Get current user if not provided
            let targetUserId = userId;
            if (!targetUserId) {
                const { data: { user } } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].auth.getUser();
                if (!user) {
                    throw new Error('User not authenticated');
                }
                targetUserId = user.id;
            }
            // Call Supabase rate limiting function
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].rpc('check_rate_limit', {
                p_user_id: targetUserId,
                p_action_type: action,
                p_max_requests: config.maxRequests,
                p_window_minutes: config.windowMinutes
            });
            if (error) {
                console.error(`Server rate limit check failed for ${action}:`, error);
                // On error, allow but log
                return {
                    allowed: true,
                    remainingRequests: 0,
                    resetTime: Date.now() + config.windowMinutes * 60 * 1000
                };
            }
            const allowed = data === true;
            const windowMs = config.windowMinutes * 60 * 1000;
            const resetTime = Date.now() + windowMs;
            if (!allowed) {
                return {
                    allowed: false,
                    remainingRequests: 0,
                    resetTime,
                    retryAfter: Math.ceil(windowMs / 1000)
                };
            }
            return {
                allowed: true,
                remainingRequests: config.maxRequests - 1,
                resetTime
            };
        } catch (error) {
            console.error(`Server rate limit error for ${action}:`, error);
            // On error, allow but log
            return {
                allowed: true,
                remainingRequests: 0,
                resetTime: Date.now() + 60000
            };
        }
    }
}
const clientRateLimiter = new ClientRateLimiter();
const serverRateLimiter = new ServerRateLimiter();
async function checkRateLimit(action, options = {}) {
    const { clientOnly = false, serverOnly = false, userId } = options;
    try {
        // Client-side check
        if (!serverOnly) {
            const clientResult = await clientRateLimiter.checkLimit(action, userId);
            if (!clientResult.allowed) {
                return {
                    ...clientResult,
                    source: 'client'
                };
            }
            if (clientOnly) {
                return {
                    ...clientResult,
                    source: 'client'
                };
            }
        }
        // Server-side check
        if (!clientOnly) {
            const serverResult = await serverRateLimiter.checkLimit(action, userId);
            return {
                ...serverResult,
                source: serverOnly ? 'server' : 'both'
            };
        }
        // Fallback (shouldn't reach here)
        return {
            allowed: true,
            remainingRequests: 0,
            resetTime: Date.now() + 60000,
            source: 'client'
        };
    } catch (error) {
        console.error(`Rate limit check failed for ${action}:`, error);
        return {
            allowed: true,
            remainingRequests: 0,
            resetTime: Date.now() + 60000,
            source: 'client'
        };
    }
}
class RateLimitError extends Error {
    action;
    retryAfter;
    resetTime;
    constructor(action, retryAfter, resetTime){
        super(`Rate limit exceeded for ${action}. Try again in ${retryAfter} seconds.`), this.action = action, this.retryAfter = retryAfter, this.resetTime = resetTime;
        this.name = 'RateLimitError';
    }
}
function useRateLimit(action) {
    return {
        checkLimit: (options)=>checkRateLimit(action, options),
        resetLimit: ()=>clientRateLimiter.resetLimit(action),
        getStatus: ()=>clientRateLimiter.getStatus(action)
    };
}
}),
"[project]/src/hooks/use-projects.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "useCreateProject": ()=>useCreateProject,
    "useDeleteProject": ()=>useDeleteProject,
    "useProject": ()=>useProject,
    "useProjects": ()=>useProjects,
    "useUpdateProject": ()=>useUpdateProject,
    "useUpdateProjectNameSecure": ()=>useUpdateProjectNameSecure
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase-browser.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$plan$2d$limits$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/plan-limits.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$project$2d$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/project-validation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rate$2d$limiter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/rate-limiter.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
function useProjects() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            'projects'
        ],
        queryFn: async ()=>{
            console.log(`📁 [USE_PROJECTS] Fetching projects...`);
            try {
                // Check if we have a session first
                const { data: { session }, error: sessionError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].auth.getSession();
                console.log(`📁 [USE_PROJECTS] Session check:`, {
                    hasSession: !!session,
                    sessionError: sessionError?.message,
                    userId: session?.user?.id
                });
                const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].from('projects').select('*').order('created_at', {
                    ascending: false
                });
                if (error) {
                    console.error(`❌ [USE_PROJECTS] Error fetching projects:`, error);
                    throw new Error(error.message);
                }
                console.log(`✅ [USE_PROJECTS] Projects fetched:`, data?.length || 0, 'projects');
                return data || [];
            } catch (err) {
                console.error(`💥 [USE_PROJECTS] Exception:`, err);
                throw err;
            }
        }
    });
}
function useProject(projectId) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            'project',
            projectId
        ],
        queryFn: async ()=>{
            if (!projectId) return null;
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].from('projects').select('*').eq('id', projectId).single();
            if (error) {
                throw new Error(error.message);
            }
            return data;
        },
        enabled: !!projectId
    });
}
function useCreateProject() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (project)=>{
            // Plan limiti kontrol et
            const limitCheck = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$plan$2d$limits$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["canCreateProject"])();
            if (!limitCheck.allowed) {
                throw new Error(limitCheck.reason || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$plan$2d$limits$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PLAN_LIMIT_MESSAGES"].PROJECT_LIMIT_REACHED);
            }
            const { data: { user }, error: userError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].auth.getUser();
            if (userError || !user) {
                throw new Error('Kullanıcı oturumu bulunamadı');
            }
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].from('projects').insert({
                ...project,
                user_id: user.id
            }).select().single();
            if (error) {
                throw new Error(error.message);
            }
            return data;
        },
        onSuccess: async ()=>{
            queryClient.invalidateQueries({
                queryKey: [
                    'projects'
                ]
            });
            // Kullanım istatistiklerini güncelle
            try {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$plan$2d$limits$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateUsageStats"])();
                queryClient.invalidateQueries({
                    queryKey: [
                        'user-limits'
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        'usage-stats'
                    ]
                });
            } catch (error) {
                console.warn('Kullanım istatistikleri güncellenemedi:', error);
            }
        }
    });
}
function useUpdateProject() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ id, ...updates })=>{
            console.log('🔄 [UPDATE_PROJECT] Starting update:', {
                id,
                updates
            });
            // Önce mevcut kullanıcıyı kontrol et
            const { data: user } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].auth.getUser();
            if (!user.user) {
                throw new Error('Kullanıcı girişi gerekli');
            }
            // Güncelleme işlemini yap - RLS politikaları otomatik olarak kontrol edecek
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].from('projects').update({
                ...updates,
                updated_at: new Date().toISOString()
            }).eq('id', id).eq('user_id', user.user.id) // Ekstra güvenlik için user_id kontrolü
            .select().single();
            if (error) {
                console.error('❌ [UPDATE_PROJECT] Database error:', error);
                throw new Error(`Proje güncellenirken hata oluştu: ${error.message}`);
            }
            if (!data) {
                throw new Error('Proje bulunamadı veya güncelleme yetkisi yok');
            }
            console.log('✅ [UPDATE_PROJECT] Success:', data);
            return data;
        },
        onSuccess: (data)=>{
            queryClient.invalidateQueries({
                queryKey: [
                    'projects'
                ]
            });
            queryClient.invalidateQueries({
                queryKey: [
                    'project',
                    data.id
                ]
            });
        },
        onError: (error)=>{
            console.error('❌ [UPDATE_PROJECT] Mutation error:', error);
        }
    });
}
function useUpdateProjectNameSecure() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ projectId, newName })=>{
            console.log(`🔒 [UPDATE_PROJECT_NAME] Starting secure update:`, {
                projectId,
                newName
            });
            // Advanced rate limiting check
            const rateLimitResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rate$2d$limiter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["checkRateLimit"])('project_update');
            if (!rateLimitResult.allowed) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rate$2d$limiter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RateLimitError"]('project_update', rateLimitResult.retryAfter || 60, rateLimitResult.resetTime);
            }
            // Client-side validation
            const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$project$2d$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateProjectName"])(newName);
            if (!validation.isValid) {
                throw new Error(validation.error || 'Geçersiz proje adı');
            }
            const sanitizedName = validation.sanitizedValue;
            try {
                // Güvenli database function'ını çağır
                const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].rpc('update_project_name_secure', {
                    p_project_id: projectId,
                    p_new_name: sanitizedName
                });
                if (error) {
                    console.error(`❌ [UPDATE_PROJECT_NAME] Database error:`, error);
                    throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$project$2d$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatValidationError"])(error.message));
                }
                // Function response kontrolü
                if (!data?.success) {
                    console.error(`❌ [UPDATE_PROJECT_NAME] Function error:`, data);
                    throw new Error(data?.message || 'Proje adı güncellenemedi');
                }
                console.log(`✅ [UPDATE_PROJECT_NAME] Success:`, data.data);
                return data.data;
            } catch (err) {
                console.error(`💥 [UPDATE_PROJECT_NAME] Exception:`, err);
                // Error mesajını kullanıcı dostu hale getir
                let errorMessage = err instanceof Error ? err.message : 'Bilinmeyen hata';
                if (errorMessage.includes('unique_violation') || errorMessage.includes('DuplicateName')) {
                    errorMessage = 'Bu isimde bir proje zaten mevcut';
                } else if (errorMessage.includes('check_violation') || errorMessage.includes('InvalidInput')) {
                    errorMessage = 'Proje adı geçersiz karakterler içeriyor';
                } else if (errorMessage.includes('RateLimitExceeded')) {
                    errorMessage = 'Çok fazla güncelleme isteği. Lütfen bekleyin.';
                }
                throw new Error(errorMessage);
            }
        },
        onMutate: async ({ projectId, newName })=>{
            console.log(`🚀 [UPDATE_PROJECT_NAME] Starting optimistic update:`, {
                projectId,
                newName
            });
            // Cancel outgoing refetches
            await queryClient.cancelQueries({
                queryKey: [
                    'projects'
                ]
            });
            await queryClient.cancelQueries({
                queryKey: [
                    'project',
                    projectId
                ]
            });
            // Snapshot previous values
            const previousProjects = queryClient.getQueryData([
                'projects'
            ]);
            const previousProject = queryClient.getQueryData([
                'project',
                projectId
            ]);
            // Optimistically update projects list
            if (previousProjects) {
                queryClient.setQueryData([
                    'projects'
                ], (old)=>{
                    if (!old) return old;
                    return old.map((project)=>project.id === projectId ? {
                            ...project,
                            name: newName.trim(),
                            updated_at: new Date().toISOString()
                        } : project);
                });
            }
            // Optimistically update single project
            if (previousProject) {
                queryClient.setQueryData([
                    'project',
                    projectId
                ], {
                    ...previousProject,
                    name: newName.trim(),
                    updated_at: new Date().toISOString()
                });
            }
            return {
                previousProjects,
                previousProject
            };
        },
        onSuccess: (data)=>{
            console.log(`✅ [UPDATE_PROJECT_NAME] Success, updating cache with server data:`, data.id);
            // Update with actual server data
            queryClient.setQueryData([
                'projects'
            ], (old)=>{
                if (!old) return old;
                return old.map((project)=>project.id === data.id ? {
                        ...project,
                        ...data
                    } : project);
            });
            queryClient.setQueryData([
                'project',
                data.id
            ], data);
        },
        onError: (error, variables, context)=>{
            console.error(`💥 [UPDATE_PROJECT_NAME] Error, rolling back:`, error);
            // Rollback optimistic updates
            if (context?.previousProjects) {
                queryClient.setQueryData([
                    'projects'
                ], context.previousProjects);
            }
            if (context?.previousProject) {
                queryClient.setQueryData([
                    'project',
                    variables.projectId
                ], context.previousProject);
            }
        },
        onSettled: ()=>{
            // Always refetch to ensure consistency
            queryClient.invalidateQueries({
                queryKey: [
                    'projects'
                ]
            });
        }
    });
}
function useDeleteProject() {
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (projectId)=>{
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2d$browser$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabaseBrowser"].from('projects').delete().eq('id', projectId);
            if (error) {
                throw new Error(error.message);
            }
        },
        onSuccess: async ()=>{
            queryClient.invalidateQueries({
                queryKey: [
                    'projects'
                ]
            });
            // Kullanım istatistiklerini güncelle
            try {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$plan$2d$limits$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateUsageStats"])();
                queryClient.invalidateQueries({
                    queryKey: [
                        'user-limits'
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        'usage-stats'
                    ]
                });
            } catch (error) {
                console.warn('Kullanım istatistikleri güncellenemedi:', error);
            }
        }
    });
}
}),
"[project]/src/components/context-sidebar.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ContextSidebar": ()=>ContextSidebar,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/textarea.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-ssr] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/save.js [app-ssr] (ecmascript) <export default as Save>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-ssr] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-ssr] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$toggle$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ToggleLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/toggle-left.js [app-ssr] (ecmascript) <export default as ToggleLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$toggle$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ToggleRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/toggle-right.js [app-ssr] (ecmascript) <export default as ToggleRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-ssr] (ecmascript) <export default as ChevronLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-ssr] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$grid$2d$3x3$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Grid3X3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/grid-3x3.js [app-ssr] (ecmascript) <export default as Grid3X3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/app-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$projects$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-projects.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
function ContextSidebar({ onClose, isCollapsed = false, onToggleCollapse, isContextGalleryOpen = false, onToggleContextGallery }) {
    const [contextText, setContextText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [isSaving, setIsSaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [lastSaved, setLastSaved] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [deleteConfirmText, setDeleteConfirmText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const { activeProjectId, setActiveProjectId, isContextEnabled, setIsContextEnabled } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$app$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])();
    const { data: project } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$projects$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useProject"])(activeProjectId);
    const updateProjectMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$projects$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUpdateProject"])();
    const deleteProjectMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$projects$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDeleteProject"])();
    // Aktif proje değiştiğinde context'i yükle
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (project) {
            setContextText(project.context_text || "");
        } else {
            setContextText("");
        }
    }, [
        project
    ]);
    // Otomatik kayıt devre dışı bırakıldı - artık sadece manuel kayıt
    // useEffect(() => {
    //   if (!activeProjectId || !contextText.trim()) return;
    //   const timer = setTimeout(() => {
    //     handleSaveContext();
    //   }, 2000);
    //   return () => clearTimeout(timer);
    // // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, [contextText, activeProjectId]);
    const handleSaveContext = async ()=>{
        if (!activeProjectId) return;
        setIsSaving(true);
        try {
            await updateProjectMutation.mutateAsync({
                id: activeProjectId,
                context_text: contextText
            });
            setLastSaved(new Date());
        } catch (error) {
            console.error('Context kaydetme hatası:', error);
        } finally{
            setIsSaving(false);
        }
    };
    const handleClearContext = ()=>{
        setContextText("");
        setLastSaved(null);
    };
    const handleManualSave = ()=>{
        handleSaveContext();
    };
    const handleDeleteProject = async ()=>{
        if (!activeProjectId || !project) return;
        // Proje adı kontrolü
        if (deleteConfirmText !== project.name) {
            alert('Proje adını doğru yazın!');
            return;
        }
        try {
            await deleteProjectMutation.mutateAsync(activeProjectId);
            setActiveProjectId(null);
            setShowDeleteConfirm(false);
            setDeleteConfirmText("");
        } catch (error) {
            console.error('Proje silme hatası:', error);
            alert('Proje silinirken bir hata oluştu!');
        }
    };
    const handleCancelDelete = ()=>{
        setShowDeleteConfirm(false);
        setDeleteConfirmText("");
    };
    if (!activeProjectId) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center h-full p-6",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                        className: `${isCollapsed ? 'h-6 w-6' : 'h-12 w-12'} text-gray-400 mx-auto mb-4`
                    }, void 0, false, {
                        fileName: "[project]/src/components/context-sidebar.tsx",
                        lineNumber: 123,
                        columnNumber: 11
                    }, this),
                    !isCollapsed && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium text-gray-900 mb-2",
                                children: "Context Alanı"
                            }, void 0, false, {
                                fileName: "[project]/src/components/context-sidebar.tsx",
                                lineNumber: 126,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-500 text-sm",
                                children: "Proje seçtikten sonra context metninizi buraya yazabilirsiniz"
                            }, void 0, false, {
                                fileName: "[project]/src/components/context-sidebar.tsx",
                                lineNumber: 127,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/context-sidebar.tsx",
                lineNumber: 122,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/context-sidebar.tsx",
            lineNumber: 121,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col h-full",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `border-b border-gray-200 ${isCollapsed ? 'p-2' : 'p-4 lg:p-6'}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `flex items-center ${isCollapsed ? 'justify-center mb-2' : 'justify-between mb-4'}`,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `flex items-center gap-2 ${isCollapsed ? 'flex-col' : ''}`,
                            children: [
                                onToggleCollapse && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "ghost",
                                    size: "sm",
                                    onClick: onToggleCollapse,
                                    className: "hidden xl:flex",
                                    title: isCollapsed ? 'Ayarlar panelini genişlet' : 'Ayarlar panelini daralt',
                                    children: isCollapsed ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__["ChevronLeft"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/context-sidebar.tsx",
                                        lineNumber: 150,
                                        columnNumber: 32
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/context-sidebar.tsx",
                                        lineNumber: 150,
                                        columnNumber: 70
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                    lineNumber: 143,
                                    columnNumber: 15
                                }, this),
                                !isCollapsed && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-semibold text-gray-900",
                                    children: "Context Ayarları"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                    lineNumber: 153,
                                    columnNumber: 30
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/context-sidebar.tsx",
                            lineNumber: 140,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `flex items-center gap-2 ${isCollapsed ? 'flex-col' : ''}`,
                            children: onClose && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "ghost",
                                size: "sm",
                                onClick: onClose,
                                className: "xl:hidden",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                    lineNumber: 159,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/context-sidebar.tsx",
                                lineNumber: 158,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/context-sidebar.tsx",
                            lineNumber: 155,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/context-sidebar.tsx",
                    lineNumber: 139,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/context-sidebar.tsx",
                lineNumber: 138,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `flex-1 ${isCollapsed ? 'p-2' : 'p-6'}`,
                children: isCollapsed ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col items-center space-y-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                            variant: "ghost",
                            size: "sm",
                            onClick: ()=>setIsContextEnabled(!isContextEnabled),
                            className: `w-8 h-8 p-0 rounded-md transition-all ${isContextEnabled ? 'bg-green-100 text-green-700 hover:bg-green-200' : 'bg-gray-100 text-gray-500 hover:bg-gray-200'}`,
                            title: `Context ${isContextEnabled ? 'Aktif' : 'Pasif'}`,
                            children: isContextEnabled ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$toggle$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ToggleRight$3e$__["ToggleRight"], {
                                className: "h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/context-sidebar.tsx",
                                lineNumber: 184,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$toggle$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ToggleLeft$3e$__["ToggleLeft"], {
                                className: "h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/context-sidebar.tsx",
                                lineNumber: 186,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/context-sidebar.tsx",
                            lineNumber: 172,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                            variant: "ghost",
                            size: "sm",
                            onClick: handleSaveContext,
                            disabled: isSaving,
                            className: "w-8 h-8 p-0",
                            title: "Context Kaydet",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__["Save"], {
                                className: "h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/context-sidebar.tsx",
                                lineNumber: 197,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/context-sidebar.tsx",
                            lineNumber: 189,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/context-sidebar.tsx",
                    lineNumber: 171,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: isContextGalleryOpen ? "default" : "outline",
                                size: "sm",
                                onClick: onToggleContextGallery,
                                className: `w-full justify-center transition-all duration-200 ${isContextGalleryOpen ? "bg-purple-600 hover:bg-purple-700 text-white shadow-md" : "border-purple-200 text-purple-600 hover:text-purple-700 hover:border-purple-300 hover:bg-purple-50"}`,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$grid$2d$3x3$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Grid3X3$3e$__["Grid3X3"], {
                                        className: "h-4 w-4 mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/context-sidebar.tsx",
                                        lineNumber: 214,
                                        columnNumber: 15
                                    }, this),
                                    "Context Gallery"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/context-sidebar.tsx",
                                lineNumber: 204,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/context-sidebar.tsx",
                            lineNumber: 203,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between mb-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "text-sm font-medium text-gray-700",
                                            children: "Ön Tanımlı Metin"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                            lineNumber: 221,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "ghost",
                                            size: "sm",
                                            onClick: ()=>setIsContextEnabled(!isContextEnabled),
                                            className: `flex items-center gap-2 px-3 py-1 rounded-md transition-all ${isContextEnabled ? 'bg-green-100 text-green-700 hover:bg-green-200' : 'bg-gray-100 text-gray-500 hover:bg-gray-200'}`,
                                            children: [
                                                isContextEnabled ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$toggle$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ToggleRight$3e$__["ToggleRight"], {
                                                    className: "h-4 w-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                    lineNumber: 235,
                                                    columnNumber: 19
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$toggle$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ToggleLeft$3e$__["ToggleLeft"], {
                                                    className: "h-4 w-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                    lineNumber: 237,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-xs font-medium",
                                                    children: isContextEnabled ? 'Aktif' : 'Pasif'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                    lineNumber: 239,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                            lineNumber: 224,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                    lineNumber: 220,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        isSaving && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                            variant: "secondary",
                                            className: "text-xs",
                                            children: "Kaydediliyor..."
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                            lineNumber: 246,
                                            columnNumber: 17
                                        }, this),
                                        lastSaved && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-xs text-gray-500",
                                            children: [
                                                "Son kaydedilme: ",
                                                lastSaved.toLocaleTimeString('tr-TR')
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                            lineNumber: 251,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                    lineNumber: 244,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/context-sidebar.tsx",
                            lineNumber: 219,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Textarea"], {
                            placeholder: "Tüm promptların başına eklenecek context metninizi buraya yazın...",
                            value: contextText,
                            onChange: (e)=>setContextText(e.target.value),
                            className: "min-h-[200px] resize-none",
                            disabled: isSaving
                        }, void 0, false, {
                            fileName: "[project]/src/components/context-sidebar.tsx",
                            lineNumber: 260,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "default",
                                    size: "sm",
                                    onClick: handleManualSave,
                                    disabled: isSaving || !contextText.trim(),
                                    className: "bg-blue-600 hover:bg-blue-700 text-white",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__["Save"], {
                                            className: "h-4 w-4 mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                            lineNumber: 276,
                                            columnNumber: 15
                                        }, this),
                                        isSaving ? 'Kaydediliyor...' : 'Kaydet'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                    lineNumber: 269,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "outline",
                                    size: "sm",
                                    onClick: handleClearContext,
                                    disabled: isSaving,
                                    className: "text-red-600 hover:text-red-700",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                            className: "h-4 w-4 mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                            lineNumber: 286,
                                            columnNumber: 15
                                        }, this),
                                        "Temizle"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                    lineNumber: 279,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/context-sidebar.tsx",
                            lineNumber: 268,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {
                            className: "my-6"
                        }, void 0, false, {
                            fileName: "[project]/src/components/context-sidebar.tsx",
                            lineNumber: 291,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-base",
                                        children: "Proje Ayarları"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/context-sidebar.tsx",
                                        lineNumber: 296,
                                        columnNumber: 13
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                    lineNumber: 295,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                    className: "space-y-3",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-sm text-gray-600",
                                                    children: "Karakter Sayısı"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                    lineNumber: 302,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                                    variant: "outline",
                                                    children: contextText.length.toLocaleString()
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                    lineNumber: 303,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                            lineNumber: 301,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-sm text-gray-600",
                                                    children: "Proje Adı"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                    lineNumber: 308,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                                    variant: "outline",
                                                    children: project?.name
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                    lineNumber: 309,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                            lineNumber: 307,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                            lineNumber: 311,
                                            columnNumber: 13
                                        }, this),
                                        !showDeleteConfirm ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "outline",
                                            size: "sm",
                                            className: "w-full text-red-600 hover:text-red-700 hover:bg-red-50",
                                            onClick: ()=>setShowDeleteConfirm(true),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                    className: "h-4 w-4 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                    lineNumber: 321,
                                                    columnNumber: 17
                                                }, this),
                                                "Projeyi Sil"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                            lineNumber: 315,
                                            columnNumber: 15
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-3",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                                                            className: "h-4 w-4 text-red-600 flex-shrink-0"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                                            lineNumber: 327,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-sm text-red-800 font-medium",
                                                                    children: "Dikkat!"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                                    lineNumber: 329,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-xs text-red-700",
                                                                    children: "Bu işlem geri alınamaz. Proje ve tüm prompt'lar silinecek."
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                                    lineNumber: 330,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                                            lineNumber: 328,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                    lineNumber: 326,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            className: "text-sm font-medium text-gray-700",
                                                            children: [
                                                                "Onaylamak için proje adını yazın: ",
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-red-600 font-semibold",
                                                                    children: project?.name
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                                    lineNumber: 338,
                                                                    columnNumber: 55
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                                            lineNumber: 337,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                            type: "text",
                                                            value: deleteConfirmText,
                                                            onChange: (e)=>setDeleteConfirmText(e.target.value),
                                                            className: "w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500",
                                                            placeholder: "Proje adını yazın..."
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                                            lineNumber: 340,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                    lineNumber: 336,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                            variant: "outline",
                                                            size: "sm",
                                                            onClick: handleDeleteProject,
                                                            disabled: deleteConfirmText !== project?.name || deleteProjectMutation.isPending,
                                                            className: "flex-1 text-red-600 hover:text-red-700 hover:bg-red-50",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                                    className: "h-4 w-4 mr-2"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                                    lineNumber: 357,
                                                                    columnNumber: 21
                                                                }, this),
                                                                deleteProjectMutation.isPending ? 'Siliniyor...' : 'Sil'
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                                            lineNumber: 350,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                            variant: "outline",
                                                            size: "sm",
                                                            onClick: handleCancelDelete,
                                                            className: "flex-1",
                                                            children: "İptal"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                                            lineNumber: 360,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                                    lineNumber: 349,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/context-sidebar.tsx",
                                            lineNumber: 325,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/context-sidebar.tsx",
                                    lineNumber: 298,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/context-sidebar.tsx",
                            lineNumber: 294,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/context-sidebar.tsx",
                    lineNumber: 201,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/context-sidebar.tsx",
                lineNumber: 169,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/context-sidebar.tsx",
        lineNumber: 136,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = ContextSidebar;
}),

};

//# sourceMappingURL=src_3f235404._.js.map