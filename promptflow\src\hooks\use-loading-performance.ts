import { useState, useEffect, useCallback, useRef } from 'react'
import { useQueryClient } from '@tanstack/react-query'

interface LoadingState {
  isLoading: boolean
  progress: number
  stage: string
  error?: string
  startTime: number
  estimatedTime?: number
}

interface LoadingPerformanceOptions {
  stages?: string[]
  estimatedDuration?: number
  enableProgress?: boolean
  enablePrediction?: boolean
  cacheKey?: string
}

// Performance-optimized loading state management
export function useLoadingPerformance({
  stages = ['Başlatılıyor...', 'Veriler yükleniyor...', 'Tamamlanıyor...'],
  estimatedDuration = 3000,
  enableProgress = true,
  enablePrediction = true,
  cacheKey
}: LoadingPerformanceOptions = {}) {
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    progress: 0,
    stage: stages[0],
    startTime: 0
  })

  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const stageTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const performanceDataRef = useRef<Map<string, number[]>>(new Map())

  // Get cached performance data for predictions
  const getCachedPerformance = useCallback((key: string): number[] => {
    if (typeof window === 'undefined') return []
    
    try {
      const cached = localStorage.getItem(`loading_perf_${key}`)
      return cached ? JSON.parse(cached) : []
    } catch {
      return []
    }
  }, [])

  // Cache performance data
  const cachePerformance = useCallback((key: string, duration: number) => {
    if (typeof window === 'undefined') return

    try {
      const existing = getCachedPerformance(key)
      const updated = [...existing, duration].slice(-10) // Keep last 10 measurements
      localStorage.setItem(`loading_perf_${key}`, JSON.stringify(updated))
    } catch {
      // Ignore cache errors
    }
  }, [getCachedPerformance])

  // Predict loading time based on historical data
  const predictLoadingTime = useCallback((key?: string): number => {
    if (!enablePrediction || !key) return estimatedDuration

    const historicalData = getCachedPerformance(key)
    if (historicalData.length === 0) return estimatedDuration

    // Calculate weighted average (recent data has more weight)
    const weights = historicalData.map((_, index) => index + 1)
    const weightedSum = historicalData.reduce((sum, duration, index) => 
      sum + duration * weights[index], 0
    )
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0)

    return Math.round(weightedSum / totalWeight)
  }, [enablePrediction, estimatedDuration, getCachedPerformance])

  // Start loading with performance tracking
  const startLoading = useCallback((options?: { 
    customStages?: string[]
    customDuration?: number
    trackingKey?: string
  }) => {
    const startTime = Date.now()
    const currentStages = options?.customStages || stages
    const trackingKey = options?.trackingKey || cacheKey
    const predictedDuration = trackingKey 
      ? predictLoadingTime(trackingKey)
      : (options?.customDuration || estimatedDuration)

    setLoadingState({
      isLoading: true,
      progress: 0,
      stage: currentStages[0],
      startTime,
      estimatedTime: predictedDuration
    })

    if (enableProgress) {
      // Smooth progress animation
      let currentProgress = 0
      const progressIncrement = 100 / (predictedDuration / 50) // Update every 50ms
      
      progressIntervalRef.current = setInterval(() => {
        currentProgress += progressIncrement
        
        // Slow down progress as it approaches 100%
        const slowdownFactor = currentProgress > 80 ? 0.3 : 1
        const adjustedProgress = Math.min(currentProgress * slowdownFactor, 95)

        setLoadingState(prev => ({
          ...prev,
          progress: adjustedProgress
        }))

        if (adjustedProgress >= 95) {
          clearInterval(progressIntervalRef.current!)
        }
      }, 50)
    }

    // Auto-advance stages
    const stageInterval = predictedDuration / currentStages.length
    currentStages.forEach((stage, index) => {
      if (index > 0) {
        setTimeout(() => {
          setLoadingState(prev => ({
            ...prev,
            stage
          }))
        }, stageInterval * index)
      }
    })

    return { startTime, predictedDuration, trackingKey }
  }, [stages, estimatedDuration, enableProgress, cacheKey, predictLoadingTime])

  // Complete loading with performance caching
  const completeLoading = useCallback((trackingKey?: string) => {
    const endTime = Date.now()
    const duration = endTime - loadingState.startTime

    // Cache performance data
    if (trackingKey && duration > 0) {
      cachePerformance(trackingKey, duration)
    }

    setLoadingState(prev => ({
      ...prev,
      isLoading: false,
      progress: 100,
      stage: 'Tamamlandı'
    }))

    // Clear intervals
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current)
    }
    if (stageTimeoutRef.current) {
      clearTimeout(stageTimeoutRef.current)
    }

    // Reset after a short delay
    setTimeout(() => {
      setLoadingState(prev => ({
        ...prev,
        progress: 0,
        stage: stages[0]
      }))
    }, 500)

    return duration
  }, [loadingState.startTime, cachePerformance, stages])

  // Handle loading error
  const errorLoading = useCallback((error: string) => {
    setLoadingState(prev => ({
      ...prev,
      isLoading: false,
      error,
      stage: 'Hata oluştu'
    }))

    // Clear intervals
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current)
    }
    if (stageTimeoutRef.current) {
      clearTimeout(stageTimeoutRef.current)
    }
  }, [])

  // Update progress manually
  const updateProgress = useCallback((progress: number, stage?: string) => {
    setLoadingState(prev => ({
      ...prev,
      progress: Math.min(Math.max(progress, 0), 100),
      stage: stage || prev.stage
    }))
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current)
      }
      if (stageTimeoutRef.current) {
        clearTimeout(stageTimeoutRef.current)
      }
    }
  }, [])

  return {
    loadingState,
    startLoading,
    completeLoading,
    errorLoading,
    updateProgress,
    isLoading: loadingState.isLoading,
    progress: loadingState.progress,
    stage: loadingState.stage,
    error: loadingState.error,
    estimatedTimeRemaining: loadingState.estimatedTime 
      ? Math.max(0, loadingState.estimatedTime - (Date.now() - loadingState.startTime))
      : undefined
  }
}

// Hook for managing multiple concurrent loading states
export function useMultipleLoadingStates() {
  const [loadingStates, setLoadingStates] = useState<Map<string, LoadingState>>(new Map())

  const startLoading = useCallback((key: string, stage = 'Yükleniyor...') => {
    setLoadingStates(prev => new Map(prev.set(key, {
      isLoading: true,
      progress: 0,
      stage,
      startTime: Date.now()
    })))
  }, [])

  const updateLoading = useCallback((key: string, updates: Partial<LoadingState>) => {
    setLoadingStates(prev => {
      const current = prev.get(key)
      if (!current) return prev
      
      return new Map(prev.set(key, { ...current, ...updates }))
    })
  }, [])

  const completeLoading = useCallback((key: string) => {
    setLoadingStates(prev => {
      const newMap = new Map(prev)
      newMap.delete(key)
      return newMap
    })
  }, [])

  const isAnyLoading = Array.from(loadingStates.values()).some(state => state.isLoading)
  const loadingCount = Array.from(loadingStates.values()).filter(state => state.isLoading).length

  return {
    loadingStates: Object.fromEntries(loadingStates),
    startLoading,
    updateLoading,
    completeLoading,
    isAnyLoading,
    loadingCount
  }
}

// Hook for query-specific loading optimization
export function useQueryLoadingOptimization(queryKey: unknown[]) {
  const queryClient = useQueryClient()
  const [isOptimizing, setIsOptimizing] = useState(false)

  // Preload related queries
  const preloadRelatedQueries = useCallback(async (relatedKeys: unknown[][]) => {
    setIsOptimizing(true)
    
    try {
      await Promise.all(
        relatedKeys.map(key => 
          queryClient.prefetchQuery({
            queryKey: key,
            staleTime: 5 * 60 * 1000 // 5 minutes
          })
        )
      )
    } catch (error) {
      console.warn('Failed to preload related queries:', error)
    } finally {
      setIsOptimizing(false)
    }
  }, [queryClient])

  // Warm up cache for frequently accessed data
  const warmUpCache = useCallback(async (warmUpQueries: Array<{
    queryKey: unknown[]
    queryFn: () => Promise<unknown>
  }>) => {
    setIsOptimizing(true)

    try {
      await Promise.all(
        warmUpQueries.map(({ queryKey: key, queryFn }) =>
          queryClient.prefetchQuery({
            queryKey: key,
            queryFn,
            staleTime: 10 * 60 * 1000 // 10 minutes
          })
        )
      )
    } catch (error) {
      console.warn('Failed to warm up cache:', error)
    } finally {
      setIsOptimizing(false)
    }
  }, [queryClient])

  // Get cache statistics
  const getCacheStats = useCallback(() => {
    const cache = queryClient.getQueryCache()
    const queries = cache.getAll()
    
    return {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.getObserversCount() > 0).length,
      staleQueries: queries.filter(q => q.isStale()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length
    }
  }, [queryClient])

  return {
    preloadRelatedQueries,
    warmUpCache,
    getCacheStats,
    isOptimizing
  }
}
