/**
 * Simple Authentication API Route
 * Basic implementation for SEO build compatibility
 */

import { NextRequest, NextResponse } from "next/server"

// Security headers
const SECURITY_HEADERS = {
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",
  "Referrer-Policy": "strict-origin-when-cross-origin",
  "Content-Security-Policy": "default-src \"self\"",
  "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
}

/**
 * POST /api/auth - Authentication endpoint
 */
export async function POST(request: NextRequest) {
  return NextResponse.json(
    { message: "Auth endpoint - under development" },
    { status: 200, headers: SECURITY_HEADERS }
  )
}

/**
 * GET - Block GET method
 */
export async function GET() {
  return NextResponse.json(
    { error: "Method not allowed", code: "METHOD_NOT_ALLOWED" },
    { status: 405, headers: SECURITY_HEADERS }
  )
}
