'use client'

import { useState, useEffect, useRef, useCallback, memo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Edit2, Check, X, Loader2, CheckCircle } from 'lucide-react'
import { useUpdateProjectNameSecure, useProjects } from '@/hooks/use-projects'
import {
  createAdvancedDebouncedValidator,
  isSameProjectName
} from '@/lib/project-validation'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

interface ProjectNameEditorProps {
  projectId: string
  currentName: string
  onNameUpdated?: (newName: string) => void
  className?: string
  disabled?: boolean
}

const ProjectNameEditor = memo(function ProjectNameEditor({
  projectId,
  currentName,
  onNameUpdated,
  className,
  disabled = false
}: ProjectNameEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(currentName)
  const [validationError, setValidationError] = useState<string>('')
  const [isValidating, setIsValidating] = useState(false)
  const [validationSuccess, setValidationSuccess] = useState(false)
  
  const inputRef = useRef<HTMLInputElement>(null)
  const updateMutation = useUpdateProjectNameSecure()
  const { data: projects = [] } = useProjects()

  // Advanced debounced validator with duplicate check
  const debouncedValidator = useCallback(() => {
    return createAdvancedDebouncedValidator(projects, projectId, 300)
  }, [projects, projectId])();

  // Edit mode'a geçiş
  const startEditing = useCallback(() => {
    if (disabled || updateMutation.isPending) return
    
    setIsEditing(true)
    setEditValue(currentName)
    setValidationError('')
    setIsValidating(false)
    setValidationSuccess(false)
  }, [disabled, updateMutation.isPending, currentName])

  // Edit mode'dan çıkış
  const cancelEditing = useCallback(() => {
    setIsEditing(false)
    setEditValue(currentName)
    setValidationError('')
    setIsValidating(false)
    setValidationSuccess(false)
  }, [currentName])

  // Kaydetme işlemi
  const saveChanges = useCallback(async () => {
    if (!editValue.trim() || validationError || isValidating) {
      return
    }

    // Değişiklik var mı kontrol et
    if (isSameProjectName(editValue, currentName)) {
      cancelEditing()
      return
    }

    try {
      const result = await updateMutation.mutateAsync({
        projectId,
        newName: editValue.trim()
      })

      toast.success('Proje adı başarıyla güncellendi!')
      setIsEditing(false)
      onNameUpdated?.(result.name)
      
    } catch (error) {
      console.error('Project name update error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Proje adı güncellenirken bir hata oluştu'
      toast.error(errorMessage)
      
      // Input'a focus ver ki kullanıcı düzeltebilsin
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [editValue, validationError, isValidating, currentName, projectId, updateMutation, onNameUpdated, cancelEditing])

  // Input değişikliği
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setEditValue(value)
    
    if (value.trim()) {
      setIsValidating(true)
      setValidationSuccess(false)
      debouncedValidator(value, (result) => {
        setValidationError(result.isValid ? '' : result.error || '')
        setValidationSuccess(result.isValid && !isSameProjectName(value, currentName))
        setIsValidating(false)
      })
    } else {
      setValidationError('Proje adı boş olamaz')
      setValidationSuccess(false)
      setIsValidating(false)
    }
  }, [debouncedValidator, currentName])

  // Keyboard event handlers
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      saveChanges()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      cancelEditing()
    }
  }, [saveChanges, cancelEditing])

  // Auto-focus when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isEditing])

  // Update editValue when currentName changes
  useEffect(() => {
    if (!isEditing) {
      setEditValue(currentName)
    }
  }, [currentName, isEditing])

  // Editing mode UI
  if (isEditing) {
    return (
      <div className={cn("flex items-center gap-2 w-full", className)}>
        <div className="flex-1 relative">
          <Input
            ref={inputRef}
            value={editValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            className={cn(
              "text-sm font-medium",
              validationError && "border-red-500 focus:border-red-500",
              validationSuccess && "border-green-500 focus:border-green-500",
              "focus:ring-2 focus:ring-blue-500/20"
            )}
            placeholder="Proje adı..."
            disabled={updateMutation.isPending}
            maxLength={50}
            aria-label="Proje adını düzenle"
            aria-describedby={`project-name-feedback-${projectId}`}
            aria-invalid={!!validationError}
          />

          {/* Character counter */}
          <div className="absolute top-full right-0 mt-1 text-xs text-gray-400 z-10">
            <span className="sr-only">Karakter sayısı: </span>
            {editValue.length}/50
          </div>

          {/* Validation feedback */}
          {(validationError || isValidating || validationSuccess) && (
            <div
              id={`project-name-feedback-${projectId}`}
              className="absolute top-full left-0 mt-1 text-xs z-10"
              role="status"
              aria-live="polite"
            >
              {isValidating ? (
                <span className="text-gray-500 flex items-center gap-1">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  Kontrol ediliyor...
                </span>
              ) : validationError ? (
                <span className="text-red-500 flex items-center gap-1">
                  <X className="h-3 w-3" />
                  {validationError}
                </span>
              ) : validationSuccess ? (
                <span className="text-green-600 flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  Proje adı kullanılabilir
                </span>
              ) : null}
            </div>
          )}
        </div>

        {/* Action buttons */}
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={saveChanges}
            disabled={
              updateMutation.isPending ||
              !!validationError ||
              isValidating ||
              !editValue.trim() ||
              isSameProjectName(editValue, currentName)
            }
            className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50 touch-target"
            title="Kaydet (Enter)"
            aria-label="Proje adını kaydet"
          >
            {updateMutation.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Check className="h-4 w-4" />
            )}
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={cancelEditing}
            disabled={updateMutation.isPending}
            className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-50 touch-target"
            title="İptal (Esc)"
            aria-label="Düzenlemeyi iptal et"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  // Display mode UI
  return (
    <div className={cn("flex items-center gap-2 group w-full", className)}>
      <span
        className="flex-1 text-sm font-medium text-gray-900 truncate"
        title={currentName}
      >
        {currentName}
      </span>

      <Button
        size="sm"
        variant="ghost"
        onClick={(e) => {
          e.stopPropagation(); // Prevent triggering parent click handlers
          startEditing();
        }}
        disabled={disabled || updateMutation.isPending}
        className={cn(
          "h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity touch-target",
          "text-gray-400 hover:text-gray-600 hover:bg-gray-50",
          "focus:opacity-100 focus:ring-2 focus:ring-blue-500/20",
          "lg:opacity-0 opacity-100" // Mobile'da her zaman görünür, desktop'ta sadece hover'da
        )}
        title="Proje adını düzenle"
        aria-label="Proje adını düzenle"
      >
        <Edit2 className="h-3 w-3" />
      </Button>
    </div>
  )
})

export { ProjectNameEditor }
