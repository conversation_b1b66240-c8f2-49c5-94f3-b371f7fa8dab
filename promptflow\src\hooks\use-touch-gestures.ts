/**
 * Touch Gestures Hook
 * Advanced touch and gesture support for mobile devices
 */

'use client'

import { useRef, useEffect, useCallback, useState } from 'react'

interface TouchPoint {
  x: number
  y: number
  timestamp: number
}

interface SwipeGesture {
  direction: 'left' | 'right' | 'up' | 'down'
  distance: number
  velocity: number
  duration: number
}

interface PinchGesture {
  scale: number
  center: { x: number; y: number }
}

interface TapGesture {
  x: number
  y: number
  tapCount: number
}

interface GestureHandlers {
  onSwipe?: (gesture: SwipeGesture) => void
  onPinch?: (gesture: PinchGesture) => void
  onTap?: (gesture: TapGesture) => void
  onDoubleTap?: (gesture: TapGesture) => void
  onLongPress?: (point: TouchPoint) => void
  onPan?: (delta: { x: number; y: number }, point: TouchPoint) => void
}

interface GestureOptions {
  swipeThreshold?: number
  longPressDelay?: number
  doubleTapDelay?: number
  pinchThreshold?: number
  preventDefault?: boolean
}

export function useTouchGestures(
  handlers: GestureHandlers = {},
  options: GestureOptions = {}
) {
  const elementRef = useRef<HTMLElement>(null)
  const [isGestureActive, setIsGestureActive] = useState(false)
  
  const {
    swipeThreshold = 50,
    longPressDelay = 500,
    doubleTapDelay = 300,
    pinchThreshold = 0.1,
    preventDefault = true
  } = options

  // Touch state
  const touchState = useRef({
    startPoint: null as TouchPoint | null,
    lastPoint: null as TouchPoint | null,
    startDistance: 0,
    lastTap: null as TouchPoint | null,
    tapCount: 0,
    longPressTimer: null as NodeJS.Timeout | null,
    isLongPress: false,
    isPinching: false,
    isPanning: false
  })

  // Helper functions
  const getTouchPoint = useCallback((touch: Touch): TouchPoint => ({
    x: touch.clientX,
    y: touch.clientY,
    timestamp: Date.now()
  }), [])

  const getDistance = useCallback((point1: TouchPoint, point2: TouchPoint): number => {
    const dx = point2.x - point1.x
    const dy = point2.y - point1.y
    return Math.sqrt(dx * dx + dy * dy)
  }, [])

  const getDirection = useCallback((start: TouchPoint, end: TouchPoint): SwipeGesture['direction'] => {
    const dx = end.x - start.x
    const dy = end.y - start.y
    
    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? 'right' : 'left'
    } else {
      return dy > 0 ? 'down' : 'up'
    }
  }, [])

  const getTouchDistance = useCallback((touches: TouchList): number => {
    if (touches.length < 2) return 0
    
    const touch1 = touches[0]
    const touch2 = touches[1]
    
    const dx = touch2.clientX - touch1.clientX
    const dy = touch2.clientY - touch1.clientY
    
    return Math.sqrt(dx * dx + dy * dy)
  }, [])

  const getTouchCenter = useCallback((touches: TouchList): { x: number; y: number } => {
    if (touches.length === 0) return { x: 0, y: 0 }
    
    let x = 0
    let y = 0
    
    for (let i = 0; i < touches.length; i++) {
      x += touches[i].clientX
      y += touches[i].clientY
    }
    
    return {
      x: x / touches.length,
      y: y / touches.length
    }
  }, [])

  // Touch event handlers
  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (preventDefault) {
      e.preventDefault()
    }

    setIsGestureActive(true)
    const touch = e.touches[0]
    const point = getTouchPoint(touch)
    
    touchState.current.startPoint = point
    touchState.current.lastPoint = point
    touchState.current.isLongPress = false
    touchState.current.isPanning = false

    // Handle multi-touch (pinch)
    if (e.touches.length === 2) {
      touchState.current.isPinching = true
      touchState.current.startDistance = getTouchDistance(e.touches)
    }

    // Long press detection
    if (handlers.onLongPress) {
      touchState.current.longPressTimer = setTimeout(() => {
        touchState.current.isLongPress = true
        handlers.onLongPress!(point)
      }, longPressDelay)
    }

    // Double tap detection
    if (handlers.onDoubleTap && touchState.current.lastTap) {
      const timeDiff = point.timestamp - touchState.current.lastTap.timestamp
      const distance = getDistance(point, touchState.current.lastTap)
      
      if (timeDiff < doubleTapDelay && distance < 50) {
        touchState.current.tapCount++
        if (touchState.current.tapCount === 2) {
          handlers.onDoubleTap({ ...point, tapCount: 2 })
          touchState.current.tapCount = 0
          touchState.current.lastTap = null
          return
        }
      } else {
        touchState.current.tapCount = 1
      }
    } else {
      touchState.current.tapCount = 1
    }
    
    touchState.current.lastTap = point
  }, [
    preventDefault,
    getTouchPoint,
    getTouchDistance,
    getDistance,
    handlers.onLongPress,
    handlers.onDoubleTap,
    longPressDelay,
    doubleTapDelay
  ])

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (preventDefault) {
      e.preventDefault()
    }

    const touch = e.touches[0]
    const point = getTouchPoint(touch)
    
    if (!touchState.current.startPoint || !touchState.current.lastPoint) return

    // Clear long press timer on movement
    if (touchState.current.longPressTimer) {
      clearTimeout(touchState.current.longPressTimer)
      touchState.current.longPressTimer = null
    }

    // Handle pinch gesture
    if (e.touches.length === 2 && touchState.current.isPinching && handlers.onPinch) {
      const currentDistance = getTouchDistance(e.touches)
      const scale = currentDistance / touchState.current.startDistance
      const center = getTouchCenter(e.touches)
      
      if (Math.abs(scale - 1) > pinchThreshold) {
        handlers.onPinch({ scale, center })
      }
    }

    // Handle pan gesture
    if (e.touches.length === 1 && handlers.onPan && !touchState.current.isPinching) {
      const delta = {
        x: point.x - touchState.current.lastPoint.x,
        y: point.y - touchState.current.lastPoint.y
      }
      
      touchState.current.isPanning = true
      handlers.onPan(delta, point)
    }

    touchState.current.lastPoint = point
  }, [
    preventDefault,
    getTouchPoint,
    getTouchDistance,
    getTouchCenter,
    handlers.onPinch,
    handlers.onPan,
    pinchThreshold
  ])

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (preventDefault) {
      e.preventDefault()
    }

    setIsGestureActive(false)
    
    // Clear long press timer
    if (touchState.current.longPressTimer) {
      clearTimeout(touchState.current.longPressTimer)
      touchState.current.longPressTimer = null
    }

    const touch = e.changedTouches[0]
    const endPoint = getTouchPoint(touch)
    
    if (!touchState.current.startPoint) return

    // Handle swipe gesture
    if (handlers.onSwipe && !touchState.current.isLongPress && !touchState.current.isPanning) {
      const distance = getDistance(touchState.current.startPoint, endPoint)
      const duration = endPoint.timestamp - touchState.current.startPoint.timestamp
      
      if (distance > swipeThreshold && duration < 1000) {
        const direction = getDirection(touchState.current.startPoint, endPoint)
        const velocity = distance / duration
        
        handlers.onSwipe({
          direction,
          distance,
          velocity,
          duration
        })
      }
    }

    // Handle tap gesture
    if (handlers.onTap && !touchState.current.isLongPress && !touchState.current.isPanning) {
      const distance = getDistance(touchState.current.startPoint, endPoint)
      const duration = endPoint.timestamp - touchState.current.startPoint.timestamp
      
      if (distance < 10 && duration < 200) {
        handlers.onTap({ ...endPoint, tapCount: touchState.current.tapCount })
      }
    }

    // Reset state
    touchState.current.startPoint = null
    touchState.current.lastPoint = null
    touchState.current.isPinching = false
    touchState.current.isPanning = false
    touchState.current.startDistance = 0
  }, [
    preventDefault,
    getTouchPoint,
    getDistance,
    getDirection,
    handlers.onSwipe,
    handlers.onTap,
    swipeThreshold
  ])

  // Setup event listeners
  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    element.addEventListener('touchstart', handleTouchStart, { passive: !preventDefault })
    element.addEventListener('touchmove', handleTouchMove, { passive: !preventDefault })
    element.addEventListener('touchend', handleTouchEnd, { passive: !preventDefault })

    return () => {
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchmove', handleTouchMove)
      element.removeEventListener('touchend', handleTouchEnd)
    }
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, preventDefault])

  return {
    ref: elementRef,
    isGestureActive
  }
}

// Simplified swipe hook
export function useSwipe(
  onSwipe: (direction: SwipeGesture['direction']) => void,
  threshold: number = 50
) {
  return useTouchGestures({
    onSwipe: (gesture) => onSwipe(gesture.direction)
  }, {
    swipeThreshold: threshold
  })
}

// Simplified pinch hook
export function usePinch(
  onPinch: (scale: number) => void,
  threshold: number = 0.1
) {
  return useTouchGestures({
    onPinch: (gesture) => onPinch(gesture.scale)
  }, {
    pinchThreshold: threshold
  })
}
