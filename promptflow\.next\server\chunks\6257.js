"use strict";exports.id=6257,exports.ids=[6257],exports.modules={16391:(a,b,c)=>{c.d(b,{N:()=>d.L});var d=c(8266)},48730:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},56257:(a,b,c)=>{c.r(b),c.d(b,{default:()=>J});var d=c(60687),e=c(43210),f=c(96474),g=c(99270),h=c(62688);let i=(0,h.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var j=c(64398),k=c(37360),l=c(11437),m=c(64021),n=c(5336),o=c(48730),p=c(11860),q=c(13861),r=c(70615);let s=(0,h.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var t=c(58869),u=c(75034),v=c(63503),w=c(29523),x=c(89667),y=c(96834),z=c(44493),A=c(24711),B=c(35950),C=c(3178),D=c(74940),E=c(31568);c(16391);var F=c(8693),G=c(34617),H=c(79450),I=c(52581);function J({onSelectContext:a,open:b,onOpenChange:c,contentOnly:h=!1}){let[J,K]=(0,e.useState)(""),[L,M]=(0,e.useState)("all"),[N,O]=(0,e.useState)("all"),[P,Q]=(0,e.useState)(null),[R,S]=(0,e.useState)(!1),[T,U]=(0,e.useState)(null),[V,W]=(0,e.useState)(!1),X=(0,e.useRef)(null),{data:Y}=(0,E.Jd)(),{data:Z=[]}=(0,C.RH)(),$=(0,C._v)(),_=(0,C.YD)(),{addContext:aa,isLoading:ab}=(0,D.KU)(),ac=(0,F.jE)(),{data:ad=[]}=(0,C.IL)(),ae=(0,e.useMemo)(()=>{let a={};switch("all"!==L&&(a.category_id=L),J&&(a.search=J),N){case"public":a.is_public=!0;break;case"private":a.is_public=!1,a.author_id=Y?.id;break;case"templates":a.is_template=!0;break;case"featured":a.is_featured=!0}return a},[J,L,N,Y?.id]),{data:af=[],isLoading:ag}=(0,C.W2)(ae),ah=async b=>{try{await aa(b),a(b),Q(null)}catch(a){console.error("Failed to add context to project:",a),I.oR.error("Context projeye eklenirken hata oluştu")}},ai=async(a,b)=>{b.stopPropagation();try{await navigator.clipboard.writeText(a.content),await $.mutateAsync({contextId:a.id,projectId:void 0}),I.oR.success("Context panoya kopyalandı!")}catch(a){console.error("Copy failed:",a),I.oR.error("Kopyalama başarısız oldu")}},aj=async(a,b)=>{b.stopPropagation();try{await _.mutateAsync(a),I.oR.success("Beğeni durumu g\xfcncellendi!")}catch(a){console.error("Like toggle failed:",a),I.oR.error("Beğeni g\xfcncellenemedi")}},ak=void 0!==b&&void 0!==c;return h?(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)(w.$,{size:"sm",onClick:()=>S(!0),className:"gap-2 ml-auto",children:[(0,d.jsx)(f.A,{className:"h-4 w-4"}),"Yeni Ekle"]})}),(0,d.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,d.jsx)(x.p,{placeholder:"Context ara... (başlık, a\xe7ıklama, etiketler)",value:J,onChange:a=>K(a.target.value),className:"pl-10"})]}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,d.jsxs)(w.$,{variant:"all"===L?"default":"outline",size:"sm",onClick:()=>M("all"),className:"gap-2",children:[(0,d.jsx)("span",{children:"\uD83D\uDCC2"}),"T\xfcm\xfc"]}),Z.map(a=>(0,d.jsxs)(w.$,{variant:L===a.id?"default":"outline",size:"sm",onClick:()=>M(a.id),className:"gap-2",style:L===a.id?{backgroundColor:a.color}:{},children:[(0,d.jsx)("span",{children:a.icon}),a.name]},a.id))]}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,d.jsxs)(w.$,{variant:"all"===N?"default":"outline",size:"sm",onClick:()=>O("all"),className:"gap-2",children:[(0,d.jsx)(i,{className:"h-4 w-4"}),"T\xfcm\xfc"]}),(0,d.jsxs)(w.$,{variant:"featured"===N?"default":"outline",size:"sm",onClick:()=>O("featured"),className:"gap-2",children:[(0,d.jsx)(j.A,{className:"h-4 w-4"}),"\xd6ne \xc7ıkan"]}),(0,d.jsxs)(w.$,{variant:"templates"===N?"default":"outline",size:"sm",onClick:()=>O("templates"),className:"gap-2",children:[(0,d.jsx)(k.A,{className:"h-4 w-4"}),"Şablonlar"]}),(0,d.jsxs)(w.$,{variant:"public"===N?"default":"outline",size:"sm",onClick:()=>O("public"),className:"gap-2",children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),"Herkese A\xe7ık"]}),(0,d.jsxs)(w.$,{variant:"private"===N?"default":"outline",size:"sm",onClick:()=>O("private"),className:"gap-2",children:[(0,d.jsx)(m.A,{className:"h-4 w-4"}),"\xd6zel"]})]})]}),(0,d.jsx)("div",{className:"space-y-4",children:ag?(0,d.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,d.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Contextler y\xfckleniyor..."})]})}):(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-4",children:[af.map(a=>(0,d.jsxs)(z.Zp,{className:"cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] group",onClick:()=>Q(a),children:[(0,d.jsxs)(z.aR,{className:"pb-2",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{style:{color:a.category.color},children:a.category.icon}),(0,d.jsx)(y.E,{variant:"secondary",className:"text-xs",children:a.category.name})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[a.is_featured&&(0,d.jsx)("div",{title:"\xd6ne \xc7ıkan",children:(0,d.jsx)(j.A,{className:"h-4 w-4 text-yellow-500 fill-current"})}),a.is_public?(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)("div",{title:"Herkese A\xe7ık",children:(0,d.jsx)(l.A,{className:"h-4 w-4 text-green-500"})}),"approved"===a.approval_status&&(0,d.jsx)("div",{title:"Onaylanmış",children:(0,d.jsx)(n.A,{className:"h-3 w-3 text-green-500"})}),"pending"===a.approval_status&&(0,d.jsx)("div",{title:"Onay Bekliyor",children:(0,d.jsx)(o.A,{className:"h-3 w-3 text-yellow-500"})}),"rejected"===a.approval_status&&(0,d.jsx)("div",{title:"Reddedildi",children:(0,d.jsx)(p.A,{className:"h-3 w-3 text-red-500"})})]}):(0,d.jsx)("div",{title:"\xd6zel",children:(0,d.jsx)(m.A,{className:"h-4 w-4 text-gray-500"})}),a.is_template&&(0,d.jsx)("div",{title:"Şablon",children:(0,d.jsx)(k.A,{className:"h-4 w-4 text-blue-500"})})]})]}),(0,d.jsx)(z.ZB,{className:"text-lg line-clamp-2",children:a.title}),(0,d.jsx)(z.BT,{className:"line-clamp-2",children:a.description})]}),(0,d.jsxs)(z.Wu,{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex flex-wrap gap-1",children:[a.tags.slice(0,3).map((a,b)=>(0,d.jsx)(y.E,{variant:"outline",className:"text-xs",children:a},b)),a.tags.length>3&&(0,d.jsxs)(y.E,{variant:"outline",className:"text-xs",children:["+",a.tags.length-3]})]}),(0,d.jsx)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(q.A,{className:"h-3 w-3"}),a.view_count]}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(r.A,{className:"h-3 w-3"}),a.usage_count]}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(s,{className:"h-3 w-3"}),a.like_count]})]})}),(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[(0,d.jsx)(t.A,{className:"h-3 w-3"}),a.author_name]}),(0,d.jsxs)("div",{className:"flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,d.jsx)(w.$,{size:"sm",onClick:b=>{b.stopPropagation(),ah(a)},disabled:ab,className:"flex-1",children:ab?"Ekleniyor...":"Projeye Ekle"}),(0,d.jsx)(w.$,{size:"sm",variant:"outline",onClick:b=>ai(a,b),children:(0,d.jsx)(r.A,{className:"h-4 w-4"})}),Y&&a.author_id===Y.id&&(0,d.jsx)(w.$,{size:"sm",variant:"outline",onClick:b=>{b.stopPropagation(),U(a),W(!0)},title:"D\xfczenle",children:(0,d.jsx)(u.A,{className:"h-4 w-4"})}),(0,d.jsx)(w.$,{size:"sm",variant:"outline",onClick:b=>aj(a.id,b),className:ad.includes(a.id)?"text-red-500":"",children:(0,d.jsx)(s,{className:`h-4 w-4 ${ad.includes(a.id)?"fill-current":""}`})})]})]})]},a.id)),0===af.length&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Context bulunamadı"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Arama kriterlerinizi değiştirmeyi deneyin veya yeni bir context oluşturun."})]})]})}),P&&(0,d.jsx)(v.lG,{open:!!P,onOpenChange:()=>Q(null),children:(0,d.jsx)(v.Cf,{className:"max-w-4xl max-h-[90vh]",children:(0,d.jsxs)(v.c7,{children:[(0,d.jsxs)(v.L3,{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{style:{color:P.category.color},children:P.category.icon}),P.title]}),(0,d.jsx)("div",{className:"flex items-start justify-between",children:(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)(y.E,{variant:"secondary",children:P.category.name}),P.is_featured&&(0,d.jsxs)(y.E,{className:"bg-yellow-100 text-yellow-800",children:[(0,d.jsx)(j.A,{className:"h-3 w-3 mr-1"}),"\xd6ne \xc7ıkan"]})]})})})]})})}),(0,d.jsx)(G.ContextCreationModal,{open:R,onOpenChange:S,onSuccess:()=>{ac.invalidateQueries({queryKey:["contexts"]})}}),(0,d.jsx)(H.ContextEditModal,{open:V,onOpenChange:W,context:T,onSuccess:()=>{ac.invalidateQueries({queryKey:["contexts"]})}})]}):(0,d.jsxs)(v.lG,{open:b,onOpenChange:c,children:[!ak&&(0,d.jsx)(v.zM,{asChild:!0,children:(0,d.jsxs)(w.$,{variant:"outline",className:"gap-2 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 hover:from-blue-100 hover:to-indigo-100",children:[(0,d.jsx)(g.A,{className:"h-4 w-4"}),"Context Galerisi"]})}),(0,d.jsxs)(v.Cf,{className:"max-w-6xl max-h-[90vh] flex flex-col",children:[(0,d.jsx)(v.c7,{className:"flex-shrink-0",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(v.L3,{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"Context Galerisi"}),(0,d.jsxs)(w.$,{size:"sm",onClick:()=>S(!0),className:"gap-2",children:[(0,d.jsx)(f.A,{className:"h-4 w-4"}),"Yeni Ekle"]})]})}),(0,d.jsxs)("div",{className:"flex flex-col gap-4 p-2 flex-shrink-0",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,d.jsx)(x.p,{placeholder:"Context ara... (başlık, a\xe7ıklama, etiketler)",value:J,onChange:a=>K(a.target.value),className:"pl-10"})]}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,d.jsxs)(w.$,{variant:"all"===L?"default":"outline",size:"sm",onClick:()=>M("all"),className:"gap-2",children:[(0,d.jsx)("span",{children:"\uD83D\uDCC2"}),"T\xfcm\xfc"]}),Z.map(a=>(0,d.jsxs)(w.$,{variant:L===a.id?"default":"outline",size:"sm",onClick:()=>M(a.id),className:"gap-2",style:L===a.id?{backgroundColor:a.color}:{},children:[(0,d.jsx)("span",{children:a.icon}),a.name]},a.id))]}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,d.jsxs)(w.$,{variant:"all"===N?"default":"outline",size:"sm",onClick:()=>O("all"),className:"gap-2",children:[(0,d.jsx)(i,{className:"h-4 w-4"}),"T\xfcm\xfc"]}),(0,d.jsxs)(w.$,{variant:"featured"===N?"default":"outline",size:"sm",onClick:()=>O("featured"),className:"gap-2",children:[(0,d.jsx)(j.A,{className:"h-4 w-4"}),"\xd6ne \xc7ıkan"]}),(0,d.jsxs)(w.$,{variant:"templates"===N?"default":"outline",size:"sm",onClick:()=>O("templates"),className:"gap-2",children:[(0,d.jsx)(k.A,{className:"h-4 w-4"}),"Şablonlar"]}),(0,d.jsxs)(w.$,{variant:"public"===N?"default":"outline",size:"sm",onClick:()=>O("public"),className:"gap-2",children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),"Herkese A\xe7ık"]}),(0,d.jsxs)(w.$,{variant:"private"===N?"default":"outline",size:"sm",onClick:()=>O("private"),className:"gap-2",children:[(0,d.jsx)(m.A,{className:"h-4 w-4"}),"\xd6zel"]})]})]}),(0,d.jsx)(B.w,{className:"flex-shrink-0"}),(0,d.jsx)("div",{className:"flex-1 min-h-0",children:(0,d.jsx)(A.F,{ref:X,className:"h-full w-full",style:{scrollBehavior:"smooth",WebkitOverflowScrolling:"touch"},children:(0,d.jsx)("div",{className:"p-4 space-y-4",children:ag?(0,d.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,d.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Contextler y\xfckleniyor..."})]})}):(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-4",children:[af.map(a=>(0,d.jsx)(z.Zp,{className:"cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] group",onClick:()=>Q(a),children:(0,d.jsx)(z.aR,{className:"pb-2",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{style:{color:a.category.color},children:a.category.icon}),(0,d.jsx)(y.E,{variant:"secondary",className:"text-xs",children:a.category.name})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[a.is_featured&&(0,d.jsx)("div",{title:"\xd6ne \xc7ıkan",children:(0,d.jsx)(j.A,{className:"h-4 w-4 text-yellow-500 fill-current"})}),a.is_public?(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)("div",{title:"Herkese A\xe7ık",children:(0,d.jsx)(l.A,{className:"h-4 w-4 text-green-500"})}),"approved"===a.approval_status&&(0,d.jsx)("div",{title:"Onaylanmış",children:(0,d.jsx)(n.A,{className:"h-3 w-3 text-green-500"})}),"pending"===a.approval_status&&(0,d.jsx)("div",{title:"Onay Bekliyor",children:(0,d.jsx)(o.A,{className:"h-3 w-3 text-yellow-500"})}),"rejected"===a.approval_status&&(0,d.jsx)("div",{title:"Reddedildi",children:(0,d.jsx)(p.A,{className:"h-3 w-3 text-red-500"})})]}):(0,d.jsx)("div",{title:"\xd6zel",children:(0,d.jsx)(m.A,{className:"h-4 w-4 text-gray-500"})})]})]})})},a.id)),0===af.length&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Context bulunamadı"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Arama kriterlerinizi değiştirmeyi deneyin veya yeni bir context oluşturun."})]})]})})})})]}),P&&(0,d.jsx)(v.lG,{open:!!P,onOpenChange:()=>Q(null),children:(0,d.jsx)(v.Cf,{className:"max-w-4xl max-h-[90vh]",children:(0,d.jsxs)(v.c7,{children:[(0,d.jsxs)(v.L3,{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{style:{color:P.category.color},children:P.category.icon}),P.title]}),(0,d.jsx)("div",{className:"flex items-start justify-between",children:(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)(y.E,{variant:"secondary",children:P.category.name}),P.is_featured&&(0,d.jsxs)(y.E,{className:"bg-yellow-100 text-yellow-800",children:[(0,d.jsx)(j.A,{className:"h-3 w-3 mr-1"}),"\xd6ne \xc7ıkan"]})]})})})]})})}),(0,d.jsx)(G.ContextCreationModal,{open:R,onOpenChange:S,onSuccess:()=>{ac.invalidateQueries({queryKey:["contexts"]})}}),(0,d.jsx)(H.ContextEditModal,{open:V,onOpenChange:W,context:T,onSuccess:()=>{ac.invalidateQueries({queryKey:["contexts"]})}})]})}},64398:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])}};