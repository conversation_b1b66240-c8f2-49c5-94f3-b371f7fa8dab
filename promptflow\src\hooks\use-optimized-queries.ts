import { useQuery, useInfiniteQuery, useMutation, useQueryClient, QueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { queryKeys, CACHE_TIMES, invalidationPatterns } from '@/lib/query-cache-config'

// Optimized project queries with selective fields
export function useOptimizedProjects() {
  return useQuery({
    queryKey: queryKeys.projects.all(),
    queryFn: async () => {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          id,
          name,
          description,
          created_at,
          updated_at,
          prompt_count:prompts(count)
        `)
        .order('updated_at', { ascending: false })
        .limit(50) // Limit initial load
      
      if (error) throw error
      return data
    },
    staleTime: CACHE_TIMES.MEDIUM,
    gcTime: CACHE_TIMES.MEDIUM * 2,
  })
}

// Optimized project detail with related data
export function useOptimizedProject(projectId: string) {
  return useQuery({
    queryKey: queryKeys.projects.detail(projectId),
    queryFn: async () => {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          prompts:prompts(
            id,
            title,
            content,
            category,
            hashtags,
            created_at,
            updated_at
          )
        `)
        .eq('id', projectId)
        .single()
      
      if (error) throw error
      return data
    },
    staleTime: CACHE_TIMES.MEDIUM,
    enabled: !!projectId,
  })
}

// Infinite scroll for prompts with pagination
export function useInfinitePrompts(projectId: string, pageSize = 20) {
  return useInfiniteQuery({
    queryKey: queryKeys.prompts.all(projectId),
    queryFn: async ({ pageParam = 0 }) => {
      const { data, error, count } = await supabase
        .from('prompts')
        .select(`
          id,
          title,
          content,
          category,
          hashtags,
          created_at,
          updated_at
        `, { count: 'exact' })
        .eq('project_id', projectId)
        .order('updated_at', { ascending: false })
        .range(pageParam * pageSize, (pageParam + 1) * pageSize - 1)
      
      if (error) throw error
      
      return {
        data: data || [],
        nextPage: data && data.length === pageSize ? pageParam + 1 : undefined,
        totalCount: count || 0
      }
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    staleTime: CACHE_TIMES.SHORT,
    enabled: !!projectId,
    initialPageParam: 0,
  })
}

// Optimized search with debouncing
export function useOptimizedPromptSearch(projectId: string, searchQuery: string, debounceMs = 300) {
  return useQuery({
    queryKey: queryKeys.prompts.search(projectId, searchQuery),
    queryFn: async () => {
      if (!searchQuery.trim()) return []
      
      const { data, error } = await supabase
        .from('prompts')
        .select(`
          id,
          title,
          content,
          category,
          hashtags,
          created_at
        `)
        .eq('project_id', projectId)
        .or(`title.ilike.%${searchQuery}%,content.ilike.%${searchQuery}%,hashtags.cs.{${searchQuery}}`)
        .order('updated_at', { ascending: false })
        .limit(50)
      
      if (error) throw error
      return data || []
    },
    staleTime: CACHE_TIMES.SHORT,
    enabled: !!projectId && searchQuery.length >= 2,
  })
}

// Optimized hashtag aggregation
export function useOptimizedHashtags(projectId: string) {
  return useQuery({
    queryKey: queryKeys.hashtags.all(projectId),
    queryFn: async () => {
      const { data, error } = await supabase
        .rpc('get_project_hashtags', { project_id: projectId })
      
      if (error) throw error
      return data || []
    },
    staleTime: CACHE_TIMES.LONG,
    enabled: !!projectId,
  })
}

// Optimized context templates with categories
export function useOptimizedContextTemplates(category?: string) {
  return useQuery({
    queryKey: queryKeys.contexts.templates(category),
    queryFn: async () => {
      let query = supabase
        .from('context_templates')
        .select(`
          id,
          title,
          content,
          category,
          tags,
          usage_count,
          created_at
        `)
        .order('usage_count', { ascending: false })
        .limit(100)
      
      if (category) {
        query = query.eq('category', category)
      }
      
      const { data, error } = await query
      
      if (error) throw error
      return data || []
    },
    staleTime: CACHE_TIMES.STATIC,
  })
}

// Batch operations for better performance
export function useBatchPromptOperations() {
  const queryClient = useQueryClient()
  
  const batchDelete = useMutation({
    mutationFn: async ({ promptIds, projectId }: { promptIds: string[], projectId: string }) => {
      const { error } = await supabase
        .from('prompts')
        .delete()
        .in('id', promptIds)
      
      if (error) throw error
      return { promptIds, projectId }
    },
    onSuccess: ({ projectId }) => {
      // Invalidate related queries
      invalidationPatterns.promptUpdate(projectId).forEach(pattern => {
        queryClient.invalidateQueries({ queryKey: pattern })
      })
    },
  })
  
  const batchUpdate = useMutation({
    mutationFn: async ({ 
      updates, 
      projectId 
    }: { 
      updates: Array<{ id: string; [key: string]: unknown }>, 
      projectId: string 
    }) => {
      const results = await Promise.all(
        updates.map(update => 
          supabase
            .from('prompts')
            .update(update)
            .eq('id', update.id)
            .select()
        )
      )
      
      const errors = results.filter(result => result.error)
      if (errors.length > 0) {
        throw new Error(`Batch update failed: ${errors.map(e => e.error?.message).join(', ')}`)
      }
      
      return results.map(result => result.data).flat()
    },
    onSuccess: (_, { projectId }) => {
      invalidationPatterns.promptUpdate(projectId).forEach(pattern => {
        queryClient.invalidateQueries({ queryKey: pattern })
      })
    },
  })
  
  return {
    batchDelete,
    batchUpdate,
  }
}

// Real-time subscriptions with optimizations
export function useOptimizedRealtime(projectId: string) {
  const queryClient = useQueryClient()
  
  return useQuery({
    queryKey: ['realtime', projectId],
    queryFn: () => {
      const channel = supabase
        .channel(`project-${projectId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'prompts',
            filter: `project_id=eq.${projectId}`,
          },
          (payload) => {
            console.log('Realtime update:', payload)
            
            // Optimistic updates
            if (payload.eventType === 'INSERT') {
              queryClient.setQueryData(
                queryKeys.prompts.all(projectId),
                (oldData: unknown) => {
                  if (!oldData) return oldData
                  const data = oldData as { pages: Array<{ data: unknown[] }> }
                  return {
                    ...data,
                    pages: data.pages.map((page, index: number) =>
                      index === 0
                        ? { ...page, data: [payload.new, ...page.data] }
                        : page
                    )
                  }
                }
              )
            }
            
            if (payload.eventType === 'DELETE') {
              queryClient.setQueryData(
                queryKeys.prompts.all(projectId),
                (oldData: unknown) => {
                  if (!oldData) return oldData
                  const data = oldData as { pages: Array<{ data: Array<{ id: string }> }> }
                  return {
                    ...data,
                    pages: data.pages.map((page) => ({
                      ...page,
                      data: page.data.filter((item) => item.id !== payload.old.id)
                    }))
                  }
                }
              )
            }
            
            // Invalidate related queries after a delay
            setTimeout(() => {
              invalidationPatterns.promptUpdate(projectId).forEach(pattern => {
                queryClient.invalidateQueries({ queryKey: pattern })
              })
            }, 1000)
          }
        )
        .subscribe()
      
      return channel
    },
    staleTime: Infinity,
    gcTime: Infinity,
    enabled: !!projectId,
  })
}

// Connection pooling and query optimization utilities
export const queryOptimizations = {
  // Prefetch related data
  prefetchProjectData: async (queryClient: QueryClient, projectId: string) => {
    await Promise.allSettled([
      queryClient.prefetchQuery({
        queryKey: queryKeys.projects.detail(projectId),
        staleTime: CACHE_TIMES.MEDIUM,
      }),
      queryClient.prefetchQuery({
        queryKey: queryKeys.prompts.all(projectId),
        staleTime: CACHE_TIMES.SHORT,
      }),
      queryClient.prefetchQuery({
        queryKey: queryKeys.hashtags.all(projectId),
        staleTime: CACHE_TIMES.LONG,
      }),
    ])
  },
  
  // Batch invalidation
  invalidateProjectData: (queryClient: QueryClient, projectId: string) => {
    invalidationPatterns.promptUpdate(projectId).forEach(pattern => {
      queryClient.invalidateQueries({ queryKey: pattern })
    })
  },
  
  // Query deduplication
  deduplicateQueries: (queryClient: QueryClient) => {
    // TanStack Query handles this automatically, but we can add custom logic
    console.log('Query deduplication active')
  },
} as const
