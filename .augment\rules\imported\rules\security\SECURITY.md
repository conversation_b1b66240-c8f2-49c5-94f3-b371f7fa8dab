---
type: "security_guide"
role: "security_implementation_patterns"
dependencies:
  - "core/PROJECT_CORE.md"
  - "backend/SUPABASE.md"
  - "frontend/MAIN_APP.md"
  - "fixes/TYPESCRIPT_PATTERNS.md"
related_security:
  - "backend/RLS_POLICIES.md"
  - "frontend/INPUT_VALIDATION.md"
auto_update_from:
  - "backend/SUPABASE.md"
  - "frontend/MAIN_APP.md"
security_implementations:
  - date: "2025-01-29"
    feature: "Project Name Editing"
    measures: "Input validation, rate limiting, XSS protection, RLS policies"
    status: "implemented"
  - date: "2025-01-30"
    feature: "Comprehensive Security System"
    measures: "Advanced input validation, CSRF protection, rate limiting, secure API wrapper"
    status: "implemented"
last_updated: "2025-01-30"
---

# PromptFlow Security Implementation Guide

## Security-First Development Principles

### 1. Input Validation & Sanitization
All user input must be validated and sanitized at multiple layers:

#### Advanced Validation System (NEW - 2025-01-30)
```typescript
import { AdvancedValidator, COMMON_VALIDATION_RULES } from '@/lib/advanced-validation'

// Email validation with sanitization
const emailValidation = AdvancedValidator.validate(email, COMMON_VALIDATION_RULES.email)
if (!emailValidation.isValid) {
  throw new Error(emailValidation.errors[0])
}
const sanitizedEmail = emailValidation.sanitizedValue

// Password strength validation
const passwordValidation = AdvancedValidator.validate(password, COMMON_VALIDATION_RULES.password)
if (!passwordValidation.isValid) {
  throw new Error(passwordValidation.errors[0])
}

// Custom validation rules
const customValidation = AdvancedValidator.validate(input, {
  required: true,
  minLength: 3,
  maxLength: 50,
  pattern: /^[a-zA-Z0-9\s\-_.]+$/,
  blockedPatterns: [/script/gi, /javascript/gi],
  sanitizer: (value) => DOMPurify.sanitize(value)
})
```

#### Frontend Validation
```typescript
// ✅ Comprehensive input validation
export function validateProjectName(name: string): ValidationResult {
  const sanitized = sanitizeProjectName(name)
  
  // Empty check
  if (!sanitized) {
    return { isValid: false, error: 'Proje adı boş olamaz' }
  }
  
  // Length validation
  if (sanitized.length < PROJECT_NAME_RULES.minLength) {
    return { isValid: false, error: `Proje adı en az ${PROJECT_NAME_RULES.minLength} karakter olmalıdır` }
  }
  
  if (sanitized.length > PROJECT_NAME_RULES.maxLength) {
    return { isValid: false, error: `Proje adı en fazla ${PROJECT_NAME_RULES.maxLength} karakter olabilir` }
  }
  
  // Character validation (prevent injection)
  if (!PROJECT_NAME_RULES.allowedCharsRegex.test(sanitized)) {
    return { isValid: false, error: 'Proje adı sadece harf, rakam, boşluk ve -_. karakterlerini içerebilir' }
  }
  
  return { isValid: true, sanitizedValue: sanitized }
}

// ✅ Input sanitization
export function sanitizeProjectName(name: string): string {
  if (!name) return ''
  
  // Trim and normalize
  let sanitized = name.trim()
  
  // Convert multiple spaces to single space
  sanitized = sanitized.replace(/\s+/g, ' ')
  
  return sanitized.trim()
}
```

#### XSS Protection
```typescript
// ✅ HTML escaping for XSS prevention
export function escapeHtml(text: string): string {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

// ✅ Validation rules prevent script injection
const PROJECT_NAME_RULES = {
  minLength: 3,
  maxLength: 50,
  allowedCharsRegex: /^[a-zA-Z0-9\s\-_.]+$/, // Only safe characters
  debounceMs: 300,
  rateLimit: { maxRequests: 10, windowMinutes: 1 }
}
```

### 2. Rate Limiting Implementation

#### Client-Side Rate Limiting
```typescript
// ✅ Client-side rate limiting to prevent spam
export function checkClientRateLimit(): boolean {
  try {
    const stored = localStorage.getItem(RATE_LIMIT_KEY)
    const now = Date.now()
    
    if (!stored) {
      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({
        count: 1,
        windowStart: now
      }))
      return true
    }
    
    const data = JSON.parse(stored)
    const windowDuration = PROJECT_NAME_RULES.rateLimit.windowMinutes * 60 * 1000
    
    // Check if window expired
    if (now - data.windowStart > windowDuration) {
      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({
        count: 1,
        windowStart: now
      }))
      return true
    }
    
    // Check limit
    if (data.count >= PROJECT_NAME_RULES.rateLimit.maxRequests) {
      return false
    }
    
    // Increment counter
    localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({
      count: data.count + 1,
      windowStart: data.windowStart
    }))
    
    return true
  } catch (error) {
    console.warn('Rate limit check failed:', error)
    return true // Allow on error
  }
}
```

#### Database-Level Rate Limiting
```sql
-- ✅ Server-side rate limiting function
CREATE OR REPLACE FUNCTION public.check_rate_limit(
  p_user_id uuid,
  p_action_type text,
  p_max_requests integer DEFAULT 10,
  p_window_minutes integer DEFAULT 1
) RETURNS boolean AS $$
DECLARE
  current_count integer;
  window_start_time timestamp with time zone;
BEGIN
  window_start_time := now() - (p_window_minutes || ' minutes')::interval;
  
  SELECT COALESCE(SUM(action_count), 0) INTO current_count
  FROM public.rate_limits
  WHERE user_id = p_user_id 
    AND action_type = p_action_type 
    AND window_start >= window_start_time;
  
  IF current_count >= p_max_requests THEN
    RETURN false;
  END IF;
  
  INSERT INTO public.rate_limits (user_id, action_type, window_start)
  VALUES (p_user_id, p_action_type, now());
  
  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 3. Database Security (RLS Policies)

#### Row Level Security Implementation
```sql
-- ✅ Users can only access their own projects
CREATE POLICY "Users can manage their own projects" ON public.projects
  FOR ALL USING (user_id = auth.uid());

-- ✅ Users can update their own projects
CREATE POLICY "Users can update their own projects" ON public.projects
  FOR UPDATE USING (auth.uid() = user_id);

-- ✅ Rate limits are user-specific
CREATE POLICY "Users can view their own rate limits" ON public.rate_limits
  FOR SELECT USING (auth.uid() = user_id);
```

#### Database Constraints for Security
```sql
-- ✅ Input validation at database level
ALTER TABLE public.projects 
ADD CONSTRAINT projects_name_length_check 
CHECK (char_length(name) >= 3 AND char_length(name) <= 50);

-- ✅ Character validation
ALTER TABLE public.projects 
ADD CONSTRAINT projects_name_format_check 
CHECK (name ~ '^[a-zA-Z0-9\s\-_.]+$');

-- ✅ Prevent empty names
ALTER TABLE public.projects 
ADD CONSTRAINT projects_name_not_empty_check 
CHECK (trim(name) != '' AND name IS NOT NULL);

-- ✅ Unique names per user
CREATE UNIQUE INDEX projects_user_name_unique 
ON public.projects (user_id, LOWER(TRIM(name))) 
WHERE status != 'archived';
```

### 4. Secure Database Functions

#### Secure Update Function
```sql
-- ✅ Comprehensive secure update function
CREATE OR REPLACE FUNCTION public.update_project_name_secure(
  p_project_id uuid,
  p_new_name text
) RETURNS json AS $$
DECLARE
  current_user_id uuid;
  project_record record;
  rate_limit_ok boolean;
  cleaned_name text;
BEGIN
  -- Authentication check
  current_user_id := auth.uid();
  IF current_user_id IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Unauthorized',
      'message', 'Kullanıcı oturumu bulunamadı'
    );
  END IF;
  
  -- Rate limiting
  SELECT public.check_rate_limit(current_user_id, 'project_name_update', 10, 1) 
  INTO rate_limit_ok;
  
  IF NOT rate_limit_ok THEN
    RETURN json_build_object(
      'success', false,
      'error', 'RateLimitExceeded',
      'message', 'Çok fazla güncelleme isteği. Lütfen bekleyin.'
    );
  END IF;
  
  -- Input sanitization and validation
  cleaned_name := TRIM(p_new_name);
  
  IF cleaned_name IS NULL OR cleaned_name = '' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'InvalidInput',
      'message', 'Proje adı boş olamaz'
    );
  END IF;
  
  -- Update with RLS protection
  UPDATE public.projects 
  SET name = cleaned_name, updated_at = now()
  WHERE id = p_project_id AND user_id = current_user_id
  RETURNING * INTO project_record;
  
  IF project_record IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'NotFound',
      'message', 'Proje bulunamadı veya erişim yetkiniz yok'
    );
  END IF;
  
  RETURN json_build_object(
    'success', true,
    'data', row_to_json(project_record),
    'message', 'Proje adı başarıyla güncellendi'
  );
  
EXCEPTION
  WHEN unique_violation THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DuplicateName',
      'message', 'Bu isimde bir proje zaten mevcut'
    );
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'DatabaseError',
      'message', 'Veritabanı hatası: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Security Testing Requirements

### Input Validation Tests
```typescript
// ✅ Comprehensive security tests
describe('Security Tests', () => {
  it('prevents XSS injection attempts', () => {
    const injectionAttempts = [
      '<script>alert("xss")</script>',
      'javascript:alert(1)',
      '${alert(1)}',
      '{{alert(1)}}',
      '#{alert(1)}'
    ]

    injectionAttempts.forEach(attempt => {
      const result = validateProjectName(attempt)
      expect(result.isValid).toBe(false)
    })
  })

  it('prevents SQL injection attempts', () => {
    const sqlInjections = [
      "'; DROP TABLE projects; --",
      '"; DELETE FROM projects; --',
      "' OR '1'='1",
      '" OR "1"="1'
    ]

    sqlInjections.forEach(injection => {
      const result = validateProjectName(injection)
      expect(result.isValid).toBe(false)
    })
  })

  it('enforces rate limiting', () => {
    // Test rate limiting logic
    for (let i = 0; i < PROJECT_NAME_RULES.rateLimit.maxRequests; i++) {
      expect(checkClientRateLimit()).toBe(true)
    }
    expect(checkClientRateLimit()).toBe(false)
  })
})
```

## Security Checklist

### Before Deployment
- [ ] All user inputs validated and sanitized
- [ ] XSS protection implemented
- [ ] SQL injection prevention verified
- [ ] Rate limiting configured
- [ ] RLS policies tested
- [ ] Database constraints verified
- [ ] Security tests passing
- [ ] Error messages don't leak sensitive information

### Ongoing Security
- [ ] Regular security audits
- [ ] Monitor rate limiting effectiveness
- [ ] Review and update validation rules
- [ ] Test new attack vectors
- [ ] Update dependencies for security patches

## Update Requirements
- Document new security measures as implemented
- Update validation patterns for new features
- Maintain security test coverage
- Review and update rate limiting rules
- Document security incidents and resolutions
