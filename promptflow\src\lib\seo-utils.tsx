/**
 * SEO Utilities
 * Comprehensive SEO optimization tools and meta tag management
 */

import { Metadata } from 'next'

// Base SEO configuration
export const BASE_SEO = {
  siteName: 'PromptFlow',
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://promptbir.com',
  defaultTitle: 'PromptFlow - AI Destekli Prompt Yönetim Platformu',
  defaultDescription: 'AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformu. Projelerinizi organize edin, prompt\'larınızı yönetin ve AI ile daha verimli çalışın.',
  defaultKeywords: [
    'prompt yönetimi',
    'AI araçları',
    'yapay zeka',
    'geliştirici araçları',
    'prompt engineering',
    'AI destekli geliştirme',
    'kod optimizasyonu',
    'proje yönetimi'
  ],
  author: 'PromptFlow Team',
  language: 'tr',
  locale: 'tr_TR',
  twitterHandle: '@promptflow',
  facebookAppId: '',
  themeColor: '#3b82f6',
  backgroundColor: '#ffffff'
} as const

// Page types for structured data
export type PageType = 
  | 'website'
  | 'article'
  | 'profile'
  | 'product'
  | 'organization'
  | 'webapp'

// SEO metadata generator
export class SEOGenerator {
  static generateMetadata(options: {
    title?: string
    description?: string
    keywords?: string[]
    image?: string
    url?: string
    type?: PageType
    publishedTime?: string
    modifiedTime?: string
    author?: string
    noIndex?: boolean
    noFollow?: boolean
  } = {}): Metadata {
    const {
      title,
      description = BASE_SEO.defaultDescription,
      keywords = BASE_SEO.defaultKeywords,
      image,
      url,
      type = 'website',
      publishedTime,
      modifiedTime,
      author = BASE_SEO.author,
      noIndex = false,
      noFollow = false
    } = options

    const fullTitle = title 
      ? `${title} | ${BASE_SEO.siteName}`
      : BASE_SEO.defaultTitle

    const fullUrl = url 
      ? `${BASE_SEO.siteUrl}${url}`
      : BASE_SEO.siteUrl

    const imageUrl = image 
      ? image.startsWith('http') 
        ? image 
        : `${BASE_SEO.siteUrl}${image}`
      : `${BASE_SEO.siteUrl}/og-image.png`

    const robots = [
      noIndex ? 'noindex' : 'index',
      noFollow ? 'nofollow' : 'follow'
    ].join(', ')

    return {
      title: fullTitle,
      description,
      keywords: keywords.join(', '),
      authors: [{ name: author }],
      creator: author,
      publisher: BASE_SEO.siteName,
      robots,
      
      // Open Graph
      openGraph: {
        title: fullTitle,
        description,
        url: fullUrl,
        siteName: BASE_SEO.siteName,
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: title || BASE_SEO.defaultTitle
          }
        ],
        locale: BASE_SEO.locale,
        type: type === 'article' ? 'article' : 'website',
        ...(publishedTime && { publishedTime }),
        ...(modifiedTime && { modifiedTime })
      },

      // Twitter
      twitter: {
        card: 'summary_large_image',
        title: fullTitle,
        description,
        images: [imageUrl],
        creator: BASE_SEO.twitterHandle,
        site: BASE_SEO.twitterHandle
      },

      // Additional meta tags
      other: {
        'theme-color': BASE_SEO.themeColor,
        'msapplication-TileColor': BASE_SEO.themeColor,
        'apple-mobile-web-app-capable': 'yes',
        'apple-mobile-web-app-status-bar-style': 'default',
        'format-detection': 'telephone=no'
      }
    }
  }

  // Generate structured data (JSON-LD)
  static generateStructuredData(options: {
    type: 'WebSite' | 'WebApplication' | 'Article' | 'Person' | 'Organization' | 'Product'
    data: Record<string, any>
  }) {
    const { type, data } = options

    const baseStructure = {
      '@context': 'https://schema.org',
      '@type': type,
      ...data
    }

    // Add common properties based on type
    switch (type) {
      case 'WebSite':
        return {
          ...baseStructure,
          name: data.name || BASE_SEO.siteName,
          url: data.url || BASE_SEO.siteUrl,
          description: data.description || BASE_SEO.defaultDescription,
          inLanguage: BASE_SEO.language,
          potentialAction: {
            '@type': 'SearchAction',
            target: `${BASE_SEO.siteUrl}/search?q={search_term_string}`,
            'query-input': 'required name=search_term_string'
          }
        }

      case 'WebApplication':
        return {
          ...baseStructure,
          name: data.name || BASE_SEO.siteName,
          url: data.url || BASE_SEO.siteUrl,
          description: data.description || BASE_SEO.defaultDescription,
          applicationCategory: 'DeveloperApplication',
          operatingSystem: 'Web Browser',
          offers: {
            '@type': 'Offer',
            price: '0',
            priceCurrency: 'USD'
          }
        }

      case 'Article':
        return {
          ...baseStructure,
          headline: data.headline,
          description: data.description,
          author: {
            '@type': 'Person',
            name: data.author || BASE_SEO.author
          },
          publisher: {
            '@type': 'Organization',
            name: BASE_SEO.siteName,
            logo: {
              '@type': 'ImageObject',
              url: `${BASE_SEO.siteUrl}/logo.png`
            }
          },
          datePublished: data.datePublished,
          dateModified: data.dateModified || data.datePublished,
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': data.url
          }
        }

      case 'Organization':
        return {
          ...baseStructure,
          name: data.name || BASE_SEO.siteName,
          url: data.url || BASE_SEO.siteUrl,
          description: data.description || BASE_SEO.defaultDescription,
          logo: `${BASE_SEO.siteUrl}/logo.png`,
          sameAs: data.sameAs || [],
          contactPoint: {
            '@type': 'ContactPoint',
            contactType: 'customer service',
            availableLanguage: ['Turkish', 'English']
          }
        }

      default:
        return baseStructure
    }
  }

  // Generate breadcrumb structured data
  static generateBreadcrumbs(items: Array<{ name: string; url: string }>) {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: items.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: item.name,
        item: `${BASE_SEO.siteUrl}${item.url}`
      }))
    }
  }

  // Generate FAQ structured data
  static generateFAQ(faqs: Array<{ question: string; answer: string }>) {
    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: faqs.map(faq => ({
        '@type': 'Question',
        name: faq.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: faq.answer
        }
      }))
    }
  }
}

// SEO component for injecting structured data
export function StructuredData({ data }: { data: Record<string, any> }) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  )
}

// Page-specific SEO configurations
export const PAGE_SEO = {
  home: {
    title: 'Ana Sayfa',
    description: 'AI destekli prompt yönetim platformu ile projelerinizi organize edin ve geliştirme sürecinizi optimize edin.',
    keywords: ['ana sayfa', 'prompt yönetimi', 'AI araçları', 'geliştirici platformu']
  },
  
  dashboard: {
    title: 'Dashboard',
    description: 'Prompt projelerinizi yönetin, organize edin ve AI destekli geliştirme sürecinizi optimize edin.',
    keywords: ['dashboard', 'proje yönetimi', 'prompt organizasyonu'],
    noIndex: true // Private area
  },
  
  profile: {
    title: 'Profil',
    description: 'Kullanıcı profili ve hesap ayarları. Plan bilgilerinizi görüntüleyin ve hesabınızı yönetin.',
    keywords: ['profil', 'hesap ayarları', 'kullanıcı bilgileri'],
    noIndex: true // Private area
  },
  
  auth: {
    title: 'Giriş Yap',
    description: 'PromptFlow hesabınıza giriş yapın veya yeni hesap oluşturun. AI destekli prompt yönetimi başlasın.',
    keywords: ['giriş', 'kayıt', 'hesap oluştur', 'login']
  },
  
  templates: {
    title: 'Şablon Galerisi',
    description: 'Hazır prompt şablonları ile hızlı başlayın. Kategorilere göre düzenlenmiş profesyonel prompt koleksiyonu.',
    keywords: ['şablonlar', 'prompt şablonları', 'hazır promptlar', 'galeri']
  }
} as const

// Sitemap generation helper
export class SitemapGenerator {
  static generateSitemap(pages: Array<{
    url: string
    lastModified?: Date
    changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
    priority?: number
  }>) {
    const urls = pages.map(page => ({
      url: `${BASE_SEO.siteUrl}${page.url}`,
      lastModified: page.lastModified || new Date(),
      changeFrequency: page.changeFrequency || 'weekly',
      priority: page.priority || 0.5
    }))

    return urls
  }

  static generateRobotsTxt(options: {
    allowAll?: boolean
    disallowPaths?: string[]
    sitemapUrl?: string
  } = {}) {
    const {
      allowAll = true,
      disallowPaths = ['/api/', '/admin/', '/_next/'],
      sitemapUrl = `${BASE_SEO.siteUrl}/sitemap.xml`
    } = options

    let robotsTxt = 'User-agent: *\n'
    
    if (allowAll) {
      robotsTxt += 'Allow: /\n'
    }
    
    disallowPaths.forEach(path => {
      robotsTxt += `Disallow: ${path}\n`
    })
    
    robotsTxt += `\nSitemap: ${sitemapUrl}\n`
    
    return robotsTxt
  }
}

// Performance optimization for SEO
export class SEOPerformance {
  static preloadCriticalResources() {
    if (typeof window === 'undefined') return

    // Preload critical fonts
    const fontLinks = [
      '/fonts/inter-var.woff2',
      '/fonts/inter-var-italic.woff2'
    ]

    fontLinks.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'font'
      link.type = 'font/woff2'
      link.crossOrigin = 'anonymous'
      link.href = href
      document.head.appendChild(link)
    })

    // Preload critical images
    const imageLinks = [
      '/logo.png',
      '/og-image.png'
    ]

    imageLinks.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      link.href = href
      document.head.appendChild(link)
    })
  }

  static optimizeImages() {
    if (typeof window === 'undefined') return

    // Add loading="lazy" to images below the fold
    const images = document.querySelectorAll('img:not([loading])')
    
    images.forEach((img, index) => {
      if (index > 2) { // First 3 images load eagerly
        img.setAttribute('loading', 'lazy')
      }
    })
  }

  static addStructuredDataToHead(data: Record<string, any>) {
    if (typeof window === 'undefined') return

    const script = document.createElement('script')
    script.type = 'application/ld+json'
    script.textContent = JSON.stringify(data)
    document.head.appendChild(script)
  }
}
