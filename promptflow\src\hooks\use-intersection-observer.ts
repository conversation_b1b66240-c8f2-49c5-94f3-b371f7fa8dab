import { useEffect, useRef, useState } from 'react'

interface UseIntersectionObserverOptions {
  threshold?: number
  rootMargin?: string
  triggerOnce?: boolean
}

export function useIntersectionObserver(
  options: UseIntersectionObserverOptions = {}
) {
  const {
    threshold = 0.1,
    rootMargin = '0px',
    triggerOnce = true
  } = options

  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasTriggered, setHasTriggered] = useState(false)
  const targetRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const target = targetRef.current
    if (!target) return

    // If triggerOnce is true and already triggered, don't observe
    if (triggerOnce && hasTriggered) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isVisible = entry.isIntersecting
        setIsIntersecting(isVisible)
        
        if (isVisible && triggerOnce) {
          setHasTriggered(true)
        }
      },
      {
        threshold,
        rootMargin
      }
    )

    observer.observe(target)

    return () => {
      observer.disconnect()
    }
  }, [threshold, rootMargin, triggerOnce, hasTriggered])

  return {
    ref: targetRef,
    isIntersecting: triggerOnce ? (hasTriggered || isIntersecting) : isIntersecting,
    hasTriggered
  }
}

// Hook for lazy loading lists with pagination
export function useLazyList<T>(
  items: T[],
  initialCount: number = 10,
  incrementCount: number = 10
) {
  const [visibleCount, setVisibleCount] = useState(initialCount)
  const { ref, isIntersecting } = useIntersectionObserver({
    threshold: 0.5,
    triggerOnce: false
  })

  useEffect(() => {
    if (isIntersecting && visibleCount < items.length) {
      setVisibleCount(prev => Math.min(prev + incrementCount, items.length))
    }
  }, [isIntersecting, visibleCount, items.length, incrementCount])

  const visibleItems = items.slice(0, visibleCount)
  const hasMore = visibleCount < items.length

  return {
    visibleItems,
    hasMore,
    loadMoreRef: ref,
    visibleCount,
    totalCount: items.length
  }
}

// Hook for progressive image loading
export function useProgressiveImage(src: string, placeholder?: string) {
  const [currentSrc, setCurrentSrc] = useState(placeholder || '')
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    if (!src) return

    setIsLoading(true)
    setHasError(false)

    const img = new Image()
    
    img.onload = () => {
      setCurrentSrc(src)
      setIsLoading(false)
    }
    
    img.onerror = () => {
      setHasError(true)
      setIsLoading(false)
    }
    
    img.src = src

    return () => {
      img.onload = null
      img.onerror = null
    }
  }, [src])

  return {
    src: currentSrc,
    isLoading,
    hasError
  }
}

// Hook for lazy loading with retry mechanism
export function useLazyLoadWithRetry<T>(
  loadFunction: () => Promise<T>,
  dependencies: unknown[] = [],
  maxRetries: number = 3
) {
  const [data, setData] = useState<T | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const { ref, isIntersecting } = useIntersectionObserver()

  const load = async () => {
    if (isLoading || (data && retryCount === 0)) return

    setIsLoading(true)
    setError(null)

    try {
      const result = await loadFunction()
      setData(result)
      setRetryCount(0)
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      setError(error)
      
      if (retryCount < maxRetries) {
        setTimeout(() => {
          setRetryCount(prev => prev + 1)
          load()
        }, Math.pow(2, retryCount) * 1000) // Exponential backoff
      }
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (isIntersecting) {
      load()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isIntersecting, ...dependencies])

  const retry = () => {
    setRetryCount(0)
    load()
  }

  return {
    ref,
    data,
    isLoading,
    error,
    retry,
    retryCount,
    canRetry: retryCount < maxRetries
  }
}

// Hook for virtual scrolling with intersection observer
export function useVirtualScrolling(
  itemCount: number,
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) {
  const [scrollTop, setScrollTop] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    itemCount - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )

  const visibleItems = Array.from(
    { length: endIndex - startIndex + 1 },
    (_, index) => startIndex + index
  )

  const totalHeight = itemCount * itemHeight
  const offsetY = startIndex * itemHeight

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const handleScroll = () => {
      setScrollTop(container.scrollTop)
    }

    container.addEventListener('scroll', handleScroll, { passive: true })
    return () => container.removeEventListener('scroll', handleScroll)
  }, [])

  return {
    containerRef,
    visibleItems,
    totalHeight,
    offsetY,
    startIndex,
    endIndex
  }
}
