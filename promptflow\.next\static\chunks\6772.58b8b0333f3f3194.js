"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6772],{42099:(e,s,a)=>{a.d(s,{N:()=>l.L});var l=a(70478);l.L.auth.onAuthStateChange((e,s)=>{var a,l;console.log("\uD83D\uDD10 [SUPABASE_CLIENT] Auth state change: ".concat(e),{hasSession:!!s,userId:null==s||null==(a=s.user)?void 0:a.id,email:null==s||null==(l=s.user)?void 0:l.email,expiresAt:(null==s?void 0:s.expires_at)?new Date(1e3*s.expires_at).toISOString():null})}),l.L.auth.onAuthStateChange(e=>{if("SIGNED_OUT"===e){let e="https://iqehopwgrczylqliajww.supabase.co";e&&localStorage.removeItem("sb-"+e.split("//")[1].split(".")[0]+"-auth-token")}})},86772:(e,s,a)=>{a.r(s),a.d(s,{default:()=>R});var l=a(95155),t=a(12115),i=a(84616),n=a(47924),r=a(66932),c=a(38564),d=a(43332),o=a(34869),x=a(32919),m=a(40646),h=a(14186),u=a(54416),p=a(92657),j=a(24357),g=a(51976),v=a(71007),N=a(44940),f=a(54165),y=a(30285),w=a(62523),b=a(26126),k=a(66695),C=a(66424),A=a(22346),_=a(15790),z=a(53230),E=a(22100),S=a(42099),$=a(26715),D=a(35565),L=a(41038),O=a(56671);function R(e){let{onSelectContext:s,open:a,onOpenChange:R,contentOnly:q=!1}=e,[B,I]=(0,t.useState)(""),[K,P]=(0,t.useState)("all"),[T,G]=(0,t.useState)("all"),[M,Q]=(0,t.useState)(null),[H,U]=(0,t.useState)(!1),[W,Y]=(0,t.useState)(null),[Z,F]=(0,t.useState)(!1),J=(0,t.useRef)(null),{data:V}=(0,E.Jd)(),{data:X=[]}=(0,_.RH)(),ee=(0,_._v)(),es=(0,_.YD)(),{addContext:ea,isLoading:el}=(0,z.KU)(),et=(0,$.jE)(),{data:ei=[]}=(0,_.IL)(),en=(0,t.useMemo)(()=>{let e={};switch("all"!==K&&(e.category_id=K),B&&(e.search=B),T){case"public":e.is_public=!0;break;case"private":e.is_public=!1,e.author_id=null==V?void 0:V.id;break;case"templates":e.is_template=!0;break;case"featured":e.is_featured=!0}return e},[B,K,T,null==V?void 0:V.id]),{data:er=[],isLoading:ec}=(0,_.W2)(en);(0,t.useEffect)(()=>{let e=S.N.channel("context_changes").on("postgres_changes",{event:"*",schema:"public",table:"contexts"},e=>{console.log("Context change received:",e),et.invalidateQueries({queryKey:["contexts"]}),et.invalidateQueries({queryKey:["context-categories"]})}).subscribe();return()=>{e.unsubscribe()}},[et]),(0,t.useEffect)(()=>{var e;let s=null==(e=J.current)?void 0:e.querySelector("[data-radix-scroll-area-viewport]");if(!s)return;let a=e=>{e.stopPropagation()},l=e=>{e.stopPropagation()};return s.addEventListener("wheel",a,{passive:!0}),s.addEventListener("touchstart",l,{passive:!0}),()=>{s.removeEventListener("wheel",a),s.removeEventListener("touchstart",l)}},[]);let ed=async e=>{try{await ea(e),s(e),Q(null)}catch(e){console.error("Failed to add context to project:",e),O.oR.error("Context projeye eklenirken hata oluştu")}},eo=async(e,s)=>{s.stopPropagation();try{await navigator.clipboard.writeText(e.content),await ee.mutateAsync({contextId:e.id,projectId:void 0}),O.oR.success("Context panoya kopyalandı!")}catch(e){console.error("Copy failed:",e),O.oR.error("Kopyalama başarısız oldu")}},ex=async(e,s)=>{s.stopPropagation();try{await es.mutateAsync(e),O.oR.success("Beğeni durumu g\xfcncellendi!")}catch(e){console.error("Like toggle failed:",e),O.oR.error("Beğeni g\xfcncellenemedi")}},em=void 0!==a&&void 0!==R;return q?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"flex items-center justify-between",children:(0,l.jsxs)(y.$,{size:"sm",onClick:()=>U(!0),className:"gap-2 ml-auto",children:[(0,l.jsx)(i.A,{className:"h-4 w-4"}),"Yeni Ekle"]})}),(0,l.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,l.jsx)(w.p,{placeholder:"Context ara... (başlık, a\xe7ıklama, etiketler)",value:B,onChange:e=>I(e.target.value),className:"pl-10"})]}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,l.jsxs)(y.$,{variant:"all"===K?"default":"outline",size:"sm",onClick:()=>P("all"),className:"gap-2",children:[(0,l.jsx)("span",{children:"\uD83D\uDCC2"}),"T\xfcm\xfc"]}),X.map(e=>(0,l.jsxs)(y.$,{variant:K===e.id?"default":"outline",size:"sm",onClick:()=>P(e.id),className:"gap-2",style:K===e.id?{backgroundColor:e.color}:{},children:[(0,l.jsx)("span",{children:e.icon}),e.name]},e.id))]}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,l.jsxs)(y.$,{variant:"all"===T?"default":"outline",size:"sm",onClick:()=>G("all"),className:"gap-2",children:[(0,l.jsx)(r.A,{className:"h-4 w-4"}),"T\xfcm\xfc"]}),(0,l.jsxs)(y.$,{variant:"featured"===T?"default":"outline",size:"sm",onClick:()=>G("featured"),className:"gap-2",children:[(0,l.jsx)(c.A,{className:"h-4 w-4"}),"\xd6ne \xc7ıkan"]}),(0,l.jsxs)(y.$,{variant:"templates"===T?"default":"outline",size:"sm",onClick:()=>G("templates"),className:"gap-2",children:[(0,l.jsx)(d.A,{className:"h-4 w-4"}),"Şablonlar"]}),(0,l.jsxs)(y.$,{variant:"public"===T?"default":"outline",size:"sm",onClick:()=>G("public"),className:"gap-2",children:[(0,l.jsx)(o.A,{className:"h-4 w-4"}),"Herkese A\xe7ık"]}),(0,l.jsxs)(y.$,{variant:"private"===T?"default":"outline",size:"sm",onClick:()=>G("private"),className:"gap-2",children:[(0,l.jsx)(x.A,{className:"h-4 w-4"}),"\xd6zel"]})]})]}),(0,l.jsx)("div",{className:"space-y-4",children:ec?(0,l.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,l.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,l.jsx)("p",{className:"text-gray-500",children:"Contextler y\xfckleniyor..."})]})}):(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-4",children:[er.map(e=>(0,l.jsxs)(k.Zp,{className:"cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] group",onClick:()=>Q(e),children:[(0,l.jsxs)(k.aR,{className:"pb-2",children:[(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{style:{color:e.category.color},children:e.category.icon}),(0,l.jsx)(b.E,{variant:"secondary",className:"text-xs",children:e.category.name})]}),(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[e.is_featured&&(0,l.jsx)("div",{title:"\xd6ne \xc7ıkan",children:(0,l.jsx)(c.A,{className:"h-4 w-4 text-yellow-500 fill-current"})}),e.is_public?(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)("div",{title:"Herkese A\xe7ık",children:(0,l.jsx)(o.A,{className:"h-4 w-4 text-green-500"})}),"approved"===e.approval_status&&(0,l.jsx)("div",{title:"Onaylanmış",children:(0,l.jsx)(m.A,{className:"h-3 w-3 text-green-500"})}),"pending"===e.approval_status&&(0,l.jsx)("div",{title:"Onay Bekliyor",children:(0,l.jsx)(h.A,{className:"h-3 w-3 text-yellow-500"})}),"rejected"===e.approval_status&&(0,l.jsx)("div",{title:"Reddedildi",children:(0,l.jsx)(u.A,{className:"h-3 w-3 text-red-500"})})]}):(0,l.jsx)("div",{title:"\xd6zel",children:(0,l.jsx)(x.A,{className:"h-4 w-4 text-gray-500"})}),e.is_template&&(0,l.jsx)("div",{title:"Şablon",children:(0,l.jsx)(d.A,{className:"h-4 w-4 text-blue-500"})})]})]}),(0,l.jsx)(k.ZB,{className:"text-lg line-clamp-2",children:e.title}),(0,l.jsx)(k.BT,{className:"line-clamp-2",children:e.description})]}),(0,l.jsxs)(k.Wu,{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map((e,s)=>(0,l.jsx)(b.E,{variant:"outline",className:"text-xs",children:e},s)),e.tags.length>3&&(0,l.jsxs)(b.E,{variant:"outline",className:"text-xs",children:["+",e.tags.length-3]})]}),(0,l.jsx)("div",{className:"flex items-center justify-between text-sm text-gray-500",children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(p.A,{className:"h-3 w-3"}),e.view_count]}),(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(j.A,{className:"h-3 w-3"}),e.usage_count]}),(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(g.A,{className:"h-3 w-3"}),e.like_count]})]})}),(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[(0,l.jsx)(v.A,{className:"h-3 w-3"}),e.author_name]}),(0,l.jsxs)("div",{className:"flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,l.jsx)(y.$,{size:"sm",onClick:s=>{s.stopPropagation(),ed(e)},disabled:el,className:"flex-1",children:el?"Ekleniyor...":"Projeye Ekle"}),(0,l.jsx)(y.$,{size:"sm",variant:"outline",onClick:s=>eo(e,s),children:(0,l.jsx)(j.A,{className:"h-4 w-4"})}),V&&e.author_id===V.id&&(0,l.jsx)(y.$,{size:"sm",variant:"outline",onClick:s=>{s.stopPropagation(),Y(e),F(!0)},title:"D\xfczenle",children:(0,l.jsx)(N.A,{className:"h-4 w-4"})}),(0,l.jsx)(y.$,{size:"sm",variant:"outline",onClick:s=>ex(e.id,s),className:ei.includes(e.id)?"text-red-500":"",children:(0,l.jsx)(g.A,{className:"h-4 w-4 ".concat(ei.includes(e.id)?"fill-current":"")})})]})]})]},e.id)),0===er.length&&(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)(n.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Context bulunamadı"}),(0,l.jsx)("p",{className:"text-gray-500",children:"Arama kriterlerinizi değiştirmeyi deneyin veya yeni bir context oluşturun."})]})]})}),M&&(0,l.jsx)(f.lG,{open:!!M,onOpenChange:()=>Q(null),children:(0,l.jsx)(f.Cf,{className:"max-w-4xl max-h-[90vh]",children:(0,l.jsxs)(f.c7,{children:[(0,l.jsxs)(f.L3,{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{style:{color:M.category.color},children:M.category.icon}),M.title]}),(0,l.jsx)("div",{className:"flex items-start justify-between",children:(0,l.jsx)("div",{children:(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,l.jsx)(b.E,{variant:"secondary",children:M.category.name}),M.is_featured&&(0,l.jsxs)(b.E,{className:"bg-yellow-100 text-yellow-800",children:[(0,l.jsx)(c.A,{className:"h-3 w-3 mr-1"}),"\xd6ne \xc7ıkan"]})]})})})]})})}),(0,l.jsx)(D.ContextCreationModal,{open:H,onOpenChange:U,onSuccess:()=>{et.invalidateQueries({queryKey:["contexts"]})}}),(0,l.jsx)(L.ContextEditModal,{open:Z,onOpenChange:F,context:W,onSuccess:()=>{et.invalidateQueries({queryKey:["contexts"]})}})]}):(0,l.jsxs)(f.lG,{open:a,onOpenChange:R,children:[!em&&(0,l.jsx)(f.zM,{asChild:!0,children:(0,l.jsxs)(y.$,{variant:"outline",className:"gap-2 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 hover:from-blue-100 hover:to-indigo-100",children:[(0,l.jsx)(n.A,{className:"h-4 w-4"}),"Context Galerisi"]})}),(0,l.jsxs)(f.Cf,{className:"max-w-6xl max-h-[90vh] flex flex-col",children:[(0,l.jsx)(f.c7,{className:"flex-shrink-0",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(f.L3,{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"Context Galerisi"}),(0,l.jsxs)(y.$,{size:"sm",onClick:()=>U(!0),className:"gap-2",children:[(0,l.jsx)(i.A,{className:"h-4 w-4"}),"Yeni Ekle"]})]})}),(0,l.jsxs)("div",{className:"flex flex-col gap-4 p-2 flex-shrink-0",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,l.jsx)(w.p,{placeholder:"Context ara... (başlık, a\xe7ıklama, etiketler)",value:B,onChange:e=>I(e.target.value),className:"pl-10"})]}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,l.jsxs)(y.$,{variant:"all"===K?"default":"outline",size:"sm",onClick:()=>P("all"),className:"gap-2",children:[(0,l.jsx)("span",{children:"\uD83D\uDCC2"}),"T\xfcm\xfc"]}),X.map(e=>(0,l.jsxs)(y.$,{variant:K===e.id?"default":"outline",size:"sm",onClick:()=>P(e.id),className:"gap-2",style:K===e.id?{backgroundColor:e.color}:{},children:[(0,l.jsx)("span",{children:e.icon}),e.name]},e.id))]}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,l.jsxs)(y.$,{variant:"all"===T?"default":"outline",size:"sm",onClick:()=>G("all"),className:"gap-2",children:[(0,l.jsx)(r.A,{className:"h-4 w-4"}),"T\xfcm\xfc"]}),(0,l.jsxs)(y.$,{variant:"featured"===T?"default":"outline",size:"sm",onClick:()=>G("featured"),className:"gap-2",children:[(0,l.jsx)(c.A,{className:"h-4 w-4"}),"\xd6ne \xc7ıkan"]}),(0,l.jsxs)(y.$,{variant:"templates"===T?"default":"outline",size:"sm",onClick:()=>G("templates"),className:"gap-2",children:[(0,l.jsx)(d.A,{className:"h-4 w-4"}),"Şablonlar"]}),(0,l.jsxs)(y.$,{variant:"public"===T?"default":"outline",size:"sm",onClick:()=>G("public"),className:"gap-2",children:[(0,l.jsx)(o.A,{className:"h-4 w-4"}),"Herkese A\xe7ık"]}),(0,l.jsxs)(y.$,{variant:"private"===T?"default":"outline",size:"sm",onClick:()=>G("private"),className:"gap-2",children:[(0,l.jsx)(x.A,{className:"h-4 w-4"}),"\xd6zel"]})]})]}),(0,l.jsx)(A.w,{className:"flex-shrink-0"}),(0,l.jsx)("div",{className:"flex-1 min-h-0",children:(0,l.jsx)(C.F,{ref:J,className:"h-full w-full",style:{scrollBehavior:"smooth",WebkitOverflowScrolling:"touch"},children:(0,l.jsx)("div",{className:"p-4 space-y-4",children:ec?(0,l.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,l.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,l.jsx)("p",{className:"text-gray-500",children:"Contextler y\xfckleniyor..."})]})}):(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-4",children:[er.map(e=>(0,l.jsx)(k.Zp,{className:"cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] group",onClick:()=>Q(e),children:(0,l.jsx)(k.aR,{className:"pb-2",children:(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{style:{color:e.category.color},children:e.category.icon}),(0,l.jsx)(b.E,{variant:"secondary",className:"text-xs",children:e.category.name})]}),(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[e.is_featured&&(0,l.jsx)("div",{title:"\xd6ne \xc7ıkan",children:(0,l.jsx)(c.A,{className:"h-4 w-4 text-yellow-500 fill-current"})}),e.is_public?(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)("div",{title:"Herkese A\xe7ık",children:(0,l.jsx)(o.A,{className:"h-4 w-4 text-green-500"})}),"approved"===e.approval_status&&(0,l.jsx)("div",{title:"Onaylanmış",children:(0,l.jsx)(m.A,{className:"h-3 w-3 text-green-500"})}),"pending"===e.approval_status&&(0,l.jsx)("div",{title:"Onay Bekliyor",children:(0,l.jsx)(h.A,{className:"h-3 w-3 text-yellow-500"})}),"rejected"===e.approval_status&&(0,l.jsx)("div",{title:"Reddedildi",children:(0,l.jsx)(u.A,{className:"h-3 w-3 text-red-500"})})]}):(0,l.jsx)("div",{title:"\xd6zel",children:(0,l.jsx)(x.A,{className:"h-4 w-4 text-gray-500"})})]})]})})},e.id)),0===er.length&&(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)(n.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Context bulunamadı"}),(0,l.jsx)("p",{className:"text-gray-500",children:"Arama kriterlerinizi değiştirmeyi deneyin veya yeni bir context oluşturun."})]})]})})})})]}),M&&(0,l.jsx)(f.lG,{open:!!M,onOpenChange:()=>Q(null),children:(0,l.jsx)(f.Cf,{className:"max-w-4xl max-h-[90vh]",children:(0,l.jsxs)(f.c7,{children:[(0,l.jsxs)(f.L3,{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{style:{color:M.category.color},children:M.category.icon}),M.title]}),(0,l.jsx)("div",{className:"flex items-start justify-between",children:(0,l.jsx)("div",{children:(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,l.jsx)(b.E,{variant:"secondary",children:M.category.name}),M.is_featured&&(0,l.jsxs)(b.E,{className:"bg-yellow-100 text-yellow-800",children:[(0,l.jsx)(c.A,{className:"h-3 w-3 mr-1"}),"\xd6ne \xc7ıkan"]})]})})})]})})}),(0,l.jsx)(D.ContextCreationModal,{open:H,onOpenChange:U,onSuccess:()=>{et.invalidateQueries({queryKey:["contexts"]})}}),(0,l.jsx)(L.ContextEditModal,{open:Z,onOpenChange:F,context:W,onSuccess:()=>{et.invalidateQueries({queryKey:["contexts"]})}})]})}}}]);