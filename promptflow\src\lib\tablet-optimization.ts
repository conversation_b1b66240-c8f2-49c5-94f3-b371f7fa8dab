/**
 * Tablet Optimization
 * Specialized layout optimizations, orientation handling, and adaptive UI for tablets
 */

import { useState, useEffect } from 'react'
import { DeviceDetection, BREAKPOINTS } from '@/lib/responsive-utils'

// Tablet-specific breakpoints and configurations
export const TABLET_CONFIG = {
  breakpoints: {
    tabletPortrait: 768,
    tabletLandscape: 1024,
    largePad: 1366
  },
  
  layouts: {
    portrait: {
      columns: 1,
      sidebar: 'bottom',
      navigation: 'tabs',
      spacing: 'comfortable'
    },
    landscape: {
      columns: 2,
      sidebar: 'left',
      navigation: 'sidebar',
      spacing: 'compact'
    }
  },
  
  typography: {
    portrait: {
      scale: 1.1,
      lineHeight: 1.6,
      paragraphSpacing: 1.5
    },
    landscape: {
      scale: 1.0,
      lineHeight: 1.5,
      paragraphSpacing: 1.2
    }
  },
  
  touch: {
    targetSize: 48, // Larger than mobile for easier tablet interaction
    spacing: 12,
    gestureThreshold: 60
  }
} as const

// Tablet detection and optimization
export class TabletOptimizer {
  private static currentOrientation: 'portrait' | 'landscape' = 'landscape'
  private static isTabletDevice = false
  
  // Initialize tablet optimization
  static initialize() {
    if (typeof window === 'undefined') return

    this.detectTablet()
    this.handleOrientationChange()
    this.setupEventListeners()
    this.applyTabletOptimizations()
  }

  // Detect if device is a tablet
  private static detectTablet() {
    const width = window.innerWidth
    const height = window.innerHeight
    const minDimension = Math.min(width, height)
    const maxDimension = Math.max(width, height)
    
    // Tablet detection logic
    this.isTabletDevice = (
      minDimension >= TABLET_CONFIG.breakpoints.tabletPortrait &&
      maxDimension >= TABLET_CONFIG.breakpoints.tabletLandscape &&
      DeviceDetection.isTouchDevice() &&
      !this.isDesktopBrowser()
    )
    
    // Add tablet class to body
    if (this.isTabletDevice) {
      document.body.classList.add('is-tablet')
    } else {
      document.body.classList.remove('is-tablet')
    }
  }

  // Check if it's a desktop browser (not a tablet browser)
  private static isDesktopBrowser(): boolean {
    const userAgent = navigator.userAgent.toLowerCase()
    return !(
      userAgent.includes('ipad') ||
      userAgent.includes('android') ||
      userAgent.includes('tablet')
    )
  }

  // Handle orientation changes
  private static handleOrientationChange() {
    const width = window.innerWidth
    const height = window.innerHeight
    
    this.currentOrientation = width > height ? 'landscape' : 'portrait'
    
    // Add orientation class
    document.body.classList.remove('tablet-portrait', 'tablet-landscape')
    if (this.isTabletDevice) {
      document.body.classList.add(`tablet-${this.currentOrientation}`)
    }
    
    // Trigger custom event
    window.dispatchEvent(new CustomEvent('tabletOrientationChange', {
      detail: { orientation: this.currentOrientation }
    }))
  }

  // Setup event listeners
  private static setupEventListeners() {
    window.addEventListener('resize', () => {
      this.detectTablet()
      this.handleOrientationChange()
      this.applyTabletOptimizations()
    })
    
    window.addEventListener('orientationchange', () => {
      // Wait for orientation change to complete
      setTimeout(() => {
        this.handleOrientationChange()
        this.applyTabletOptimizations()
      }, 100)
    })
  }

  // Apply tablet-specific optimizations
  private static applyTabletOptimizations() {
    if (!this.isTabletDevice) return

    this.optimizeLayout()
    this.optimizeTypography()
    this.optimizeTouchTargets()
    this.optimizeNavigation()
  }

  // Layout optimization
  private static optimizeLayout() {
    const config = TABLET_CONFIG.layouts[this.currentOrientation]
    
    // Apply layout classes
    document.body.setAttribute('data-tablet-layout', this.currentOrientation)
    document.body.setAttribute('data-tablet-columns', config.columns.toString())
    document.body.setAttribute('data-tablet-sidebar', config.sidebar)
    document.body.setAttribute('data-tablet-navigation', config.navigation)
  }

  // Typography optimization
  private static optimizeTypography() {
    const config = TABLET_CONFIG.typography[this.currentOrientation]
    
    // Apply typography scale
    document.documentElement.style.setProperty('--tablet-font-scale', config.scale.toString())
    document.documentElement.style.setProperty('--tablet-line-height', config.lineHeight.toString())
    document.documentElement.style.setProperty('--tablet-paragraph-spacing', `${config.paragraphSpacing}rem`)
  }

  // Touch target optimization
  private static optimizeTouchTargets() {
    const config = TABLET_CONFIG.touch
    
    // Apply touch target sizes
    document.documentElement.style.setProperty('--tablet-touch-target', `${config.targetSize}px`)
    document.documentElement.style.setProperty('--tablet-touch-spacing', `${config.spacing}px`)
  }

  // Navigation optimization
  private static optimizeNavigation() {
    const config = TABLET_CONFIG.layouts[this.currentOrientation]
    
    // Find navigation elements and optimize them
    const navElements = document.querySelectorAll('nav, [role="navigation"]')
    navElements.forEach(nav => {
      nav.setAttribute('data-tablet-nav-style', config.navigation)
    })
  }

  // Public getters
  static get isTablet(): boolean {
    return this.isTabletDevice
  }

  static get orientation(): 'portrait' | 'landscape' {
    return this.currentOrientation
  }

  static get config() {
    return TABLET_CONFIG.layouts[this.currentOrientation]
  }
}

// Tablet-specific CSS utilities
export const TABLET_CSS = `
  /* Tablet base styles */
  .is-tablet {
    --touch-target-size: ${TABLET_CONFIG.touch.targetSize}px;
    --touch-spacing: ${TABLET_CONFIG.touch.spacing}px;
  }

  /* Portrait orientation */
  .tablet-portrait {
    --columns: 1;
    --sidebar-position: bottom;
    --font-scale: ${TABLET_CONFIG.typography.portrait.scale};
    --line-height: ${TABLET_CONFIG.typography.portrait.lineHeight};
  }

  /* Landscape orientation */
  .tablet-landscape {
    --columns: 2;
    --sidebar-position: left;
    --font-scale: ${TABLET_CONFIG.typography.landscape.scale};
    --line-height: ${TABLET_CONFIG.typography.landscape.lineHeight};
  }

  /* Touch-friendly elements */
  .is-tablet button,
  .is-tablet a,
  .is-tablet input,
  .is-tablet select {
    min-height: var(--touch-target-size);
    min-width: var(--touch-target-size);
    padding: var(--touch-spacing);
  }

  /* Tablet-specific layouts */
  .tablet-grid {
    display: grid;
    grid-template-columns: repeat(var(--columns), 1fr);
    gap: var(--touch-spacing);
  }

  /* Sidebar positioning */
  .tablet-sidebar {
    position: relative;
  }

  .tablet-landscape .tablet-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    width: 280px;
  }

  .tablet-portrait .tablet-sidebar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    height: auto;
  }

  /* Navigation styles */
  [data-tablet-nav-style="tabs"] {
    display: flex;
    justify-content: space-around;
    border-top: 1px solid #e5e7eb;
    padding: 12px 0;
  }

  [data-tablet-nav-style="sidebar"] {
    display: flex;
    flex-direction: column;
    padding: 24px 16px;
  }

  /* Typography scaling */
  .is-tablet {
    font-size: calc(1rem * var(--font-scale));
    line-height: var(--line-height);
  }

  .is-tablet p {
    margin-bottom: var(--tablet-paragraph-spacing, 1.2rem);
  }

  /* Responsive images for tablets */
  .is-tablet img {
    max-width: 100%;
    height: auto;
  }

  /* Form optimizations */
  .is-tablet form {
    max-width: none;
  }

  .tablet-landscape form {
    max-width: 600px;
    margin: 0 auto;
  }

  /* Modal optimizations */
  .is-tablet .modal {
    max-width: 90vw;
    max-height: 90vh;
  }

  .tablet-landscape .modal {
    max-width: 70vw;
    max-height: 80vh;
  }

  /* Table optimizations */
  .is-tablet table {
    font-size: 0.9rem;
  }

  .tablet-portrait table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  /* Card layouts */
  .tablet-card-grid {
    display: grid;
    gap: 16px;
  }

  .tablet-portrait .tablet-card-grid {
    grid-template-columns: 1fr;
  }

  .tablet-landscape .tablet-card-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
`

// React hook for tablet optimization
export function useTabletOptimization() {
  const [isTablet, setIsTablet] = useState(false)
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('landscape')

  useEffect(() => {
    // Initialize tablet optimizer
    TabletOptimizer.initialize()
    
    // Set initial state
    setIsTablet(TabletOptimizer.isTablet)
    setOrientation(TabletOptimizer.orientation)

    // Listen for orientation changes
    const handleOrientationChange = (event: CustomEvent) => {
      setOrientation(event.detail.orientation)
    }

    window.addEventListener('tabletOrientationChange', handleOrientationChange as EventListener)
    
    // Listen for resize events
    const handleResize = () => {
      setIsTablet(TabletOptimizer.isTablet)
      setOrientation(TabletOptimizer.orientation)
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('tabletOrientationChange', handleOrientationChange as EventListener)
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return {
    isTablet,
    orientation,
    config: TabletOptimizer.config,
    isPortrait: orientation === 'portrait',
    isLandscape: orientation === 'landscape'
  }
}

// Inject tablet CSS
export function injectTabletStyles() {
  if (typeof document === 'undefined') return

  const styleId = 'tablet-optimization-styles'
  if (document.getElementById(styleId)) return

  const style = document.createElement('style')
  style.id = styleId
  style.textContent = TABLET_CSS
  document.head.appendChild(style)
}

// Auto-initialize on module load
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      injectTabletStyles()
      TabletOptimizer.initialize()
    })
  } else {
    injectTabletStyles()
    TabletOptimizer.initialize()
  }
}
