"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[686],{88539:(e,s,a)=>{a.d(s,{T:()=>r});var t=a(95155);a(12115);var l=a(59434);function r(e){let{className:s,...a}=e;return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,l.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...a})}},98305:(e,s,a)=>{a.r(s),a.d(s,{ContextSidebar:()=>N,default:()=>w});var t=a(95155),l=a(12115),r=a(30285),i=a(66695),n=a(88539),c=a(26126),d=a(22346),x=a(57434),o=a(42355),m=a(13052),h=a(54416),u=a(62559),j=a(52918),g=a(4229),p=a(54653),f=a(62525),v=a(1243),b=a(41978),y=a(67238);function N(e){let{onClose:s,isCollapsed:a=!1,onToggleCollapse:N,isContextGalleryOpen:w=!1,onToggleContextGallery:k}=e,[A,C]=(0,l.useState)(""),[z,S]=(0,l.useState)(!1),[P,$]=(0,l.useState)(null),[T,E]=(0,l.useState)(!1),[_,K]=(0,l.useState)(""),{activeProjectId:B,setActiveProjectId:D,isContextEnabled:L,setIsContextEnabled:R}=(0,b.C)(),{data:W}=(0,y.By)(B),Z=(0,y.sS)(),F=(0,y.eW)();(0,l.useEffect)(()=>{W?C(W.context_text||""):C("")},[W]);let G=async()=>{if(B){S(!0);try{await Z.mutateAsync({id:B,context_text:A}),$(new Date)}catch(e){console.error("Context kaydetme hatası:",e)}finally{S(!1)}}},M=async()=>{if(B&&W){if(_!==W.name)return void alert("Proje adını doğru yazın!");try{await F.mutateAsync(B),D(null),E(!1),K("")}catch(e){console.error("Proje silme hatası:",e),alert("Proje silinirken bir hata oluştu!")}}};return B?(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsx)("div",{className:"border-b border-gray-200 ".concat(a?"p-2":"p-4 lg:p-6"),children:(0,t.jsxs)("div",{className:"flex items-center ".concat(a?"justify-center mb-2":"justify-between mb-4"),children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 ".concat(a?"flex-col":""),children:[N&&(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:N,className:"hidden xl:flex",title:a?"Ayarlar panelini genişlet":"Ayarlar panelini daralt",children:a?(0,t.jsx)(o.A,{className:"h-4 w-4"}):(0,t.jsx)(m.A,{className:"h-4 w-4"})}),!a&&(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Context Ayarları"})]}),(0,t.jsx)("div",{className:"flex items-center gap-2 ".concat(a?"flex-col":""),children:s&&(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:s,className:"xl:hidden",children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})})]})}),(0,t.jsx)("div",{className:"flex-1 ".concat(a?"p-2":"p-6"),children:a?(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>R(!L),className:"w-8 h-8 p-0 rounded-md transition-all ".concat(L?"bg-green-100 text-green-700 hover:bg-green-200":"bg-gray-100 text-gray-500 hover:bg-gray-200"),title:"Context ".concat(L?"Aktif":"Pasif"),children:L?(0,t.jsx)(u.A,{className:"h-4 w-4"}):(0,t.jsx)(j.A,{className:"h-4 w-4"})}),(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:G,disabled:z,className:"w-8 h-8 p-0",title:"Context Kaydet",children:(0,t.jsx)(g.A,{className:"h-4 w-4"})})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsxs)(r.$,{variant:w?"default":"outline",size:"sm",onClick:k,className:"w-full justify-center transition-all duration-200 ".concat(w?"bg-purple-600 hover:bg-purple-700 text-white shadow-md":"border-purple-200 text-purple-600 hover:text-purple-700 hover:border-purple-300 hover:bg-purple-50"),children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Context Gallery"]})}),(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"\xd6n Tanımlı Metin"}),(0,t.jsxs)(r.$,{variant:"ghost",size:"sm",onClick:()=>R(!L),className:"flex items-center gap-2 px-3 py-1 rounded-md transition-all ".concat(L?"bg-green-100 text-green-700 hover:bg-green-200":"bg-gray-100 text-gray-500 hover:bg-gray-200"),children:[L?(0,t.jsx)(u.A,{className:"h-4 w-4"}):(0,t.jsx)(j.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-xs font-medium",children:L?"Aktif":"Pasif"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[z&&(0,t.jsx)(c.E,{variant:"secondary",className:"text-xs",children:"Kaydediliyor..."}),P&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["Son kaydedilme: ",P.toLocaleTimeString("tr-TR")]})]})]}),(0,t.jsx)(n.T,{placeholder:"T\xfcm promptların başına eklenecek context metninizi buraya yazın...",value:A,onChange:e=>C(e.target.value),className:"min-h-[200px] resize-none",disabled:z}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(r.$,{variant:"default",size:"sm",onClick:()=>{G()},disabled:z||!A.trim(),className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),z?"Kaydediliyor...":"Kaydet"]}),(0,t.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>{C(""),$(null)},disabled:z,className:"text-red-600 hover:text-red-700",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Temizle"]})]}),(0,t.jsx)(d.w,{className:"my-6"}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{className:"text-base",children:"Proje Ayarları"})}),(0,t.jsxs)(i.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Karakter Sayısı"}),(0,t.jsx)(c.E,{variant:"outline",children:A.length.toLocaleString()})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Proje Adı"}),(0,t.jsx)(c.E,{variant:"outline",children:null==W?void 0:W.name})]}),(0,t.jsx)(d.w,{}),T?(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 text-red-600 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"Dikkat!"}),(0,t.jsx)("p",{className:"text-xs text-red-700",children:"Bu işlem geri alınamaz. Proje ve t\xfcm prompt'lar silinecek."})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-700",children:["Onaylamak i\xe7in proje adını yazın: ",(0,t.jsx)("span",{className:"text-red-600 font-semibold",children:null==W?void 0:W.name})]}),(0,t.jsx)("input",{type:"text",value:_,onChange:e=>K(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500",placeholder:"Proje adını yazın..."})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(r.$,{variant:"outline",size:"sm",onClick:M,disabled:_!==(null==W?void 0:W.name)||F.isPending,className:"flex-1 text-red-600 hover:text-red-700 hover:bg-red-50",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),F.isPending?"Siliniyor...":"Sil"]}),(0,t.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>{E(!1),K("")},className:"flex-1",children:"İptal"})]})]}):(0,t.jsxs)(r.$,{variant:"outline",size:"sm",className:"w-full text-red-600 hover:text-red-700 hover:bg-red-50",onClick:()=>E(!0),children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Projeyi Sil"]})]})]})]})})]}):(0,t.jsx)("div",{className:"flex items-center justify-center h-full p-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(x.A,{className:"".concat(a?"h-6 w-6":"h-12 w-12"," text-gray-400 mx-auto mb-4")}),!a&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Ayarlar"}),(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"Proje se\xe7tikten sonra ayarları buradan y\xf6netebilirsiniz"})]})]})})}let w=N}}]);