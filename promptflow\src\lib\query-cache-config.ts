import { QueryClient, QueryCache, MutationCache } from '@tanstack/react-query'

// Cache configuration constants
export const CACHE_TIMES = {
  // Short-lived data (user interactions)
  SHORT: 1 * 60 * 1000, // 1 minute
  
  // Medium-lived data (prompts, projects)
  MEDIUM: 5 * 60 * 1000, // 5 minutes
  
  // Long-lived data (user profile, plans)
  LONG: 15 * 60 * 1000, // 15 minutes
  
  // Static data (categories, templates)
  STATIC: 60 * 60 * 1000, // 1 hour
} as const

export const GC_TIMES = {
  SHORT: 2 * 60 * 1000, // 2 minutes
  MEDIUM: 10 * 60 * 1000, // 10 minutes
  LONG: 30 * 60 * 1000, // 30 minutes
  STATIC: 2 * 60 * 60 * 1000, // 2 hours
} as const

// Query key factories for consistent caching
export const queryKeys = {
  // User-related queries
  user: {
    profile: () => ['user', 'profile'] as const,
    plan: () => ['user', 'plan'] as const,
    limits: () => ['user', 'limits'] as const,
  },
  
  // Project-related queries
  projects: {
    all: () => ['projects'] as const,
    detail: (id: string) => ['projects', id] as const,
    prompts: (id: string) => ['projects', id, 'prompts'] as const,
  },
  
  // Prompt-related queries
  prompts: {
    all: (projectId: string) => ['prompts', projectId] as const,
    detail: (id: string) => ['prompts', 'detail', id] as const,
    search: (projectId: string, query: string) => ['prompts', projectId, 'search', query] as const,
  },
  
  // Context-related queries
  contexts: {
    all: () => ['contexts'] as const,
    categories: () => ['contexts', 'categories'] as const,
    templates: (category?: string) => ['contexts', 'templates', category] as const,
  },
  
  // Hashtag-related queries
  hashtags: {
    all: (projectId: string) => ['hashtags', projectId] as const,
    popular: (projectId: string) => ['hashtags', projectId, 'popular'] as const,
  },
  
  // Analytics queries
  analytics: {
    usage: () => ['analytics', 'usage'] as const,
    stats: (period: string) => ['analytics', 'stats', period] as const,
  }
} as const

// Cache invalidation patterns
export const invalidationPatterns = {
  // When user updates profile
  userUpdate: () => [
    queryKeys.user.profile(),
    queryKeys.user.plan(),
    queryKeys.user.limits(),
  ],
  
  // When project is created/updated/deleted
  projectUpdate: (projectId?: string) => [
    queryKeys.projects.all(),
    ...(projectId ? [queryKeys.projects.detail(projectId)] : []),
  ],
  
  // When prompt is created/updated/deleted
  promptUpdate: (projectId: string, promptId?: string) => [
    queryKeys.prompts.all(projectId),
    queryKeys.projects.prompts(projectId),
    queryKeys.hashtags.all(projectId),
    queryKeys.hashtags.popular(projectId),
    ...(promptId ? [queryKeys.prompts.detail(promptId)] : []),
  ],
  
  // When context is created/updated
  contextUpdate: () => [
    queryKeys.contexts.all(),
    queryKeys.contexts.categories(),
    queryKeys.contexts.templates(),
  ],
} as const

// Enhanced query client configuration
export function createOptimizedQueryClient() {
  const queryCache = new QueryCache({
    onError: (error, query) => {
      console.error('Query error:', error, 'Query key:', query.queryKey)
    },
    onSuccess: (data, query) => {
      // Log successful cache hits for debugging
      if (process.env.NODE_ENV === 'development') {
        console.log('Query success:', query.queryKey, 'Data:', data)
      }
    },
  })

  const mutationCache = new MutationCache({
    onError: (error, variables, context, mutation) => {
      console.error('Mutation error:', error, 'Variables:', variables)
    },
    onSuccess: (data, variables) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('Mutation success:', 'Data:', data)
      }
    },
  })

  return new QueryClient({
    queryCache,
    mutationCache,
    defaultOptions: {
      queries: {
        // Default cache times
        staleTime: CACHE_TIMES.MEDIUM,
        gcTime: GC_TIMES.MEDIUM,
        
        // Retry configuration
        retry: (failureCount, error) => {
          // Don't retry on auth errors
          if (error instanceof Error && (
            error.message.includes('401') ||
            error.message.includes('403') ||
            error.message.includes('Unauthorized')
          )) {
            return false
          }
          
          // Don't retry on client errors (4xx)
          if (error instanceof Error && error.message.includes('4')) {
            return false
          }
          
          // Retry up to 3 times for other errors
          return failureCount < 3
        },
        
        // Retry delay with exponential backoff
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        
        // Background refetch settings
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
        refetchOnMount: true,
        
        // Network mode
        networkMode: 'online',
      },
      
      mutations: {
        // Retry mutations once
        retry: 1,
        retryDelay: 1000,
        networkMode: 'online',
      },
    },
  })
}

// Cache utilities
export const cacheUtils = {
  // Prefetch data
  prefetchQuery: async (queryClient: QueryClient, queryKey: unknown[], queryFn: () => Promise<unknown>) => {
    await queryClient.prefetchQuery({
      queryKey,
      queryFn,
      staleTime: CACHE_TIMES.MEDIUM,
    })
  },
  
  // Invalidate specific patterns
  invalidateQueries: async (queryClient: QueryClient, patterns: unknown[][]) => {
    await Promise.all(
      patterns.map(pattern => 
        queryClient.invalidateQueries({ queryKey: pattern })
      )
    )
  },
  
  // Set query data manually
  setQueryData: <T>(queryClient: QueryClient, queryKey: unknown[], data: T) => {
    queryClient.setQueryData(queryKey, data)
  },
  
  // Get cached data
  getQueryData: <T>(queryClient: QueryClient, queryKey: unknown[]): T | undefined => {
    return queryClient.getQueryData<T>(queryKey)
  },
  
  // Remove specific queries from cache
  removeQueries: (queryClient: QueryClient, patterns: unknown[][]) => {
    patterns.forEach(pattern => {
      queryClient.removeQueries({ queryKey: pattern })
    })
  },
  
  // Clear all cache
  clearCache: (queryClient: QueryClient) => {
    queryClient.clear()
  },
  
  // Get cache stats
  getCacheStats: (queryClient: QueryClient) => {
    const cache = queryClient.getQueryCache()
    const queries = cache.getAll()
    
    return {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.getObserversCount() > 0).length,
      staleQueries: queries.filter(q => q.isStale()).length,
      invalidQueries: queries.filter(q => (q as any).isInvalidated?.() || false).length,
      cacheSize: JSON.stringify(queries).length, // Approximate size
    }
  }
} as const

// Cache warming strategies
export const cacheWarming = {
  // Warm essential data on app start
  warmEssentialData: async (queryClient: QueryClient) => {
    // Prefetch user profile and plan
    await Promise.allSettled([
      cacheUtils.prefetchQuery(queryClient, [...queryKeys.user.profile()], async () => {
        // This would be replaced with actual API call
        return null
      }),
      cacheUtils.prefetchQuery(queryClient, [...queryKeys.user.plan()], async () => {
        return null
      }),
      cacheUtils.prefetchQuery(queryClient, [...queryKeys.contexts.categories()], async () => {
        return null
      }),
    ])
  },
  
  // Warm project data when user navigates to dashboard
  warmProjectData: async (queryClient: QueryClient, projectId: string) => {
    await Promise.allSettled([
      cacheUtils.prefetchQuery(queryClient, [...queryKeys.projects.detail(projectId)], async () => {
        return null
      }),
      cacheUtils.prefetchQuery(queryClient, [...queryKeys.prompts.all(projectId)], async () => {
        return null
      }),
      cacheUtils.prefetchQuery(queryClient, [...queryKeys.hashtags.all(projectId)], async () => {
        return null
      }),
    ])
  }
} as const
