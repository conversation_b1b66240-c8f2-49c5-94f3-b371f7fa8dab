import { useState, useCallback, useRef } from 'react'
import { useQueryClient, useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'

interface OptimisticUpdate<T> {
  id: string
  type: 'create' | 'update' | 'delete'
  data: T
  queryKey: unknown[]
  rollbackData?: T
  timestamp: number
}

interface UseOptimisticUpdatesOptions<T> {
  queryKey: unknown[]
  mutationFn: (data: T) => Promise<T>
  onSuccess?: (data: T) => void
  onError?: (error: Error, data: T) => void
  enableRollback?: boolean
  showToasts?: boolean
}

export function useOptimisticUpdates<T extends { id?: string }>({
  queryKey,
  mutationFn,
  onSuccess,
  onError,
  enableRollback = true,
  showToasts = true
}: UseOptimisticUpdatesOptions<T>) {
  const queryClient = useQueryClient()
  const [pendingUpdates, setPendingUpdates] = useState<OptimisticUpdate<T>[]>([])
  const rollbackTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Generate unique ID for optimistic updates
  const generateOptimisticId = useCallback(() => {
    return `optimistic_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }, [])

  // Apply optimistic update to query cache
  const applyOptimisticUpdate = useCallback((
    type: 'create' | 'update' | 'delete',
    data: T,
    tempId?: string
  ) => {
    const optimisticId = tempId || generateOptimisticId()
    const optimisticData = { ...data, id: data.id || optimisticId }

    // Store the current data for potential rollback
    const currentData = queryClient.getQueryData(queryKey)

    const update: OptimisticUpdate<T> = {
      id: optimisticId,
      type,
      data: optimisticData,
      queryKey,
      rollbackData: currentData as T,
      timestamp: Date.now()
    }

    setPendingUpdates(prev => [...prev, update])

    // Apply optimistic update to cache
    queryClient.setQueryData(queryKey, (oldData: unknown) => {
      if (!oldData) return oldData

      if (Array.isArray(oldData)) {
        switch (type) {
          case 'create':
            return [optimisticData, ...oldData]
          case 'update':
            return oldData.map(item => 
              (item as T).id === optimisticData.id ? optimisticData : item
            )
          case 'delete':
            return oldData.filter(item => (item as T).id !== optimisticData.id)
          default:
            return oldData
        }
      }

      // Handle paginated data structure
      if (typeof oldData === 'object' && oldData !== null && 'pages' in oldData) {
        const paginatedData = oldData as { pages: Array<{ data: T[] }> }
        return {
          ...paginatedData,
          pages: paginatedData.pages.map((page, index) => {
            if (index === 0) { // Apply to first page
              switch (type) {
                case 'create':
                  return { ...page, data: [optimisticData, ...page.data] }
                case 'update':
                  return {
                    ...page,
                    data: page.data.map(item => 
                      item.id === optimisticData.id ? optimisticData : item
                    )
                  }
                case 'delete':
                  return {
                    ...page,
                    data: page.data.filter(item => item.id !== optimisticData.id)
                  }
                default:
                  return page
              }
            }
            return page
          })
        }
      }

      return oldData
    })

    if (showToasts) {
      const messages = {
        create: 'Ekleniyor...',
        update: 'Güncelleniyor...',
        delete: 'Siliniyor...'
      }
      toast.loading(messages[type], { id: optimisticId })
    }

    return optimisticId
  }, [queryKey, queryClient, generateOptimisticId, showToasts])

  // Confirm optimistic update (remove from pending)
  const confirmOptimisticUpdate = useCallback((optimisticId: string, realData?: T) => {
    setPendingUpdates(prev => prev.filter(update => update.id !== optimisticId))

    if (realData) {
      // Replace optimistic data with real data
      queryClient.setQueryData(queryKey, (oldData: unknown) => {
        if (!oldData) return oldData

        if (Array.isArray(oldData)) {
          return oldData.map(item => 
            (item as T).id === optimisticId ? realData : item
          )
        }

        if (typeof oldData === 'object' && oldData !== null && 'pages' in oldData) {
          const paginatedData = oldData as { pages: Array<{ data: T[] }> }
          return {
            ...paginatedData,
            pages: paginatedData.pages.map(page => ({
              ...page,
              data: page.data.map(item => 
                item.id === optimisticId ? realData : item
              )
            }))
          }
        }

        return oldData
      })
    }

    if (showToasts) {
      toast.dismiss(optimisticId)
      toast.success('İşlem tamamlandı')
    }
  }, [queryKey, queryClient, showToasts])

  // Rollback optimistic update
  const rollbackOptimisticUpdate = useCallback((optimisticId: string) => {
    const update = pendingUpdates.find(u => u.id === optimisticId)
    if (!update || !enableRollback) return

    // Restore original data
    if (update.rollbackData) {
      queryClient.setQueryData(queryKey, update.rollbackData)
    } else {
      // If no rollback data, invalidate the query
      queryClient.invalidateQueries({ queryKey })
    }

    setPendingUpdates(prev => prev.filter(u => u.id !== optimisticId))

    if (showToasts) {
      toast.dismiss(optimisticId)
      toast.error('İşlem başarısız oldu')
    }
  }, [pendingUpdates, enableRollback, queryKey, queryClient, showToasts])

  // Create mutation with optimistic updates
  const mutation = useMutation({
    mutationFn,
    onMutate: async (data: T) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey })

      // Apply optimistic update
      const optimisticId = applyOptimisticUpdate('update', data)
      
      return { optimisticId, data }
    },
    onSuccess: (realData, variables, context) => {
      if (context?.optimisticId) {
        confirmOptimisticUpdate(context.optimisticId, realData)
      }
      onSuccess?.(realData)
    },
    onError: (error, variables, context) => {
      if (context?.optimisticId) {
        rollbackOptimisticUpdate(context.optimisticId)
      }
      onError?.(error as Error, variables)
    },
    onSettled: () => {
      // Clear any remaining rollback timeouts
      if (rollbackTimeoutRef.current) {
        clearTimeout(rollbackTimeoutRef.current)
      }
    }
  })

  // Optimistic create
  const optimisticCreate = useCallback((data: Omit<T, 'id'>) => {
    const optimisticId = applyOptimisticUpdate('create', data as T)
    
    // Auto-rollback after timeout if not confirmed
    if (enableRollback) {
      rollbackTimeoutRef.current = setTimeout(() => {
        rollbackOptimisticUpdate(optimisticId)
      }, 10000) // 10 second timeout
    }

    return optimisticId
  }, [applyOptimisticUpdate, enableRollback, rollbackOptimisticUpdate])

  // Optimistic update
  const optimisticUpdate = useCallback((data: T) => {
    const optimisticId = applyOptimisticUpdate('update', data)
    
    if (enableRollback) {
      rollbackTimeoutRef.current = setTimeout(() => {
        rollbackOptimisticUpdate(optimisticId)
      }, 10000)
    }

    return optimisticId
  }, [applyOptimisticUpdate, enableRollback, rollbackOptimisticUpdate])

  // Optimistic delete
  const optimisticDelete = useCallback((data: T) => {
    const optimisticId = applyOptimisticUpdate('delete', data)
    
    if (enableRollback) {
      rollbackTimeoutRef.current = setTimeout(() => {
        rollbackOptimisticUpdate(optimisticId)
      }, 10000)
    }

    return optimisticId
  }, [applyOptimisticUpdate, enableRollback, rollbackOptimisticUpdate])

  return {
    mutation,
    optimisticCreate,
    optimisticUpdate,
    optimisticDelete,
    confirmOptimisticUpdate,
    rollbackOptimisticUpdate,
    pendingUpdates,
    isPending: mutation.isPending || pendingUpdates.length > 0
  }
}

// Hook for batch optimistic updates
export function useBatchOptimisticUpdates<T extends { id?: string }>({
  queryKey,
  mutationFn,
  onSuccess,
  onError,
  showToasts = true
}: Omit<UseOptimisticUpdatesOptions<T[]>, 'enableRollback'>) {
  const queryClient = useQueryClient()
  const [pendingBatches, setPendingBatches] = useState<string[]>([])

  const batchMutation = useMutation({
    mutationFn,
    onMutate: async (dataArray: T[]) => {
      await queryClient.cancelQueries({ queryKey })
      
      const batchId = `batch_${Date.now()}`
      setPendingBatches(prev => [...prev, batchId])

      // Apply all optimistic updates
      dataArray.forEach(data => {
        queryClient.setQueryData(queryKey, (oldData: unknown) => {
          if (Array.isArray(oldData)) {
            return oldData.map(item => 
              (item as T).id === data.id ? data : item
            )
          }
          return oldData
        })
      })

      if (showToasts) {
        toast.loading(`${dataArray.length} öğe güncelleniyor...`, { id: batchId })
      }

      return { batchId, dataArray }
    },
    onSuccess: (realData, variables, context) => {
      if (context?.batchId) {
        setPendingBatches(prev => prev.filter(id => id !== context.batchId))
        
        if (showToasts) {
          toast.dismiss(context.batchId)
          toast.success(`${variables.length} öğe başarıyla güncellendi`)
        }
      }
      onSuccess?.(realData)
    },
    onError: (error, variables, context) => {
      if (context?.batchId) {
        setPendingBatches(prev => prev.filter(id => id !== context.batchId))
        queryClient.invalidateQueries({ queryKey })
        
        if (showToasts) {
          toast.dismiss(context.batchId)
          toast.error('Toplu güncelleme başarısız oldu')
        }
      }
      onError?.(error as Error, variables)
    }
  })

  return {
    batchMutation,
    isPending: batchMutation.isPending || pendingBatches.length > 0,
    pendingBatches
  }
}
