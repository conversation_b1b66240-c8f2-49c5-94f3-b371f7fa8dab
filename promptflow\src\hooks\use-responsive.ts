'use client'

import React, { useState, useEffect, useCallback, useMemo } from 'react'

// Enhanced breakpoint system
export const BREAKPOINTS = {
  xs: 0,      // Extra small devices (phones)
  sm: 640,    // Small devices (large phones)
  md: 768,    // Medium devices (tablets)
  lg: 1024,   // Large devices (laptops)
  xl: 1280,   // Extra large devices (desktops)
  '2xl': 1536 // 2X large devices (large desktops)
} as const

export type Breakpoint = keyof typeof BREAKPOINTS
export type BreakpointValue = typeof BREAKPOINTS[Breakpoint]

// Device type detection
export type DeviceType = 'mobile' | 'tablet' | 'desktop'
export type Orientation = 'portrait' | 'landscape'

interface ResponsiveState {
  width: number
  height: number
  breakpoint: Breakpoint
  deviceType: DeviceType
  orientation: Orientation
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isTouch: boolean
  pixelRatio: number
}

// Enhanced responsive hook with device detection
export function useResponsive(): ResponsiveState {
  const [state, setState] = useState<ResponsiveState>(() => {
    if (typeof window === 'undefined') {
      return {
        width: 1024,
        height: 768,
        breakpoint: 'lg' as Breakpoint,
        deviceType: 'desktop' as DeviceType,
        orientation: 'landscape' as Orientation,
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        isTouch: false,
        pixelRatio: 1
      }
    }

    const width = window.innerWidth
    const height = window.innerHeight
    const breakpoint = getBreakpoint(width)
    const deviceType = getDeviceType(width)
    const orientation = width > height ? 'landscape' : 'portrait'
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0

    return {
      width,
      height,
      breakpoint,
      deviceType,
      orientation,
      isMobile: deviceType === 'mobile',
      isTablet: deviceType === 'tablet',
      isDesktop: deviceType === 'desktop',
      isTouch,
      pixelRatio: window.devicePixelRatio || 1
    }
  })

  const updateState = useCallback(() => {
    if (typeof window === 'undefined') return

    const width = window.innerWidth
    const height = window.innerHeight
    const breakpoint = getBreakpoint(width)
    const deviceType = getDeviceType(width)
    const orientation = width > height ? 'landscape' : 'portrait'
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0

    setState({
      width,
      height,
      breakpoint,
      deviceType,
      orientation,
      isMobile: deviceType === 'mobile',
      isTablet: deviceType === 'tablet',
      isDesktop: deviceType === 'desktop',
      isTouch,
      pixelRatio: window.devicePixelRatio || 1
    })
  }, [])

  useEffect(() => {
    if (typeof window === 'undefined') return

    // Debounced resize handler
    let timeoutId: NodeJS.Timeout
    const debouncedUpdate = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(updateState, 150)
    }

    window.addEventListener('resize', debouncedUpdate)
    window.addEventListener('orientationchange', updateState)

    return () => {
      window.removeEventListener('resize', debouncedUpdate)
      window.removeEventListener('orientationchange', updateState)
      clearTimeout(timeoutId)
    }
  }, [updateState])

  return state
}

// Utility functions
function getBreakpoint(width: number): Breakpoint {
  if (width >= BREAKPOINTS['2xl']) return '2xl'
  if (width >= BREAKPOINTS.xl) return 'xl'
  if (width >= BREAKPOINTS.lg) return 'lg'
  if (width >= BREAKPOINTS.md) return 'md'
  if (width >= BREAKPOINTS.sm) return 'sm'
  return 'xs'
}

function getDeviceType(width: number): DeviceType {
  if (width < BREAKPOINTS.md) return 'mobile'
  if (width < BREAKPOINTS.lg) return 'tablet'
  return 'desktop'
}

// Media query hook
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    if (typeof window === 'undefined') return

    const mediaQuery = window.matchMedia(query)
    setMatches(mediaQuery.matches)

    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    mediaQuery.addEventListener('change', handler)
    return () => mediaQuery.removeEventListener('change', handler)
  }, [query])

  return matches
}

// Breakpoint-specific hooks
export function useBreakpoint(breakpoint: Breakpoint): boolean {
  return useMediaQuery(`(min-width: ${BREAKPOINTS[breakpoint]}px)`)
}

export function useBreakpointValue<T>(values: Partial<Record<Breakpoint, T>>): T | undefined {
  const { breakpoint } = useResponsive()
  
  return useMemo(() => {
    // Find the best matching value for current breakpoint
    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs']
    const currentIndex = breakpointOrder.indexOf(breakpoint)
    
    // Look for exact match first
    if (values[breakpoint] !== undefined) {
      return values[breakpoint]
    }
    
    // Look for smaller breakpoints
    for (let i = currentIndex + 1; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i]
      if (values[bp] !== undefined) {
        return values[bp]
      }
    }
    
    return undefined
  }, [breakpoint, values])
}

// Container query hook (for component-level responsive design)
export function useContainerQuery(containerRef: React.RefObject<HTMLElement>) {
  const [containerWidth, setContainerWidth] = useState(0)

  useEffect(() => {
    if (!containerRef.current) return

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setContainerWidth(entry.contentRect.width)
      }
    })

    resizeObserver.observe(containerRef.current)

    return () => {
      resizeObserver.disconnect()
    }
  }, [containerRef])

  return {
    containerWidth,
    isContainerSmall: containerWidth < 400,
    isContainerMedium: containerWidth >= 400 && containerWidth < 768,
    isContainerLarge: containerWidth >= 768
  }
}

// Responsive grid hook
export function useResponsiveGrid(options: {
  minItemWidth: number
  gap?: number
  maxColumns?: number
}) {
  const { width } = useResponsive()
  const { minItemWidth, gap = 16, maxColumns = Infinity } = options

  return useMemo(() => {
    const availableWidth = width - gap
    const itemsPerRow = Math.floor(availableWidth / (minItemWidth + gap))
    const columns = Math.min(Math.max(itemsPerRow, 1), maxColumns)
    const itemWidth = (availableWidth - (columns - 1) * gap) / columns

    return {
      columns,
      itemWidth,
      gridTemplateColumns: `repeat(${columns}, 1fr)`,
      gap: `${gap}px`
    }
  }, [width, minItemWidth, gap, maxColumns])
}

// Safe area hook for mobile devices
export function useSafeArea() {
  const [safeArea, setSafeArea] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  })

  useEffect(() => {
    if (typeof window === 'undefined') return

    const updateSafeArea = () => {
      const style = getComputedStyle(document.documentElement)
      
      setSafeArea({
        top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
        right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0'),
        bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
        left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0')
      })
    }

    updateSafeArea()
    window.addEventListener('resize', updateSafeArea)
    window.addEventListener('orientationchange', updateSafeArea)

    return () => {
      window.removeEventListener('resize', updateSafeArea)
      window.removeEventListener('orientationchange', updateSafeArea)
    }
  }, [])

  return safeArea
}

// Responsive text scaling
export function useResponsiveText() {
  const { deviceType, width } = useResponsive()

  return useMemo(() => {
    const baseSize = 16 // Base font size in px
    
    if (deviceType === 'mobile') {
      // Ensure minimum 16px to prevent zoom on iOS
      return Math.max(baseSize, 16)
    }
    
    if (deviceType === 'tablet') {
      return baseSize * 1.1
    }
    
    // Desktop scaling based on width
    if (width > 1920) {
      return baseSize * 1.2
    }
    
    return baseSize
  }, [deviceType, width])
}

// Performance-optimized responsive component wrapper
export function withResponsive<P extends object>(
  Component: React.ComponentType<P & { responsive: ResponsiveState }>
) {
  return function ResponsiveComponent(props: P) {
    const responsive = useResponsive()
    const combinedProps = { ...props, responsive } as P & { responsive: ResponsiveState }

    return React.createElement(Component, combinedProps)
  }
}
