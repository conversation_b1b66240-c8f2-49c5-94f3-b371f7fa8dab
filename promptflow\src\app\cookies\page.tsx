import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  ArrowLeft, 
  Sparkles, 
  Cookie, 
  Settings, 
  Eye, 
  BarChart3,
  Shield,
  Globe,
  CheckCircle,
  XCircle
} from 'lucide-react'

export const metadata: Metadata = {
  title: 'Çerez Politikası - PromptBir | Cookie Policy',
  description: 'PromptBir çerez politikası. Web sitemizdeki çerezlerin kullanımı, türleri ve yönetimi hakkında detaylı bilgi.',
  keywords: [
    'çerez politikası',
    'cookie policy',
    'çerez yönetimi',
    'web çerezleri',
    'GDPR çerezler',
    'çerez türleri'
  ],
  openGraph: {
    title: 'Çerez Politikası - PromptBir',
    description: 'Web sitemizdeki çerezlerin kullanımı ve yönetimi hakkında bilgi',
    type: 'website',
    url: 'https://promptbir.com/cookies'
  }
}

export default function CookiesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link 
              href="/" 
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Ana Sayfaya Dön</span>
            </Link>
            <div className="flex items-center space-x-2">
              <Sparkles className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">PromptBir</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Cookie className="h-8 w-8 text-orange-600" />
          </div>
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
            Çerez Politikası
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Bu politika, PromptBir web sitesinde kullanılan çerezler hakkında bilgi sağlar. 
            Çerezlerin nasıl kullanıldığını ve bunları nasıl yönetebileceğinizi öğrenin.
          </p>
          <p className="text-sm text-gray-500 mt-4">
            Son güncelleme: 1 Ocak 2024
          </p>
        </div>

        {/* What are Cookies */}
        <Card className="shadow-lg mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Cookie className="h-6 w-6 text-orange-600" />
              Çerez Nedir?
            </CardTitle>
          </CardHeader>
          <CardContent className="prose prose-gray max-w-none">
            <p className="mb-4">
              Çerezler, web sitelerinin kullanıcıların cihazlarında sakladığı küçük metin dosyalarıdır. 
              Bu dosyalar, web sitesinin daha iyi çalışmasını sağlar ve kullanıcı deneyimini geliştirir.
            </p>
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="font-semibold text-blue-900 mb-2">Çerezler Nasıl Çalışır?</h4>
              <p className="text-blue-800 text-sm">
                Web sitesini ziyaret ettiğinizde, çerezler tarayıcınızda saklanır. 
                Bir sonraki ziyaretinizde, web sitesi bu bilgileri okuyarak size 
                kişiselleştirilmiş bir deneyim sunar.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Cookie Types */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {/* Essential Cookies */}
          <Card className="shadow-lg border-green-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-green-700">
                <Shield className="h-6 w-6" />
                Zorunlu Çerezler
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                Web sitesinin temel işlevlerini yerine getirmesi için gerekli çerezlerdir.
              </p>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>Oturum yönetimi</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>Güvenlik doğrulaması</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>Form verilerinin korunması</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>Dil tercihleri</span>
                </li>
              </ul>
              <div className="mt-4 p-3 bg-green-50 rounded-lg">
                <p className="text-green-800 text-sm font-medium">
                  Bu çerezler devre dışı bırakılamaz
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Performance Cookies */}
          <Card className="shadow-lg border-blue-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-blue-700">
                <BarChart3 className="h-6 w-6" />
                Performans Çerezleri
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                Web sitesinin performansını analiz etmek ve iyileştirmek için kullanılır.
              </p>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <BarChart3 className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>Sayfa yükleme süreleri</span>
                </li>
                <li className="flex items-start gap-2">
                  <BarChart3 className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>Kullanıcı etkileşimleri</span>
                </li>
                <li className="flex items-start gap-2">
                  <BarChart3 className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>Hata raporları</span>
                </li>
                <li className="flex items-start gap-2">
                  <BarChart3 className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>Trafik analizi</span>
                </li>
              </ul>
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-blue-800 text-sm font-medium">
                  Bu çerezler anonim veri toplar
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Functional Cookies */}
          <Card className="shadow-lg border-purple-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-purple-700">
                <Settings className="h-6 w-6" />
                İşlevsel Çerezler
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                Kullanıcı tercihlerini hatırlamak ve kişiselleştirilmiş deneyim sunmak için kullanılır.
              </p>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <Settings className="h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0" />
                  <span>Tema tercihleri (açık/koyu)</span>
                </li>
                <li className="flex items-start gap-2">
                  <Settings className="h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0" />
                  <span>Dil seçimleri</span>
                </li>
                <li className="flex items-start gap-2">
                  <Settings className="h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0" />
                  <span>Kullanıcı arayüzü ayarları</span>
                </li>
                <li className="flex items-start gap-2">
                  <Settings className="h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0" />
                  <span>Son kullanılan özellikler</span>
                </li>
              </ul>
              <div className="mt-4 p-3 bg-purple-50 rounded-lg">
                <p className="text-purple-800 text-sm font-medium">
                  Bu çerezler deneyimi kişiselleştirir
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Targeting Cookies */}
          <Card className="shadow-lg border-orange-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-orange-700">
                <Eye className="h-6 w-6" />
                Hedefleme Çerezleri
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                Şu anda hedefleme çerezleri kullanmıyoruz, ancak gelecekte kullanabiliriz.
              </p>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <XCircle className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-500">Reklam hedefleme (kullanılmıyor)</span>
                </li>
                <li className="flex items-start gap-2">
                  <XCircle className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-500">Sosyal medya entegrasyonu (kullanılmıyor)</span>
                </li>
                <li className="flex items-start gap-2">
                  <XCircle className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-500">Üçüncü taraf izleme (kullanılmıyor)</span>
                </li>
              </ul>
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <p className="text-gray-600 text-sm font-medium">
                  Gizliliğinize saygı duyuyoruz
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Cookie Management */}
        <Card className="shadow-lg mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Settings className="h-6 w-6 text-blue-600" />
              Çerez Yönetimi
            </CardTitle>
          </CardHeader>
          <CardContent className="prose prose-gray max-w-none">
            <h4 className="font-semibold mb-2">Tarayıcı Ayarları:</h4>
            <p className="mb-4">
              Çoğu tarayıcı çerezleri otomatik olarak kabul eder, ancak bunu değiştirebilirsiniz:
            </p>
            <ul className="list-disc pl-6 mb-6 space-y-2">
              <li><strong>Chrome:</strong> Ayarlar → Gizlilik ve güvenlik → Çerezler</li>
              <li><strong>Firefox:</strong> Ayarlar → Gizlilik ve Güvenlik → Çerezler</li>
              <li><strong>Safari:</strong> Tercihler → Gizlilik → Çerezler</li>
              <li><strong>Edge:</strong> Ayarlar → Çerezler ve site izinleri</li>
            </ul>

            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-4">
              <h4 className="font-semibold text-yellow-900 mb-2">⚠️ Önemli Uyarı</h4>
              <p className="text-yellow-800 text-sm">
                Zorunlu çerezleri devre dışı bırakırsanız, web sitesinin bazı özellikleri 
                düzgün çalışmayabilir. Oturum açma, form gönderme ve güvenlik özellikleri 
                etkilenebilir.
              </p>
            </div>

            <h4 className="font-semibold mb-2">Platform İçi Ayarlar:</h4>
            <p className="mb-4">
              Hesabınıza giriş yaptıktan sonra, profil ayarlarından çerez tercihlerinizi 
              yönetebilirsiniz.
            </p>
          </CardContent>
        </Card>

        {/* Third Party Services */}
        <Card className="shadow-lg mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Globe className="h-6 w-6 text-purple-600" />
              Üçüncü Taraf Hizmetler
            </CardTitle>
          </CardHeader>
          <CardContent className="prose prose-gray max-w-none">
            <p className="mb-4">Platformumuzda kullanılan üçüncü taraf hizmetler:</p>
            
            <div className="space-y-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold mb-2">Supabase (Veritabanı)</h4>
                <p className="text-sm text-gray-600 mb-2">
                  Kullanıcı verilerini güvenli şekilde saklamak için kullanılır.
                </p>
                <p className="text-xs text-gray-500">
                  Çerez Türü: Zorunlu | Süre: Oturum süresi
                </p>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold mb-2">Vercel (Hosting)</h4>
                <p className="text-sm text-gray-600 mb-2">
                  Web sitesinin barındırılması ve performans optimizasyonu için kullanılır.
                </p>
                <p className="text-xs text-gray-500">
                  Çerez Türü: Performans | Süre: 24 saat
                </p>
              </div>
            </div>

            <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-blue-800 text-sm">
                <strong>Not:</strong> Üçüncü taraf hizmetlerin kendi çerez politikaları vardır. 
                Bu hizmetlerin çerez kullanımı hakkında daha fazla bilgi için ilgili 
                şirketlerin gizlilik politikalarını inceleyebilirsiniz.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Legal Compliance */}
        <Card className="shadow-lg mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Shield className="h-6 w-6 text-green-600" />
              Yasal Uyumluluk
            </CardTitle>
          </CardHeader>
          <CardContent className="prose prose-gray max-w-none">
            <h4 className="font-semibold mb-2">GDPR Uyumluluğu:</h4>
            <ul className="list-disc pl-6 mb-4 space-y-1">
              <li>Çerez kullanımı için açık rıza alınır</li>
              <li>Çerez türleri ve amaçları açıkça belirtilir</li>
              <li>Kullanıcılar çerezleri yönetebilir</li>
              <li>Veri işleme süreleri belirtilir</li>
            </ul>

            <h4 className="font-semibold mb-2">KVKK Uyumluluğu:</h4>
            <ul className="list-disc pl-6 space-y-1">
              <li>Kişisel veri işleme amaçları belirtilir</li>
              <li>Veri saklama süreleri açıklanır</li>
              <li>Kullanıcı hakları korunur</li>
              <li>Veri güvenliği sağlanır</li>
            </ul>
          </CardContent>
        </Card>

        {/* Cookie Consent */}
        <Card className="shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <CardContent className="p-8 text-center">
            <Cookie className="h-16 w-16 mx-auto mb-6 text-blue-100" />
            <h2 className="text-2xl font-bold mb-4">Çerez Tercihlerinizi Yönetin</h2>
            <p className="mb-6 text-blue-100">
              Çerez ayarlarınızı istediğiniz zaman değiştirebilirsiniz.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                variant="secondary" 
                size="lg"
                className="bg-white text-blue-600 hover:bg-blue-50"
              >
                Çerez Ayarları
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                Tümünü Kabul Et
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Contact */}
        <Card className="shadow-lg mt-8">
          <CardHeader>
            <CardTitle>İletişim</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 mb-4">
              Çerez politikamız hakkında sorularınız varsa bizimle iletişime geçin:
            </p>
            <div className="space-y-2 text-sm">
              <p><strong>E-posta:</strong> <EMAIL></p>
              <p><strong>Genel Sorular:</strong> <EMAIL></p>
              <p><strong>KVKK Sorumlusu:</strong> <EMAIL></p>
            </div>
          </CardContent>
        </Card>

        {/* Updates */}
        <Card className="shadow-lg mt-8 border-orange-200 bg-orange-50">
          <CardContent className="p-6">
            <h3 className="font-bold text-orange-900 mb-2">Politika Güncellemeleri</h3>
            <p className="text-orange-800 text-sm">
              Bu çerez politikası gerektiğinde güncellenebilir. Önemli değişiklikler 
              web sitesinde duyurulacak ve kullanıcılara bildirilecektir. 
              Güncellemeleri düzenli olarak kontrol etmenizi öneririz.
            </p>
          </CardContent>
        </Card>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-400">
            © 2024 PromptBir. Tüm hakları saklıdır.
          </p>
        </div>
      </footer>
    </div>
  )
}
