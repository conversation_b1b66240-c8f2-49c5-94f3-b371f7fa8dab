(()=>{var a={};a.id=8796,a.ids=[8796],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7670:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Masa\xfcst\xfc\\\\Promptbir\\\\promptflow\\\\src\\\\app\\\\share\\\\[token]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\share\\[token]\\page.tsx","default")},8266:(a,b,c)=>{"use strict";c.d(b,{L:()=>d});let d=(0,c(59522).createBrowserClient)("https://iqehopwgrczylqliajww.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlxZWhvcHdncmN6eWxxbGlhand3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2OTQyNzksImV4cCI6MjA2ODI3MDI3OX0.9zrZ8Zot6SvsjxTGMyh3IYFqrr_QXQkKSNU4rJNz0VM",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0,flowType:"pkce",debug:!1,storageKey:"sb-iqehopwgrczylqliajww-auth-token",storage:void 0},db:{schema:"public"},realtime:{params:{eventsPerSecond:10}},global:{headers:{"X-Client-Info":"promptflow-web","X-Client-Version":"1.0.0"}}})},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14163:(a,b,c)=>{"use strict";c.d(b,{hO:()=>i,sG:()=>h});var d=c(43210),e=c(51215),f=c(8730),g=c(60687),h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,f.TL)(`Primitive.${b}`),e=d.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return e.displayName=`Primitive.${b}`,{...a,[b]:e}},{});function i(a,b){a&&e.flushSync(()=>a.dispatchEvent(b))}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},35950:(a,b,c)=>{"use strict";c.d(b,{w:()=>g});var d=c(60687);c(43210);var e=c(62369),f=c(4780);function g({className:a,orientation:b="horizontal",decorative:c=!0,...g}){return(0,d.jsx)(e.b,{"data-slot":"separator",decorative:c,orientation:b,className:(0,f.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...g})}},36185:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["share",{children:["[token]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,7670)),"C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\share\\[token]\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,39998)),"C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\share\\[token]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/share/[token]/page",pathname:"/share/[token]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/share/[token]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},37360:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},39727:()=>{},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47990:()=>{},48432:(a,b,c)=>{Promise.resolve().then(c.bind(c,7670))},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},58160:(a,b,c)=>{Promise.resolve().then(c.bind(c,62605))},58869:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62369:(a,b,c)=>{"use strict";c.d(b,{b:()=>j});var d=c(43210),e=c(14163),f=c(60687),g="horizontal",h=["horizontal","vertical"],i=d.forwardRef((a,b)=>{var c;let{decorative:d,orientation:i=g,...j}=a,k=(c=i,h.includes(c))?i:g;return(0,f.jsx)(e.sG.div,{"data-orientation":k,...d?{role:"none"}:{"aria-orientation":"vertical"===k?k:void 0,role:"separator"},...j,ref:b})});i.displayName="Separator";var j=i},62605:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>x});var d=c(60687),e=c(43210),f=c(16189),g=c(44493),h=c(29523),i=c(89667),j=c(96834),k=c(35950),l=c(43649),m=c(64021),n=c(81620);let o=(0,c(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var p=c(5336),q=c(70615),r=c(58869),s=c(13861),t=c(40228),u=c(37360),v=c(77781),w=c(52581);function x(){let a=(0,f.useParams)().token,[b,c]=(0,e.useState)(""),[x,y]=(0,e.useState)(!1),[z,A]=(0,e.useState)(!1),{data:B,error:C,isLoading:D,refetch:E}=(0,v.v8)(a,b),F=()=>{b.trim()&&E()},G=async()=>{if(B)try{let a=B.prompt.task_code||`task-${B.prompt.id.slice(-4)}`,b=`${a}
${B.prompt.prompt_text}`;await navigator.clipboard.writeText(b),A(!0),w.oR.success("Prompt panoya kopyalandı!"),setTimeout(()=>A(!1),2e3)}catch(a){w.oR.error("Kopyalama başarısız")}},H=async()=>{let a=window.location.href;try{await navigator.clipboard.writeText(a),w.oR.success("Link panoya kopyalandı!")}catch(a){w.oR.error("Link kopyalanamadı")}};return D?(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,d.jsxs)(g.Zp,{className:"w-full max-w-2xl",children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse mb-2"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-2/3"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-3/4"}),(0,d.jsx)("div",{className:"h-20 bg-gray-200 rounded animate-pulse"})]})})]})}):C&&!x?(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center p-4",children:(0,d.jsxs)(g.Zp,{className:"w-full max-w-md",children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,d.jsx)(l.A,{className:"h-5 w-5"}),"Hata"]}),(0,d.jsx)(g.BT,{children:C.message})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)(h.$,{onClick:()=>window.location.href="/",className:"w-full",children:"Ana Sayfaya D\xf6n"})})]})}):x&&!B?(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-yellow-100 flex items-center justify-center p-4",children:(0,d.jsxs)(g.Zp,{className:"w-full max-w-md",children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(m.A,{className:"h-5 w-5"}),"Şifre Gerekli"]}),(0,d.jsx)(g.BT,{children:"Bu paylaşım şifre ile korunmaktadır"})]}),(0,d.jsxs)(g.Wu,{className:"space-y-4",children:[(0,d.jsx)("div",{children:(0,d.jsx)(i.p,{type:"password",placeholder:"Şifre girin",value:b,onChange:a=>c(a.target.value),onKeyDown:a=>{"Enter"===a.key&&F()}})}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(h.$,{onClick:F,disabled:!b.trim(),className:"flex-1",children:"G\xf6r\xfcnt\xfcle"}),(0,d.jsx)(h.$,{variant:"outline",onClick:()=>window.location.href="/",children:"İptal"})]}),C?.message==="Şifre yanlış"&&(0,d.jsx)("p",{className:"text-sm text-red-600 text-center",children:"Şifre yanlış, l\xfctfen tekrar deneyin"})]})]})}):B?(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,d.jsx)("div",{className:"bg-white border-b",children:(0,d.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,d.jsx)(n.A,{className:"h-4 w-4 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"PromptBir"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Paylaşılan Prompt"})]})]}),(0,d.jsxs)(h.$,{variant:"outline",size:"sm",onClick:()=>window.location.href="/",children:[(0,d.jsx)(o,{className:"h-4 w-4 mr-2"}),"PromptBir'e Git"]})]})})}),(0,d.jsxs)("div",{className:"max-w-4xl mx-auto p-4 py-8",children:[(0,d.jsxs)(g.Zp,{className:"shadow-lg",children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)(g.ZB,{className:"text-xl mb-2",children:B.title||B.prompt.task_code||"Paylaşılan Prompt"}),B.description&&(0,d.jsx)(g.BT,{className:"text-base",children:B.description})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(h.$,{variant:"outline",size:"sm",onClick:H,children:(0,d.jsx)(n.A,{className:"h-4 w-4"})}),(0,d.jsx)(h.$,{onClick:G,disabled:z,className:z?"bg-green-600 hover:bg-green-700":"",children:z?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Kopyalandı"]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Kopyala"]})})]})]}),(0,d.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 mt-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(r.A,{className:"h-4 w-4"}),B.author_email]}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(s.A,{className:"h-4 w-4"}),B.view_count," g\xf6r\xfcnt\xfclenme"]}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(t.A,{className:"h-4 w-4"}),new Date(B.created_at).toLocaleDateString("tr-TR")]})]})]}),(0,d.jsxs)(g.Wu,{className:"space-y-6",children:[(0,d.jsx)(k.w,{}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-3",children:"Prompt İ\xe7eriği"}),(0,d.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 border",children:(0,d.jsxs)("div",{className:"font-mono text-sm whitespace-pre-wrap",children:[B.prompt.task_code&&(0,d.jsx)("div",{className:"text-blue-600 font-semibold mb-2",children:B.prompt.task_code}),(0,d.jsx)("div",{className:"text-gray-800",children:B.prompt.prompt_text})]})})]}),B.prompt.tags&&B.prompt.tags.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("h4",{className:"text-sm font-medium mb-2 flex items-center gap-2",children:[(0,d.jsx)(u.A,{className:"h-4 w-4"}),"Etiketler"]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:B.prompt.tags.map((a,b)=>(0,d.jsx)(j.E,{variant:"secondary",children:a},b))})]}),(0,d.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,d.jsxs)("h4",{className:"text-sm font-medium text-blue-900 mb-1",children:["Proje: ",B.project_name]}),(0,d.jsxs)("p",{className:"text-xs text-blue-700",children:['Bu prompt "',B.project_name,'" projesinden paylaşılmıştır']})]})]})]}),(0,d.jsx)("div",{className:"text-center mt-8 text-sm text-gray-500",children:(0,d.jsxs)("p",{children:["Bu prompt"," ",(0,d.jsx)("a",{href:"/",className:"text-blue-600 hover:text-blue-700 font-medium",children:"PromptBir"})," ","ile paylaşılmıştır"]})})]})]}):null}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},70615:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},74075:a=>{"use strict";a.exports=require("zlib")},77781:(a,b,c)=>{"use strict";c.d(b,{X1:()=>i,Xm:()=>j,v8:()=>k});var d=c(51423),e=c(8693),f=c(54050),g=c(8266),h=c(52581);function i(){return(0,d.I)({queryKey:["shared-prompts","user"],queryFn:async()=>{let{data:a}=await g.L.auth.getUser();if(!a.user)throw Error("Not authenticated");let{data:b,error:c}=await g.L.from("shared_prompts").select(`
          *,
          prompt:prompts(
            id,
            prompt_text,
            title,
            description,
            category,
            tags,
            task_code,
            created_at,
            project:projects(name)
          )
        `).eq("user_id",a.user.id).eq("is_active",!0).order("created_at",{ascending:!1});if(c)throw c;return b.map(a=>({...a,project_name:a.prompt?.project?.name||"Unknown Project"}))},staleTime:3e5})}function j(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async a=>{let{data:b}=await g.L.auth.getUser();if(!b.user)throw Error("Not authenticated");let{data:c,error:d}=await g.L.from("prompts").select("id, user_id").eq("id",a.prompt_id).eq("user_id",b.user.id).single();if(d||!c)throw Error("Prompt not found or access denied");let e=function(){let a=new Uint8Array(16);return crypto.getRandomValues(a),Array.from(a,a=>a.toString(16).padStart(2,"0")).join("")}(),f=null;a.password&&(f=await l(a.password));let{data:h,error:i}=await g.L.from("shared_prompts").insert({prompt_id:a.prompt_id,user_id:b.user.id,share_token:e,title:a.title||null,description:a.description||null,is_public:a.is_public??!0,password_hash:f,expires_at:a.expires_at||null}).select().single();if(i)throw i;let j=`${window.location.origin}/share/${e}`;return{id:h.id,share_token:e,share_url:j,created_at:h.created_at}},onSuccess:b=>{a.invalidateQueries({queryKey:["shared-prompts"]}),h.oR.success("Paylaşım linki oluşturuldu!",{description:"Link panoya kopyalandı"}),navigator.clipboard.writeText(b.share_url).catch(console.error)},onError:a=>{console.error("Share creation error:",a),h.oR.error("Paylaşım oluşturulamadı",{description:a instanceof Error?a.message:"Bilinmeyen hata"})}})}function k(a,b){return(0,d.I)({queryKey:["shared-prompt",a,b],queryFn:async()=>{let{data:c,error:d}=await g.L.from("shared_prompts").select(`
          *,
          prompt:prompts(
            id,
            prompt_text,
            title,
            description,
            category,
            tags,
            task_code,
            created_at,
            project:projects(name)
          )
        `).eq("share_token",a).eq("is_active",!0).single();if(d)throw Error("Paylaşım bulunamadı veya s\xfcresi dolmuş");if(c.password_hash&&b){if(!await m(b,c.password_hash))throw Error("Şifre yanlış")}else if(c.password_hash&&!b)throw Error("Şifre gerekli");if(c.expires_at&&new Date(c.expires_at)<new Date)throw Error("Paylaşımın s\xfcresi dolmuş");return g.L.from("shared_prompts").update({view_count:c.view_count+1}).eq("id",c.id).then(()=>{fetch("/api/shared-prompts/record-view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({share_token:a,viewer_ip:null,viewer_user_agent:navigator.userAgent,referrer:document.referrer||null,session_id:`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,viewed_at:new Date().toISOString()})}).catch(console.error)}),{...c,project_name:c.prompt?.project?.name||"Unknown Project",author_email:"Unknown Author"}},enabled:!!a,staleTime:0,retry:!1})}async function l(a){let b=new TextEncoder().encode(a);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",b)),a=>a.toString(16).padStart(2,"0")).join("")}async function m(a,b){return await l(a)===b}},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81620:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>f});var d=c(60687);c(43210);var e=c(4780);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,904,3303,1093],()=>b(b.s=36185));module.exports=c})();