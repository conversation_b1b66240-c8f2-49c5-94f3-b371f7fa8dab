'use client'

import { useState, useEffect, Suspense, lazy } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ContextGallerySkeleton } from "@/components/progressive-loader";

import { FileText, Save, Trash2, Settings, AlertTriangle, ToggleLeft, ToggleRight, X, ChevronLeft, ChevronRight, Grid3X3 } from "lucide-react";
import { useAppStore } from "@/store/app-store";
import { useProject, useUpdateProject, useDeleteProject } from "@/hooks/use-projects";



interface ContextSidebarProps {
  onClose?: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  isContextGalleryOpen?: boolean;
  onToggleContextGallery?: () => void;
}

export function ContextSidebar({
  onClose,
  isCollapsed = false,
  onToggleCollapse,
  isContextGalleryOpen = false,
  onToggleContextGallery
}: ContextSidebarProps) {
  const [contextText, setContextText] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const { activeProjectId, setActiveProjectId, isContextEnabled, setIsContextEnabled } = useAppStore();
  
  const { data: project } = useProject(activeProjectId);
  const updateProjectMutation = useUpdateProject();
  const deleteProjectMutation = useDeleteProject();


  // Aktif proje değiştiğinde context'i yükle
  useEffect(() => {
    if (project) {
      setContextText(project.context_text || "");
    } else {
      setContextText("");
    }
  }, [project]);

  // Otomatik kayıt devre dışı bırakıldı - artık sadece manuel kayıt
  // useEffect(() => {
  //   if (!activeProjectId || !contextText.trim()) return;

  //   const timer = setTimeout(() => {
  //     handleSaveContext();
  //   }, 2000);

  //   return () => clearTimeout(timer);
  // // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [contextText, activeProjectId]);

  const handleSaveContext = async () => {
    if (!activeProjectId) return;

    setIsSaving(true);
    try {
      await updateProjectMutation.mutateAsync({
        id: activeProjectId,
        context_text: contextText,
      });
      
      setLastSaved(new Date());
    } catch (error) {
      console.error('Context kaydetme hatası:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleClearContext = () => {
    setContextText("");
    setLastSaved(null);
  };

  const handleManualSave = () => {
    handleSaveContext();
  };

  const handleDeleteProject = async () => {
    if (!activeProjectId || !project) return;

    // Proje adı kontrolü
    if (deleteConfirmText !== project.name) {
      alert('Proje adını doğru yazın!');
      return;
    }

    try {
      await deleteProjectMutation.mutateAsync(activeProjectId);
      setActiveProjectId(null);
      setShowDeleteConfirm(false);
      setDeleteConfirmText("");
    } catch (error) {
      console.error('Proje silme hatası:', error);
      alert('Proje silinirken bir hata oluştu!');
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
    setDeleteConfirmText("");
  };



  if (!activeProjectId) {
    return (
      <div className="flex items-center justify-center h-full p-6">
        <div className="text-center">
          <FileText className={`${isCollapsed ? 'h-6 w-6' : 'h-12 w-12'} text-gray-400 mx-auto mb-4`} />
          {!isCollapsed && (
            <>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Context Alanı</h3>
              <p className="text-gray-500 text-sm">Proje seçtikten sonra context metninizi buraya yazabilirsiniz</p>
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className={`border-b border-gray-200 ${isCollapsed ? 'p-2' : 'p-4 lg:p-6'}`}>
        <div className={`flex items-center ${isCollapsed ? 'justify-center mb-2' : 'justify-between mb-4'}`}>
          <div className={`flex items-center gap-2 ${isCollapsed ? 'flex-col' : ''}`}>
            {/* Desktop Toggle Button */}
            {onToggleCollapse && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleCollapse}
                className="hidden xl:flex"
                title={isCollapsed ? 'Ayarlar panelini genişlet' : 'Ayarlar panelini daralt'}
              >
                {isCollapsed ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </Button>
            )}
            {!isCollapsed && <h3 className="text-lg font-semibold text-gray-900">Context Ayarları</h3>}
          </div>
          <div className={`flex items-center gap-2 ${isCollapsed ? 'flex-col' : ''}`}>
            {/* Mobile Close Button */}
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose} className="xl:hidden">
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>


      </div>

      {/* Context Editor */}
      <div className={`flex-1 ${isCollapsed ? 'p-2' : 'p-6'}`}>
        {isCollapsed ? (
          <div className="flex flex-col items-center space-y-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsContextEnabled(!isContextEnabled)}
              className={`w-8 h-8 p-0 rounded-md transition-all ${
                isContextEnabled
                  ? 'bg-green-100 text-green-700 hover:bg-green-200'
                  : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
              }`}
              title={`Context ${isContextEnabled ? 'Aktif' : 'Pasif'}`}
            >
              {isContextEnabled ? (
                <ToggleRight className="h-4 w-4" />
              ) : (
                <ToggleLeft className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSaveContext}
              disabled={isSaving}
              className="w-8 h-8 p-0"
              title="Context Kaydet"
            >
              <Save className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
          {/* Context Gallery Button */}
          <div className="mb-4">
            <Button
              variant={isContextGalleryOpen ? "default" : "outline"}
              size="sm"
              onClick={onToggleContextGallery}
              className={`w-full justify-center transition-all duration-200 ${
                isContextGalleryOpen
                  ? "bg-purple-600 hover:bg-purple-700 text-white shadow-md"
                  : "border-purple-200 text-purple-600 hover:text-purple-700 hover:border-purple-300 hover:bg-purple-50"
              }`}
            >
              <Grid3X3 className="h-4 w-4 mr-2" />
              Context Gallery
            </Button>
          </div>

          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">
                Ön Tanımlı Metin
              </label>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsContextEnabled(!isContextEnabled)}
                className={`flex items-center gap-2 px-3 py-1 rounded-md transition-all ${
                  isContextEnabled 
                    ? 'bg-green-100 text-green-700 hover:bg-green-200' 
                    : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
                }`}
              >
                {isContextEnabled ? (
                  <ToggleRight className="h-4 w-4" />
                ) : (
                  <ToggleLeft className="h-4 w-4" />
                )}
                <span className="text-xs font-medium">
                  {isContextEnabled ? 'Aktif' : 'Pasif'}
                </span>
              </Button>
            </div>
            <div className="flex items-center gap-2">
              {isSaving && (
                <Badge variant="secondary" className="text-xs">
                  Kaydediliyor...
                </Badge>
              )}
              {lastSaved && (
                <span className="text-xs text-gray-500">
                  Son kaydedilme: {lastSaved.toLocaleTimeString('tr-TR')}
                </span>
              )}
            </div>
          </div>



          <Textarea
            placeholder="Tüm promptların başına eklenecek context metninizi buraya yazın..."
            value={contextText}
            onChange={(e) => setContextText(e.target.value)}
            className="min-h-[200px] resize-none"
            disabled={isSaving}
          />

          <div className="flex gap-2">
            <Button
              variant="default"
              size="sm"
              onClick={handleManualSave}
              disabled={isSaving || !contextText.trim()}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearContext}
              disabled={isSaving}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Temizle
            </Button>
          </div>

          <Separator className="my-6" />

          {/* Project Settings */}
          <Card>
          <CardHeader>
            <CardTitle className="text-base">Proje Ayarları</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">

            {/* Character Count Display */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Karakter Sayısı</span>
              <Badge variant="outline">
                {contextText.length.toLocaleString()}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Proje Adı</span>
              <Badge variant="outline">{project?.name}</Badge>
            </div>
            <Separator />
            
            {/* Proje Silme Bölümü */}
            {!showDeleteConfirm ? (
              <Button
                variant="outline"
                size="sm"
                className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={() => setShowDeleteConfirm(true)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Projeyi Sil
              </Button>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
                  <AlertTriangle className="h-4 w-4 text-red-600 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-red-800 font-medium">Dikkat!</p>
                    <p className="text-xs text-red-700">
                      Bu işlem geri alınamaz. Proje ve tüm prompt&apos;lar silinecek.
                    </p>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Onaylamak için proje adını yazın: <span className="text-red-600 font-semibold">{project?.name}</span>
                  </label>
                  <input
                    type="text"
                    value={deleteConfirmText}
                    onChange={(e) => setDeleteConfirmText(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="Proje adını yazın..."
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDeleteProject}
                    disabled={deleteConfirmText !== project?.name || deleteProjectMutation.isPending}
                    className="flex-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {deleteProjectMutation.isPending ? 'Siliniyor...' : 'Sil'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancelDelete}
                    className="flex-1"
                  >
                    İptal
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
          </div>
        )}
      </div>
    </div>
  );
}

export default ContextSidebar;