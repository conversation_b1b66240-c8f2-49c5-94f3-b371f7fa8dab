'use client'

import React, { useState, useEffect } from 'react'

/**
 * Performance Monitor Wrapper - Client Component
 * 
 * Bu component Next.js 15 App Router uyumluluğu için ayrı bir Client Component
 * olarak oluşturulmuştur. Layout.tsx Server Component içinde hook kullanımını
 * önlemek için lazy loading ile performance monitor'ü yükler.
 * 
 * @security Client-side only execution
 * @performance Lazy loading ile bundle size optimizasyonu
 * @accessibility Performance monitoring kullanıcı deneyimini etkilemez
 */
export function PerformanceMonitorWrapper() {
  const [Component, setComponent] = useState<React.ComponentType | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Performance monitor'ü sadece client-side'da yükle
    let isMounted = true

    const loadPerformanceMonitor = async () => {
      try {
        // Development ortamında performance monitoring aktif
        if (process.env.NODE_ENV === 'development') {
          const module = await import('@/components/ui/performance-monitor')
          
          if (isMounted) {
            setComponent(() => module.default)
          }
        }
      } catch (error) {
        // Performance monitor yüklenemezse sessizce devam et
        console.warn('Performance monitor could not be loaded:', error)
        
        if (isMounted) {
          setComponent(() => null)
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    loadPerformanceMonitor()

    // Cleanup function
    return () => {
      isMounted = false
    }
  }, [])

  // Loading state'inde hiçbir şey render etme
  if (isLoading) {
    return null
  }

  // Component yüklendiyse render et, yoksa null döndür
  return Component ? <Component /> : null
}

export default PerformanceMonitorWrapper
