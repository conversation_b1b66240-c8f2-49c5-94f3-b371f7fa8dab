import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Temporarily downgrade no-explicit-any to warning during build fixes
      "@typescript-eslint/no-explicit-any": "warn",
      // Allow unused vars in development
      "@typescript-eslint/no-unused-vars": "warn",
      // Allow unused expressions for lazy loading
      "@typescript-eslint/no-unused-expressions": "warn"
    }
  }
];

export default eslintConfig;
