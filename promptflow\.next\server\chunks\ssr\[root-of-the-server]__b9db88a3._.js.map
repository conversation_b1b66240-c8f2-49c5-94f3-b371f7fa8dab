{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/app/cookies/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport Link from 'next/link'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { \n  ArrowLeft, \n  Sparkles, \n  Cookie, \n  Settings, \n  Eye, \n  BarChart3,\n  Shield,\n  Globe,\n  CheckCircle,\n  XCircle\n} from 'lucide-react'\n\nexport const metadata: Metadata = {\n  title: 'Çerez Politikası - PromptBir | Cookie Policy',\n  description: 'PromptBir çerez politikası. Web sitemizdeki çerezlerin kullanımı, türleri ve yönetimi hakkında detaylı bilgi.',\n  keywords: [\n    'çerez politikası',\n    'cookie policy',\n    'çerez yönetimi',\n    'web çerezleri',\n    'GDPR çerezler',\n    'çerez türleri'\n  ],\n  openGraph: {\n    title: 'Çerez Politikası - PromptBir',\n    description: 'Web sitemizdeki çerezlerin kullanımı ve yönetimi hakkında bilgi',\n    type: 'website',\n    url: 'https://promptbir.com/cookies'\n  }\n}\n\nexport default function CookiesPage() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      {/* Header */}\n      <header className=\"border-b border-gray-200 bg-white/80 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <Link \n              href=\"/\" \n              className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <ArrowLeft className=\"h-5 w-5\" />\n              <span>Ana Sayfaya Dön</span>\n            </Link>\n            <div className=\"flex items-center space-x-2\">\n              <Sparkles className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">PromptBir</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Hero Section */}\n        <div className=\"text-center mb-12\">\n          <div className=\"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n            <Cookie className=\"h-8 w-8 text-orange-600\" />\n          </div>\n          <h1 className=\"text-4xl sm:text-5xl font-bold text-gray-900 mb-6\">\n            Çerez Politikası\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Bu politika, PromptBir web sitesinde kullanılan çerezler hakkında bilgi sağlar. \n            Çerezlerin nasıl kullanıldığını ve bunları nasıl yönetebileceğinizi öğrenin.\n          </p>\n          <p className=\"text-sm text-gray-500 mt-4\">\n            Son güncelleme: 1 Ocak 2024\n          </p>\n        </div>\n\n        {/* What are Cookies */}\n        <Card className=\"shadow-lg mb-8\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-3\">\n              <Cookie className=\"h-6 w-6 text-orange-600\" />\n              Çerez Nedir?\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"prose prose-gray max-w-none\">\n            <p className=\"mb-4\">\n              Çerezler, web sitelerinin kullanıcıların cihazlarında sakladığı küçük metin dosyalarıdır. \n              Bu dosyalar, web sitesinin daha iyi çalışmasını sağlar ve kullanıcı deneyimini geliştirir.\n            </p>\n            <div className=\"bg-blue-50 p-4 rounded-lg border border-blue-200\">\n              <h4 className=\"font-semibold text-blue-900 mb-2\">Çerezler Nasıl Çalışır?</h4>\n              <p className=\"text-blue-800 text-sm\">\n                Web sitesini ziyaret ettiğinizde, çerezler tarayıcınızda saklanır. \n                Bir sonraki ziyaretinizde, web sitesi bu bilgileri okuyarak size \n                kişiselleştirilmiş bir deneyim sunar.\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Cookie Types */}\n        <div className=\"grid md:grid-cols-2 gap-6 mb-8\">\n          {/* Essential Cookies */}\n          <Card className=\"shadow-lg border-green-200\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3 text-green-700\">\n                <Shield className=\"h-6 w-6\" />\n                Zorunlu Çerezler\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-gray-700 mb-4\">\n                Web sitesinin temel işlevlerini yerine getirmesi için gerekli çerezlerdir.\n              </p>\n              <ul className=\"space-y-2 text-sm\">\n                <li className=\"flex items-start gap-2\">\n                  <CheckCircle className=\"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0\" />\n                  <span>Oturum yönetimi</span>\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <CheckCircle className=\"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0\" />\n                  <span>Güvenlik doğrulaması</span>\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <CheckCircle className=\"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0\" />\n                  <span>Form verilerinin korunması</span>\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <CheckCircle className=\"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0\" />\n                  <span>Dil tercihleri</span>\n                </li>\n              </ul>\n              <div className=\"mt-4 p-3 bg-green-50 rounded-lg\">\n                <p className=\"text-green-800 text-sm font-medium\">\n                  Bu çerezler devre dışı bırakılamaz\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Performance Cookies */}\n          <Card className=\"shadow-lg border-blue-200\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3 text-blue-700\">\n                <BarChart3 className=\"h-6 w-6\" />\n                Performans Çerezleri\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-gray-700 mb-4\">\n                Web sitesinin performansını analiz etmek ve iyileştirmek için kullanılır.\n              </p>\n              <ul className=\"space-y-2 text-sm\">\n                <li className=\"flex items-start gap-2\">\n                  <BarChart3 className=\"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0\" />\n                  <span>Sayfa yükleme süreleri</span>\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <BarChart3 className=\"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0\" />\n                  <span>Kullanıcı etkileşimleri</span>\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <BarChart3 className=\"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0\" />\n                  <span>Hata raporları</span>\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <BarChart3 className=\"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0\" />\n                  <span>Trafik analizi</span>\n                </li>\n              </ul>\n              <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n                <p className=\"text-blue-800 text-sm font-medium\">\n                  Bu çerezler anonim veri toplar\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Functional Cookies */}\n          <Card className=\"shadow-lg border-purple-200\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3 text-purple-700\">\n                <Settings className=\"h-6 w-6\" />\n                İşlevsel Çerezler\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-gray-700 mb-4\">\n                Kullanıcı tercihlerini hatırlamak ve kişiselleştirilmiş deneyim sunmak için kullanılır.\n              </p>\n              <ul className=\"space-y-2 text-sm\">\n                <li className=\"flex items-start gap-2\">\n                  <Settings className=\"h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0\" />\n                  <span>Tema tercihleri (açık/koyu)</span>\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <Settings className=\"h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0\" />\n                  <span>Dil seçimleri</span>\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <Settings className=\"h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0\" />\n                  <span>Kullanıcı arayüzü ayarları</span>\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <Settings className=\"h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0\" />\n                  <span>Son kullanılan özellikler</span>\n                </li>\n              </ul>\n              <div className=\"mt-4 p-3 bg-purple-50 rounded-lg\">\n                <p className=\"text-purple-800 text-sm font-medium\">\n                  Bu çerezler deneyimi kişiselleştirir\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Targeting Cookies */}\n          <Card className=\"shadow-lg border-orange-200\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3 text-orange-700\">\n                <Eye className=\"h-6 w-6\" />\n                Hedefleme Çerezleri\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-gray-700 mb-4\">\n                Şu anda hedefleme çerezleri kullanmıyoruz, ancak gelecekte kullanabiliriz.\n              </p>\n              <ul className=\"space-y-2 text-sm\">\n                <li className=\"flex items-start gap-2\">\n                  <XCircle className=\"h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0\" />\n                  <span className=\"text-gray-500\">Reklam hedefleme (kullanılmıyor)</span>\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <XCircle className=\"h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0\" />\n                  <span className=\"text-gray-500\">Sosyal medya entegrasyonu (kullanılmıyor)</span>\n                </li>\n                <li className=\"flex items-start gap-2\">\n                  <XCircle className=\"h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0\" />\n                  <span className=\"text-gray-500\">Üçüncü taraf izleme (kullanılmıyor)</span>\n                </li>\n              </ul>\n              <div className=\"mt-4 p-3 bg-gray-50 rounded-lg\">\n                <p className=\"text-gray-600 text-sm font-medium\">\n                  Gizliliğinize saygı duyuyoruz\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Cookie Management */}\n        <Card className=\"shadow-lg mb-8\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-3\">\n              <Settings className=\"h-6 w-6 text-blue-600\" />\n              Çerez Yönetimi\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"prose prose-gray max-w-none\">\n            <h4 className=\"font-semibold mb-2\">Tarayıcı Ayarları:</h4>\n            <p className=\"mb-4\">\n              Çoğu tarayıcı çerezleri otomatik olarak kabul eder, ancak bunu değiştirebilirsiniz:\n            </p>\n            <ul className=\"list-disc pl-6 mb-6 space-y-2\">\n              <li><strong>Chrome:</strong> Ayarlar → Gizlilik ve güvenlik → Çerezler</li>\n              <li><strong>Firefox:</strong> Ayarlar → Gizlilik ve Güvenlik → Çerezler</li>\n              <li><strong>Safari:</strong> Tercihler → Gizlilik → Çerezler</li>\n              <li><strong>Edge:</strong> Ayarlar → Çerezler ve site izinleri</li>\n            </ul>\n\n            <div className=\"bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-4\">\n              <h4 className=\"font-semibold text-yellow-900 mb-2\">⚠️ Önemli Uyarı</h4>\n              <p className=\"text-yellow-800 text-sm\">\n                Zorunlu çerezleri devre dışı bırakırsanız, web sitesinin bazı özellikleri \n                düzgün çalışmayabilir. Oturum açma, form gönderme ve güvenlik özellikleri \n                etkilenebilir.\n              </p>\n            </div>\n\n            <h4 className=\"font-semibold mb-2\">Platform İçi Ayarlar:</h4>\n            <p className=\"mb-4\">\n              Hesabınıza giriş yaptıktan sonra, profil ayarlarından çerez tercihlerinizi \n              yönetebilirsiniz.\n            </p>\n          </CardContent>\n        </Card>\n\n        {/* Third Party Services */}\n        <Card className=\"shadow-lg mb-8\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-3\">\n              <Globe className=\"h-6 w-6 text-purple-600\" />\n              Üçüncü Taraf Hizmetler\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"prose prose-gray max-w-none\">\n            <p className=\"mb-4\">Platformumuzda kullanılan üçüncü taraf hizmetler:</p>\n            \n            <div className=\"space-y-4\">\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <h4 className=\"font-semibold mb-2\">Supabase (Veritabanı)</h4>\n                <p className=\"text-sm text-gray-600 mb-2\">\n                  Kullanıcı verilerini güvenli şekilde saklamak için kullanılır.\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  Çerez Türü: Zorunlu | Süre: Oturum süresi\n                </p>\n              </div>\n\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <h4 className=\"font-semibold mb-2\">Vercel (Hosting)</h4>\n                <p className=\"text-sm text-gray-600 mb-2\">\n                  Web sitesinin barındırılması ve performans optimizasyonu için kullanılır.\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  Çerez Türü: Performans | Süre: 24 saat\n                </p>\n              </div>\n            </div>\n\n            <div className=\"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200\">\n              <p className=\"text-blue-800 text-sm\">\n                <strong>Not:</strong> Üçüncü taraf hizmetlerin kendi çerez politikaları vardır. \n                Bu hizmetlerin çerez kullanımı hakkında daha fazla bilgi için ilgili \n                şirketlerin gizlilik politikalarını inceleyebilirsiniz.\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Legal Compliance */}\n        <Card className=\"shadow-lg mb-8\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-3\">\n              <Shield className=\"h-6 w-6 text-green-600\" />\n              Yasal Uyumluluk\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"prose prose-gray max-w-none\">\n            <h4 className=\"font-semibold mb-2\">GDPR Uyumluluğu:</h4>\n            <ul className=\"list-disc pl-6 mb-4 space-y-1\">\n              <li>Çerez kullanımı için açık rıza alınır</li>\n              <li>Çerez türleri ve amaçları açıkça belirtilir</li>\n              <li>Kullanıcılar çerezleri yönetebilir</li>\n              <li>Veri işleme süreleri belirtilir</li>\n            </ul>\n\n            <h4 className=\"font-semibold mb-2\">KVKK Uyumluluğu:</h4>\n            <ul className=\"list-disc pl-6 space-y-1\">\n              <li>Kişisel veri işleme amaçları belirtilir</li>\n              <li>Veri saklama süreleri açıklanır</li>\n              <li>Kullanıcı hakları korunur</li>\n              <li>Veri güvenliği sağlanır</li>\n            </ul>\n          </CardContent>\n        </Card>\n\n        {/* Cookie Consent */}\n        <Card className=\"shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white\">\n          <CardContent className=\"p-8 text-center\">\n            <Cookie className=\"h-16 w-16 mx-auto mb-6 text-blue-100\" />\n            <h2 className=\"text-2xl font-bold mb-4\">Çerez Tercihlerinizi Yönetin</h2>\n            <p className=\"mb-6 text-blue-100\">\n              Çerez ayarlarınızı istediğiniz zaman değiştirebilirsiniz.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button \n                variant=\"secondary\" \n                size=\"lg\"\n                className=\"bg-white text-blue-600 hover:bg-blue-50\"\n              >\n                Çerez Ayarları\n              </Button>\n              <Button \n                variant=\"outline\" \n                size=\"lg\"\n                className=\"border-white text-white hover:bg-white hover:text-blue-600\"\n              >\n                Tümünü Kabul Et\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Contact */}\n        <Card className=\"shadow-lg mt-8\">\n          <CardHeader>\n            <CardTitle>İletişim</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-gray-700 mb-4\">\n              Çerez politikamız hakkında sorularınız varsa bizimle iletişime geçin:\n            </p>\n            <div className=\"space-y-2 text-sm\">\n              <p><strong>E-posta:</strong> <EMAIL></p>\n              <p><strong>Genel Sorular:</strong> <EMAIL></p>\n              <p><strong>KVKK Sorumlusu:</strong> <EMAIL></p>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Updates */}\n        <Card className=\"shadow-lg mt-8 border-orange-200 bg-orange-50\">\n          <CardContent className=\"p-6\">\n            <h3 className=\"font-bold text-orange-900 mb-2\">Politika Güncellemeleri</h3>\n            <p className=\"text-orange-800 text-sm\">\n              Bu çerez politikası gerektiğinde güncellenebilir. Önemli değişiklikler \n              web sitesinde duyurulacak ve kullanıcılara bildirilecektir. \n              Güncellemeleri düzenli olarak kontrol etmenizi öneririz.\n            </p>\n          </CardContent>\n        </Card>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-8 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 PromptBir. Tüm hakları saklıdır.\n          </p>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAaO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,KAAK;IACP;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;0CAIvE,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAM5C,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAA4B;;;;;;;;;;;;0CAIlD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAE,WAAU;kDAAO;;;;;;kDAIpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAU3C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIlC,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;;;;;;;;;;;;0CAQxD,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,kNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIrC,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,kNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,kNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,kNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,kNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;0CAQvD,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIpC,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAsC;;;;;;;;;;;;;;;;;;;;;;;0CAQzD,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAI/B,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,4MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,4MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,4MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGpC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzD,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;;;;;;0CAIlD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAO;;;;;;kDAGpB,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;;kEAAG,8OAAC;kEAAO;;;;;;oDAAgB;;;;;;;0DAC5B,8OAAC;;kEAAG,8OAAC;kEAAO;;;;;;oDAAiB;;;;;;;0DAC7B,8OAAC;;kEAAG,8OAAC;kEAAO;;;;;;oDAAgB;;;;;;;0DAC5B,8OAAC;;kEAAG,8OAAC;kEAAO;;;;;;oDAAc;;;;;;;;;;;;;kDAG5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;kDAOzC,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAO;;;;;;;;;;;;;;;;;;kCAQxB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAA4B;;;;;;;;;;;;0CAIjD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAE,WAAU;kDAAO;;;;;;kDAEpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAKvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAMzC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;8DAAO;;;;;;gDAAa;;;;;;;;;;;;;;;;;;;;;;;;kCAS7B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAA2B;;;;;;;;;;;;0CAIjD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAMV,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAiB;;;;;;;0DAC5B,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAuB;;;;;;;0DAClC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;kCAMzC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;;;;;;;;;;;;0BAU7C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}]}