"use strict";exports.id=4617,exports.ids=[4617],exports.modules={3178:(a,b,c)=>{c.d(b,{IL:()=>n,RH:()=>h,W2:()=>i,YD:()=>m,_v:()=>l,lL:()=>j,sd:()=>k});var d=c(51423),e=c(8693),f=c(54050),g=c(8266);function h(){return(0,d.I)({queryKey:["context-categories"],queryFn:async()=>{let{data:a,error:b}=await g.<PERSON>.from("context_categories").select("*").eq("is_active",!0).order("sort_order",{ascending:!0});if(b)throw b;return a},staleTime:3e5})}function i(a){return(0,d.I)({queryKey:["contexts",a],queryFn:async()=>{let b=g.L.from("contexts").select(`
          *,
          category:context_categories(*)
        `).eq("status","active");a?.category_id&&(b=b.eq("category_id",a.category_id)),a?.is_public!==void 0&&(b=b.eq("is_public",a.is_public)),a?.is_template!==void 0&&(b=b.eq("is_template",a.is_template)),a?.is_featured!==void 0&&(b=b.eq("is_featured",a.is_featured)),a?.author_id&&(b=b.eq("author_id",a.author_id)),a?.search&&(b=b.or(`title.ilike.%${a.search}%,description.ilike.%${a.search}%,tags.cs.{${a.search}}`)),b=b.order("is_featured",{ascending:!1}).order("usage_count",{ascending:!1});let{data:c,error:d}=await b;if(d)throw d;return c.map(a=>({id:a.id,title:a.title,description:a.description,content:a.content,category:a.category,author_id:a.author_id,author_name:"Kullanıcı",is_public:a.is_public,is_featured:a.is_featured,is_template:a.is_template,tags:a.tags||[],usage_count:a.usage_count,like_count:a.like_count,view_count:a.view_count,approval_status:a.approval_status||"approved",approved_by:a.approved_by,approved_at:a.approved_at,approval_notes:a.approval_notes,created_at:a.created_at,updated_at:a.updated_at}))},staleTime:12e4})}function j(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async a=>{let{data:b}=await g.L.auth.getUser();if(!b.user)throw Error("Kullanıcı girişi gerekli");let{data:c,error:d}=await g.L.from("contexts").insert({...a,author_id:b.user.id}).select().single();if(d)throw d;return c},onSuccess:()=>{a.invalidateQueries({queryKey:["contexts"]})}})}function k(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async({id:a,updates:b})=>{let{data:c}=await g.L.auth.getUser();if(!c.user)throw Error("Kullanıcı girişi gerekli");let{data:d,error:e}=await g.L.from("contexts").update({...b,updated_at:new Date().toISOString()}).eq("id",a).eq("author_id",c.user.id).select().single();if(e)throw e;return d},onSuccess:(b,c)=>{a.invalidateQueries({queryKey:["contexts"]}),a.invalidateQueries({queryKey:["context",c.id]})}})}function l(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async({contextId:a,projectId:b})=>{let{data:c}=await g.L.auth.getUser();if(!c.user)throw Error("Kullanıcı girişi gerekli");let{error:d}=await g.L.rpc("increment_context_usage",{context_id_param:a,user_id_param:c.user.id,project_id_param:b||null});if(d)throw d},onSuccess:()=>{a.invalidateQueries({queryKey:["contexts"]})}})}function m(){let a=(0,e.jE)();return(0,f.n)({mutationFn:async a=>{let{data:b}=await g.L.auth.getUser();if(!b.user)throw Error("Kullanıcı girişi gerekli");let{data:c,error:d}=await g.L.rpc("toggle_context_like",{context_id_param:a,user_id_param:b.user.id});if(d)throw d;return c},onSuccess:()=>{a.invalidateQueries({queryKey:["contexts"]})}})}function n(){return(0,d.I)({queryKey:["user-liked-contexts"],queryFn:async()=>{let{data:a}=await g.L.auth.getUser();if(!a.user)throw Error("Kullanıcı girişi gerekli");let{data:b,error:c}=await g.L.from("context_likes").select("context_id").eq("user_id",a.user.id);if(c)throw c;return b.map(a=>a.context_id)}})}},15079:(a,b,c)=>{c.d(b,{bq:()=>l,eb:()=>n,gC:()=>m,l6:()=>j,yv:()=>k});var d=c(60687);c(43210);var e=c(42762),f=c(78272),g=c(13964),h=c(3589),i=c(4780);function j({...a}){return(0,d.jsx)(e.bL,{"data-slot":"select",...a})}function k({...a}){return(0,d.jsx)(e.WT,{"data-slot":"select-value",...a})}function l({className:a,size:b="default",children:c,...g}){return(0,d.jsxs)(e.l9,{"data-slot":"select-trigger","data-size":b,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...g,children:[c,(0,d.jsx)(e.In,{asChild:!0,children:(0,d.jsx)(f.A,{className:"size-4 opacity-50"})})]})}function m({className:a,children:b,position:c="popper",...f}){return(0,d.jsx)(e.ZL,{children:(0,d.jsxs)(e.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...f,children:[(0,d.jsx)(o,{}),(0,d.jsx)(e.LM,{className:(0,i.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:b}),(0,d.jsx)(p,{})]})})}function n({className:a,children:b,...c}){return(0,d.jsxs)(e.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...c,children:[(0,d.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,d.jsx)(e.VF,{children:(0,d.jsx)(g.A,{className:"size-4"})})}),(0,d.jsx)(e.p4,{children:b})]})}function o({className:a,...b}){return(0,d.jsx)(e.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"size-4"})})}function p({className:a,...b}){return(0,d.jsx)(e.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(f.A,{className:"size-4"})})}},34617:(a,b,c)=>{c.r(b),c.d(b,{ContextCreationModal:()=>y});var d=c(60687),e=c(43210),f=c(29523),g=c(89667),h=c(80013),i=c(34729),j=c(96834),k=c(63503),l=c(15079),m=c(10022),n=c(93613),o=c(96474),p=c(37360),q=c(11860),r=c(64021),s=c(11437),t=c(41862),u=c(3178),v=c(74940),w=c(97421),x=c(52581);function y({open:a,onOpenChange:b,onSuccess:c,showAddToProject:y=!0}){let[z,A]=(0,e.useState)({title:"",description:"",content:"",category_id:"",is_public:!1,is_template:!1,tags:[]}),[B,C]=(0,e.useState)(""),[D,E]=(0,e.useState)({}),[F,G]=(0,e.useState)(!1),{data:H=[],isLoading:I,error:J}=(0,u.RH)(),{data:K=[]}=(0,w.YK)(),L=(0,u.lL)(),{addContext:M,isLoading:N}=(0,v.KU)(),O=async a=>{if(a.preventDefault(),!(()=>{let a={};return z.title.trim()||(a.title="Başlık gereklidir"),z.content.trim()||(a.content="İ\xe7erik gereklidir"),z.category_id||(a.category_id="Kategori se\xe7imi gereklidir"),E(a),0===Object.keys(a).length})())return void x.oR.error("L\xfctfen t\xfcm gerekli alanları doldurun");try{let a=await L.mutateAsync({title:z.title.trim(),description:z.description.trim()||void 0,content:z.content.trim(),category_id:z.category_id,is_public:z.is_public,is_template:z.is_template,tags:z.tags});if(F&&y)try{await M(a,z.title.trim()),x.oR.success("Context oluşturuldu ve projeye eklendi!")}catch(a){console.error("Failed to add to project:",a),x.oR.warning("Context oluşturuldu ancak projeye eklenirken hata oluştu")}else x.oR.success("Context başarıyla oluşturuldu!");A({title:"",description:"",content:"",category_id:"",is_public:!1,is_template:!1,tags:[]}),E({}),G(!1),c?.(),b(!1)}catch(a){console.error("Context creation error:",a),x.oR.error("Context oluşturulurken bir hata oluştu")}},P=()=>{B.trim()&&!z.tags.includes(B.trim())&&(A(a=>({...a,tags:[...a.tags,B.trim()]})),C(""))};return(0,d.jsx)(k.lG,{open:a,onOpenChange:b,children:(0,d.jsxs)(k.Cf,{className:"max-w-2xl max-h-[90vh] overflow-hidden flex flex-col z-[60]",children:[(0,d.jsxs)(k.c7,{className:"flex-shrink-0",children:[(0,d.jsxs)(k.L3,{className:"flex items-center gap-2",children:[(0,d.jsx)(m.A,{className:"h-5 w-5"}),"Yeni Context Oluştur"]}),(0,d.jsx)(k.rr,{children:"Yeni bir context oluşturun. Herkese a\xe7ık contextler admin onayı gerektirir."})]}),(0,d.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,d.jsxs)("form",{onSubmit:O,className:"space-y-6 p-1",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(h.J,{htmlFor:"title",children:["Başlık ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)(g.p,{id:"title",placeholder:"Context başlığını girin...",value:z.title,onChange:a=>A(b=>({...b,title:a.target.value})),className:D.title?"border-red-500":""}),D.title&&(0,d.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,d.jsx)(n.A,{className:"h-3 w-3"}),D.title]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"description",children:"A\xe7ıklama"}),(0,d.jsx)(g.p,{id:"description",placeholder:"Context a\xe7ıklaması (isteğe bağlı)...",value:z.description,onChange:a=>A(b=>({...b,description:a.target.value}))})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(h.J,{htmlFor:"category",children:["Kategori ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsxs)(l.l6,{value:z.category_id,onValueChange:a=>A(b=>({...b,category_id:a})),children:[(0,d.jsx)(l.bq,{className:D.category_id?"border-red-500":"",children:(0,d.jsx)(l.yv,{placeholder:"Kategori se\xe7in..."})}),(0,d.jsx)(l.gC,{className:"z-[70]",children:I?(0,d.jsx)(l.eb,{value:"loading",disabled:!0,children:"Kategoriler y\xfckleniyor..."}):J?(0,d.jsxs)(l.eb,{value:"error",disabled:!0,children:["Kategori y\xfckleme hatası: ",J.message]}):0===H.length?(0,d.jsx)(l.eb,{value:"empty",disabled:!0,children:"Kategori bulunamadı"}):H.map(a=>(0,d.jsx)(l.eb,{value:a.id,children:(0,d.jsxs)("span",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{children:a.icon||"\uD83D\uDCC1"}),a.name]})},a.id))})]}),D.category_id&&(0,d.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,d.jsx)(n.A,{className:"h-3 w-3"}),D.category_id]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(h.J,{htmlFor:"content",children:["İ\xe7erik ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)(i.T,{id:"content",placeholder:"Context i\xe7eriğini girin...",value:z.content,onChange:a=>A(b=>({...b,content:a.target.value})),className:`min-h-[120px] resize-none ${D.content?"border-red-500":""}`}),D.content&&(0,d.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,d.jsx)(n.A,{className:"h-3 w-3"}),D.content]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"tags",children:"Etiketler"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(g.p,{id:"tags",placeholder:"Etiket ekle...",value:B,onChange:a=>C(a.target.value),onKeyPress:a=>{"Enter"===a.key&&(a.preventDefault(),P())},className:"flex-1"}),(0,d.jsx)(f.$,{type:"button",variant:"outline",size:"sm",onClick:P,disabled:!B.trim(),children:(0,d.jsx)(o.A,{className:"h-4 w-4"})})]}),z.tags.length>0&&(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:z.tags.map(a=>(0,d.jsxs)(j.E,{variant:"secondary",className:"flex items-center gap-1",children:[(0,d.jsx)(p.A,{className:"h-3 w-3"}),a,(0,d.jsx)("button",{type:"button",onClick:()=>{A(b=>({...b,tags:b.tags.filter(b=>b!==a)}))},className:"ml-1 hover:text-red-500",children:(0,d.jsx)(q.A,{className:"h-3 w-3"})})]},a))})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(h.J,{children:"G\xf6r\xfcn\xfcrl\xfck Ayarları"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,d.jsx)("input",{type:"radio",name:"visibility",checked:!z.is_public,onChange:()=>A(a=>({...a,is_public:!1})),className:"w-4 h-4"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(r.A,{className:"h-4 w-4 text-gray-500"}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"text-sm",children:"\xd6zel"}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"Sadece ben g\xf6rebilirim"})]})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,d.jsx)("input",{type:"radio",name:"visibility",checked:z.is_public,onChange:()=>A(a=>({...a,is_public:!0})),className:"w-4 h-4"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(s.A,{className:"h-4 w-4 text-blue-500"}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"text-sm",children:"Herkese A\xe7ık"}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"T\xfcm kullanıcılar g\xf6rebilir"})]})]})]})]})]}),(0,d.jsx)("div",{className:"space-y-2",children:(0,d.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:z.is_template,onChange:a=>A(b=>({...b,is_template:a.target.checked})),className:"w-4 h-4"}),(0,d.jsx)("span",{className:"text-sm",children:"Bu contexti şablon olarak işaretle"})]})}),y&&K.length>0&&(0,d.jsx)("div",{className:"space-y-2 p-3 bg-blue-50 rounded-lg border border-blue-200",children:(0,d.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:F,onChange:a=>G(a.target.checked),className:"w-4 h-4 text-blue-600"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 text-blue-600"}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"Mevcut projeye ekle"}),(0,d.jsx)("span",{className:"text-xs text-blue-700",children:"Context oluşturulduktan sonra aktif projeye prompt olarak eklenecek"})]})]})]})}),(0,d.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,d.jsx)(f.$,{type:"button",variant:"outline",onClick:()=>b(!1),className:"flex-1",children:"İptal"}),(0,d.jsxs)(f.$,{type:"submit",disabled:L.isPending||N,className:"flex-1",children:[(L.isPending||N)&&(0,d.jsx)(t.A,{className:"mr-2 h-4 w-4 animate-spin"}),F?"Oluştur ve Ekle":"Oluştur"]})]})]})})]})})}},37360:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},74940:(a,b,c)=>{c.d(b,{KU:()=>i});var d=c(8693),e=c(54050),f=c(33295),g=c(97421),h=c(52581);function i(){let a=function(){let a=(0,d.jE)(),b=(0,f.sW)(),{data:c}=(0,g.YK)();return(0,e.n)({mutationFn:async({context:d,options:e={}})=>{try{let f=e.projectId||function(a){if(!a||0===a.length)return null;let b=localStorage.getItem("activeProjectId");return b&&a.find(a=>a?.id===b)?b:a[0]?.id||null}(c||[]);if(!f)throw Error("L\xfctfen \xf6nce bir proje se\xe7in");let g={project_id:f,prompt_text:d.content,title:e.customTitle||d.title||"Context Gallery Prompt",category:e.customCategory||function(a){if(a.category)return a.category.name||a.category.toString();if(a.tags&&a.tags.length>0){let b=a.tags[0];if(b.startsWith("/"))return b}}(d),tags:function(a,b){let c=a.tags||[];return Array.from(new Set([...c,...b||[]])).filter(a=>a.trim().length>0)}(d,e.additionalTags),order_index:await j(f),is_used:!1,is_favorite:e.markAsFavorite||!1},h=await b.mutateAsync(g);return await k(d.id,f),a.invalidateQueries({queryKey:["prompts",f]}),a.invalidateQueries({queryKey:["projects"]}),{success:!0,promptId:h.id}}catch(a){return console.error("Context to prompt conversion error:",a),{success:!1,error:a instanceof Error?a.message:"Context prompt olarak eklenirken hata oluştu"}}},onSuccess:a=>{a.success?h.oR.success("Context başarıyla projeye eklendi!"):h.oR.error(a.error||"Bir hata oluştu")},onError:a=>{console.error("Context to prompt mutation error:",a),h.oR.error("Context eklenirken beklenmeyen bir hata oluştu")}})}();return{addContext:async(b,c)=>a.mutateAsync({context:b,options:{customTitle:c}}),isLoading:a.isPending,error:a.error}}async function j(a){return Date.now()}async function k(a,b){try{console.log(`Context ${a} used in project ${b}`)}catch(a){console.warn("Failed to track context usage:",a)}}},93613:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};