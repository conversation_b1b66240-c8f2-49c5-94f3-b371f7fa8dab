import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase-server'
import { rateLimitMiddleware, RateLimitType } from '@/lib/rate-limiting'
import { validateApiCSRF } from '@/lib/csrf-protection'

// Security headers for all responses
const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'X-DNS-Prefetch-Control': 'off',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
}

// Rate limit configurations for different routes
const ROUTE_RATE_LIMITS: Record<string, RateLimitType> = {
  '/api/auth': 'auth',
  '/api/projects': 'api',
  '/api/prompts': 'api',
  '/api/upload': 'uploads',
  '/api/admin': 'admin'
}

export async function middleware(req: NextRequest) {
  const startTime = Date.now()
  const pathname = req.nextUrl.pathname

  console.log(`🛡️ [SECURITY_MIDDLEWARE] ${req.method} ${pathname} - Starting security checks...`)

  const response = NextResponse.next({
    request: {
      headers: req.headers,
    },
  })

  // Apply security headers to all responses
  Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value)
  })

  // API route security
  if (pathname.startsWith('/api/')) {
    const apiSecurityResult = await handleApiSecurity(req, response)
    if (apiSecurityResult !== response) {
      return apiSecurityResult
    }
  }

  const supabase = createSupabaseServerClient(req, response)

  try {
    // Refresh session if expired - required for Server Components
    console.log(`🔐 [MIDDLEWARE] Getting session...`)
    const {
      data: { session },
      error: sessionError
    } = await supabase.auth.getSession()

    if (sessionError) {
      console.error(`❌ [MIDDLEWARE] Session error:`, sessionError)
    }

    // Also try to get user to double-check
    let user = null
    if (session) {
      try {
        const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser()
        if (userError) {
          console.warn(`⚠️ [MIDDLEWARE] User verification error:`, userError)
        } else {
          user = currentUser
        }
      } catch (e) {
        console.warn(`⚠️ [MIDDLEWARE] Could not verify user:`, e)
      }
    }

    // Check if session is expired
    const isSessionExpired = session && session.expires_at ? new Date(session.expires_at * 1000) < new Date() : false

    // Session is valid if we have both session and user and it's not expired
    const hasValidSession = !!(session && session.user && user && !isSessionExpired)

    console.log(`🔐 [MIDDLEWARE] Session status: ${hasValidSession ? `VALID (user: ${user?.email})` : 'INVALID/NO_SESSION'}`)
    console.log(`🔐 [MIDDLEWARE] Session details:`, {
      hasSession: !!session,
      hasSessionUser: !!session?.user,
      hasUser: !!user,
      sessionExpiry: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : 'N/A',
      isExpired: isSessionExpired
    })

    // Protected routes that require authentication
    const protectedRoutes = ['/dashboard', '/profile', '/settings']
    const isProtectedRoute = protectedRoutes.some(route =>
      pathname.startsWith(route)
    )

    console.log(`🛡️ [MIDDLEWARE] Route protection: ${pathname} is ${isProtectedRoute ? 'PROTECTED' : 'PUBLIC'}`)

    // If accessing a protected route without a valid session, redirect to auth page
    if (isProtectedRoute && !hasValidSession) {
      console.log(`🚫 [MIDDLEWARE] REDIRECT: ${pathname} -> /auth (no valid session)`)
      const redirectUrl = new URL('/auth', req.url)
      return NextResponse.redirect(redirectUrl)
    }

    // If user is logged in and trying to access auth page, redirect to dashboard
    if (pathname === '/auth' && hasValidSession) {
      console.log(`🚫 [MIDDLEWARE] REDIRECT: /auth -> /dashboard (has valid session)`)
      const redirectUrl = new URL('/dashboard', req.url)
      return NextResponse.redirect(redirectUrl)
    }

    // If user is logged in and trying to access root, redirect to dashboard
    if (pathname === '/' && hasValidSession) {
      console.log(`🚫 [MIDDLEWARE] REDIRECT: / -> /dashboard (has valid session)`)
      const redirectUrl = new URL('/dashboard', req.url)
      return NextResponse.redirect(redirectUrl)
    }

    const duration = Date.now() - startTime
    console.log(`✅ [MIDDLEWARE] ${pathname} - Completed in ${duration}ms (session: ${hasValidSession ? 'VALID' : 'INVALID'})`)
    return response

  } catch (error) {
    console.error(`💥 [MIDDLEWARE] Error:`, error)
    return response
  }
}

/**
 * Handle API route security
 */
async function handleApiSecurity(request: NextRequest, response: NextResponse): Promise<NextResponse> {
  const { pathname } = request.nextUrl

  try {
    // 1. Determine rate limit type
    let rateLimitType: RateLimitType = 'api'
    for (const [route, type] of Object.entries(ROUTE_RATE_LIMITS)) {
      if (pathname.startsWith(route)) {
        rateLimitType = type
        break
      }
    }

    // 2. Apply rate limiting
    const rateLimitResult = rateLimitMiddleware(request, rateLimitType)

    // Add rate limit headers
    Object.entries(rateLimitResult.headers).forEach(([key, value]) => {
      response.headers.set(key, value)
    })

    if (!rateLimitResult.allowed) {
      console.warn(`🚫 [SECURITY_MIDDLEWARE] Rate limit exceeded: ${pathname}`)
      return NextResponse.json(
        {
          error: rateLimitResult.message || 'Rate limit exceeded',
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: response.headers.get('X-RateLimit-Reset')
        },
        {
          status: rateLimitResult.status || 429,
          headers: response.headers
        }
      )
    }

    // 3. CSRF protection for state-changing methods (only in production)
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(request.method)) {
      const csrfValid = await validateApiCSRF(request)

      if (!csrfValid && process.env.NODE_ENV === 'production') {
        console.warn(`🚫 [SECURITY_MIDDLEWARE] CSRF validation failed: ${pathname}`)
        return NextResponse.json(
          {
            error: 'CSRF token validation failed',
            code: 'CSRF_INVALID'
          },
          {
            status: 403,
            headers: response.headers
          }
        )
      }
    }

    console.log(`✅ [SECURITY_MIDDLEWARE] API security passed: ${pathname}`)
    return response

  } catch (error) {
    console.error(`❌ [SECURITY_MIDDLEWARE] API security error:`, error)
    return NextResponse.json(
      {
        error: 'Security validation failed',
        code: 'SECURITY_ERROR'
      },
      {
        status: 500,
        headers: response.headers
      }
    )
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
