{"name": "promptbir", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 4444", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "performance": "node scripts/performance-monitor.js", "lighthouse": "lhci autorun --config=lighthouse.config.js", "bundle-analyzer": "cross-env ANALYZE=true npm run build"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.51.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "framer-motion": "^12.23.12", "isomorphic-dompurify": "^2.26.0", "lucide-react": "^0.525.0", "next": "15.4.1", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "react": "19.1.0", "react-dom": "19.1.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^3.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.14", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.4.5", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}