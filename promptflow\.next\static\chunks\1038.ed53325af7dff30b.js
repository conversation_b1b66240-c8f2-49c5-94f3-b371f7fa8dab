"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1038],{41038:(e,s,t)=>{t.r(s),t.d(s,{ContextEditModal:()=>v});var a=t(95155),l=t(12115),i=t(30285),c=t(62523),n=t(85057),r=t(88539),d=t(26126),x=t(54165),o=t(59409),m=t(44940),h=t(85339),p=t(84616),g=t(43332),j=t(54416),u=t(32919),N=t(34869),y=t(51154),b=t(15790),f=t(56671);function v(e){let{open:s,onOpenChange:t,context:v,onSuccess:k}=e,[_,C]=(0,l.useState)({title:"",description:"",content:"",category_id:"",is_public:!1,is_template:!1,tags:[]}),[w,A]=(0,l.useState)(""),[E,z]=(0,l.useState)({}),{data:J=[],isLoading:F}=(0,b.RH)(),K=(0,b.sd)();(0,l.useEffect)(()=>{v&&C({title:v.title,description:v.description||"",content:v.content,category_id:v.category.id,is_public:v.is_public,is_template:v.is_template,tags:v.tags||[]})},[v]),(0,l.useEffect)(()=>{s||(z({}),A(""))},[s]);let S=async e=>{if(e.preventDefault(),v){if(!(()=>{let e={};return _.title.trim()||(e.title="Başlık gereklidir"),_.content.trim()||(e.content="İ\xe7erik gereklidir"),_.category_id||(e.category_id="Kategori se\xe7imi gereklidir"),z(e),0===Object.keys(e).length})())return void f.oR.error("L\xfctfen t\xfcm gerekli alanları doldurun");try{await K.mutateAsync({id:v.id,updates:{title:_.title.trim(),description:_.description.trim()||void 0,content:_.content.trim(),category_id:_.category_id,is_public:_.is_public,is_template:_.is_template,tags:_.tags}}),f.oR.success("Context başarıyla g\xfcncellendi!"),null==k||k(),t(!1)}catch(e){console.error("Context update error:",e),f.oR.error("Context g\xfcncellenirken bir hata oluştu")}}},R=()=>{w.trim()&&!_.tags.includes(w.trim())&&(C(e=>({...e,tags:[...e.tags,w.trim()]})),A(""))};return v?(0,a.jsx)(x.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(x.Cf,{className:"max-w-2xl max-h-[90vh] overflow-hidden flex flex-col z-[60]",children:[(0,a.jsxs)(x.c7,{className:"flex-shrink-0",children:[(0,a.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Context D\xfczenle"]}),(0,a.jsx)(x.rr,{children:"Context bilgilerini d\xfczenleyin. Herkese a\xe7ık contextler admin onayı gerektirir."})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsxs)("form",{onSubmit:S,className:"space-y-6 p-1",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(n.J,{htmlFor:"title",children:["Başlık ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)(c.p,{id:"title",placeholder:"Context başlığını girin...",value:_.title,onChange:e=>C(s=>({...s,title:e.target.value})),className:E.title?"border-red-500":""}),E.title&&(0,a.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,a.jsx)(h.A,{className:"h-3 w-3"}),E.title]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{htmlFor:"description",children:"A\xe7ıklama"}),(0,a.jsx)(c.p,{id:"description",placeholder:"Context a\xe7ıklaması (isteğe bağlı)...",value:_.description,onChange:e=>C(s=>({...s,description:e.target.value}))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(n.J,{htmlFor:"category",children:["Kategori ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)(o.l6,{value:_.category_id,onValueChange:e=>C(s=>({...s,category_id:e})),children:[(0,a.jsx)(o.bq,{className:E.category_id?"border-red-500":"",children:(0,a.jsx)(o.yv,{placeholder:"Kategori se\xe7in..."})}),(0,a.jsx)(o.gC,{children:F?(0,a.jsx)(o.eb,{value:"loading",disabled:!0,children:"Kategoriler y\xfckleniyor..."}):J.map(e=>(0,a.jsx)(o.eb,{value:e.id,children:(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{children:e.icon}),e.name]})},e.id))})]}),E.category_id&&(0,a.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,a.jsx)(h.A,{className:"h-3 w-3"}),E.category_id]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(n.J,{htmlFor:"content",children:["İ\xe7erik ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)(r.T,{id:"content",placeholder:"Context i\xe7eriğini girin...",value:_.content,onChange:e=>C(s=>({...s,content:e.target.value})),className:"min-h-[120px] resize-none ".concat(E.content?"border-red-500":"")}),E.content&&(0,a.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,a.jsx)(h.A,{className:"h-3 w-3"}),E.content]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{htmlFor:"tags",children:"Etiketler"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(c.p,{id:"tags",placeholder:"Etiket ekle...",value:w,onChange:e=>A(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),R())},className:"flex-1"}),(0,a.jsx)(i.$,{type:"button",variant:"outline",size:"sm",onClick:R,disabled:!w.trim(),children:(0,a.jsx)(p.A,{className:"h-4 w-4"})})]}),_.tags.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:_.tags.map(e=>(0,a.jsxs)(d.E,{variant:"secondary",className:"flex items-center gap-1",children:[(0,a.jsx)(g.A,{className:"h-3 w-3"}),e,(0,a.jsx)("button",{type:"button",onClick:()=>{C(s=>({...s,tags:s.tags.filter(s=>s!==e)}))},className:"ml-1 hover:text-red-500",children:(0,a.jsx)(j.A,{className:"h-3 w-3"})})]},e))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(n.J,{children:"G\xf6r\xfcn\xfcrl\xfck Ayarları"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",name:"visibility",checked:!_.is_public,onChange:()=>C(e=>({...e,is_public:!1})),className:"w-4 h-4"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-sm",children:"\xd6zel"}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"Sadece ben g\xf6rebilirim"})]})]})]}),(0,a.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",name:"visibility",checked:_.is_public,onChange:()=>C(e=>({...e,is_public:!0})),className:"w-4 h-4"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 text-blue-500"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-sm",children:"Herkese A\xe7ık"}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"T\xfcm kullanıcılar g\xf6rebilir"})]})]})]})]})]}),(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:_.is_template,onChange:e=>C(s=>({...s,is_template:e.target.checked})),className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Bu contexti şablon olarak işaretle"})]})}),(0,a.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>t(!1),className:"flex-1",children:"İptal"}),(0,a.jsxs)(i.$,{type:"submit",disabled:K.isPending,className:"flex-1",children:[K.isPending&&(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"G\xfcncelle"]})]})]})})]})}):null}}}]);