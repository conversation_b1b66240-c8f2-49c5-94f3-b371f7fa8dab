{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Kapat</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n} "], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,CAGpC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,OAGpC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe;QAAC,EACpB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,6JAAA,CAAA,aAAgB,OAGxC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-contexts.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { supabaseBrowser as supabase } from \"@/lib/supabase-browser\";\r\nimport { Context, ContextCategory } from \"@/components/context-gallery\";\r\n\r\n// Context Categories Hooks\r\nexport function useContextCategories() {\r\n  return useQuery({\r\n    queryKey: [\"context-categories\"],\r\n    queryFn: async () => {\r\n      const { data, error } = await supabase\r\n        .from(\"context_categories\")\r\n        .select(\"*\")\r\n        .eq(\"is_active\", true)\r\n        .order(\"sort_order\", { ascending: true });\r\n\r\n      if (error) throw error;\r\n      return data as ContextCategory[];\r\n    },\r\n    staleTime: 5 * 60 * 1000, // 5 dakika\r\n  });\r\n}\r\n\r\n// Get all contexts with filters\r\nexport function useContexts(filters?: {\r\n  category_id?: string;\r\n  is_public?: boolean;\r\n  is_template?: boolean;\r\n  is_featured?: boolean;\r\n  search?: string;\r\n  author_id?: string;\r\n}) {\r\n  return useQuery({\r\n    queryKey: [\"contexts\", filters],\r\n    queryFn: async () => {\r\n      let query = supabase\r\n        .from(\"contexts\")\r\n        .select(`\r\n          *,\r\n          category:context_categories(*)\r\n        `)\r\n        .eq(\"status\", \"active\");\r\n\r\n      // Apply filters\r\n      if (filters?.category_id) {\r\n        query = query.eq(\"category_id\", filters.category_id);\r\n      }\r\n      \r\n      if (filters?.is_public !== undefined) {\r\n        query = query.eq(\"is_public\", filters.is_public);\r\n      }\r\n      \r\n      if (filters?.is_template !== undefined) {\r\n        query = query.eq(\"is_template\", filters.is_template);\r\n      }\r\n      \r\n      if (filters?.is_featured !== undefined) {\r\n        query = query.eq(\"is_featured\", filters.is_featured);\r\n      }\r\n\r\n      if (filters?.author_id) {\r\n        query = query.eq(\"author_id\", filters.author_id);\r\n      }\r\n\r\n      if (filters?.search) {\r\n        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%,tags.cs.{${filters.search}}`);\r\n      }\r\n\r\n      // Order by featured first, then by usage count\r\n      query = query.order(\"is_featured\", { ascending: false })\r\n                   .order(\"usage_count\", { ascending: false });\r\n\r\n      const { data, error } = await query;\r\n\r\n      if (error) throw error;\r\n\r\n      // Transform data to match Context interface\r\n      return data.map((item) => ({\r\n        id: item.id,\r\n        title: item.title,\r\n        description: item.description,\r\n        content: item.content,\r\n        category: item.category,\r\n        author_id: item.author_id,\r\n        author_name: 'Kullanıcı', // Şimdilik static, sonra profiles tablosu eklenebilir\r\n        is_public: item.is_public,\r\n        is_featured: item.is_featured,\r\n        is_template: item.is_template,\r\n        tags: item.tags || [],\r\n        usage_count: item.usage_count,\r\n        like_count: item.like_count,\r\n        view_count: item.view_count,\r\n        approval_status: item.approval_status || 'approved',\r\n        approved_by: item.approved_by,\r\n        approved_at: item.approved_at,\r\n        approval_notes: item.approval_notes,\r\n        created_at: item.created_at,\r\n        updated_at: item.updated_at,\r\n      })) as Context[];\r\n    },\r\n    staleTime: 2 * 60 * 1000, // 2 dakika\r\n  });\r\n}\r\n\r\n// Get single context\r\nexport function useContext(id: string) {\r\n  return useQuery({\r\n    queryKey: [\"context\", id],\r\n    queryFn: async () => {\r\n      const { data, error } = await supabase\r\n        .from(\"contexts\")\r\n        .select(`\r\n          *,\r\n          category:context_categories(*)\r\n        `)\r\n        .eq(\"id\", id)\r\n        .single();\r\n\r\n      if (error) throw error;\r\n\r\n      return {\r\n        id: data.id,\r\n        title: data.title,\r\n        description: data.description,\r\n        content: data.content,\r\n        category: data.category,\r\n        author_id: data.author_id,\r\n        author_name: 'Kullanıcı', // Şimdilik static, sonra profiles tablosu eklenebilir\r\n        is_public: data.is_public,\r\n        is_featured: data.is_featured,\r\n        is_template: data.is_template,\r\n        tags: data.tags || [],\r\n        usage_count: data.usage_count,\r\n        like_count: data.like_count,\r\n        view_count: data.view_count,\r\n        created_at: data.created_at,\r\n        updated_at: data.updated_at,\r\n      } as Context;\r\n    },\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\n// Create context\r\nexport function useCreateContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async (newContext: {\r\n      title: string;\r\n      description?: string;\r\n      content: string;\r\n      category_id: string;\r\n      is_public?: boolean;\r\n      is_template?: boolean;\r\n      tags?: string[];\r\n    }) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { data, error } = await supabase\r\n        .from(\"contexts\")\r\n        .insert({\r\n          ...newContext,\r\n          author_id: user.user.id,\r\n        })\r\n        .select()\r\n        .single();\r\n\r\n      if (error) throw error;\r\n      return data;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Update context\r\nexport function useUpdateContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      id,\r\n      updates\r\n    }: {\r\n      id: string;\r\n      updates: {\r\n        title?: string;\r\n        description?: string;\r\n        content?: string;\r\n        category_id?: string;\r\n        is_public?: boolean;\r\n        is_template?: boolean;\r\n        is_featured?: boolean;\r\n        tags?: string[];\r\n        status?: 'active' | 'inactive' | 'archived';\r\n      }\r\n    }) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { data, error } = await supabase\r\n        .from(\"contexts\")\r\n        .update({\r\n          ...updates,\r\n          updated_at: new Date().toISOString(),\r\n        })\r\n        .eq(\"id\", id)\r\n        .eq(\"author_id\", user.user.id) // Sadece kendi context'lerini güncelleyebilir\r\n        .select()\r\n        .single();\r\n\r\n      if (error) throw error;\r\n      return data;\r\n    },\r\n    onSuccess: (data, variables) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"context\", variables.id] });\r\n    },\r\n  });\r\n}\r\n\r\n// Delete context\r\nexport function useDeleteContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async (id: string) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { error } = await supabase\r\n        .from(\"contexts\")\r\n        .delete()\r\n        .eq(\"id\", id)\r\n        .eq(\"author_id\", user.user.id); // Sadece kendi context'lerini silebilir\r\n\r\n      if (error) throw error;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Approve/Reject context (Admin only)\r\nexport function useApproveContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      contextId,\r\n      status,\r\n      notes\r\n    }: {\r\n      contextId: string;\r\n      status: 'approved' | 'rejected';\r\n      notes?: string\r\n    }) => {\r\n      const { error } = await supabase.rpc('approve_context', {\r\n        context_id_param: contextId,\r\n        approval_status_param: status,\r\n        approval_notes_param: notes\r\n      });\r\n\r\n      if (error) throw error;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Use context (increment usage count)\r\nexport function useUseContext() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ \r\n      contextId, \r\n      projectId \r\n    }: { \r\n      contextId: string; \r\n      projectId?: string; \r\n    }) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { error } = await supabase.rpc(\"increment_context_usage\", {\r\n        context_id_param: contextId,\r\n        user_id_param: user.user.id,\r\n        project_id_param: projectId || null,\r\n      });\r\n\r\n      if (error) throw error;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Toggle like\r\nexport function useToggleContextLike() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async (contextId: string) => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { data, error } = await supabase.rpc(\"toggle_context_like\", {\r\n        context_id_param: contextId,\r\n        user_id_param: user.user.id,\r\n      });\r\n\r\n      if (error) throw error;\r\n      return data; // Returns true if liked, false if unliked\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"contexts\"] });\r\n    },\r\n  });\r\n}\r\n\r\n// Get user's liked contexts\r\nexport function useUserLikedContexts() {\r\n  return useQuery({\r\n    queryKey: [\"user-liked-contexts\"],\r\n    queryFn: async () => {\r\n      const { data: user } = await supabase.auth.getUser();\r\n      if (!user.user) throw new Error(\"Kullanıcı girişi gerekli\");\r\n\r\n      const { data, error } = await supabase\r\n        .from(\"context_likes\")\r\n        .select(\"context_id\")\r\n        .eq(\"user_id\", user.user.id);\r\n\r\n      if (error) throw error;\r\n      return data.map(item => item.context_id);\r\n    },\r\n  });\r\n}\r\n\r\n// Get context statistics\r\nexport function useContextStats() {\r\n  return useQuery({\r\n    queryKey: [\"context-stats\"],\r\n    queryFn: async () => {\r\n      const { data, error } = await supabase\r\n        .from(\"contexts\")\r\n        .select(\"is_public, is_template, is_featured, status\")\r\n        .eq(\"status\", \"active\");\r\n\r\n      if (error) throw error;\r\n\r\n      const stats = {\r\n        total: data.length,\r\n        public: data.filter(c => c.is_public).length,\r\n        private: data.filter(c => !c.is_public).length,\r\n        templates: data.filter(c => c.is_template).length,\r\n        featured: data.filter(c => c.is_featured).length,\r\n      };\r\n\r\n      return stats;\r\n    },\r\n    staleTime: 5 * 60 * 1000, // 5 dakika\r\n  });\r\n} "], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAAA;AAAA;AACA;;AAHA;;;AAOO,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAqB;QAChC,OAAO;6CAAE;gBACP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAK;gBAEzC,IAAI,OAAO,MAAM;gBACjB,OAAO;YACT;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;GAfgB;;QACP,8KAAA,CAAA,WAAQ;;;AAiBV,SAAS,YAAY,OAO3B;;IACC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAQ;QAC/B,OAAO;oCAAE;gBACP,IAAI,QAAQ,oIAAA,CAAA,kBAAQ,CACjB,IAAI,CAAC,YACL,MAAM,CAAE,sEAIR,EAAE,CAAC,UAAU;gBAEhB,gBAAgB;gBAChB,IAAI,oBAAA,8BAAA,QAAS,WAAW,EAAE;oBACxB,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;gBACrD;gBAEA,IAAI,CAAA,oBAAA,8BAAA,QAAS,SAAS,MAAK,WAAW;oBACpC,QAAQ,MAAM,EAAE,CAAC,aAAa,QAAQ,SAAS;gBACjD;gBAEA,IAAI,CAAA,oBAAA,8BAAA,QAAS,WAAW,MAAK,WAAW;oBACtC,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;gBACrD;gBAEA,IAAI,CAAA,oBAAA,8BAAA,QAAS,WAAW,MAAK,WAAW;oBACtC,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;gBACrD;gBAEA,IAAI,oBAAA,8BAAA,QAAS,SAAS,EAAE;oBACtB,QAAQ,MAAM,EAAE,CAAC,aAAa,QAAQ,SAAS;gBACjD;gBAEA,IAAI,oBAAA,8BAAA,QAAS,MAAM,EAAE;oBACnB,QAAQ,MAAM,EAAE,CAAC,AAAC,gBAAqD,OAAtC,QAAQ,MAAM,EAAC,yBAAmD,OAA5B,QAAQ,MAAM,EAAC,eAA4B,OAAf,QAAQ,MAAM,EAAC;gBACpH;gBAEA,+CAA+C;gBAC/C,QAAQ,MAAM,KAAK,CAAC,eAAe;oBAAE,WAAW;gBAAM,GACxC,KAAK,CAAC,eAAe;oBAAE,WAAW;gBAAM;gBAEtD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;gBAE9B,IAAI,OAAO,MAAM;gBAEjB,4CAA4C;gBAC5C,OAAO,KAAK,GAAG;4CAAC,CAAC,OAAS,CAAC;4BACzB,IAAI,KAAK,EAAE;4BACX,OAAO,KAAK,KAAK;4BACjB,aAAa,KAAK,WAAW;4BAC7B,SAAS,KAAK,OAAO;4BACrB,UAAU,KAAK,QAAQ;4BACvB,WAAW,KAAK,SAAS;4BACzB,aAAa;4BACb,WAAW,KAAK,SAAS;4BACzB,aAAa,KAAK,WAAW;4BAC7B,aAAa,KAAK,WAAW;4BAC7B,MAAM,KAAK,IAAI,IAAI,EAAE;4BACrB,aAAa,KAAK,WAAW;4BAC7B,YAAY,KAAK,UAAU;4BAC3B,YAAY,KAAK,UAAU;4BAC3B,iBAAiB,KAAK,eAAe,IAAI;4BACzC,aAAa,KAAK,WAAW;4BAC7B,aAAa,KAAK,WAAW;4BAC7B,gBAAgB,KAAK,cAAc;4BACnC,YAAY,KAAK,UAAU;4BAC3B,YAAY,KAAK,UAAU;wBAC7B,CAAC;;YACH;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;IA9EgB;;QAQP,8KAAA,CAAA,WAAQ;;;AAyEV,SAAS,WAAW,EAAU;;IACnC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAG;QACzB,OAAO;mCAAE;gBACP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAE,sEAIR,EAAE,CAAC,MAAM,IACT,MAAM;gBAET,IAAI,OAAO,MAAM;gBAEjB,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,aAAa,KAAK,WAAW;oBAC7B,SAAS,KAAK,OAAO;oBACrB,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS;oBACzB,aAAa;oBACb,WAAW,KAAK,SAAS;oBACzB,aAAa,KAAK,WAAW;oBAC7B,aAAa,KAAK,WAAW;oBAC7B,MAAM,KAAK,IAAI,IAAI,EAAE;oBACrB,aAAa,KAAK,WAAW;oBAC7B,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;gBAC7B;YACF;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IApCgB;;QACP,8KAAA,CAAA,WAAQ;;;AAsCV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,OAAO;gBASjB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;gBAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;oBACN,GAAG,UAAU;oBACb,WAAW,KAAK,IAAI,CAAC,EAAE;gBACzB,GACC,MAAM,GACN,MAAM;gBAET,IAAI,OAAO,MAAM;gBACjB,OAAO;YACT;;QACA,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;YACzD;;IACF;AACF;IAhCgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAgCb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE;oBAAO,EACjB,EAAE,EACF,OAAO,EAcR;gBACC,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;gBAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;oBACN,GAAG,OAAO;oBACV,YAAY,IAAI,OAAO,WAAW;gBACpC,GACC,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,EAAE,EAAE,8CAA8C;iBAC5E,MAAM,GACN,MAAM;gBAET,IAAI,OAAO,MAAM;gBACjB,OAAO;YACT;;QACA,SAAS;4CAAE,CAAC,MAAM;gBAChB,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW,UAAU,EAAE;qBAAC;gBAAC;YACtE;;IACF;AACF;IA3CgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA2Cb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,OAAO;gBACjB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;gBAEhC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,EAAE,GAAG,wCAAwC;gBAE1E,IAAI,OAAO,MAAM;YACnB;;QACA,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;YACzD;;IACF;AACF;IApBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAoBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;6CAAE;oBAAO,EACjB,SAAS,EACT,MAAM,EACN,KAAK,EAKN;gBACC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,mBAAmB;oBACtD,kBAAkB;oBAClB,uBAAuB;oBACvB,sBAAsB;gBACxB;gBAEA,IAAI,OAAO,MAAM;YACnB;;QACA,SAAS;6CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;YACzD;;IACF;AACF;IAzBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAyBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE;oBAAO,EACjB,SAAS,EACT,SAAS,EAIV;gBACC,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;gBAEhC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,2BAA2B;oBAC9D,kBAAkB;oBAClB,eAAe,KAAK,IAAI,CAAC,EAAE;oBAC3B,kBAAkB,aAAa;gBACjC;gBAEA,IAAI,OAAO,MAAM;YACnB;;QACA,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;YACzD;;IACF;AACF;IA1BgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA0Bb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;gDAAE,OAAO;gBACjB,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;gBAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,uBAAuB;oBAChE,kBAAkB;oBAClB,eAAe,KAAK,IAAI,CAAC,EAAE;gBAC7B;gBAEA,IAAI,OAAO,MAAM;gBACjB,OAAO,MAAM,0CAA0C;YACzD;;QACA,SAAS;gDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;YACzD;;IACF;AACF;IApBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAoBb,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAsB;QACjC,OAAO;6CAAE;gBACP,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAClD,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,IAAI,MAAM;gBAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC,cACP,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE;gBAE7B,IAAI,OAAO,MAAM;gBACjB,OAAO,KAAK,GAAG;qDAAC,CAAA,OAAQ,KAAK,UAAU;;YACzC;;IACF;AACF;IAhBgB;;QACP,8KAAA,CAAA,WAAQ;;;AAkBV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAgB;QAC3B,OAAO;wCAAE;gBACP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,+CACP,EAAE,CAAC,UAAU;gBAEhB,IAAI,OAAO,MAAM;gBAEjB,MAAM,QAAQ;oBACZ,OAAO,KAAK,MAAM;oBAClB,QAAQ,KAAK,MAAM;oDAAC,CAAA,IAAK,EAAE,SAAS;mDAAE,MAAM;oBAC5C,SAAS,KAAK,MAAM;oDAAC,CAAA,IAAK,CAAC,EAAE,SAAS;mDAAE,MAAM;oBAC9C,WAAW,KAAK,MAAM;oDAAC,CAAA,IAAK,EAAE,WAAW;mDAAE,MAAM;oBACjD,UAAU,KAAK,MAAM;oDAAC,CAAA,IAAK,EAAE,WAAW;mDAAE,MAAM;gBAClD;gBAEA,OAAO;YACT;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;KAvBgB;;QACP,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/plan-limits.ts"], "sourcesContent": ["// Plan Limitleri Kontrol Middleware ve Utility Fonksiyonları\n\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\nexport interface PlanLimits {\n  current_projects: number\n  current_prompts: number\n  max_projects: number\n  max_prompts_per_project: number\n  can_create_project: boolean\n  can_create_prompt: boolean\n}\n\nexport interface UserPlan {\n  plan_id: string\n  plan_name: string\n  display_name: string\n  max_projects: number\n  max_prompts_per_project: number\n  features: Record<string, boolean | string | number>\n  status: string\n  expires_at: string | null\n}\n\n// Plan limitlerini kontrol et\nexport async function checkUserLimits(): Promise<PlanLimits | null> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { data, error } = await supabase.rpc('check_user_limits', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Plan limitleri kontrol edilemedi:', error)\n      throw new Error(error.message)\n    }\n\n    return data?.[0] || null\n  } catch (error) {\n    console.error('Plan limitleri kontrol hatası:', error)\n    return null\n  }\n}\n\n// Kullanıcının aktif planını getir\nexport async function getUserActivePlan(): Promise<UserPlan | null> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { data, error } = await supabase.rpc('get_user_active_plan', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Aktif plan getirilemedi:', error)\n      throw new Error(error.message)\n    }\n\n    return data?.[0] || null\n  } catch (error) {\n    console.error('Aktif plan getirme hatası:', error)\n    return null\n  }\n}\n\n// Proje oluşturma limiti kontrol et\nexport async function canCreateProject(): Promise<{ allowed: boolean; reason?: string }> {\n  const limits = await checkUserLimits()\n  \n  if (!limits) {\n    return { allowed: false, reason: 'Plan bilgileri alınamadı' }\n  }\n\n  if (!limits.can_create_project) {\n    return { \n      allowed: false, \n      reason: `Proje limiti aşıldı (${limits.current_projects}/${limits.max_projects})` \n    }\n  }\n\n  return { allowed: true }\n}\n\n// Prompt oluşturma limiti kontrol et\nexport async function canCreatePrompt(projectId?: string): Promise<{ allowed: boolean; reason?: string }> {\n  const limits = await checkUserLimits()\n  \n  if (!limits) {\n    return { allowed: false, reason: 'Plan bilgileri alınamadı' }\n  }\n\n  if (!limits.can_create_prompt) {\n    return { \n      allowed: false, \n      reason: `Prompt limiti aşıldı (${limits.current_prompts}/${limits.max_prompts_per_project})` \n    }\n  }\n\n  // Eğer belirli bir proje için kontrol ediliyorsa, o projenin prompt sayısını kontrol et\n  if (projectId) {\n    try {\n      const { count, error } = await supabase\n        .from('prompts')\n        .select('*', { count: 'exact', head: true })\n        .eq('project_id', projectId)\n\n      if (error) {\n        console.error('Proje prompt sayısı kontrol edilemedi:', error)\n        return { allowed: false, reason: 'Proje bilgileri alınamadı' }\n      }\n\n      const currentPrompts = count || 0\n      if (limits.max_prompts_per_project !== -1 && currentPrompts >= limits.max_prompts_per_project) {\n        return { \n          allowed: false, \n          reason: `Bu proje için prompt limiti aşıldı (${currentPrompts}/${limits.max_prompts_per_project})` \n        }\n      }\n    } catch (error) {\n      console.error('Proje prompt sayısı kontrol hatası:', error)\n      return { allowed: false, reason: 'Proje bilgileri kontrol edilemedi' }\n    }\n  }\n\n  return { allowed: true }\n}\n\n// Plan özelliği kontrol et\nexport async function hasPlanFeature(featureName: string): Promise<boolean> {\n  const plan = await getUserActivePlan()\n  return plan?.features?.[featureName] === true\n}\n\n// Kullanım istatistiklerini güncelle\nexport async function updateUsageStats(): Promise<void> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { error } = await supabase.rpc('update_usage_stats', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Kullanım istatistikleri güncellenemedi:', error)\n      throw new Error(error.message)\n    }\n  } catch (error) {\n    console.error('Kullanım istatistikleri güncelleme hatası:', error)\n    throw error\n  }\n}\n\n// Plan upgrade önerisi\nexport function getPlanUpgradeSuggestion(limits: PlanLimits): {\n  shouldUpgrade: boolean\n  reason: string\n  suggestedPlan: string\n} {\n  // Proje limiti %80'e ulaştıysa\n  if (limits.max_projects !== -1 && limits.current_projects / limits.max_projects >= 0.8) {\n    return {\n      shouldUpgrade: true,\n      reason: 'Proje limitinizin %80\\'ine ulaştınız',\n      suggestedPlan: 'professional'\n    }\n  }\n\n  // Prompt limiti %80'e ulaştıysa\n  if (limits.max_prompts_per_project !== -1 && limits.current_prompts / limits.max_prompts_per_project >= 0.8) {\n    return {\n      shouldUpgrade: true,\n      reason: 'Prompt limitinizin %80\\'ine ulaştınız',\n      suggestedPlan: 'professional'\n    }\n  }\n\n  return {\n    shouldUpgrade: false,\n    reason: '',\n    suggestedPlan: ''\n  }\n}\n\n// Plan limiti hata mesajları\nexport const PLAN_LIMIT_MESSAGES = {\n  PROJECT_LIMIT_REACHED: 'Proje oluşturma limitinize ulaştınız. Daha fazla proje oluşturmak için planınızı yükseltin.',\n  PROMPT_LIMIT_REACHED: 'Prompt oluşturma limitinize ulaştınız. Daha fazla prompt oluşturmak için planınızı yükseltin.',\n  FEATURE_NOT_AVAILABLE: 'Bu özellik mevcut planınızda bulunmamaktadır. Planınızı yükseltin.',\n  PLAN_EXPIRED: 'Planınızın süresi dolmuştur. Lütfen planınızı yenileyin.',\n  PLAN_SUSPENDED: 'Hesabınız askıya alınmıştır. Lütfen destek ekibi ile iletişime geçin.'\n}\n\n// Plan durumu kontrol et\nexport async function checkPlanStatus(): Promise<{\n  isActive: boolean\n  status: string\n  message?: string\n}> {\n  const plan = await getUserActivePlan()\n  \n  if (!plan) {\n    return {\n      isActive: false,\n      status: 'no_plan',\n      message: 'Aktif plan bulunamadı'\n    }\n  }\n\n  if (plan.status !== 'active') {\n    return {\n      isActive: false,\n      status: plan.status,\n      message: PLAN_LIMIT_MESSAGES.PLAN_SUSPENDED\n    }\n  }\n\n  if (plan.expires_at && new Date(plan.expires_at) < new Date()) {\n    return {\n      isActive: false,\n      status: 'expired',\n      message: PLAN_LIMIT_MESSAGES.PLAN_EXPIRED\n    }\n  }\n\n  return {\n    isActive: true,\n    status: 'active'\n  }\n}\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;AAE7D;;AAuBO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,qBAAqB;YAC9D,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,CAAA,iBAAA,2BAAA,IAAM,CAAC,EAAE,KAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,wBAAwB;YACjE,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,CAAA,iBAAA,2BAAA,IAAM,CAAC,EAAE,KAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,SAAS,MAAM;IAErB,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,QAAQ;QAA2B;IAC9D;IAEA,IAAI,CAAC,OAAO,kBAAkB,EAAE;QAC9B,OAAO;YACL,SAAS;YACT,QAAQ,AAAC,wBAAkD,OAA3B,OAAO,gBAAgB,EAAC,KAAuB,OAApB,OAAO,YAAY,EAAC;QACjF;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,gBAAgB,SAAkB;IACtD,MAAM,SAAS,MAAM;IAErB,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,QAAQ;QAA2B;IAC9D;IAEA,IAAI,CAAC,OAAO,iBAAiB,EAAE;QAC7B,OAAO;YACL,SAAS;YACT,QAAQ,AAAC,yBAAkD,OAA1B,OAAO,eAAe,EAAC,KAAkC,OAA/B,OAAO,uBAAuB,EAAC;QAC5F;IACF;IAEA,wFAAwF;IACxF,IAAI,WAAW;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACpC,IAAI,CAAC,WACL,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GACzC,EAAE,CAAC,cAAc;YAEpB,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,OAAO;oBAAE,SAAS;oBAAO,QAAQ;gBAA4B;YAC/D;YAEA,MAAM,iBAAiB,SAAS;YAChC,IAAI,OAAO,uBAAuB,KAAK,CAAC,KAAK,kBAAkB,OAAO,uBAAuB,EAAE;gBAC7F,OAAO;oBACL,SAAS;oBACT,QAAQ,AAAC,uCAAwD,OAAlB,gBAAe,KAAkC,OAA/B,OAAO,uBAAuB,EAAC;gBAClG;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;gBAAE,SAAS;gBAAO,QAAQ;YAAoC;QACvE;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,eAAe,WAAmB;QAE/C;IADP,MAAM,OAAO,MAAM;IACnB,OAAO,CAAA,iBAAA,4BAAA,iBAAA,KAAM,QAAQ,cAAd,qCAAA,cAAgB,CAAC,YAAY,MAAK;AAC3C;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,sBAAsB;YACzD,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,MAAM;IACR;AACF;AAGO,SAAS,yBAAyB,MAAkB;IAKzD,+BAA+B;IAC/B,IAAI,OAAO,YAAY,KAAK,CAAC,KAAK,OAAO,gBAAgB,GAAG,OAAO,YAAY,IAAI,KAAK;QACtF,OAAO;YACL,eAAe;YACf,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,gCAAgC;IAChC,IAAI,OAAO,uBAAuB,KAAK,CAAC,KAAK,OAAO,eAAe,GAAG,OAAO,uBAAuB,IAAI,KAAK;QAC3G,OAAO;YACL,eAAe;YACf,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,OAAO;QACL,eAAe;QACf,QAAQ;QACR,eAAe;IACjB;AACF;AAGO,MAAM,sBAAsB;IACjC,uBAAuB;IACvB,sBAAsB;IACtB,uBAAuB;IACvB,cAAc;IACd,gBAAgB;AAClB;AAGO,eAAe;IAKpB,MAAM,OAAO,MAAM;IAEnB,IAAI,CAAC,MAAM;QACT,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS;QACX;IACF;IAEA,IAAI,KAAK,MAAM,KAAK,UAAU;QAC5B,OAAO;YACL,UAAU;YACV,QAAQ,KAAK,MAAM;YACnB,SAAS,oBAAoB,cAAc;QAC7C;IACF;IAEA,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,QAAQ;QAC7D,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS,oBAAoB,YAAY;QAC3C;IACF;IAEA,OAAO;QACL,UAAU;QACV,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 1168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-prompts.ts"], "sourcesContent": ["'use client'\r\n\r\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\r\nimport { Database } from '@/lib/supabase'\r\nimport { canCreatePrompt, updateUsageStats, PLAN_LIMIT_MESSAGES } from '@/lib/plan-limits'\r\n// Advanced validation will be integrated in future updates\r\n// import { checkRateLimit, RateLimitError } from '@/lib/rate-limiter'\r\n// import { AdvancedValidator, COMMON_VALIDATION_RULES } from '@/lib/advanced-validation'\r\n\r\ntype Prompt = Database['public']['Tables']['prompts']['Row']\r\ntype PromptInsert = Database['public']['Tables']['prompts']['Insert']\r\ntype PromptUpdate = Database['public']['Tables']['prompts']['Update']\r\n\r\n// Proje prompt'larını getir\r\nexport function usePrompts(projectId: string | null) {\r\n  return useQuery({\r\n    queryKey: ['prompts', projectId],\r\n    queryFn: async (): Promise<Prompt[]> => {\r\n      if (!projectId) {\r\n        console.log(`📝 [USE_PROMPTS] No project ID provided`)\r\n        return []\r\n      }\r\n\r\n      console.log(`📝 [USE_PROMPTS] Fetching prompts for project:`, projectId)\r\n\r\n      try {\r\n        // Check if we have a session first\r\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession()\r\n        console.log(`📝 [USE_PROMPTS] Session check:`, {\r\n          hasSession: !!session,\r\n          sessionError: sessionError?.message,\r\n          userId: session?.user?.id\r\n        })\r\n\r\n        const { data, error } = await supabase\r\n          .from('prompts')\r\n          .select('*')\r\n          .eq('project_id', projectId)\r\n          .order('order_index', { ascending: true })\r\n\r\n        if (error) {\r\n          console.error(`❌ [USE_PROMPTS] Error fetching prompts:`, error)\r\n          throw new Error(error.message)\r\n        }\r\n\r\n        console.log(`✅ [USE_PROMPTS] Prompts fetched:`, data?.length || 0, 'prompts')\r\n        return data || []\r\n      } catch (err) {\r\n        console.error(`💥 [USE_PROMPTS] Exception:`, err)\r\n        throw err\r\n      }\r\n    },\r\n    enabled: !!projectId,\r\n  })\r\n}\r\n\r\n// Prompt oluştur\r\nexport function useCreatePrompt() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (prompt: Omit<PromptInsert, 'user_id'>): Promise<Prompt> => {\r\n      // Plan limiti kontrol et\r\n      const limitCheck = await canCreatePrompt(prompt.project_id)\r\n      if (!limitCheck.allowed) {\r\n        throw new Error(limitCheck.reason || PLAN_LIMIT_MESSAGES.PROMPT_LIMIT_REACHED)\r\n      }\r\n\r\n      const { data: { user }, error: userError } = await supabase.auth.getUser()\r\n\r\n      if (userError || !user) {\r\n        throw new Error('Kullanıcı oturumu bulunamadı')\r\n      }\r\n\r\n      // Task code otomatik oluştur\r\n      const taskCode = prompt.task_code || `task-${prompt.order_index}`\r\n\r\n      const { data, error } = await supabase\r\n        .from('prompts')\r\n        .insert({\r\n          ...prompt,\r\n          user_id: user.id,\r\n          task_code: taskCode,\r\n          tags: prompt.tags || [],\r\n          is_favorite: prompt.is_favorite || false,\r\n          usage_count: prompt.usage_count || 0,\r\n        })\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onSuccess: async (data) => {\r\n      queryClient.invalidateQueries({ queryKey: ['prompts', data.project_id] })\r\n      // Invalidate hashtag and category queries\r\n      queryClient.invalidateQueries({ queryKey: ['popular-hashtags', data.project_id] })\r\n      queryClient.invalidateQueries({ queryKey: ['all-hashtags', data.project_id] })\r\n      queryClient.invalidateQueries({ queryKey: ['popular-categories', data.project_id] })\r\n      queryClient.invalidateQueries({ queryKey: ['all-categories', data.project_id] })\r\n\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Prompt güncelle\r\nexport function useUpdatePrompt() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ id, ...updates }: PromptUpdate & { id: string }): Promise<Prompt> => {\r\n      const { data, error } = await supabase\r\n        .from('prompts')\r\n        .update({\r\n          ...updates,\r\n          updated_at: new Date().toISOString(),\r\n        })\r\n        .eq('id', id)\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: ['prompts', data.project_id] })\r\n      // Invalidate hashtag and category queries if tags or category were updated\r\n      queryClient.invalidateQueries({ queryKey: ['popular-hashtags', data.project_id] })\r\n      queryClient.invalidateQueries({ queryKey: ['all-hashtags', data.project_id] })\r\n      queryClient.invalidateQueries({ queryKey: ['popular-categories', data.project_id] })\r\n      queryClient.invalidateQueries({ queryKey: ['all-categories', data.project_id] })\r\n    },\r\n  })\r\n}\r\n\r\n// Prompt sil\r\nexport function useDeletePrompt() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ id }: { id: string; projectId: string }): Promise<void> => {\r\n      const { error } = await supabase\r\n        .from('prompts')\r\n        .delete()\r\n        .eq('id', id)\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n    },\r\n    onSuccess: (_, { projectId }) => {\r\n      queryClient.invalidateQueries({ queryKey: ['prompts', projectId] })\r\n    },\r\n  })\r\n}\r\n\r\n// Prompt'u kullanıldı olarak işaretle (optimistic update ile)\r\nexport function useMarkPromptAsUsed() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (promptId: string): Promise<Prompt> => {\r\n      const { data, error } = await supabase\r\n        .from('prompts')\r\n        .update({ is_used: true })\r\n        .eq('id', promptId)\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onMutate: async (promptId) => {\r\n      // Optimistic update\r\n      const queryKey = ['prompts']\r\n      await queryClient.cancelQueries({ queryKey })\r\n\r\n      const previousPrompts = queryClient.getQueriesData({ queryKey })\r\n\r\n      queryClient.setQueriesData({ queryKey }, (old: unknown) => {\r\n        if (!old || !Array.isArray(old)) return old\r\n        return old.map((prompt: Prompt) =>\r\n          prompt.id === promptId ? { ...prompt, is_used: true } : prompt\r\n        )\r\n      })\r\n\r\n      return { previousPrompts }\r\n    },\r\n    onError: (err, _promptId, context) => {\r\n      // Hata durumunda geri al\r\n      if (context?.previousPrompts) {\r\n        context.previousPrompts.forEach(([queryKey, data]) => {\r\n          queryClient.setQueryData(queryKey, data)\r\n        })\r\n      }\r\n    },\r\n    onSettled: (data) => {\r\n      // Her durumda cache'i yenile\r\n      if (data) {\r\n        queryClient.invalidateQueries({ queryKey: ['prompts', data.project_id] })\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Proje prompt'larını sıfırla\r\nexport function useResetPrompts() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (projectId: string): Promise<void> => {\r\n      const { error } = await supabase\r\n        .from('prompts')\r\n        .update({ is_used: false })\r\n        .eq('project_id', projectId)\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n    },\r\n    onSuccess: (_, projectId) => {\r\n      queryClient.invalidateQueries({ queryKey: ['prompts', projectId] })\r\n    },\r\n  })\r\n}\r\n\r\n// Toplu prompt güncelleme (optimistic update ile)\r\nexport function useBulkUpdatePrompts() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (updates: Array<{ id: string; order_index?: number; task_code?: string; is_used?: boolean }>): Promise<Prompt[]> => {\r\n      // Try to use RPC function for order updates, fallback to individual updates for other fields\r\n      const orderUpdates = updates.filter(u => u.order_index !== undefined || u.task_code !== undefined)\r\n      const otherUpdates = updates.filter(u => u.order_index === undefined && u.task_code === undefined)\r\n\r\n      const results: Prompt[] = []\r\n\r\n      // Use RPC function for order/task_code updates (more efficient)\r\n      if (orderUpdates.length > 0) {\r\n        try {\r\n          const { data: rpcData, error: rpcError } = await supabase.rpc('bulk_update_prompts_order', {\r\n            prompt_updates: orderUpdates\r\n          })\r\n\r\n          if (rpcError) {\r\n            console.warn('RPC function failed, falling back to individual updates:', rpcError)\r\n            // Fallback to individual updates\r\n            const fallbackPromises = orderUpdates.map(async (update) => {\r\n              const { data, error } = await supabase\r\n                .from('prompts')\r\n                .update({\r\n                  ...update,\r\n                  updated_at: new Date().toISOString(),\r\n                })\r\n                .eq('id', update.id)\r\n                .select()\r\n                .single()\r\n\r\n              if (error) {\r\n                throw new Error(`Failed to update prompt ${update.id}: ${error.message}`)\r\n              }\r\n              return data\r\n            })\r\n\r\n            const fallbackResults = await Promise.all(fallbackPromises)\r\n            results.push(...fallbackResults)\r\n          } else if (rpcData) {\r\n            // Get full prompt data for RPC results\r\n            const rpcIds = rpcData.map((r: { id: string }) => r.id)\r\n            const { data: fullPrompts, error: selectError } = await supabase\r\n              .from('prompts')\r\n              .select('*')\r\n              .in('id', rpcIds)\r\n\r\n            if (selectError) {\r\n              throw new Error(`Failed to fetch updated prompts: ${selectError.message}`)\r\n            }\r\n\r\n            results.push(...(fullPrompts || []))\r\n          }\r\n        } catch (error) {\r\n          console.error('Bulk update error:', error)\r\n          throw error\r\n        }\r\n      }\r\n\r\n      // Handle other updates (is_used, etc.) with individual calls\r\n      if (otherUpdates.length > 0) {\r\n        const updatePromises = otherUpdates.map(async (update) => {\r\n          const { data, error } = await supabase\r\n            .from('prompts')\r\n            .update({\r\n              ...update,\r\n              updated_at: new Date().toISOString(),\r\n            })\r\n            .eq('id', update.id)\r\n            .select()\r\n            .single()\r\n\r\n          if (error) {\r\n            throw new Error(`Failed to update prompt ${update.id}: ${error.message}`)\r\n          }\r\n\r\n          return data\r\n        })\r\n\r\n        const updatedPrompts = await Promise.all(updatePromises)\r\n        results.push(...updatedPrompts)\r\n      }\r\n\r\n      return results\r\n    },\r\n    onMutate: async (updates) => {\r\n      // Optimistic update\r\n      const queryKey = ['prompts']\r\n      await queryClient.cancelQueries({ queryKey })\r\n\r\n      const previousPrompts = queryClient.getQueriesData({ queryKey })\r\n\r\n      queryClient.setQueriesData({ queryKey }, (old: unknown) => {\r\n        if (!old || !Array.isArray(old)) return old\r\n\r\n        return old.map((prompt: Prompt) => {\r\n          const update = updates.find(u => u.id === prompt.id)\r\n          return update ? { ...prompt, ...update } : prompt\r\n        })\r\n      })\r\n\r\n      return { previousPrompts }\r\n    },\r\n    onError: (err, _updates, context) => {\r\n      // Hata durumunda geri al\r\n      if (context?.previousPrompts) {\r\n        context.previousPrompts.forEach(([queryKey, data]) => {\r\n          queryClient.setQueryData(queryKey, data)\r\n        })\r\n      }\r\n    },\r\n    onSettled: (data) => {\r\n      // Her durumda cache'i yenile\r\n      if (data && data.length > 0) {\r\n        queryClient.invalidateQueries({ queryKey: ['prompts', data[0].project_id] })\r\n      }\r\n    },\r\n  })\r\n}"], "names": [], "mappings": ";;;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;;AALA;;;;AAeO,SAAS,WAAW,SAAwB;;IACjD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAU;QAChC,OAAO;mCAAE;gBACP,IAAI,CAAC,WAAW;oBACd,QAAQ,GAAG,CAAE;oBACb,OAAO,EAAE;gBACX;gBAEA,QAAQ,GAAG,CAAE,kDAAiD;gBAE9D,IAAI;wBAMQ;oBALV,mCAAmC;oBACnC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;oBACjF,QAAQ,GAAG,CAAE,mCAAkC;wBAC7C,YAAY,CAAC,CAAC;wBACd,YAAY,EAAE,yBAAA,mCAAA,aAAc,OAAO;wBACnC,MAAM,EAAE,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,EAAE;oBAC3B;oBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,KAAK,CAAC,eAAe;wBAAE,WAAW;oBAAK;oBAE1C,IAAI,OAAO;wBACT,QAAQ,KAAK,CAAE,2CAA0C;wBACzD,MAAM,IAAI,MAAM,MAAM,OAAO;oBAC/B;oBAEA,QAAQ,GAAG,CAAE,oCAAmC,CAAA,iBAAA,2BAAA,KAAM,MAAM,KAAI,GAAG;oBACnE,OAAO,QAAQ,EAAE;gBACnB,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAE,+BAA8B;oBAC7C,MAAM;gBACR;YACF;;QACA,SAAS,CAAC,CAAC;IACb;AACF;GAxCgB;;QACP,8KAAA,CAAA,WAAQ;;;AA0CV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,OAAO;gBACjB,yBAAyB;gBACzB,MAAM,aAAa,MAAM,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,UAAU;gBAC1D,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,MAAM,IAAI,MAAM,WAAW,MAAM,IAAI,+HAAA,CAAA,sBAAmB,CAAC,oBAAoB;gBAC/E;gBAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAExE,IAAI,aAAa,CAAC,MAAM;oBACtB,MAAM,IAAI,MAAM;gBAClB;gBAEA,6BAA6B;gBAC7B,MAAM,WAAW,OAAO,SAAS,IAAI,AAAC,QAA0B,OAAnB,OAAO,WAAW;gBAE/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;oBACN,GAAG,MAAM;oBACT,SAAS,KAAK,EAAE;oBAChB,WAAW;oBACX,MAAM,OAAO,IAAI,IAAI,EAAE;oBACvB,aAAa,OAAO,WAAW,IAAI;oBACnC,aAAa,OAAO,WAAW,IAAI;gBACrC,GACC,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT;;QACA,SAAS;2CAAE,OAAO;gBAChB,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW,KAAK,UAAU;qBAAC;gBAAC;gBACvE,0CAA0C;gBAC1C,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAoB,KAAK,UAAU;qBAAC;gBAAC;gBAChF,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAgB,KAAK,UAAU;qBAAC;gBAAC;gBAC5E,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAsB,KAAK,UAAU;qBAAC;gBAAC;gBAClF,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAkB,KAAK,UAAU;qBAAC;gBAAC;gBAE9E,qCAAqC;gBACrC,IAAI;oBACF,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;oBACrB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;oBAC1D,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;gBAC5D,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,2CAA2C;gBAC1D;YACF;;IACF;AACF;IAzDgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAyDb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE;oBAAO,EAAE,EAAE,EAAE,GAAG,SAAwC;gBAClE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;oBACN,GAAG,OAAO;oBACV,YAAY,IAAI,OAAO,WAAW;gBACpC,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT;;QACA,SAAS;2CAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW,KAAK,UAAU;qBAAC;gBAAC;gBACvE,2EAA2E;gBAC3E,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAoB,KAAK,UAAU;qBAAC;gBAAC;gBAChF,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAgB,KAAK,UAAU;qBAAC;gBAAC;gBAC5E,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAsB,KAAK,UAAU;qBAAC;gBAAC;gBAClF,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAkB,KAAK,UAAU;qBAAC;gBAAC;YAChF;;IACF;AACF;IA9BgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA8Bb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE;oBAAO,EAAE,EAAE,EAAqC;gBAC1D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,WACL,MAAM,GACN,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;YACF;;QACA,SAAS;2CAAE,CAAC;oBAAG,EAAE,SAAS,EAAE;gBAC1B,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW;qBAAU;gBAAC;YACnE;;IACF;AACF;IAlBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;+CAAE,OAAO;gBACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;oBAAE,SAAS;gBAAK,GACvB,EAAE,CAAC,MAAM,UACT,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT;;QACA,QAAQ;+CAAE,OAAO;gBACf,oBAAoB;gBACpB,MAAM,WAAW;oBAAC;iBAAU;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE;gBAAS;gBAE3C,MAAM,kBAAkB,YAAY,cAAc,CAAC;oBAAE;gBAAS;gBAE9D,YAAY,cAAc,CAAC;oBAAE;gBAAS;uDAAG,CAAC;wBACxC,IAAI,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC,MAAM,OAAO;wBACxC,OAAO,IAAI,GAAG;+DAAC,CAAC,SACd,OAAO,EAAE,KAAK,WAAW;oCAAE,GAAG,MAAM;oCAAE,SAAS;gCAAK,IAAI;;oBAE5D;;gBAEA,OAAO;oBAAE;gBAAgB;YAC3B;;QACA,OAAO;+CAAE,CAAC,KAAK,WAAW;gBACxB,yBAAyB;gBACzB,IAAI,oBAAA,8BAAA,QAAS,eAAe,EAAE;oBAC5B,QAAQ,eAAe,CAAC,OAAO;2DAAC;gCAAC,CAAC,UAAU,KAAK;4BAC/C,YAAY,YAAY,CAAC,UAAU;wBACrC;;gBACF;YACF;;QACA,SAAS;+CAAE,CAAC;gBACV,6BAA6B;gBAC7B,IAAI,MAAM;oBACR,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAW,KAAK,UAAU;yBAAC;oBAAC;gBACzE;YACF;;IACF;AACF;IAjDgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiDb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,OAAO;gBACjB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,WACL,MAAM,CAAC;oBAAE,SAAS;gBAAM,GACxB,EAAE,CAAC,cAAc;gBAEpB,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;YACF;;QACA,SAAS;2CAAE,CAAC,GAAG;gBACb,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW;qBAAU;gBAAC;YACnE;;IACF;AACF;IAlBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;gDAAE,OAAO;gBACjB,6FAA6F;gBAC7F,MAAM,eAAe,QAAQ,MAAM;qEAAC,CAAA,IAAK,EAAE,WAAW,KAAK,aAAa,EAAE,SAAS,KAAK;;gBACxF,MAAM,eAAe,QAAQ,MAAM;qEAAC,CAAA,IAAK,EAAE,WAAW,KAAK,aAAa,EAAE,SAAS,KAAK;;gBAExF,MAAM,UAAoB,EAAE;gBAE5B,gEAAgE;gBAChE,IAAI,aAAa,MAAM,GAAG,GAAG;oBAC3B,IAAI;wBACF,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,6BAA6B;4BACzF,gBAAgB;wBAClB;wBAEA,IAAI,UAAU;4BACZ,QAAQ,IAAI,CAAC,4DAA4D;4BACzE,iCAAiC;4BACjC,MAAM,mBAAmB,aAAa,GAAG;qFAAC,OAAO;oCAC/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;wCACN,GAAG,MAAM;wCACT,YAAY,IAAI,OAAO,WAAW;oCACpC,GACC,EAAE,CAAC,MAAM,OAAO,EAAE,EAClB,MAAM,GACN,MAAM;oCAET,IAAI,OAAO;wCACT,MAAM,IAAI,MAAM,AAAC,2BAAwC,OAAd,OAAO,EAAE,EAAC,MAAkB,OAAd,MAAM,OAAO;oCACxE;oCACA,OAAO;gCACT;;4BAEA,MAAM,kBAAkB,MAAM,QAAQ,GAAG,CAAC;4BAC1C,QAAQ,IAAI,IAAI;wBAClB,OAAO,IAAI,SAAS;4BAClB,uCAAuC;4BACvC,MAAM,SAAS,QAAQ,GAAG;2EAAC,CAAC,IAAsB,EAAE,EAAE;;4BACtD,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC7D,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM;4BAEZ,IAAI,aAAa;gCACf,MAAM,IAAI,MAAM,AAAC,oCAAuD,OAApB,YAAY,OAAO;4BACzE;4BAEA,QAAQ,IAAI,IAAK,eAAe,EAAE;wBACpC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,sBAAsB;wBACpC,MAAM;oBACR;gBACF;gBAEA,6DAA6D;gBAC7D,IAAI,aAAa,MAAM,GAAG,GAAG;oBAC3B,MAAM,iBAAiB,aAAa,GAAG;2EAAC,OAAO;4BAC7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;gCACN,GAAG,MAAM;gCACT,YAAY,IAAI,OAAO,WAAW;4BACpC,GACC,EAAE,CAAC,MAAM,OAAO,EAAE,EAClB,MAAM,GACN,MAAM;4BAET,IAAI,OAAO;gCACT,MAAM,IAAI,MAAM,AAAC,2BAAwC,OAAd,OAAO,EAAE,EAAC,MAAkB,OAAd,MAAM,OAAO;4BACxE;4BAEA,OAAO;wBACT;;oBAEA,MAAM,iBAAiB,MAAM,QAAQ,GAAG,CAAC;oBACzC,QAAQ,IAAI,IAAI;gBAClB;gBAEA,OAAO;YACT;;QACA,QAAQ;gDAAE,OAAO;gBACf,oBAAoB;gBACpB,MAAM,WAAW;oBAAC;iBAAU;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE;gBAAS;gBAE3C,MAAM,kBAAkB,YAAY,cAAc,CAAC;oBAAE;gBAAS;gBAE9D,YAAY,cAAc,CAAC;oBAAE;gBAAS;wDAAG,CAAC;wBACxC,IAAI,CAAC,OAAO,CAAC,MAAM,OAAO,CAAC,MAAM,OAAO;wBAExC,OAAO,IAAI,GAAG;gEAAC,CAAC;gCACd,MAAM,SAAS,QAAQ,IAAI;+EAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;;gCACnD,OAAO,SAAS;oCAAE,GAAG,MAAM;oCAAE,GAAG,MAAM;gCAAC,IAAI;4BAC7C;;oBACF;;gBAEA,OAAO;oBAAE;gBAAgB;YAC3B;;QACA,OAAO;gDAAE,CAAC,KAAK,UAAU;gBACvB,yBAAyB;gBACzB,IAAI,oBAAA,8BAAA,QAAS,eAAe,EAAE;oBAC5B,QAAQ,eAAe,CAAC,OAAO;4DAAC;gCAAC,CAAC,UAAU,KAAK;4BAC/C,YAAY,YAAY,CAAC,UAAU;wBACrC;;gBACF;YACF;;QACA,SAAS;gDAAE,CAAC;gBACV,6BAA6B;gBAC7B,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;oBAC3B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAW,IAAI,CAAC,EAAE,CAAC,UAAU;yBAAC;oBAAC;gBAC5E;YACF;;IACF;AACF;IAvHgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/project-validation.ts"], "sourcesContent": ["/**\n * Proje adı validation ve güvenlik utilities\n * Güvenlik odaklı input validation ve sanitization\n */\n\n// Proje adı limitleri ve kuralları\nexport const PROJECT_NAME_RULES = {\n  minLength: 3,\n  maxLength: 50,\n  allowedCharsRegex: /^[a-zA-Z0-9\\s\\-_.]+$/,\n  debounceMs: 300,\n  rateLimit: {\n    maxRequests: 10,\n    windowMinutes: 1\n  }\n} as const;\n\n// Validation sonuç tipi\nexport interface ValidationResult {\n  isValid: boolean;\n  error?: string;\n  sanitizedValue?: string;\n}\n\n// Rate limiting için local storage key\nconst RATE_LIMIT_KEY = 'project_name_update_rate_limit';\n\n/**\n * Proje adını sanitize eder\n */\nexport function sanitizeProjectName(name: string): string {\n  if (!name) return '';\n  \n  // Trim ve normalize\n  let sanitized = name.trim();\n  \n  // Çoklu boşlukları tek boşluğa çevir\n  sanitized = sanitized.replace(/\\s+/g, ' ');\n  \n  // Başlangıç ve bitiş boşluklarını kaldır\n  sanitized = sanitized.trim();\n  \n  return sanitized;\n}\n\n/**\n * Proje adını validate eder\n */\nexport function validateProjectName(name: string): ValidationResult {\n  const sanitized = sanitizeProjectName(name);\n  \n  // Boş kontrol\n  if (!sanitized) {\n    return {\n      isValid: false,\n      error: 'Proje adı boş olamaz'\n    };\n  }\n  \n  // Uzunluk kontrol\n  if (sanitized.length < PROJECT_NAME_RULES.minLength) {\n    return {\n      isValid: false,\n      error: `Proje adı en az ${PROJECT_NAME_RULES.minLength} karakter olmalıdır`\n    };\n  }\n  \n  if (sanitized.length > PROJECT_NAME_RULES.maxLength) {\n    return {\n      isValid: false,\n      error: `Proje adı en fazla ${PROJECT_NAME_RULES.maxLength} karakter olabilir`\n    };\n  }\n  \n  // Karakter kontrol\n  if (!PROJECT_NAME_RULES.allowedCharsRegex.test(sanitized)) {\n    return {\n      isValid: false,\n      error: 'Proje adı sadece harf, rakam, boşluk ve -_. karakterlerini içerebilir'\n    };\n  }\n  \n  // Sadece boşluk kontrolü\n  if (sanitized.replace(/\\s/g, '').length === 0) {\n    return {\n      isValid: false,\n      error: 'Proje adı sadece boşluk karakterlerinden oluşamaz'\n    };\n  }\n  \n  return {\n    isValid: true,\n    sanitizedValue: sanitized\n  };\n}\n\n/**\n * XSS koruması için HTML encode\n */\nexport function escapeHtml(text: string): string {\n  const div = document.createElement('div');\n  div.textContent = text;\n  return div.innerHTML;\n}\n\n/**\n * Client-side rate limiting kontrolü\n */\nexport function checkClientRateLimit(): boolean {\n  try {\n    const stored = localStorage.getItem(RATE_LIMIT_KEY);\n    const now = Date.now();\n    \n    if (!stored) {\n      // İlk istek\n      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n        count: 1,\n        windowStart: now\n      }));\n      return true;\n    }\n    \n    const data = JSON.parse(stored);\n    const windowDuration = PROJECT_NAME_RULES.rateLimit.windowMinutes * 60 * 1000; // ms\n    \n    // Pencere süresi dolmuş mu?\n    if (now - data.windowStart > windowDuration) {\n      // Yeni pencere başlat\n      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n        count: 1,\n        windowStart: now\n      }));\n      return true;\n    }\n    \n    // Limit kontrolü\n    if (data.count >= PROJECT_NAME_RULES.rateLimit.maxRequests) {\n      return false;\n    }\n    \n    // Sayacı artır\n    localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n      count: data.count + 1,\n      windowStart: data.windowStart\n    }));\n    \n    return true;\n  } catch (error) {\n    console.warn('Rate limit check failed:', error);\n    return true; // Hata durumunda izin ver\n  }\n}\n\n/**\n * Rate limit reset\n */\nexport function resetClientRateLimit(): void {\n  try {\n    localStorage.removeItem(RATE_LIMIT_KEY);\n  } catch (error) {\n    console.warn('Rate limit reset failed:', error);\n  }\n}\n\n/**\n * Debounced validation hook için utility\n */\nexport function createDebouncedValidator(\n  validator: (value: string) => ValidationResult,\n  delay: number = PROJECT_NAME_RULES.debounceMs\n) {\n  let timeoutId: NodeJS.Timeout;\n  \n  return (value: string, callback: (result: ValidationResult) => void) => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(() => {\n      const result = validator(value);\n      callback(result);\n    }, delay);\n  };\n}\n\n/**\n * Güvenli proje adı karşılaştırması\n */\nexport function isSameProjectName(name1: string, name2: string): boolean {\n  const sanitized1 = sanitizeProjectName(name1).toLowerCase();\n  const sanitized2 = sanitizeProjectName(name2).toLowerCase();\n  return sanitized1 === sanitized2;\n}\n\n/**\n * Duplicate name kontrolü için async validation\n */\nexport async function validateProjectNameUnique(\n  name: string,\n  currentProjectId: string,\n  projects: Array<{ id: string; name: string }>\n): Promise<ValidationResult> {\n  const sanitized = sanitizeProjectName(name);\n\n  // Önce temel validation\n  const basicValidation = validateProjectName(name);\n  if (!basicValidation.isValid) {\n    return basicValidation;\n  }\n\n  // Duplicate kontrolü (case-insensitive)\n  const isDuplicate = projects.some(project =>\n    project.id !== currentProjectId &&\n    isSameProjectName(project.name, sanitized)\n  );\n\n  if (isDuplicate) {\n    return {\n      isValid: false,\n      error: 'Bu isimde bir proje zaten mevcut'\n    };\n  }\n\n  return {\n    isValid: true,\n    sanitizedValue: sanitized\n  };\n}\n\n/**\n * Error mesajlarını kullanıcı dostu hale getir\n */\nexport function formatValidationError(error: string): string {\n  // Teknik hataları kullanıcı dostu mesajlara çevir\n  const errorMap: Record<string, string> = {\n    'unique_violation': 'Bu isimde bir proje zaten mevcut',\n    'check_violation': 'Proje adı geçersiz karakterler içeriyor',\n    'not_null_violation': 'Proje adı boş olamaz',\n    'foreign_key_violation': 'Geçersiz proje referansı',\n    'RateLimitExceeded': 'Çok fazla güncelleme isteği. Lütfen bekleyin.',\n    'InvalidInput': 'Geçersiz proje adı',\n    'DuplicateName': 'Bu isimde bir proje zaten mevcut',\n    'NotFound': 'Proje bulunamadı',\n    'Unauthorized': 'Bu işlem için yetkiniz yok'\n  };\n\n  return errorMap[error] || error;\n}\n\n/**\n * Advanced debounced validator with duplicate check\n */\nexport function createAdvancedDebouncedValidator(\n  projects: Array<{ id: string; name: string }>,\n  currentProjectId: string,\n  delay: number = PROJECT_NAME_RULES.debounceMs\n) {\n  let timeoutId: NodeJS.Timeout;\n\n  return (value: string, callback: (result: ValidationResult) => void) => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(async () => {\n      const result = await validateProjectNameUnique(value, currentProjectId, projects);\n      callback(result);\n    }, delay);\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,mCAAmC;;;;;;;;;;;;;;AAC5B,MAAM,qBAAqB;IAChC,WAAW;IACX,WAAW;IACX,mBAAmB;IACnB,YAAY;IACZ,WAAW;QACT,aAAa;QACb,eAAe;IACjB;AACF;AASA,uCAAuC;AACvC,MAAM,iBAAiB;AAKhB,SAAS,oBAAoB,IAAY;IAC9C,IAAI,CAAC,MAAM,OAAO;IAElB,oBAAoB;IACpB,IAAI,YAAY,KAAK,IAAI;IAEzB,qCAAqC;IACrC,YAAY,UAAU,OAAO,CAAC,QAAQ;IAEtC,yCAAyC;IACzC,YAAY,UAAU,IAAI;IAE1B,OAAO;AACT;AAKO,SAAS,oBAAoB,IAAY;IAC9C,MAAM,YAAY,oBAAoB;IAEtC,cAAc;IACd,IAAI,CAAC,WAAW;QACd,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,kBAAkB;IAClB,IAAI,UAAU,MAAM,GAAG,mBAAmB,SAAS,EAAE;QACnD,OAAO;YACL,SAAS;YACT,OAAO,AAAC,mBAA+C,OAA7B,mBAAmB,SAAS,EAAC;QACzD;IACF;IAEA,IAAI,UAAU,MAAM,GAAG,mBAAmB,SAAS,EAAE;QACnD,OAAO;YACL,SAAS;YACT,OAAO,AAAC,sBAAkD,OAA7B,mBAAmB,SAAS,EAAC;QAC5D;IACF;IAEA,mBAAmB;IACnB,IAAI,CAAC,mBAAmB,iBAAiB,CAAC,IAAI,CAAC,YAAY;QACzD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,IAAI,UAAU,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;QAC7C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,gBAAgB;IAClB;AACF;AAKO,SAAS,WAAW,IAAY;IACrC,MAAM,MAAM,SAAS,aAAa,CAAC;IACnC,IAAI,WAAW,GAAG;IAClB,OAAO,IAAI,SAAS;AACtB;AAKO,SAAS;IACd,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,CAAC,QAAQ;YACX,YAAY;YACZ,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBAClD,OAAO;gBACP,aAAa;YACf;YACA,OAAO;QACT;QAEA,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,MAAM,iBAAiB,mBAAmB,SAAS,CAAC,aAAa,GAAG,KAAK,MAAM,KAAK;QAEpF,4BAA4B;QAC5B,IAAI,MAAM,KAAK,WAAW,GAAG,gBAAgB;YAC3C,sBAAsB;YACtB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBAClD,OAAO;gBACP,aAAa;YACf;YACA,OAAO;QACT;QAEA,iBAAiB;QACjB,IAAI,KAAK,KAAK,IAAI,mBAAmB,SAAS,CAAC,WAAW,EAAE;YAC1D,OAAO;QACT;QAEA,eAAe;QACf,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;YAClD,OAAO,KAAK,KAAK,GAAG;YACpB,aAAa,KAAK,WAAW;QAC/B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;QACzC,OAAO,MAAM,0BAA0B;IACzC;AACF;AAKO,SAAS;IACd,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;IAC3C;AACF;AAKO,SAAS,yBACd,SAA8C;QAC9C,QAAA,iEAAgB,mBAAmB,UAAU;IAE7C,IAAI;IAEJ,OAAO,CAAC,OAAe;QACrB,aAAa;QACb,YAAY,WAAW;YACrB,MAAM,SAAS,UAAU;YACzB,SAAS;QACX,GAAG;IACL;AACF;AAKO,SAAS,kBAAkB,KAAa,EAAE,KAAa;IAC5D,MAAM,aAAa,oBAAoB,OAAO,WAAW;IACzD,MAAM,aAAa,oBAAoB,OAAO,WAAW;IACzD,OAAO,eAAe;AACxB;AAKO,eAAe,0BACpB,IAAY,EACZ,gBAAwB,EACxB,QAA6C;IAE7C,MAAM,YAAY,oBAAoB;IAEtC,wBAAwB;IACxB,MAAM,kBAAkB,oBAAoB;IAC5C,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;IACT;IAEA,wCAAwC;IACxC,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,UAChC,QAAQ,EAAE,KAAK,oBACf,kBAAkB,QAAQ,IAAI,EAAE;IAGlC,IAAI,aAAa;QACf,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,gBAAgB;IAClB;AACF;AAKO,SAAS,sBAAsB,KAAa;IACjD,kDAAkD;IAClD,MAAM,WAAmC;QACvC,oBAAoB;QACpB,mBAAmB;QACnB,sBAAsB;QACtB,yBAAyB;QACzB,qBAAqB;QACrB,gBAAgB;QAChB,iBAAiB;QACjB,YAAY;QACZ,gBAAgB;IAClB;IAEA,OAAO,QAAQ,CAAC,MAAM,IAAI;AAC5B;AAKO,SAAS,iCACd,QAA6C,EAC7C,gBAAwB;QACxB,QAAA,iEAAgB,mBAAmB,UAAU;IAE7C,IAAI;IAEJ,OAAO,CAAC,OAAe;QACrB,aAAa;QACb,YAAY,WAAW;YACrB,MAAM,SAAS,MAAM,0BAA0B,OAAO,kBAAkB;YACxE,SAAS;QACX,GAAG;IACL;AACF", "debugId": null}}, {"offset": {"line": 1878, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/rate-limiter.ts"], "sourcesContent": ["'use client'\n\n/**\n * Advanced Rate Limiting System for PromptFlow\n * Provides both client-side and server-side rate limiting\n */\n\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\n// Rate limiting configurations for different actions\nexport const RATE_LIMITS = {\n  // Authentication actions\n  login: { maxRequests: 5, windowMinutes: 15 },\n  signup: { maxRequests: 3, windowMinutes: 60 },\n  password_reset: { maxRequests: 3, windowMinutes: 60 },\n  \n  // Project actions\n  project_create: { maxRequests: 10, windowMinutes: 60 },\n  project_update: { maxRequests: 20, windowMinutes: 10 },\n  project_delete: { maxRequests: 5, windowMinutes: 60 },\n  \n  // Prompt actions\n  prompt_create: { maxRequests: 50, windowMinutes: 10 },\n  prompt_update: { maxRequests: 100, windowMinutes: 10 },\n  prompt_delete: { maxRequests: 20, windowMinutes: 10 },\n  \n  // Context actions\n  context_create: { maxRequests: 20, windowMinutes: 10 },\n  context_batch_add: { maxRequests: 5, windowMinutes: 10 },\n  \n  // Plan actions\n  plan_change: { maxRequests: 3, windowMinutes: 60 },\n  \n  // General API\n  api_general: { maxRequests: 200, windowMinutes: 10 },\n} as const\n\nexport type RateLimitAction = keyof typeof RATE_LIMITS\n\ninterface RateLimitData {\n  count: number\n  windowStart: number\n  lastRequest: number\n}\n\n/**\n * Client-side rate limiting with localStorage\n */\nexport class ClientRateLimiter {\n  private getStorageKey(action: RateLimitAction, userId?: string): string {\n    const userSuffix = userId ? `_${userId}` : ''\n    return `rate_limit_${action}${userSuffix}`\n  }\n\n  /**\n   * Check if action is allowed based on rate limits\n   */\n  async checkLimit(action: RateLimitAction, userId?: string): Promise<{\n    allowed: boolean\n    remainingRequests: number\n    resetTime: number\n    retryAfter?: number\n  }> {\n    try {\n      const config = RATE_LIMITS[action]\n      const storageKey = this.getStorageKey(action, userId)\n      const now = Date.now()\n      const windowMs = config.windowMinutes * 60 * 1000\n\n      // Get current data\n      const stored = localStorage.getItem(storageKey)\n      let data: RateLimitData\n\n      if (!stored) {\n        // First request\n        data = {\n          count: 1,\n          windowStart: now,\n          lastRequest: now\n        }\n        localStorage.setItem(storageKey, JSON.stringify(data))\n        \n        return {\n          allowed: true,\n          remainingRequests: config.maxRequests - 1,\n          resetTime: now + windowMs\n        }\n      }\n\n      data = JSON.parse(stored)\n\n      // Check if window has expired\n      if (now - data.windowStart > windowMs) {\n        // Reset window\n        data = {\n          count: 1,\n          windowStart: now,\n          lastRequest: now\n        }\n        localStorage.setItem(storageKey, JSON.stringify(data))\n        \n        return {\n          allowed: true,\n          remainingRequests: config.maxRequests - 1,\n          resetTime: now + windowMs\n        }\n      }\n\n      // Check if limit exceeded\n      if (data.count >= config.maxRequests) {\n        const resetTime = data.windowStart + windowMs\n        const retryAfter = Math.ceil((resetTime - now) / 1000)\n        \n        return {\n          allowed: false,\n          remainingRequests: 0,\n          resetTime,\n          retryAfter\n        }\n      }\n\n      // Increment counter\n      data.count++\n      data.lastRequest = now\n      localStorage.setItem(storageKey, JSON.stringify(data))\n\n      return {\n        allowed: true,\n        remainingRequests: config.maxRequests - data.count,\n        resetTime: data.windowStart + windowMs\n      }\n    } catch (error) {\n      console.warn(`Rate limit check failed for ${action}:`, error)\n      // On error, allow the request but log it\n      return {\n        allowed: true,\n        remainingRequests: 0,\n        resetTime: Date.now() + 60000 // 1 minute fallback\n      }\n    }\n  }\n\n  /**\n   * Reset rate limit for specific action\n   */\n  resetLimit(action: RateLimitAction, userId?: string): void {\n    try {\n      const storageKey = this.getStorageKey(action, userId)\n      localStorage.removeItem(storageKey)\n    } catch (error) {\n      console.warn(`Rate limit reset failed for ${action}:`, error)\n    }\n  }\n\n  /**\n   * Get current rate limit status\n   */\n  getStatus(action: RateLimitAction, userId?: string): {\n    count: number\n    remaining: number\n    resetTime: number\n  } | null {\n    try {\n      const config = RATE_LIMITS[action]\n      const storageKey = this.getStorageKey(action, userId)\n      const stored = localStorage.getItem(storageKey)\n      \n      if (!stored) {\n        return {\n          count: 0,\n          remaining: config.maxRequests,\n          resetTime: Date.now() + config.windowMinutes * 60 * 1000\n        }\n      }\n\n      const data: RateLimitData = JSON.parse(stored)\n      const windowMs = config.windowMinutes * 60 * 1000\n      const now = Date.now()\n\n      // Check if window expired\n      if (now - data.windowStart > windowMs) {\n        return {\n          count: 0,\n          remaining: config.maxRequests,\n          resetTime: now + windowMs\n        }\n      }\n\n      return {\n        count: data.count,\n        remaining: Math.max(0, config.maxRequests - data.count),\n        resetTime: data.windowStart + windowMs\n      }\n    } catch (error) {\n      console.warn(`Rate limit status check failed for ${action}:`, error)\n      return null\n    }\n  }\n}\n\n/**\n * Server-side rate limiting with Supabase\n */\nexport class ServerRateLimiter {\n  /**\n   * Check server-side rate limit using Supabase function\n   */\n  async checkLimit(\n    action: RateLimitAction,\n    userId?: string\n  ): Promise<{\n    allowed: boolean\n    remainingRequests: number\n    resetTime: number\n    retryAfter?: number\n  }> {\n    try {\n      const config = RATE_LIMITS[action]\n      \n      // Get current user if not provided\n      let targetUserId = userId\n      if (!targetUserId) {\n        const { data: { user } } = await supabase.auth.getUser()\n        if (!user) {\n          throw new Error('User not authenticated')\n        }\n        targetUserId = user.id\n      }\n\n      // Call Supabase rate limiting function\n      const { data, error } = await supabase.rpc('check_rate_limit', {\n        p_user_id: targetUserId,\n        p_action_type: action,\n        p_max_requests: config.maxRequests,\n        p_window_minutes: config.windowMinutes\n      })\n\n      if (error) {\n        console.error(`Server rate limit check failed for ${action}:`, error)\n        // On error, allow but log\n        return {\n          allowed: true,\n          remainingRequests: 0,\n          resetTime: Date.now() + config.windowMinutes * 60 * 1000\n        }\n      }\n\n      const allowed = data === true\n      const windowMs = config.windowMinutes * 60 * 1000\n      const resetTime = Date.now() + windowMs\n\n      if (!allowed) {\n        return {\n          allowed: false,\n          remainingRequests: 0,\n          resetTime,\n          retryAfter: Math.ceil(windowMs / 1000)\n        }\n      }\n\n      return {\n        allowed: true,\n        remainingRequests: config.maxRequests - 1, // Approximate\n        resetTime\n      }\n    } catch (error) {\n      console.error(`Server rate limit error for ${action}:`, error)\n      // On error, allow but log\n      return {\n        allowed: true,\n        remainingRequests: 0,\n        resetTime: Date.now() + 60000\n      }\n    }\n  }\n}\n\n// Global instances\nexport const clientRateLimiter = new ClientRateLimiter()\nexport const serverRateLimiter = new ServerRateLimiter()\n\n/**\n * Combined rate limiting check (client + server)\n */\nexport async function checkRateLimit(\n  action: RateLimitAction,\n  options: {\n    clientOnly?: boolean\n    serverOnly?: boolean\n    userId?: string\n  } = {}\n): Promise<{\n  allowed: boolean\n  remainingRequests: number\n  resetTime: number\n  retryAfter?: number\n  source: 'client' | 'server' | 'both'\n}> {\n  const { clientOnly = false, serverOnly = false, userId } = options\n\n  try {\n    // Client-side check\n    if (!serverOnly) {\n      const clientResult = await clientRateLimiter.checkLimit(action, userId)\n      if (!clientResult.allowed) {\n        return {\n          ...clientResult,\n          source: 'client'\n        }\n      }\n      \n      if (clientOnly) {\n        return {\n          ...clientResult,\n          source: 'client'\n        }\n      }\n    }\n\n    // Server-side check\n    if (!clientOnly) {\n      const serverResult = await serverRateLimiter.checkLimit(action, userId)\n      return {\n        ...serverResult,\n        source: serverOnly ? 'server' : 'both'\n      }\n    }\n\n    // Fallback (shouldn't reach here)\n    return {\n      allowed: true,\n      remainingRequests: 0,\n      resetTime: Date.now() + 60000,\n      source: 'client'\n    }\n  } catch (error) {\n    console.error(`Rate limit check failed for ${action}:`, error)\n    return {\n      allowed: true,\n      remainingRequests: 0,\n      resetTime: Date.now() + 60000,\n      source: 'client'\n    }\n  }\n}\n\n/**\n * Rate limit error class\n */\nexport class RateLimitError extends Error {\n  constructor(\n    public action: RateLimitAction,\n    public retryAfter: number,\n    public resetTime: number\n  ) {\n    super(`Rate limit exceeded for ${action}. Try again in ${retryAfter} seconds.`)\n    this.name = 'RateLimitError'\n  }\n}\n\n/**\n * Rate limiting hook for React components\n */\nexport function useRateLimit(action: RateLimitAction) {\n  return {\n    checkLimit: (options?: { clientOnly?: boolean; serverOnly?: boolean }) =>\n      checkRateLimit(action, options),\n    resetLimit: () => clientRateLimiter.resetLimit(action),\n    getStatus: () => clientRateLimiter.getStatus(action)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;;;CAGC,GAED;AAPA;;;AAUO,MAAM,cAAc;IACzB,yBAAyB;IACzB,OAAO;QAAE,aAAa;QAAG,eAAe;IAAG;IAC3C,QAAQ;QAAE,aAAa;QAAG,eAAe;IAAG;IAC5C,gBAAgB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEpD,kBAAkB;IAClB,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,gBAAgB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEpD,iBAAiB;IACjB,eAAe;QAAE,aAAa;QAAI,eAAe;IAAG;IACpD,eAAe;QAAE,aAAa;QAAK,eAAe;IAAG;IACrD,eAAe;QAAE,aAAa;QAAI,eAAe;IAAG;IAEpD,kBAAkB;IAClB,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,mBAAmB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEvD,eAAe;IACf,aAAa;QAAE,aAAa;QAAG,eAAe;IAAG;IAEjD,cAAc;IACd,aAAa;QAAE,aAAa;QAAK,eAAe;IAAG;AACrD;AAaO,MAAM;IACH,cAAc,MAAuB,EAAE,MAAe,EAAU;QACtE,MAAM,aAAa,SAAS,AAAC,IAAU,OAAP,UAAW;QAC3C,OAAO,AAAC,cAAsB,OAAT,QAAoB,OAAX;IAChC;IAEA;;GAEC,GACD,MAAM,WAAW,MAAuB,EAAE,MAAe,EAKtD;QACD,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAClC,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAE7C,mBAAmB;YACnB,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI;YAEJ,IAAI,CAAC,QAAQ;gBACX,gBAAgB;gBAChB,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,OAAO;oBACL,SAAS;oBACT,mBAAmB,OAAO,WAAW,GAAG;oBACxC,WAAW,MAAM;gBACnB;YACF;YAEA,OAAO,KAAK,KAAK,CAAC;YAElB,8BAA8B;YAC9B,IAAI,MAAM,KAAK,WAAW,GAAG,UAAU;gBACrC,eAAe;gBACf,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,OAAO;oBACL,SAAS;oBACT,mBAAmB,OAAO,WAAW,GAAG;oBACxC,WAAW,MAAM;gBACnB;YACF;YAEA,0BAA0B;YAC1B,IAAI,KAAK,KAAK,IAAI,OAAO,WAAW,EAAE;gBACpC,MAAM,YAAY,KAAK,WAAW,GAAG;gBACrC,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,YAAY,GAAG,IAAI;gBAEjD,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB;oBACA;gBACF;YACF;YAEA,oBAAoB;YACpB,KAAK,KAAK;YACV,KAAK,WAAW,GAAG;YACnB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAEhD,OAAO;gBACL,SAAS;gBACT,mBAAmB,OAAO,WAAW,GAAG,KAAK,KAAK;gBAClD,WAAW,KAAK,WAAW,GAAG;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,+BAAqC,OAAP,QAAO,MAAI;YACvD,yCAAyC;YACzC,OAAO;gBACL,SAAS;gBACT,mBAAmB;gBACnB,WAAW,KAAK,GAAG,KAAK,MAAM,oBAAoB;YACpD;QACF;IACF;IAEA;;GAEC,GACD,WAAW,MAAuB,EAAE,MAAe,EAAQ;QACzD,IAAI;YACF,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,+BAAqC,OAAP,QAAO,MAAI;QACzD;IACF;IAEA;;GAEC,GACD,UAAU,MAAuB,EAAE,MAAe,EAIzC;QACP,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAClC,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM,SAAS,aAAa,OAAO,CAAC;YAEpC,IAAI,CAAC,QAAQ;gBACX,OAAO;oBACL,OAAO;oBACP,WAAW,OAAO,WAAW;oBAC7B,WAAW,KAAK,GAAG,KAAK,OAAO,aAAa,GAAG,KAAK;gBACtD;YACF;YAEA,MAAM,OAAsB,KAAK,KAAK,CAAC;YACvC,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAC7C,MAAM,MAAM,KAAK,GAAG;YAEpB,0BAA0B;YAC1B,IAAI,MAAM,KAAK,WAAW,GAAG,UAAU;gBACrC,OAAO;oBACL,OAAO;oBACP,WAAW,OAAO,WAAW;oBAC7B,WAAW,MAAM;gBACnB;YACF;YAEA,OAAO;gBACL,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO,WAAW,GAAG,KAAK,KAAK;gBACtD,WAAW,KAAK,WAAW,GAAG;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,AAAC,sCAA4C,OAAP,QAAO,MAAI;YAC9D,OAAO;QACT;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,MAAM,WACJ,MAAuB,EACvB,MAAe,EAMd;QACD,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAElC,mCAAmC;YACnC,IAAI,eAAe;YACnB,IAAI,CAAC,cAAc;gBACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBACA,eAAe,KAAK,EAAE;YACxB;YAEA,uCAAuC;YACvC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,oBAAoB;gBAC7D,WAAW;gBACX,eAAe;gBACf,gBAAgB,OAAO,WAAW;gBAClC,kBAAkB,OAAO,aAAa;YACxC;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,AAAC,sCAA4C,OAAP,QAAO,MAAI;gBAC/D,0BAA0B;gBAC1B,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,WAAW,KAAK,GAAG,KAAK,OAAO,aAAa,GAAG,KAAK;gBACtD;YACF;YAEA,MAAM,UAAU,SAAS;YACzB,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAC7C,MAAM,YAAY,KAAK,GAAG,KAAK;YAE/B,IAAI,CAAC,SAAS;gBACZ,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB;oBACA,YAAY,KAAK,IAAI,CAAC,WAAW;gBACnC;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,mBAAmB,OAAO,WAAW,GAAG;gBACxC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,+BAAqC,OAAP,QAAO,MAAI;YACxD,0BAA0B;YAC1B,OAAO;gBACL,SAAS;gBACT,mBAAmB;gBACnB,WAAW,KAAK,GAAG,KAAK;YAC1B;QACF;IACF;AACF;AAGO,MAAM,oBAAoB,IAAI;AAC9B,MAAM,oBAAoB,IAAI;AAK9B,eAAe,eACpB,MAAuB;QACvB,UAAA,iEAII,CAAC;IAQL,MAAM,EAAE,aAAa,KAAK,EAAE,aAAa,KAAK,EAAE,MAAM,EAAE,GAAG;IAE3D,IAAI;QACF,oBAAoB;QACpB,IAAI,CAAC,YAAY;YACf,MAAM,eAAe,MAAM,kBAAkB,UAAU,CAAC,QAAQ;YAChE,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;gBACV;YACF;YAEA,IAAI,YAAY;gBACd,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;gBACV;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,CAAC,YAAY;YACf,MAAM,eAAe,MAAM,kBAAkB,UAAU,CAAC,QAAQ;YAChE,OAAO;gBACL,GAAG,YAAY;gBACf,QAAQ,aAAa,WAAW;YAClC;QACF;QAEA,kCAAkC;QAClC,OAAO;YACL,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,AAAC,+BAAqC,OAAP,QAAO,MAAI;QACxD,OAAO;YACL,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;QACV;IACF;AACF;AAKO,MAAM,uBAAuB;IAClC,YACE,AAAO,MAAuB,EAC9B,AAAO,UAAkB,EACzB,AAAO,SAAiB,CACxB;QACA,KAAK,CAAC,AAAC,2BAAkD,OAAxB,QAAO,mBAA4B,OAAX,YAAW,imBAJ7D,SAAA,aACA,aAAA,iBACA,YAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAKO,SAAS,aAAa,MAAuB;IAClD,OAAO;QACL,YAAY,CAAC,UACX,eAAe,QAAQ;QACzB,YAAY,IAAM,kBAAkB,UAAU,CAAC;QAC/C,WAAW,IAAM,kBAAkB,SAAS,CAAC;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 2205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-projects.ts"], "sourcesContent": ["'use client'\r\n\r\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\r\nimport { Database } from '@/lib/supabase'\r\nimport { canCreateProject, updateUsageStats, PLAN_LIMIT_MESSAGES } from '@/lib/plan-limits'\r\nimport {\r\n  validateProjectName,\r\n  formatValidationError\r\n} from '@/lib/project-validation'\r\nimport { checkRateLimit, RateLimitError } from '@/lib/rate-limiter'\r\nimport { OptimizedQueries, QueryPerformanceMonitor } from '@/lib/database-optimization'\r\nimport { CACHE_CONFIGS } from '@/lib/cache-strategies'\r\n\r\ntype Project = Database['public']['Tables']['projects']['Row']\r\ntype ProjectInsert = Database['public']['Tables']['projects']['Insert']\r\ntype ProjectUpdate = Database['public']['Tables']['projects']['Update']\r\n\r\n// Projeleri getir\r\nexport function useProjects() {\r\n  return useQuery({\r\n    queryKey: ['projects'],\r\n    queryFn: async (): Promise<Project[]> => {\r\n      console.log(`📁 [USE_PROJECTS] Fetching projects...`)\r\n\r\n      try {\r\n        // Check if we have a session first\r\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession()\r\n        console.log(`📁 [USE_PROJECTS] Session check:`, {\r\n          hasSession: !!session,\r\n          sessionError: sessionError?.message,\r\n          userId: session?.user?.id\r\n        })\r\n\r\n        const { data, error } = await supabase\r\n          .from('projects')\r\n          .select('*')\r\n          .order('created_at', { ascending: false })\r\n\r\n        if (error) {\r\n          console.error(`❌ [USE_PROJECTS] Error fetching projects:`, error)\r\n          throw new Error(error.message)\r\n        }\r\n\r\n        console.log(`✅ [USE_PROJECTS] Projects fetched:`, data?.length || 0, 'projects')\r\n        return data || []\r\n      } catch (err) {\r\n        console.error(`💥 [USE_PROJECTS] Exception:`, err)\r\n        throw err\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Tek proje getir\r\nexport function useProject(projectId: string | null) {\r\n  return useQuery({\r\n    queryKey: ['project', projectId],\r\n    queryFn: async (): Promise<Project | null> => {\r\n      if (!projectId) return null\r\n\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .select('*')\r\n        .eq('id', projectId)\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    enabled: !!projectId,\r\n  })\r\n}\r\n\r\n// Proje oluştur\r\nexport function useCreateProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (project: Omit<ProjectInsert, 'user_id'>): Promise<Project> => {\r\n      // Plan limiti kontrol et\r\n      const limitCheck = await canCreateProject()\r\n      if (!limitCheck.allowed) {\r\n        throw new Error(limitCheck.reason || PLAN_LIMIT_MESSAGES.PROJECT_LIMIT_REACHED)\r\n      }\r\n\r\n      const { data: { user }, error: userError } = await supabase.auth.getUser()\r\n\r\n      if (userError || !user) {\r\n        throw new Error('Kullanıcı oturumu bulunamadı')\r\n      }\r\n\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .insert({\r\n          ...project,\r\n          user_id: user.id,\r\n        })\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onSuccess: async () => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Proje güncelle\r\nexport function useUpdateProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ id, ...updates }: ProjectUpdate & { id: string }): Promise<Project> => {\r\n      console.log('🔄 [UPDATE_PROJECT] Starting update:', { id, updates })\r\n\r\n      // Önce mevcut kullanıcıyı kontrol et\r\n      const { data: user } = await supabase.auth.getUser()\r\n      if (!user.user) {\r\n        throw new Error('Kullanıcı girişi gerekli')\r\n      }\r\n\r\n      // Güncelleme işlemini yap - RLS politikaları otomatik olarak kontrol edecek\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .update({\r\n          ...updates,\r\n          updated_at: new Date().toISOString()\r\n        })\r\n        .eq('id', id)\r\n        .eq('user_id', user.user.id) // Ekstra güvenlik için user_id kontrolü\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        console.error('❌ [UPDATE_PROJECT] Database error:', error)\r\n        throw new Error(`Proje güncellenirken hata oluştu: ${error.message}`)\r\n      }\r\n\r\n      if (!data) {\r\n        throw new Error('Proje bulunamadı veya güncelleme yetkisi yok')\r\n      }\r\n\r\n      console.log('✅ [UPDATE_PROJECT] Success:', data)\r\n      return data\r\n    },\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      queryClient.invalidateQueries({ queryKey: ['project', data.id] })\r\n    },\r\n    onError: (error) => {\r\n      console.error('❌ [UPDATE_PROJECT] Mutation error:', error)\r\n    }\r\n  })\r\n}\r\n\r\n// Güvenli proje adı güncelleme\r\nexport function useUpdateProjectNameSecure() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      projectId,\r\n      newName\r\n    }: {\r\n      projectId: string;\r\n      newName: string\r\n    }): Promise<Project> => {\r\n      console.log(`🔒 [UPDATE_PROJECT_NAME] Starting secure update:`, { projectId, newName })\r\n\r\n      // Advanced rate limiting check\r\n      const rateLimitResult = await checkRateLimit('project_update')\r\n      if (!rateLimitResult.allowed) {\r\n        throw new RateLimitError(\r\n          'project_update',\r\n          rateLimitResult.retryAfter || 60,\r\n          rateLimitResult.resetTime\r\n        )\r\n      }\r\n\r\n      // Client-side validation\r\n      const validation = validateProjectName(newName)\r\n      if (!validation.isValid) {\r\n        throw new Error(validation.error || 'Geçersiz proje adı')\r\n      }\r\n\r\n      const sanitizedName = validation.sanitizedValue!\r\n\r\n      try {\r\n        // Güvenli database function'ını çağır\r\n        const { data, error } = await supabase.rpc('update_project_name_secure', {\r\n          p_project_id: projectId,\r\n          p_new_name: sanitizedName\r\n        })\r\n\r\n        if (error) {\r\n          console.error(`❌ [UPDATE_PROJECT_NAME] Database error:`, error)\r\n          throw new Error(formatValidationError(error.message))\r\n        }\r\n\r\n        // Function response kontrolü\r\n        if (!data?.success) {\r\n          console.error(`❌ [UPDATE_PROJECT_NAME] Function error:`, data)\r\n          throw new Error(data?.message || 'Proje adı güncellenemedi')\r\n        }\r\n\r\n        console.log(`✅ [UPDATE_PROJECT_NAME] Success:`, data.data)\r\n        return data.data as Project\r\n\r\n      } catch (err) {\r\n        console.error(`💥 [UPDATE_PROJECT_NAME] Exception:`, err)\r\n\r\n        // Error mesajını kullanıcı dostu hale getir\r\n        let errorMessage = err instanceof Error ? err.message : 'Bilinmeyen hata'\r\n\r\n        if (errorMessage.includes('unique_violation') || errorMessage.includes('DuplicateName')) {\r\n          errorMessage = 'Bu isimde bir proje zaten mevcut'\r\n        } else if (errorMessage.includes('check_violation') || errorMessage.includes('InvalidInput')) {\r\n          errorMessage = 'Proje adı geçersiz karakterler içeriyor'\r\n        } else if (errorMessage.includes('RateLimitExceeded')) {\r\n          errorMessage = 'Çok fazla güncelleme isteği. Lütfen bekleyin.'\r\n        }\r\n\r\n        throw new Error(errorMessage)\r\n      }\r\n    },\r\n    onMutate: async ({ projectId, newName }) => {\r\n      console.log(`🚀 [UPDATE_PROJECT_NAME] Starting optimistic update:`, { projectId, newName })\r\n\r\n      // Cancel outgoing refetches\r\n      await queryClient.cancelQueries({ queryKey: ['projects'] })\r\n      await queryClient.cancelQueries({ queryKey: ['project', projectId] })\r\n\r\n      // Snapshot previous values\r\n      const previousProjects = queryClient.getQueryData<Project[]>(['projects'])\r\n      const previousProject = queryClient.getQueryData<Project>(['project', projectId])\r\n\r\n      // Optimistically update projects list\r\n      if (previousProjects) {\r\n        queryClient.setQueryData<Project[]>(['projects'], (old) => {\r\n          if (!old) return old\r\n          return old.map(project =>\r\n            project.id === projectId\r\n              ? { ...project, name: newName.trim(), updated_at: new Date().toISOString() }\r\n              : project\r\n          )\r\n        })\r\n      }\r\n\r\n      // Optimistically update single project\r\n      if (previousProject) {\r\n        queryClient.setQueryData<Project>(['project', projectId], {\r\n          ...previousProject,\r\n          name: newName.trim(),\r\n          updated_at: new Date().toISOString()\r\n        })\r\n      }\r\n\r\n      return { previousProjects, previousProject }\r\n    },\r\n    onSuccess: (data) => {\r\n      console.log(`✅ [UPDATE_PROJECT_NAME] Success, updating cache with server data:`, data.id)\r\n\r\n      // Update with actual server data\r\n      queryClient.setQueryData<Project[]>(['projects'], (old) => {\r\n        if (!old) return old\r\n        return old.map(project =>\r\n          project.id === data.id ? { ...project, ...data } : project\r\n        )\r\n      })\r\n\r\n      queryClient.setQueryData<Project>(['project', data.id], data)\r\n    },\r\n    onError: (error, variables, context) => {\r\n      console.error(`💥 [UPDATE_PROJECT_NAME] Error, rolling back:`, error)\r\n\r\n      // Rollback optimistic updates\r\n      if (context?.previousProjects) {\r\n        queryClient.setQueryData(['projects'], context.previousProjects)\r\n      }\r\n      if (context?.previousProject) {\r\n        queryClient.setQueryData(['project', variables.projectId], context.previousProject)\r\n      }\r\n    },\r\n    onSettled: () => {\r\n      // Always refetch to ensure consistency\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n    }\r\n  })\r\n}\r\n\r\n// Proje sil\r\nexport function useDeleteProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (projectId: string): Promise<void> => {\r\n      const { error } = await supabase\r\n        .from('projects')\r\n        .delete()\r\n        .eq('id', projectId)\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n    },\r\n    onSuccess: async () => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n} "], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AAIA;;AAVA;;;;;;AAmBO,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAW;QACtB,OAAO;oCAAE;gBACP,QAAQ,GAAG,CAAE;gBAEb,IAAI;wBAMQ;oBALV,mCAAmC;oBACnC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;oBACjF,QAAQ,GAAG,CAAE,oCAAmC;wBAC9C,YAAY,CAAC,CAAC;wBACd,YAAY,EAAE,yBAAA,mCAAA,aAAc,OAAO;wBACnC,MAAM,EAAE,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,EAAE;oBAC3B;oBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;wBAAE,WAAW;oBAAM;oBAE1C,IAAI,OAAO;wBACT,QAAQ,KAAK,CAAE,6CAA4C;wBAC3D,MAAM,IAAI,MAAM,MAAM,OAAO;oBAC/B;oBAEA,QAAQ,GAAG,CAAE,sCAAqC,CAAA,iBAAA,2BAAA,KAAM,MAAM,KAAI,GAAG;oBACrE,OAAO,QAAQ,EAAE;gBACnB,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAE,gCAA+B;oBAC9C,MAAM;gBACR;YACF;;IACF;AACF;GAjCgB;;QACP,8KAAA,CAAA,WAAQ;;;AAmCV,SAAS,WAAW,SAAwB;;IACjD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAU;QAChC,OAAO;mCAAE;gBACP,IAAI,CAAC,WAAW,OAAO;gBAEvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,WACT,MAAM;gBAET,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IApBgB;;QACP,8KAAA,CAAA,WAAQ;;;AAsBV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,OAAO;gBACjB,yBAAyB;gBACzB,MAAM,aAAa,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;gBACxC,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,MAAM,IAAI,MAAM,WAAW,MAAM,IAAI,+HAAA,CAAA,sBAAmB,CAAC,qBAAqB;gBAChF;gBAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAExE,IAAI,aAAa,CAAC,MAAM;oBACtB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;oBACN,GAAG,OAAO;oBACV,SAAS,KAAK,EAAE;gBAClB,GACC,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT;;QACA,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,qCAAqC;gBACrC,IAAI;oBACF,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;oBACrB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;oBAC1D,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;gBAC5D,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,2CAA2C;gBAC1D;YACF;;IACF;AACF;IA5CgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA4Cb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE;oBAAO,EAAE,EAAE,EAAE,GAAG,SAAyC;gBACnE,QAAQ,GAAG,CAAC,wCAAwC;oBAAE;oBAAI;gBAAQ;gBAElE,qCAAqC;gBACrC,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAClD,IAAI,CAAC,KAAK,IAAI,EAAE;oBACd,MAAM,IAAI,MAAM;gBAClB;gBAEA,4EAA4E;gBAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;oBACN,GAAG,OAAO;oBACV,YAAY,IAAI,OAAO,WAAW;gBACpC,GACC,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,EAAE,wCAAwC;iBACpE,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,sCAAsC;oBACpD,MAAM,IAAI,MAAM,AAAC,qCAAkD,OAAd,MAAM,OAAO;gBACpE;gBAEA,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBAEA,QAAQ,GAAG,CAAC,+BAA+B;gBAC3C,OAAO;YACT;;QACA,SAAS;4CAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW,KAAK,EAAE;qBAAC;gBAAC;YACjE;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AACF;IA7CgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA6Cb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;sDAAE;oBAAO,EACjB,SAAS,EACT,OAAO,EAIR;gBACC,QAAQ,GAAG,CAAE,oDAAmD;oBAAE;oBAAW;gBAAQ;gBAErF,+BAA+B;gBAC/B,MAAM,kBAAkB,MAAM,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;gBAC7C,IAAI,CAAC,gBAAgB,OAAO,EAAE;oBAC5B,MAAM,IAAI,gIAAA,CAAA,iBAAc,CACtB,kBACA,gBAAgB,UAAU,IAAI,IAC9B,gBAAgB,SAAS;gBAE7B;gBAEA,yBAAyB;gBACzB,MAAM,aAAa,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD,EAAE;gBACvC,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,MAAM,IAAI,MAAM,WAAW,KAAK,IAAI;gBACtC;gBAEA,MAAM,gBAAgB,WAAW,cAAc;gBAE/C,IAAI;oBACF,sCAAsC;oBACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,8BAA8B;wBACvE,cAAc;wBACd,YAAY;oBACd;oBAEA,IAAI,OAAO;wBACT,QAAQ,KAAK,CAAE,2CAA0C;wBACzD,MAAM,IAAI,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,OAAO;oBACrD;oBAEA,6BAA6B;oBAC7B,IAAI,EAAC,iBAAA,2BAAA,KAAM,OAAO,GAAE;wBAClB,QAAQ,KAAK,CAAE,2CAA0C;wBACzD,MAAM,IAAI,MAAM,CAAA,iBAAA,2BAAA,KAAM,OAAO,KAAI;oBACnC;oBAEA,QAAQ,GAAG,CAAE,oCAAmC,KAAK,IAAI;oBACzD,OAAO,KAAK,IAAI;gBAElB,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAE,uCAAsC;oBAErD,4CAA4C;oBAC5C,IAAI,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAExD,IAAI,aAAa,QAAQ,CAAC,uBAAuB,aAAa,QAAQ,CAAC,kBAAkB;wBACvF,eAAe;oBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB,aAAa,QAAQ,CAAC,iBAAiB;wBAC5F,eAAe;oBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB;wBACrD,eAAe;oBACjB;oBAEA,MAAM,IAAI,MAAM;gBAClB;YACF;;QACA,QAAQ;sDAAE;oBAAO,EAAE,SAAS,EAAE,OAAO,EAAE;gBACrC,QAAQ,GAAG,CAAE,wDAAuD;oBAAE;oBAAW;gBAAQ;gBAEzF,4BAA4B;gBAC5B,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACzD,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU;wBAAC;wBAAW;qBAAU;gBAAC;gBAEnE,2BAA2B;gBAC3B,MAAM,mBAAmB,YAAY,YAAY,CAAY;oBAAC;iBAAW;gBACzE,MAAM,kBAAkB,YAAY,YAAY,CAAU;oBAAC;oBAAW;iBAAU;gBAEhF,sCAAsC;gBACtC,IAAI,kBAAkB;oBACpB,YAAY,YAAY,CAAY;wBAAC;qBAAW;kEAAE,CAAC;4BACjD,IAAI,CAAC,KAAK,OAAO;4BACjB,OAAO,IAAI,GAAG;0EAAC,CAAA,UACb,QAAQ,EAAE,KAAK,YACX;wCAAE,GAAG,OAAO;wCAAE,MAAM,QAAQ,IAAI;wCAAI,YAAY,IAAI,OAAO,WAAW;oCAAG,IACzE;;wBAER;;gBACF;gBAEA,uCAAuC;gBACvC,IAAI,iBAAiB;oBACnB,YAAY,YAAY,CAAU;wBAAC;wBAAW;qBAAU,EAAE;wBACxD,GAAG,eAAe;wBAClB,MAAM,QAAQ,IAAI;wBAClB,YAAY,IAAI,OAAO,WAAW;oBACpC;gBACF;gBAEA,OAAO;oBAAE;oBAAkB;gBAAgB;YAC7C;;QACA,SAAS;sDAAE,CAAC;gBACV,QAAQ,GAAG,CAAE,qEAAoE,KAAK,EAAE;gBAExF,iCAAiC;gBACjC,YAAY,YAAY,CAAY;oBAAC;iBAAW;8DAAE,CAAC;wBACjD,IAAI,CAAC,KAAK,OAAO;wBACjB,OAAO,IAAI,GAAG;sEAAC,CAAA,UACb,QAAQ,EAAE,KAAK,KAAK,EAAE,GAAG;oCAAE,GAAG,OAAO;oCAAE,GAAG,IAAI;gCAAC,IAAI;;oBAEvD;;gBAEA,YAAY,YAAY,CAAU;oBAAC;oBAAW,KAAK,EAAE;iBAAC,EAAE;YAC1D;;QACA,OAAO;sDAAE,CAAC,OAAO,WAAW;gBAC1B,QAAQ,KAAK,CAAE,iDAAgD;gBAE/D,8BAA8B;gBAC9B,IAAI,oBAAA,8BAAA,QAAS,gBAAgB,EAAE;oBAC7B,YAAY,YAAY,CAAC;wBAAC;qBAAW,EAAE,QAAQ,gBAAgB;gBACjE;gBACA,IAAI,oBAAA,8BAAA,QAAS,eAAe,EAAE;oBAC5B,YAAY,YAAY,CAAC;wBAAC;wBAAW,UAAU,SAAS;qBAAC,EAAE,QAAQ,eAAe;gBACpF;YACF;;QACA,SAAS;sDAAE;gBACT,uCAAuC;gBACvC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;YACzD;;IACF;AACF;IApIgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAoIb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,OAAO;gBACjB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,oIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO;oBACT,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;YACF;;QACA,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,qCAAqC;gBACrC,IAAI;oBACF,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;oBACrB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;oBAC1D,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAc;oBAAC;gBAC5D,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,2CAA2C;gBAC1D;YACF;;IACF;AACF;IA1BgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 2638, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-context-to-prompt.ts"], "sourcesContent": ["'use client'\n\nimport { useMutation, useQueryClient } from '@tanstack/react-query'\nimport { useCreatePrompt } from './use-prompts'\nimport { useProjects } from './use-projects'\nimport { Context } from '@/components/context-gallery'\nimport { toast } from 'sonner'\n\ninterface ContextToPromptOptions {\n  /** Target project ID. If not provided, uses active project */\n  projectId?: string\n  /** Custom title for the prompt. If not provided, uses context title */\n  customTitle?: string\n  /** Additional tags to add to the prompt */\n  additionalTags?: string[]\n  /** Whether to mark as favorite */\n  markAsFavorite?: boolean\n  /** Custom category for the prompt */\n  customCategory?: string\n}\n\ninterface ContextToPromptResult {\n  success: boolean\n  promptId?: string\n  error?: string\n}\n\n/**\n * Hook for converting Context Gallery contexts to prompts in current project\n * Handles validation, limits checking, and proper data transformation\n */\nexport function useContextToPrompt() {\n  const queryClient = useQueryClient()\n  const createPromptMutation = useCreatePrompt()\n  const { data: projects } = useProjects()\n\n  return useMutation({\n    mutationFn: async ({\n      context,\n      options = {}\n    }: {\n      context: Context\n      options?: ContextToPromptOptions\n    }): Promise<ContextToPromptResult> => {\n      try {\n        // 1. Determine target project\n        const targetProjectId = options.projectId || getActiveProjectId(projects || [])\n        \n        if (!targetProjectId) {\n          throw new Error('Lütfen önce bir proje seçin')\n        }\n\n        // 2. Prepare prompt data\n        const promptData = {\n          project_id: targetProjectId,\n          prompt_text: context.content,\n          title: options.customTitle || context.title || 'Context Gallery Prompt',\n          category: options.customCategory || extractCategoryFromContext(context),\n          tags: combineTagsFromContext(context, options.additionalTags),\n          order_index: await getNextOrderIndex(targetProjectId),\n          is_used: false,\n          is_favorite: options.markAsFavorite || false\n        }\n\n        // 3. Create prompt using existing hook\n        const newPrompt = await createPromptMutation.mutateAsync(promptData)\n\n        // 4. Track context usage\n        await trackContextUsage(context.id, targetProjectId)\n\n        // 5. Update UI state\n        queryClient.invalidateQueries({ queryKey: ['prompts', targetProjectId] })\n        queryClient.invalidateQueries({ queryKey: ['projects'] })\n\n        return {\n          success: true,\n          promptId: newPrompt.id\n        }\n\n      } catch (error) {\n        console.error('Context to prompt conversion error:', error)\n        \n        const errorMessage = error instanceof Error \n          ? error.message \n          : 'Context prompt olarak eklenirken hata oluştu'\n\n        return {\n          success: false,\n          error: errorMessage\n        }\n      }\n    },\n    onSuccess: (result) => {\n      if (result.success) {\n        toast.success('Context başarıyla projeye eklendi!')\n      } else {\n        toast.error(result.error || 'Bir hata oluştu')\n      }\n    },\n    onError: (error) => {\n      console.error('Context to prompt mutation error:', error)\n      toast.error('Context eklenirken beklenmeyen bir hata oluştu')\n    }\n  })\n}\n\n/**\n * Simplified hook for quick context addition to active project\n */\nexport function useAddContextToProject() {\n  const contextToPromptMutation = useContextToPrompt()\n\n  return {\n    addContext: async (context: Context, customTitle?: string) => {\n      return contextToPromptMutation.mutateAsync({\n        context,\n        options: { customTitle }\n      })\n    },\n    isLoading: contextToPromptMutation.isPending,\n    error: contextToPromptMutation.error\n  }\n}\n\n/**\n * Hook for batch adding multiple contexts to project\n */\nexport function useBatchAddContexts() {\n  const contextToPromptMutation = useContextToPrompt()\n  // const queryClient = useQueryClient() // Unused for now\n\n  return useMutation({\n    mutationFn: async ({\n      contexts,\n      projectId,\n      options = {}\n    }: {\n      contexts: Context[]\n      projectId?: string\n      options?: Omit<ContextToPromptOptions, 'projectId'>\n    }) => {\n      const results = []\n      \n      for (const context of contexts) {\n        try {\n          const result = await contextToPromptMutation.mutateAsync({\n            context,\n            options: { ...options, projectId }\n          })\n          results.push({ context: context.id, result })\n        } catch (error) {\n          results.push({ \n            context: context.id, \n            result: { success: false, error: error instanceof Error ? error.message : 'Unknown error' }\n          })\n        }\n      }\n\n      return results\n    },\n    onSuccess: (results) => {\n      const successCount = results.filter(r => r.result.success).length\n      const totalCount = results.length\n      \n      if (successCount === totalCount) {\n        toast.success(`${successCount} context başarıyla eklendi!`)\n      } else {\n        toast.warning(`${successCount}/${totalCount} context eklendi. Bazı hatalar oluştu.`)\n      }\n    }\n  })\n}\n\n// Helper functions\ninterface ProjectLike {\n  id: string\n}\n\nfunction getActiveProjectId(projects: unknown[]): string | null {\n  if (!projects || projects.length === 0) return null\n\n  // Try to get from localStorage or URL params\n  const stored = localStorage.getItem('activeProjectId')\n  if (stored && projects.find((p: unknown) => (p as ProjectLike)?.id === stored)) {\n    return stored\n  }\n\n  // Fallback to first project\n  return (projects[0] as ProjectLike)?.id || null\n}\n\nfunction extractCategoryFromContext(context: Context): string | undefined {\n  // Extract category from context metadata or tags\n  if (context.category) return context.category.name || context.category.toString()\n  if (context.tags && context.tags.length > 0) {\n    // Use first tag as category if it looks like a category\n    const firstTag = context.tags[0]\n    if (firstTag.startsWith('/')) return firstTag\n  }\n  return undefined\n}\n\nfunction combineTagsFromContext(context: Context, additionalTags?: string[]): string[] {\n  const contextTags = context.tags || []\n  const additional = additionalTags || []\n  \n  // Combine and deduplicate tags\n  const allTags = [...contextTags, ...additional]\n  return Array.from(new Set(allTags)).filter(tag => tag.trim().length > 0)\n}\n\nasync function getNextOrderIndex(_projectId: string): Promise<number> {\n  // This would typically query the database to get the next order index\n  // For now, return a timestamp-based index\n  return Date.now()\n}\n\nasync function trackContextUsage(contextId: string, projectId: string): Promise<void> {\n  try {\n    // Track context usage in analytics\n    // This could be implemented with Supabase or analytics service\n    console.log(`Context ${contextId} used in project ${projectId}`)\n  } catch (error) {\n    console.warn('Failed to track context usage:', error)\n    // Don't throw error for analytics failure\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;AAEA;;AANA;;;;;AA+BO,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,uBAAuB,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD;IAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAErC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;8CAAE;oBAAO,EACjB,OAAO,EACP,UAAU,CAAC,CAAC,EAIb;gBACC,IAAI;oBACF,8BAA8B;oBAC9B,MAAM,kBAAkB,QAAQ,SAAS,IAAI,mBAAmB,YAAY,EAAE;oBAE9E,IAAI,CAAC,iBAAiB;wBACpB,MAAM,IAAI,MAAM;oBAClB;oBAEA,yBAAyB;oBACzB,MAAM,aAAa;wBACjB,YAAY;wBACZ,aAAa,QAAQ,OAAO;wBAC5B,OAAO,QAAQ,WAAW,IAAI,QAAQ,KAAK,IAAI;wBAC/C,UAAU,QAAQ,cAAc,IAAI,2BAA2B;wBAC/D,MAAM,uBAAuB,SAAS,QAAQ,cAAc;wBAC5D,aAAa,MAAM,kBAAkB;wBACrC,SAAS;wBACT,aAAa,QAAQ,cAAc,IAAI;oBACzC;oBAEA,uCAAuC;oBACvC,MAAM,YAAY,MAAM,qBAAqB,WAAW,CAAC;oBAEzD,yBAAyB;oBACzB,MAAM,kBAAkB,QAAQ,EAAE,EAAE;oBAEpC,qBAAqB;oBACrB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAW;yBAAgB;oBAAC;oBACvE,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAW;oBAAC;oBAEvD,OAAO;wBACL,SAAS;wBACT,UAAU,UAAU,EAAE;oBACxB;gBAEF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uCAAuC;oBAErD,MAAM,eAAe,iBAAiB,QAClC,MAAM,OAAO,GACb;oBAEJ,OAAO;wBACL,SAAS;wBACT,OAAO;oBACT;gBACF;YACF;;QACA,SAAS;8CAAE,CAAC;gBACV,IAAI,OAAO,OAAO,EAAE;oBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB,OAAO;oBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;gBAC9B;YACF;;QACA,OAAO;8CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;GAzEgB;;QACM,yLAAA,CAAA,iBAAc;QACL,iIAAA,CAAA,kBAAe;QACjB,kIAAA,CAAA,cAAW;QAE/B,iLAAA,CAAA,cAAW;;;AAyEb,SAAS;;IACd,MAAM,0BAA0B;IAEhC,OAAO;QACL,YAAY,OAAO,SAAkB;YACnC,OAAO,wBAAwB,WAAW,CAAC;gBACzC;gBACA,SAAS;oBAAE;gBAAY;YACzB;QACF;QACA,WAAW,wBAAwB,SAAS;QAC5C,OAAO,wBAAwB,KAAK;IACtC;AACF;IAbgB;;QACkB;;;AAiB3B,SAAS;;IACd,MAAM,0BAA0B;IAChC,yDAAyD;IAEzD,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;+CAAE;oBAAO,EACjB,QAAQ,EACR,SAAS,EACT,UAAU,CAAC,CAAC,EAKb;gBACC,MAAM,UAAU,EAAE;gBAElB,KAAK,MAAM,WAAW,SAAU;oBAC9B,IAAI;wBACF,MAAM,SAAS,MAAM,wBAAwB,WAAW,CAAC;4BACvD;4BACA,SAAS;gCAAE,GAAG,OAAO;gCAAE;4BAAU;wBACnC;wBACA,QAAQ,IAAI,CAAC;4BAAE,SAAS,QAAQ,EAAE;4BAAE;wBAAO;oBAC7C,EAAE,OAAO,OAAO;wBACd,QAAQ,IAAI,CAAC;4BACX,SAAS,QAAQ,EAAE;4BACnB,QAAQ;gCAAE,SAAS;gCAAO,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;4BAAgB;wBAC5F;oBACF;gBACF;gBAEA,OAAO;YACT;;QACA,SAAS;+CAAE,CAAC;gBACV,MAAM,eAAe,QAAQ,MAAM;uDAAC,CAAA,IAAK,EAAE,MAAM,CAAC,OAAO;sDAAE,MAAM;gBACjE,MAAM,aAAa,QAAQ,MAAM;gBAEjC,IAAI,iBAAiB,YAAY;oBAC/B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,GAAe,OAAb,cAAa;gBAChC,OAAO;oBACL,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,GAAkB,OAAhB,cAAa,KAAc,OAAX,YAAW;gBAC9C;YACF;;IACF;AACF;IA5CgB;;QACkB;QAGzB,iLAAA,CAAA,cAAW;;;AA+CpB,SAAS,mBAAmB,QAAmB;QAUtC;IATP,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG,OAAO;IAE/C,6CAA6C;IAC7C,MAAM,SAAS,aAAa,OAAO,CAAC;IACpC,IAAI,UAAU,SAAS,IAAI,CAAC,CAAC;YAAe;eAAA,EAAA,QAAC,eAAD,4BAAA,MAAoB,EAAE,MAAK;QAAS;QAC9E,OAAO;IACT;IAEA,4BAA4B;IAC5B,OAAO,EAAA,QAAC,QAAQ,CAAC,EAAE,cAAZ,4BAAA,MAA8B,EAAE,KAAI;AAC7C;AAEA,SAAS,2BAA2B,OAAgB;IAClD,iDAAiD;IACjD,IAAI,QAAQ,QAAQ,EAAE,OAAO,QAAQ,QAAQ,CAAC,IAAI,IAAI,QAAQ,QAAQ,CAAC,QAAQ;IAC/E,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;QAC3C,wDAAwD;QACxD,MAAM,WAAW,QAAQ,IAAI,CAAC,EAAE;QAChC,IAAI,SAAS,UAAU,CAAC,MAAM,OAAO;IACvC;IACA,OAAO;AACT;AAEA,SAAS,uBAAuB,OAAgB,EAAE,cAAyB;IACzE,MAAM,cAAc,QAAQ,IAAI,IAAI,EAAE;IACtC,MAAM,aAAa,kBAAkB,EAAE;IAEvC,+BAA+B;IAC/B,MAAM,UAAU;WAAI;WAAgB;KAAW;IAC/C,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,GAAG;AACxE;AAEA,eAAe,kBAAkB,UAAkB;IACjD,sEAAsE;IACtE,0CAA0C;IAC1C,OAAO,KAAK,GAAG;AACjB;AAEA,eAAe,kBAAkB,SAAiB,EAAE,SAAiB;IACnE,IAAI;QACF,mCAAmC;QACnC,+DAA+D;QAC/D,QAAQ,GAAG,CAAC,AAAC,WAAuC,OAA7B,WAAU,qBAA6B,OAAV;IACtD,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,kCAAkC;IAC/C,0CAA0C;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 2870, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/context-creation-modal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Dialog, \n  DialogContent, \n  DialogDescription, \n  DialogHeader, \n  DialogTitle \n} from '@/components/ui/dialog'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { \n  Plus, \n  X, \n  Loader2, \n  AlertCircle, \n  Globe, \n  Lock, \n  Tag,\n  FileText\n} from 'lucide-react'\nimport { useContextCategories, useCreateContext } from '@/hooks/use-contexts'\nimport { useAddContextToProject } from '@/hooks/use-context-to-prompt'\nimport { useProjects } from '@/hooks/use-projects'\nimport { toast } from 'sonner'\n\ninterface ContextCreationModalProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSuccess?: () => void\n  /** Show option to add directly to current project */\n  showAddToProject?: boolean\n}\n\nexport function ContextCreationModal({\n  open,\n  onOpenChange,\n  onSuccess,\n  showAddToProject = true\n}: ContextCreationModalProps) {\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    content: '',\n    category_id: '',\n    is_public: false,\n    is_template: false,\n    tags: [] as string[]\n  })\n  const [newTag, setNewTag] = useState('')\n  const [errors, setErrors] = useState<Record<string, string>>({})\n  const [addToProject, setAddToProject] = useState(false)\n\n  const { data: categories = [], isLoading: categoriesLoading } = useContextCategories()\n  const { data: projects = [] } = useProjects()\n  const createContextMutation = useCreateContext()\n  const { addContext: addContextToProject, isLoading: isAddingToProject } = useAddContextToProject()\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.title.trim()) {\n      newErrors.title = 'Başlık gereklidir'\n    }\n\n    if (!formData.content.trim()) {\n      newErrors.content = 'İçerik gereklidir'\n    }\n\n    if (!formData.category_id) {\n      newErrors.category_id = 'Kategori seçimi gereklidir'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!validateForm()) {\n      toast.error('Lütfen tüm gerekli alanları doldurun')\n      return\n    }\n\n    try {\n      // Create context first\n      const newContext = await createContextMutation.mutateAsync({\n        title: formData.title.trim(),\n        description: formData.description.trim() || undefined,\n        content: formData.content.trim(),\n        category_id: formData.category_id,\n        is_public: formData.is_public,\n        is_template: formData.is_template,\n        tags: formData.tags\n      })\n\n      // If \"Add to Project\" is checked, add to current project\n      if (addToProject && showAddToProject) {\n        try {\n          await addContextToProject(newContext, formData.title.trim())\n          toast.success('Context oluşturuldu ve projeye eklendi!')\n        } catch (projectError) {\n          console.error('Failed to add to project:', projectError)\n          toast.warning('Context oluşturuldu ancak projeye eklenirken hata oluştu')\n        }\n      } else {\n        toast.success('Context başarıyla oluşturuldu!')\n      }\n\n      // Reset form\n      setFormData({\n        title: '',\n        description: '',\n        content: '',\n        category_id: '',\n        is_public: false,\n        is_template: false,\n        tags: []\n      })\n      setErrors({})\n      setAddToProject(false)\n\n      onSuccess?.()\n      onOpenChange(false)\n    } catch (error) {\n      console.error('Context creation error:', error)\n      toast.error('Context oluşturulurken bir hata oluştu')\n    }\n  }\n\n  const addTag = () => {\n    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, newTag.trim()]\n      }))\n      setNewTag('')\n    }\n  }\n\n  const removeTag = (tagToRemove: string) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }))\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault()\n      addTag()\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-hidden flex flex-col z-[60]\">\n        <DialogHeader className=\"flex-shrink-0\">\n          <DialogTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            Yeni Context Oluştur\n          </DialogTitle>\n          <DialogDescription>\n            Yeni bir context oluşturun. Herkese açık contextler admin onayı gerektirir.\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"flex-1 overflow-y-auto\">\n          <form onSubmit={handleSubmit} className=\"space-y-6 p-1\">\n            {/* Title */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"title\">\n                Başlık <span className=\"text-red-500\">*</span>\n              </Label>\n              <Input\n                id=\"title\"\n                placeholder=\"Context başlığını girin...\"\n                value={formData.title}\n                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}\n                className={errors.title ? 'border-red-500' : ''}\n              />\n              {errors.title && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-3 w-3\" />\n                  {errors.title}\n                </p>\n              )}\n            </div>\n\n            {/* Description */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"description\">Açıklama</Label>\n              <Input\n                id=\"description\"\n                placeholder=\"Context açıklaması (isteğe bağlı)...\"\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n              />\n            </div>\n\n            {/* Category */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"category\">\n                Kategori <span className=\"text-red-500\">*</span>\n              </Label>\n              <Select\n                value={formData.category_id}\n                onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value }))}\n              >\n                <SelectTrigger className={errors.category_id ? 'border-red-500' : ''}>\n                  <SelectValue placeholder=\"Kategori seçin...\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {categoriesLoading ? (\n                    <SelectItem value=\"loading\" disabled>\n                      Kategoriler yükleniyor...\n                    </SelectItem>\n                  ) : (\n                    categories.map((category) => (\n                      <SelectItem key={category.id} value={category.id}>\n                        <span className=\"flex items-center gap-2\">\n                          <span>{category.icon}</span>\n                          {category.name}\n                        </span>\n                      </SelectItem>\n                    ))\n                  )}\n                </SelectContent>\n              </Select>\n              {errors.category_id && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-3 w-3\" />\n                  {errors.category_id}\n                </p>\n              )}\n            </div>\n\n            {/* Content */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"content\">\n                İçerik <span className=\"text-red-500\">*</span>\n              </Label>\n              <Textarea\n                id=\"content\"\n                placeholder=\"Context içeriğini girin...\"\n                value={formData.content}\n                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}\n                className={`min-h-[120px] resize-none ${errors.content ? 'border-red-500' : ''}`}\n              />\n              {errors.content && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-3 w-3\" />\n                  {errors.content}\n                </p>\n              )}\n            </div>\n\n            {/* Tags */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"tags\">Etiketler</Label>\n              <div className=\"flex gap-2\">\n                <Input\n                  id=\"tags\"\n                  placeholder=\"Etiket ekle...\"\n                  value={newTag}\n                  onChange={(e) => setNewTag(e.target.value)}\n                  onKeyPress={handleKeyPress}\n                  className=\"flex-1\"\n                />\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={addTag}\n                  disabled={!newTag.trim()}\n                >\n                  <Plus className=\"h-4 w-4\" />\n                </Button>\n              </div>\n              {formData.tags.length > 0 && (\n                <div className=\"flex flex-wrap gap-2 mt-2\">\n                  {formData.tags.map((tag) => (\n                    <Badge key={tag} variant=\"secondary\" className=\"flex items-center gap-1\">\n                      <Tag className=\"h-3 w-3\" />\n                      {tag}\n                      <button\n                        type=\"button\"\n                        onClick={() => removeTag(tag)}\n                        className=\"ml-1 hover:text-red-500\"\n                      >\n                        <X className=\"h-3 w-3\" />\n                      </button>\n                    </Badge>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Visibility Options */}\n            <div className=\"space-y-3\">\n              <Label>Görünürlük Ayarları</Label>\n              <div className=\"space-y-2\">\n                <label className=\"flex items-center gap-3 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"visibility\"\n                    checked={!formData.is_public}\n                    onChange={() => setFormData(prev => ({ ...prev, is_public: false }))}\n                    className=\"w-4 h-4\"\n                  />\n                  <div className=\"flex items-center gap-2\">\n                    <Lock className=\"h-4 w-4 text-gray-500\" />\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-sm\">Özel</span>\n                      <span className=\"text-xs text-gray-500\">Sadece ben görebilirim</span>\n                    </div>\n                  </div>\n                </label>\n                <label className=\"flex items-center gap-3 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"visibility\"\n                    checked={formData.is_public}\n                    onChange={() => setFormData(prev => ({ ...prev, is_public: true }))}\n                    className=\"w-4 h-4\"\n                  />\n                  <div className=\"flex items-center gap-2\">\n                    <Globe className=\"h-4 w-4 text-blue-500\" />\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-sm\">Herkese Açık</span>\n                      <span className=\"text-xs text-gray-500\">Tüm kullanıcılar görebilir</span>\n                    </div>\n                  </div>\n                </label>\n              </div>\n            </div>\n\n            {/* Template Option */}\n            <div className=\"space-y-2\">\n              <label className=\"flex items-center gap-3 cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.is_template}\n                  onChange={(e) => setFormData(prev => ({ ...prev, is_template: e.target.checked }))}\n                  className=\"w-4 h-4\"\n                />\n                <span className=\"text-sm\">Bu contexti şablon olarak işaretle</span>\n              </label>\n            </div>\n\n            {/* Add to Project Option */}\n            {showAddToProject && projects.length > 0 && (\n              <div className=\"space-y-2 p-3 bg-blue-50 rounded-lg border border-blue-200\">\n                <label className=\"flex items-center gap-3 cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={addToProject}\n                    onChange={(e) => setAddToProject(e.target.checked)}\n                    className=\"w-4 h-4 text-blue-600\"\n                  />\n                  <div className=\"flex items-center gap-2\">\n                    <FileText className=\"h-4 w-4 text-blue-600\" />\n                    <div className=\"flex flex-col\">\n                      <span className=\"text-sm font-medium text-blue-900\">Mevcut projeye ekle</span>\n                      <span className=\"text-xs text-blue-700\">Context oluşturulduktan sonra aktif projeye prompt olarak eklenecek</span>\n                    </div>\n                  </div>\n                </label>\n              </div>\n            )}\n\n            {/* Submit Buttons */}\n            <div className=\"flex gap-3 pt-4\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n                className=\"flex-1\"\n              >\n                İptal\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={createContextMutation.isPending || isAddingToProject}\n                className=\"flex-1\"\n              >\n                {(createContextMutation.isPending || isAddingToProject) && (\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                )}\n                {addToProject ? 'Oluştur ve Ekle' : 'Oluştur'}\n              </Button>\n            </div>\n          </form>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;;;AAnCA;;;;;;;;;;;;;;AA6CO,SAAS,qBAAqB,KAKT;QALS,EACnC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,mBAAmB,IAAI,EACG,GALS;;IAMnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,SAAS;QACT,aAAa;QACb,WAAW;QACX,aAAa;QACb,MAAM,EAAE;IACV;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,MAAM,aAAa,EAAE,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD;IACnF,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC1C,MAAM,wBAAwB,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD;IAC7C,MAAM,EAAE,YAAY,mBAAmB,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,yBAAsB,AAAD;IAE/F,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,CAAC,SAAS,WAAW,EAAE;YACzB,UAAU,WAAW,GAAG;QAC1B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,uBAAuB;YACvB,MAAM,aAAa,MAAM,sBAAsB,WAAW,CAAC;gBACzD,OAAO,SAAS,KAAK,CAAC,IAAI;gBAC1B,aAAa,SAAS,WAAW,CAAC,IAAI,MAAM;gBAC5C,SAAS,SAAS,OAAO,CAAC,IAAI;gBAC9B,aAAa,SAAS,WAAW;gBACjC,WAAW,SAAS,SAAS;gBAC7B,aAAa,SAAS,WAAW;gBACjC,MAAM,SAAS,IAAI;YACrB;YAEA,yDAAyD;YACzD,IAAI,gBAAgB,kBAAkB;gBACpC,IAAI;oBACF,MAAM,oBAAoB,YAAY,SAAS,KAAK,CAAC,IAAI;oBACzD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB,EAAE,OAAO,cAAc;oBACrB,QAAQ,KAAK,CAAC,6BAA6B;oBAC3C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,SAAS;gBACT,aAAa;gBACb,WAAW;gBACX,aAAa;gBACb,MAAM,EAAE;YACV;YACA,UAAU,CAAC;YACX,gBAAgB;YAEhB,sBAAA,gCAAA;YACA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,SAAS;QACb,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,KAAK;YAC3D,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,MAAM;2BAAI,KAAK,IAAI;wBAAE,OAAO,IAAI;qBAAG;gBACrC,CAAC;YACD,UAAU;QACZ;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;YACxC,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,6LAAC,qIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAQ;0DACd,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAExC,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACxE,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;oCAE9C,OAAO,KAAK,kBACX,6LAAC;wCAAE,WAAU;;0DACX,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,KAAK;;;;;;;;;;;;;0CAMnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;;;;;;;;;;;;0CAKlF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAW;0DACf,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAE1C,6LAAC,qIAAA,CAAA,SAAM;wCACL,OAAO,SAAS,WAAW;wCAC3B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa;gDAAM,CAAC;;0DAE9E,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,WAAW,OAAO,WAAW,GAAG,mBAAmB;0DAChE,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,qIAAA,CAAA,gBAAa;0DACX,kCACC,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAU,QAAQ;8DAAC;;;;;2DAIrC,WAAW,GAAG,CAAC,CAAC,yBACd,6LAAC,qIAAA,CAAA,aAAU;wDAAmB,OAAO,SAAS,EAAE;kEAC9C,cAAA,6LAAC;4DAAK,WAAU;;8EACd,6LAAC;8EAAM,SAAS,IAAI;;;;;;gEACnB,SAAS,IAAI;;;;;;;uDAHD,SAAS,EAAE;;;;;;;;;;;;;;;;oCAUnC,OAAO,WAAW,kBACjB,6LAAC;wCAAE,WAAU;;0DACX,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,WAAW;;;;;;;;;;;;;0CAMzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAU;0DAChB,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAExC,6LAAC,uIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,WAAW,AAAC,6BAAmE,OAAvC,OAAO,OAAO,GAAG,mBAAmB;;;;;;oCAE7E,OAAO,OAAO,kBACb,6LAAC;wCAAE,WAAU;;0DACX,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,OAAO;;;;;;;;;;;;;0CAMrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;kDACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,YAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,OAAO,IAAI;0DAEtB,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAGnB,SAAS,IAAI,CAAC,MAAM,GAAG,mBACtB,6LAAC;wCAAI,WAAU;kDACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,oBAClB,6LAAC,oIAAA,CAAA,QAAK;gDAAW,SAAQ;gDAAY,WAAU;;kEAC7C,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd;kEACD,6LAAC;wDACC,MAAK;wDACL,SAAS,IAAM,UAAU;wDACzB,WAAU;kEAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;+CARL;;;;;;;;;;;;;;;;0CAiBpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,SAAS,CAAC,SAAS,SAAS;wDAC5B,UAAU,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,WAAW;gEAAM,CAAC;wDAClE,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAU;;;;;;kFAC1B,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;0DAI9C,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,SAAS,SAAS,SAAS;wDAC3B,UAAU,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,WAAW;gEAAK,CAAC;wDACjE,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAU;;;;;;kFAC1B,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQlD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS,SAAS,WAAW;4CAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,OAAO;oDAAC,CAAC;4CAChF,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;4BAK7B,oBAAoB,SAAS,MAAM,GAAG,mBACrC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;4CACjD,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAoC;;;;;;sEACpD,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU,sBAAsB,SAAS,IAAI;wCAC7C,WAAU;;4CAET,CAAC,sBAAsB,SAAS,IAAI,iBAAiB,mBACpD,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAEpB,eAAe,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;GA5WgB;;QAmBkD,kIAAA,CAAA,uBAAoB;QACpD,kIAAA,CAAA,cAAW;QACb,kIAAA,CAAA,mBAAgB;QAC4B,iJAAA,CAAA,yBAAsB;;;KAtBlF", "debugId": null}}]}