{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/plan-limits.ts"], "sourcesContent": ["// Plan Limitleri Kontrol Middleware ve Utility Fonksiyonları\n\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\nexport interface PlanLimits {\n  current_projects: number\n  current_prompts: number\n  max_projects: number\n  max_prompts_per_project: number\n  can_create_project: boolean\n  can_create_prompt: boolean\n}\n\nexport interface UserPlan {\n  plan_id: string\n  plan_name: string\n  display_name: string\n  max_projects: number\n  max_prompts_per_project: number\n  features: Record<string, boolean | string | number>\n  status: string\n  expires_at: string | null\n}\n\n// Plan limitlerini kontrol et\nexport async function checkUserLimits(): Promise<PlanLimits | null> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { data, error } = await supabase.rpc('check_user_limits', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Plan limitleri kontrol edilemedi:', error)\n      throw new Error(error.message)\n    }\n\n    return data?.[0] || null\n  } catch (error) {\n    console.error('Plan limitleri kontrol hatası:', error)\n    return null\n  }\n}\n\n// Kullanıcının aktif planını getir\nexport async function getUserActivePlan(): Promise<UserPlan | null> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { data, error } = await supabase.rpc('get_user_active_plan', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Aktif plan getirilemedi:', error)\n      throw new Error(error.message)\n    }\n\n    return data?.[0] || null\n  } catch (error) {\n    console.error('Aktif plan getirme hatası:', error)\n    return null\n  }\n}\n\n// Proje oluşturma limiti kontrol et\nexport async function canCreateProject(): Promise<{ allowed: boolean; reason?: string }> {\n  const limits = await checkUserLimits()\n  \n  if (!limits) {\n    return { allowed: false, reason: 'Plan bilgileri alınamadı' }\n  }\n\n  if (!limits.can_create_project) {\n    return { \n      allowed: false, \n      reason: `Proje limiti aşıldı (${limits.current_projects}/${limits.max_projects})` \n    }\n  }\n\n  return { allowed: true }\n}\n\n// Prompt oluşturma limiti kontrol et\nexport async function canCreatePrompt(projectId?: string): Promise<{ allowed: boolean; reason?: string }> {\n  const limits = await checkUserLimits()\n  \n  if (!limits) {\n    return { allowed: false, reason: 'Plan bilgileri alınamadı' }\n  }\n\n  if (!limits.can_create_prompt) {\n    return { \n      allowed: false, \n      reason: `Prompt limiti aşıldı (${limits.current_prompts}/${limits.max_prompts_per_project})` \n    }\n  }\n\n  // Eğer belirli bir proje için kontrol ediliyorsa, o projenin prompt sayısını kontrol et\n  if (projectId) {\n    try {\n      const { count, error } = await supabase\n        .from('prompts')\n        .select('*', { count: 'exact', head: true })\n        .eq('project_id', projectId)\n\n      if (error) {\n        console.error('Proje prompt sayısı kontrol edilemedi:', error)\n        return { allowed: false, reason: 'Proje bilgileri alınamadı' }\n      }\n\n      const currentPrompts = count || 0\n      if (limits.max_prompts_per_project !== -1 && currentPrompts >= limits.max_prompts_per_project) {\n        return { \n          allowed: false, \n          reason: `Bu proje için prompt limiti aşıldı (${currentPrompts}/${limits.max_prompts_per_project})` \n        }\n      }\n    } catch (error) {\n      console.error('Proje prompt sayısı kontrol hatası:', error)\n      return { allowed: false, reason: 'Proje bilgileri kontrol edilemedi' }\n    }\n  }\n\n  return { allowed: true }\n}\n\n// Plan özelliği kontrol et\nexport async function hasPlanFeature(featureName: string): Promise<boolean> {\n  const plan = await getUserActivePlan()\n  return plan?.features?.[featureName] === true\n}\n\n// Kullanım istatistiklerini güncelle\nexport async function updateUsageStats(): Promise<void> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { error } = await supabase.rpc('update_usage_stats', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Kullanım istatistikleri güncellenemedi:', error)\n      throw new Error(error.message)\n    }\n  } catch (error) {\n    console.error('Kullanım istatistikleri güncelleme hatası:', error)\n    throw error\n  }\n}\n\n// Plan upgrade önerisi\nexport function getPlanUpgradeSuggestion(limits: PlanLimits): {\n  shouldUpgrade: boolean\n  reason: string\n  suggestedPlan: string\n} {\n  // Proje limiti %80'e ulaştıysa\n  if (limits.max_projects !== -1 && limits.current_projects / limits.max_projects >= 0.8) {\n    return {\n      shouldUpgrade: true,\n      reason: 'Proje limitinizin %80\\'ine ulaştınız',\n      suggestedPlan: 'professional'\n    }\n  }\n\n  // Prompt limiti %80'e ulaştıysa\n  if (limits.max_prompts_per_project !== -1 && limits.current_prompts / limits.max_prompts_per_project >= 0.8) {\n    return {\n      shouldUpgrade: true,\n      reason: 'Prompt limitinizin %80\\'ine ulaştınız',\n      suggestedPlan: 'professional'\n    }\n  }\n\n  return {\n    shouldUpgrade: false,\n    reason: '',\n    suggestedPlan: ''\n  }\n}\n\n// Plan limiti hata mesajları\nexport const PLAN_LIMIT_MESSAGES = {\n  PROJECT_LIMIT_REACHED: 'Proje oluşturma limitinize ulaştınız. Daha fazla proje oluşturmak için planınızı yükseltin.',\n  PROMPT_LIMIT_REACHED: 'Prompt oluşturma limitinize ulaştınız. Daha fazla prompt oluşturmak için planınızı yükseltin.',\n  FEATURE_NOT_AVAILABLE: 'Bu özellik mevcut planınızda bulunmamaktadır. Planınızı yükseltin.',\n  PLAN_EXPIRED: 'Planınızın süresi dolmuştur. Lütfen planınızı yenileyin.',\n  PLAN_SUSPENDED: 'Hesabınız askıya alınmıştır. Lütfen destek ekibi ile iletişime geçin.'\n}\n\n// Plan durumu kontrol et\nexport async function checkPlanStatus(): Promise<{\n  isActive: boolean\n  status: string\n  message?: string\n}> {\n  const plan = await getUserActivePlan()\n  \n  if (!plan) {\n    return {\n      isActive: false,\n      status: 'no_plan',\n      message: 'Aktif plan bulunamadı'\n    }\n  }\n\n  if (plan.status !== 'active') {\n    return {\n      isActive: false,\n      status: plan.status,\n      message: PLAN_LIMIT_MESSAGES.PLAN_SUSPENDED\n    }\n  }\n\n  if (plan.expires_at && new Date(plan.expires_at) < new Date()) {\n    return {\n      isActive: false,\n      status: 'expired',\n      message: PLAN_LIMIT_MESSAGES.PLAN_EXPIRED\n    }\n  }\n\n  return {\n    isActive: true,\n    status: 'active'\n  }\n}\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;AAE7D;;AAuBO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,qBAAqB;YAC9D,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,MAAM,CAAC,EAAE,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,wBAAwB;YACjE,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,MAAM,CAAC,EAAE,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,SAAS,MAAM;IAErB,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,QAAQ;QAA2B;IAC9D;IAEA,IAAI,CAAC,OAAO,kBAAkB,EAAE;QAC9B,OAAO;YACL,SAAS;YACT,QAAQ,CAAC,qBAAqB,EAAE,OAAO,gBAAgB,CAAC,CAAC,EAAE,OAAO,YAAY,CAAC,CAAC,CAAC;QACnF;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,gBAAgB,SAAkB;IACtD,MAAM,SAAS,MAAM;IAErB,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,QAAQ;QAA2B;IAC9D;IAEA,IAAI,CAAC,OAAO,iBAAiB,EAAE;QAC7B,OAAO;YACL,SAAS;YACT,QAAQ,CAAC,sBAAsB,EAAE,OAAO,eAAe,CAAC,CAAC,EAAE,OAAO,uBAAuB,CAAC,CAAC,CAAC;QAC9F;IACF;IAEA,wFAAwF;IACxF,IAAI,WAAW;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACpC,IAAI,CAAC,WACL,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GACzC,EAAE,CAAC,cAAc;YAEpB,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,OAAO;oBAAE,SAAS;oBAAO,QAAQ;gBAA4B;YAC/D;YAEA,MAAM,iBAAiB,SAAS;YAChC,IAAI,OAAO,uBAAuB,KAAK,CAAC,KAAK,kBAAkB,OAAO,uBAAuB,EAAE;gBAC7F,OAAO;oBACL,SAAS;oBACT,QAAQ,CAAC,oCAAoC,EAAE,eAAe,CAAC,EAAE,OAAO,uBAAuB,CAAC,CAAC,CAAC;gBACpG;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;gBAAE,SAAS;gBAAO,QAAQ;YAAoC;QACvE;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,eAAe,WAAmB;IACtD,MAAM,OAAO,MAAM;IACnB,OAAO,MAAM,UAAU,CAAC,YAAY,KAAK;AAC3C;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,sBAAsB;YACzD,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,MAAM;IACR;AACF;AAGO,SAAS,yBAAyB,MAAkB;IAKzD,+BAA+B;IAC/B,IAAI,OAAO,YAAY,KAAK,CAAC,KAAK,OAAO,gBAAgB,GAAG,OAAO,YAAY,IAAI,KAAK;QACtF,OAAO;YACL,eAAe;YACf,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,gCAAgC;IAChC,IAAI,OAAO,uBAAuB,KAAK,CAAC,KAAK,OAAO,eAAe,GAAG,OAAO,uBAAuB,IAAI,KAAK;QAC3G,OAAO;YACL,eAAe;YACf,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,OAAO;QACL,eAAe;QACf,QAAQ;QACR,eAAe;IACjB;AACF;AAGO,MAAM,sBAAsB;IACjC,uBAAuB;IACvB,sBAAsB;IACtB,uBAAuB;IACvB,cAAc;IACd,gBAAgB;AAClB;AAGO,eAAe;IAKpB,MAAM,OAAO,MAAM;IAEnB,IAAI,CAAC,MAAM;QACT,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS;QACX;IACF;IAEA,IAAI,KAAK,MAAM,KAAK,UAAU;QAC5B,OAAO;YACL,UAAU;YACV,QAAQ,KAAK,MAAM;YACnB,SAAS,oBAAoB,cAAc;QAC7C;IACF;IAEA,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,QAAQ;QAC7D,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS,oBAAoB,YAAY;QAC3C;IACF;IAEA,OAAO;QACL,UAAU;QACV,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/project-validation.ts"], "sourcesContent": ["/**\n * Proje adı validation ve güvenlik utilities\n * Güvenlik odaklı input validation ve sanitization\n */\n\n// Proje adı limitleri ve kuralları\nexport const PROJECT_NAME_RULES = {\n  minLength: 3,\n  maxLength: 50,\n  allowedCharsRegex: /^[a-zA-Z0-9\\s\\-_.]+$/,\n  debounceMs: 300,\n  rateLimit: {\n    maxRequests: 10,\n    windowMinutes: 1\n  }\n} as const;\n\n// Validation sonuç tipi\nexport interface ValidationResult {\n  isValid: boolean;\n  error?: string;\n  sanitizedValue?: string;\n}\n\n// Rate limiting için local storage key\nconst RATE_LIMIT_KEY = 'project_name_update_rate_limit';\n\n/**\n * Proje adını sanitize eder\n */\nexport function sanitizeProjectName(name: string): string {\n  if (!name) return '';\n  \n  // Trim ve normalize\n  let sanitized = name.trim();\n  \n  // Çoklu boşlukları tek boşluğa çevir\n  sanitized = sanitized.replace(/\\s+/g, ' ');\n  \n  // Başlangıç ve bitiş boşluklarını kaldır\n  sanitized = sanitized.trim();\n  \n  return sanitized;\n}\n\n/**\n * Proje adını validate eder\n */\nexport function validateProjectName(name: string): ValidationResult {\n  const sanitized = sanitizeProjectName(name);\n  \n  // Boş kontrol\n  if (!sanitized) {\n    return {\n      isValid: false,\n      error: 'Proje adı boş olamaz'\n    };\n  }\n  \n  // Uzunluk kontrol\n  if (sanitized.length < PROJECT_NAME_RULES.minLength) {\n    return {\n      isValid: false,\n      error: `Proje adı en az ${PROJECT_NAME_RULES.minLength} karakter olmalıdır`\n    };\n  }\n  \n  if (sanitized.length > PROJECT_NAME_RULES.maxLength) {\n    return {\n      isValid: false,\n      error: `Proje adı en fazla ${PROJECT_NAME_RULES.maxLength} karakter olabilir`\n    };\n  }\n  \n  // Karakter kontrol\n  if (!PROJECT_NAME_RULES.allowedCharsRegex.test(sanitized)) {\n    return {\n      isValid: false,\n      error: 'Proje adı sadece harf, rakam, boşluk ve -_. karakterlerini içerebilir'\n    };\n  }\n  \n  // Sadece boşluk kontrolü\n  if (sanitized.replace(/\\s/g, '').length === 0) {\n    return {\n      isValid: false,\n      error: 'Proje adı sadece boşluk karakterlerinden oluşamaz'\n    };\n  }\n  \n  return {\n    isValid: true,\n    sanitizedValue: sanitized\n  };\n}\n\n/**\n * XSS koruması için HTML encode\n */\nexport function escapeHtml(text: string): string {\n  const div = document.createElement('div');\n  div.textContent = text;\n  return div.innerHTML;\n}\n\n/**\n * Client-side rate limiting kontrolü\n */\nexport function checkClientRateLimit(): boolean {\n  try {\n    const stored = localStorage.getItem(RATE_LIMIT_KEY);\n    const now = Date.now();\n    \n    if (!stored) {\n      // İlk istek\n      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n        count: 1,\n        windowStart: now\n      }));\n      return true;\n    }\n    \n    const data = JSON.parse(stored);\n    const windowDuration = PROJECT_NAME_RULES.rateLimit.windowMinutes * 60 * 1000; // ms\n    \n    // Pencere süresi dolmuş mu?\n    if (now - data.windowStart > windowDuration) {\n      // Yeni pencere başlat\n      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n        count: 1,\n        windowStart: now\n      }));\n      return true;\n    }\n    \n    // Limit kontrolü\n    if (data.count >= PROJECT_NAME_RULES.rateLimit.maxRequests) {\n      return false;\n    }\n    \n    // Sayacı artır\n    localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n      count: data.count + 1,\n      windowStart: data.windowStart\n    }));\n    \n    return true;\n  } catch (error) {\n    console.warn('Rate limit check failed:', error);\n    return true; // Hata durumunda izin ver\n  }\n}\n\n/**\n * Rate limit reset\n */\nexport function resetClientRateLimit(): void {\n  try {\n    localStorage.removeItem(RATE_LIMIT_KEY);\n  } catch (error) {\n    console.warn('Rate limit reset failed:', error);\n  }\n}\n\n/**\n * Debounced validation hook için utility\n */\nexport function createDebouncedValidator(\n  validator: (value: string) => ValidationResult,\n  delay: number = PROJECT_NAME_RULES.debounceMs\n) {\n  let timeoutId: NodeJS.Timeout;\n  \n  return (value: string, callback: (result: ValidationResult) => void) => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(() => {\n      const result = validator(value);\n      callback(result);\n    }, delay);\n  };\n}\n\n/**\n * Güvenli proje adı karşılaştırması\n */\nexport function isSameProjectName(name1: string, name2: string): boolean {\n  const sanitized1 = sanitizeProjectName(name1).toLowerCase();\n  const sanitized2 = sanitizeProjectName(name2).toLowerCase();\n  return sanitized1 === sanitized2;\n}\n\n/**\n * Duplicate name kontrolü için async validation\n */\nexport async function validateProjectNameUnique(\n  name: string,\n  currentProjectId: string,\n  projects: Array<{ id: string; name: string }>\n): Promise<ValidationResult> {\n  const sanitized = sanitizeProjectName(name);\n\n  // Önce temel validation\n  const basicValidation = validateProjectName(name);\n  if (!basicValidation.isValid) {\n    return basicValidation;\n  }\n\n  // Duplicate kontrolü (case-insensitive)\n  const isDuplicate = projects.some(project =>\n    project.id !== currentProjectId &&\n    isSameProjectName(project.name, sanitized)\n  );\n\n  if (isDuplicate) {\n    return {\n      isValid: false,\n      error: 'Bu isimde bir proje zaten mevcut'\n    };\n  }\n\n  return {\n    isValid: true,\n    sanitizedValue: sanitized\n  };\n}\n\n/**\n * Error mesajlarını kullanıcı dostu hale getir\n */\nexport function formatValidationError(error: string): string {\n  // Teknik hataları kullanıcı dostu mesajlara çevir\n  const errorMap: Record<string, string> = {\n    'unique_violation': 'Bu isimde bir proje zaten mevcut',\n    'check_violation': 'Proje adı geçersiz karakterler içeriyor',\n    'not_null_violation': 'Proje adı boş olamaz',\n    'foreign_key_violation': 'Geçersiz proje referansı',\n    'RateLimitExceeded': 'Çok fazla güncelleme isteği. Lütfen bekleyin.',\n    'InvalidInput': 'Geçersiz proje adı',\n    'DuplicateName': 'Bu isimde bir proje zaten mevcut',\n    'NotFound': 'Proje bulunamadı',\n    'Unauthorized': 'Bu işlem için yetkiniz yok'\n  };\n\n  return errorMap[error] || error;\n}\n\n/**\n * Advanced debounced validator with duplicate check\n */\nexport function createAdvancedDebouncedValidator(\n  projects: Array<{ id: string; name: string }>,\n  currentProjectId: string,\n  delay: number = PROJECT_NAME_RULES.debounceMs\n) {\n  let timeoutId: NodeJS.Timeout;\n\n  return (value: string, callback: (result: ValidationResult) => void) => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(async () => {\n      const result = await validateProjectNameUnique(value, currentProjectId, projects);\n      callback(result);\n    }, delay);\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,mCAAmC;;;;;;;;;;;;;;AAC5B,MAAM,qBAAqB;IAChC,WAAW;IACX,WAAW;IACX,mBAAmB;IACnB,YAAY;IACZ,WAAW;QACT,aAAa;QACb,eAAe;IACjB;AACF;AASA,uCAAuC;AACvC,MAAM,iBAAiB;AAKhB,SAAS,oBAAoB,IAAY;IAC9C,IAAI,CAAC,MAAM,OAAO;IAElB,oBAAoB;IACpB,IAAI,YAAY,KAAK,IAAI;IAEzB,qCAAqC;IACrC,YAAY,UAAU,OAAO,CAAC,QAAQ;IAEtC,yCAAyC;IACzC,YAAY,UAAU,IAAI;IAE1B,OAAO;AACT;AAKO,SAAS,oBAAoB,IAAY;IAC9C,MAAM,YAAY,oBAAoB;IAEtC,cAAc;IACd,IAAI,CAAC,WAAW;QACd,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,kBAAkB;IAClB,IAAI,UAAU,MAAM,GAAG,mBAAmB,SAAS,EAAE;QACnD,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gBAAgB,EAAE,mBAAmB,SAAS,CAAC,mBAAmB,CAAC;QAC7E;IACF;IAEA,IAAI,UAAU,MAAM,GAAG,mBAAmB,SAAS,EAAE;QACnD,OAAO;YACL,SAAS;YACT,OAAO,CAAC,mBAAmB,EAAE,mBAAmB,SAAS,CAAC,kBAAkB,CAAC;QAC/E;IACF;IAEA,mBAAmB;IACnB,IAAI,CAAC,mBAAmB,iBAAiB,CAAC,IAAI,CAAC,YAAY;QACzD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,IAAI,UAAU,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;QAC7C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,gBAAgB;IAClB;AACF;AAKO,SAAS,WAAW,IAAY;IACrC,MAAM,MAAM,SAAS,aAAa,CAAC;IACnC,IAAI,WAAW,GAAG;IAClB,OAAO,IAAI,SAAS;AACtB;AAKO,SAAS;IACd,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,CAAC,QAAQ;YACX,YAAY;YACZ,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBAClD,OAAO;gBACP,aAAa;YACf;YACA,OAAO;QACT;QAEA,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,MAAM,iBAAiB,mBAAmB,SAAS,CAAC,aAAa,GAAG,KAAK,MAAM,KAAK;QAEpF,4BAA4B;QAC5B,IAAI,MAAM,KAAK,WAAW,GAAG,gBAAgB;YAC3C,sBAAsB;YACtB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBAClD,OAAO;gBACP,aAAa;YACf;YACA,OAAO;QACT;QAEA,iBAAiB;QACjB,IAAI,KAAK,KAAK,IAAI,mBAAmB,SAAS,CAAC,WAAW,EAAE;YAC1D,OAAO;QACT;QAEA,eAAe;QACf,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;YAClD,OAAO,KAAK,KAAK,GAAG;YACpB,aAAa,KAAK,WAAW;QAC/B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;QACzC,OAAO,MAAM,0BAA0B;IACzC;AACF;AAKO,SAAS;IACd,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;IAC3C;AACF;AAKO,SAAS,yBACd,SAA8C,EAC9C,QAAgB,mBAAmB,UAAU;IAE7C,IAAI;IAEJ,OAAO,CAAC,OAAe;QACrB,aAAa;QACb,YAAY,WAAW;YACrB,MAAM,SAAS,UAAU;YACzB,SAAS;QACX,GAAG;IACL;AACF;AAKO,SAAS,kBAAkB,KAAa,EAAE,KAAa;IAC5D,MAAM,aAAa,oBAAoB,OAAO,WAAW;IACzD,MAAM,aAAa,oBAAoB,OAAO,WAAW;IACzD,OAAO,eAAe;AACxB;AAKO,eAAe,0BACpB,IAAY,EACZ,gBAAwB,EACxB,QAA6C;IAE7C,MAAM,YAAY,oBAAoB;IAEtC,wBAAwB;IACxB,MAAM,kBAAkB,oBAAoB;IAC5C,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;IACT;IAEA,wCAAwC;IACxC,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,UAChC,QAAQ,EAAE,KAAK,oBACf,kBAAkB,QAAQ,IAAI,EAAE;IAGlC,IAAI,aAAa;QACf,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,gBAAgB;IAClB;AACF;AAKO,SAAS,sBAAsB,KAAa;IACjD,kDAAkD;IAClD,MAAM,WAAmC;QACvC,oBAAoB;QACpB,mBAAmB;QACnB,sBAAsB;QACtB,yBAAyB;QACzB,qBAAqB;QACrB,gBAAgB;QAChB,iBAAiB;QACjB,YAAY;QACZ,gBAAgB;IAClB;IAEA,OAAO,QAAQ,CAAC,MAAM,IAAI;AAC5B;AAKO,SAAS,iCACd,QAA6C,EAC7C,gBAAwB,EACxB,QAAgB,mBAAmB,UAAU;IAE7C,IAAI;IAEJ,OAAO,CAAC,OAAe;QACrB,aAAa;QACb,YAAY,WAAW;YACrB,MAAM,SAAS,MAAM,0BAA0B,OAAO,kBAAkB;YACxE,SAAS;QACX,GAAG;IACL;AACF", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/rate-limiter.ts"], "sourcesContent": ["'use client'\n\n/**\n * Advanced Rate Limiting System for PromptFlow\n * Provides both client-side and server-side rate limiting\n */\n\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\n// Rate limiting configurations for different actions\nexport const RATE_LIMITS = {\n  // Authentication actions\n  login: { maxRequests: 5, windowMinutes: 15 },\n  signup: { maxRequests: 3, windowMinutes: 60 },\n  password_reset: { maxRequests: 3, windowMinutes: 60 },\n  \n  // Project actions\n  project_create: { maxRequests: 10, windowMinutes: 60 },\n  project_update: { maxRequests: 20, windowMinutes: 10 },\n  project_delete: { maxRequests: 5, windowMinutes: 60 },\n  \n  // Prompt actions\n  prompt_create: { maxRequests: 50, windowMinutes: 10 },\n  prompt_update: { maxRequests: 100, windowMinutes: 10 },\n  prompt_delete: { maxRequests: 20, windowMinutes: 10 },\n  \n  // Context actions\n  context_create: { maxRequests: 20, windowMinutes: 10 },\n  context_batch_add: { maxRequests: 5, windowMinutes: 10 },\n  \n  // Plan actions\n  plan_change: { maxRequests: 3, windowMinutes: 60 },\n  \n  // General API\n  api_general: { maxRequests: 200, windowMinutes: 10 },\n} as const\n\nexport type RateLimitAction = keyof typeof RATE_LIMITS\n\ninterface RateLimitData {\n  count: number\n  windowStart: number\n  lastRequest: number\n}\n\n/**\n * Client-side rate limiting with localStorage\n */\nexport class ClientRateLimiter {\n  private getStorageKey(action: RateLimitAction, userId?: string): string {\n    const userSuffix = userId ? `_${userId}` : ''\n    return `rate_limit_${action}${userSuffix}`\n  }\n\n  /**\n   * Check if action is allowed based on rate limits\n   */\n  async checkLimit(action: RateLimitAction, userId?: string): Promise<{\n    allowed: boolean\n    remainingRequests: number\n    resetTime: number\n    retryAfter?: number\n  }> {\n    try {\n      const config = RATE_LIMITS[action]\n      const storageKey = this.getStorageKey(action, userId)\n      const now = Date.now()\n      const windowMs = config.windowMinutes * 60 * 1000\n\n      // Get current data\n      const stored = localStorage.getItem(storageKey)\n      let data: RateLimitData\n\n      if (!stored) {\n        // First request\n        data = {\n          count: 1,\n          windowStart: now,\n          lastRequest: now\n        }\n        localStorage.setItem(storageKey, JSON.stringify(data))\n        \n        return {\n          allowed: true,\n          remainingRequests: config.maxRequests - 1,\n          resetTime: now + windowMs\n        }\n      }\n\n      data = JSON.parse(stored)\n\n      // Check if window has expired\n      if (now - data.windowStart > windowMs) {\n        // Reset window\n        data = {\n          count: 1,\n          windowStart: now,\n          lastRequest: now\n        }\n        localStorage.setItem(storageKey, JSON.stringify(data))\n        \n        return {\n          allowed: true,\n          remainingRequests: config.maxRequests - 1,\n          resetTime: now + windowMs\n        }\n      }\n\n      // Check if limit exceeded\n      if (data.count >= config.maxRequests) {\n        const resetTime = data.windowStart + windowMs\n        const retryAfter = Math.ceil((resetTime - now) / 1000)\n        \n        return {\n          allowed: false,\n          remainingRequests: 0,\n          resetTime,\n          retryAfter\n        }\n      }\n\n      // Increment counter\n      data.count++\n      data.lastRequest = now\n      localStorage.setItem(storageKey, JSON.stringify(data))\n\n      return {\n        allowed: true,\n        remainingRequests: config.maxRequests - data.count,\n        resetTime: data.windowStart + windowMs\n      }\n    } catch (error) {\n      console.warn(`Rate limit check failed for ${action}:`, error)\n      // On error, allow the request but log it\n      return {\n        allowed: true,\n        remainingRequests: 0,\n        resetTime: Date.now() + 60000 // 1 minute fallback\n      }\n    }\n  }\n\n  /**\n   * Reset rate limit for specific action\n   */\n  resetLimit(action: RateLimitAction, userId?: string): void {\n    try {\n      const storageKey = this.getStorageKey(action, userId)\n      localStorage.removeItem(storageKey)\n    } catch (error) {\n      console.warn(`Rate limit reset failed for ${action}:`, error)\n    }\n  }\n\n  /**\n   * Get current rate limit status\n   */\n  getStatus(action: RateLimitAction, userId?: string): {\n    count: number\n    remaining: number\n    resetTime: number\n  } | null {\n    try {\n      const config = RATE_LIMITS[action]\n      const storageKey = this.getStorageKey(action, userId)\n      const stored = localStorage.getItem(storageKey)\n      \n      if (!stored) {\n        return {\n          count: 0,\n          remaining: config.maxRequests,\n          resetTime: Date.now() + config.windowMinutes * 60 * 1000\n        }\n      }\n\n      const data: RateLimitData = JSON.parse(stored)\n      const windowMs = config.windowMinutes * 60 * 1000\n      const now = Date.now()\n\n      // Check if window expired\n      if (now - data.windowStart > windowMs) {\n        return {\n          count: 0,\n          remaining: config.maxRequests,\n          resetTime: now + windowMs\n        }\n      }\n\n      return {\n        count: data.count,\n        remaining: Math.max(0, config.maxRequests - data.count),\n        resetTime: data.windowStart + windowMs\n      }\n    } catch (error) {\n      console.warn(`Rate limit status check failed for ${action}:`, error)\n      return null\n    }\n  }\n}\n\n/**\n * Server-side rate limiting with Supabase\n */\nexport class ServerRateLimiter {\n  /**\n   * Check server-side rate limit using Supabase function\n   */\n  async checkLimit(\n    action: RateLimitAction,\n    userId?: string\n  ): Promise<{\n    allowed: boolean\n    remainingRequests: number\n    resetTime: number\n    retryAfter?: number\n  }> {\n    try {\n      const config = RATE_LIMITS[action]\n      \n      // Get current user if not provided\n      let targetUserId = userId\n      if (!targetUserId) {\n        const { data: { user } } = await supabase.auth.getUser()\n        if (!user) {\n          throw new Error('User not authenticated')\n        }\n        targetUserId = user.id\n      }\n\n      // Call Supabase rate limiting function\n      const { data, error } = await supabase.rpc('check_rate_limit', {\n        p_user_id: targetUserId,\n        p_action_type: action,\n        p_max_requests: config.maxRequests,\n        p_window_minutes: config.windowMinutes\n      })\n\n      if (error) {\n        console.error(`Server rate limit check failed for ${action}:`, error)\n        // On error, allow but log\n        return {\n          allowed: true,\n          remainingRequests: 0,\n          resetTime: Date.now() + config.windowMinutes * 60 * 1000\n        }\n      }\n\n      const allowed = data === true\n      const windowMs = config.windowMinutes * 60 * 1000\n      const resetTime = Date.now() + windowMs\n\n      if (!allowed) {\n        return {\n          allowed: false,\n          remainingRequests: 0,\n          resetTime,\n          retryAfter: Math.ceil(windowMs / 1000)\n        }\n      }\n\n      return {\n        allowed: true,\n        remainingRequests: config.maxRequests - 1, // Approximate\n        resetTime\n      }\n    } catch (error) {\n      console.error(`Server rate limit error for ${action}:`, error)\n      // On error, allow but log\n      return {\n        allowed: true,\n        remainingRequests: 0,\n        resetTime: Date.now() + 60000\n      }\n    }\n  }\n}\n\n// Global instances\nexport const clientRateLimiter = new ClientRateLimiter()\nexport const serverRateLimiter = new ServerRateLimiter()\n\n/**\n * Combined rate limiting check (client + server)\n */\nexport async function checkRateLimit(\n  action: RateLimitAction,\n  options: {\n    clientOnly?: boolean\n    serverOnly?: boolean\n    userId?: string\n  } = {}\n): Promise<{\n  allowed: boolean\n  remainingRequests: number\n  resetTime: number\n  retryAfter?: number\n  source: 'client' | 'server' | 'both'\n}> {\n  const { clientOnly = false, serverOnly = false, userId } = options\n\n  try {\n    // Client-side check\n    if (!serverOnly) {\n      const clientResult = await clientRateLimiter.checkLimit(action, userId)\n      if (!clientResult.allowed) {\n        return {\n          ...clientResult,\n          source: 'client'\n        }\n      }\n      \n      if (clientOnly) {\n        return {\n          ...clientResult,\n          source: 'client'\n        }\n      }\n    }\n\n    // Server-side check\n    if (!clientOnly) {\n      const serverResult = await serverRateLimiter.checkLimit(action, userId)\n      return {\n        ...serverResult,\n        source: serverOnly ? 'server' : 'both'\n      }\n    }\n\n    // Fallback (shouldn't reach here)\n    return {\n      allowed: true,\n      remainingRequests: 0,\n      resetTime: Date.now() + 60000,\n      source: 'client'\n    }\n  } catch (error) {\n    console.error(`Rate limit check failed for ${action}:`, error)\n    return {\n      allowed: true,\n      remainingRequests: 0,\n      resetTime: Date.now() + 60000,\n      source: 'client'\n    }\n  }\n}\n\n/**\n * Rate limit error class\n */\nexport class RateLimitError extends Error {\n  constructor(\n    public action: RateLimitAction,\n    public retryAfter: number,\n    public resetTime: number\n  ) {\n    super(`Rate limit exceeded for ${action}. Try again in ${retryAfter} seconds.`)\n    this.name = 'RateLimitError'\n  }\n}\n\n/**\n * Rate limiting hook for React components\n */\nexport function useRateLimit(action: RateLimitAction) {\n  return {\n    checkLimit: (options?: { clientOnly?: boolean; serverOnly?: boolean }) =>\n      checkRateLimit(action, options),\n    resetLimit: () => clientRateLimiter.resetLimit(action),\n    getStatus: () => clientRateLimiter.getStatus(action)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;CAGC,GAED;AAPA;;AAUO,MAAM,cAAc;IACzB,yBAAyB;IACzB,OAAO;QAAE,aAAa;QAAG,eAAe;IAAG;IAC3C,QAAQ;QAAE,aAAa;QAAG,eAAe;IAAG;IAC5C,gBAAgB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEpD,kBAAkB;IAClB,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,gBAAgB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEpD,iBAAiB;IACjB,eAAe;QAAE,aAAa;QAAI,eAAe;IAAG;IACpD,eAAe;QAAE,aAAa;QAAK,eAAe;IAAG;IACrD,eAAe;QAAE,aAAa;QAAI,eAAe;IAAG;IAEpD,kBAAkB;IAClB,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,mBAAmB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEvD,eAAe;IACf,aAAa;QAAE,aAAa;QAAG,eAAe;IAAG;IAEjD,cAAc;IACd,aAAa;QAAE,aAAa;QAAK,eAAe;IAAG;AACrD;AAaO,MAAM;IACH,cAAc,MAAuB,EAAE,MAAe,EAAU;QACtE,MAAM,aAAa,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG;QAC3C,OAAO,CAAC,WAAW,EAAE,SAAS,YAAY;IAC5C;IAEA;;GAEC,GACD,MAAM,WAAW,MAAuB,EAAE,MAAe,EAKtD;QACD,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAClC,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAE7C,mBAAmB;YACnB,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI;YAEJ,IAAI,CAAC,QAAQ;gBACX,gBAAgB;gBAChB,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,OAAO;oBACL,SAAS;oBACT,mBAAmB,OAAO,WAAW,GAAG;oBACxC,WAAW,MAAM;gBACnB;YACF;YAEA,OAAO,KAAK,KAAK,CAAC;YAElB,8BAA8B;YAC9B,IAAI,MAAM,KAAK,WAAW,GAAG,UAAU;gBACrC,eAAe;gBACf,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,OAAO;oBACL,SAAS;oBACT,mBAAmB,OAAO,WAAW,GAAG;oBACxC,WAAW,MAAM;gBACnB;YACF;YAEA,0BAA0B;YAC1B,IAAI,KAAK,KAAK,IAAI,OAAO,WAAW,EAAE;gBACpC,MAAM,YAAY,KAAK,WAAW,GAAG;gBACrC,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,YAAY,GAAG,IAAI;gBAEjD,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB;oBACA;gBACF;YACF;YAEA,oBAAoB;YACpB,KAAK,KAAK;YACV,KAAK,WAAW,GAAG;YACnB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAEhD,OAAO;gBACL,SAAS;gBACT,mBAAmB,OAAO,WAAW,GAAG,KAAK,KAAK;gBAClD,WAAW,KAAK,WAAW,GAAG;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE;YACvD,yCAAyC;YACzC,OAAO;gBACL,SAAS;gBACT,mBAAmB;gBACnB,WAAW,KAAK,GAAG,KAAK,MAAM,oBAAoB;YACpD;QACF;IACF;IAEA;;GAEC,GACD,WAAW,MAAuB,EAAE,MAAe,EAAQ;QACzD,IAAI;YACF,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE;QACzD;IACF;IAEA;;GAEC,GACD,UAAU,MAAuB,EAAE,MAAe,EAIzC;QACP,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAClC,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM,SAAS,aAAa,OAAO,CAAC;YAEpC,IAAI,CAAC,QAAQ;gBACX,OAAO;oBACL,OAAO;oBACP,WAAW,OAAO,WAAW;oBAC7B,WAAW,KAAK,GAAG,KAAK,OAAO,aAAa,GAAG,KAAK;gBACtD;YACF;YAEA,MAAM,OAAsB,KAAK,KAAK,CAAC;YACvC,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAC7C,MAAM,MAAM,KAAK,GAAG;YAEpB,0BAA0B;YAC1B,IAAI,MAAM,KAAK,WAAW,GAAG,UAAU;gBACrC,OAAO;oBACL,OAAO;oBACP,WAAW,OAAO,WAAW;oBAC7B,WAAW,MAAM;gBACnB;YACF;YAEA,OAAO;gBACL,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO,WAAW,GAAG,KAAK,KAAK;gBACtD,WAAW,KAAK,WAAW,GAAG;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAC9D,OAAO;QACT;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,MAAM,WACJ,MAAuB,EACvB,MAAe,EAMd;QACD,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAElC,mCAAmC;YACnC,IAAI,eAAe;YACnB,IAAI,CAAC,cAAc;gBACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBACA,eAAe,KAAK,EAAE;YACxB;YAEA,uCAAuC;YACvC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,oBAAoB;gBAC7D,WAAW;gBACX,eAAe;gBACf,gBAAgB,OAAO,WAAW;gBAClC,kBAAkB,OAAO,aAAa;YACxC;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;gBAC/D,0BAA0B;gBAC1B,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,WAAW,KAAK,GAAG,KAAK,OAAO,aAAa,GAAG,KAAK;gBACtD;YACF;YAEA,MAAM,UAAU,SAAS;YACzB,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAC7C,MAAM,YAAY,KAAK,GAAG,KAAK;YAE/B,IAAI,CAAC,SAAS;gBACZ,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB;oBACA,YAAY,KAAK,IAAI,CAAC,WAAW;gBACnC;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,mBAAmB,OAAO,WAAW,GAAG;gBACxC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE;YACxD,0BAA0B;YAC1B,OAAO;gBACL,SAAS;gBACT,mBAAmB;gBACnB,WAAW,KAAK,GAAG,KAAK;YAC1B;QACF;IACF;AACF;AAGO,MAAM,oBAAoB,IAAI;AAC9B,MAAM,oBAAoB,IAAI;AAK9B,eAAe,eACpB,MAAuB,EACvB,UAII,CAAC,CAAC;IAQN,MAAM,EAAE,aAAa,KAAK,EAAE,aAAa,KAAK,EAAE,MAAM,EAAE,GAAG;IAE3D,IAAI;QACF,oBAAoB;QACpB,IAAI,CAAC,YAAY;YACf,MAAM,eAAe,MAAM,kBAAkB,UAAU,CAAC,QAAQ;YAChE,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;gBACV;YACF;YAEA,IAAI,YAAY;gBACd,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;gBACV;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,CAAC,YAAY;YACf,MAAM,eAAe,MAAM,kBAAkB,UAAU,CAAC,QAAQ;YAChE,OAAO;gBACL,GAAG,YAAY;gBACf,QAAQ,aAAa,WAAW;YAClC;QACF;QAEA,kCAAkC;QAClC,OAAO;YACL,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE;QACxD,OAAO;YACL,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;QACV;IACF;AACF;AAKO,MAAM,uBAAuB;;;;IAClC,YACE,AAAO,MAAuB,EAC9B,AAAO,UAAkB,EACzB,AAAO,SAAiB,CACxB;QACA,KAAK,CAAC,CAAC,wBAAwB,EAAE,OAAO,eAAe,EAAE,WAAW,SAAS,CAAC,QAJvE,SAAA,aACA,aAAA,iBACA,YAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAKO,SAAS,aAAa,MAAuB;IAClD,OAAO;QACL,YAAY,CAAC,UACX,eAAe,QAAQ;QACzB,YAAY,IAAM,kBAAkB,UAAU,CAAC;QAC/C,WAAW,IAAM,kBAAkB,SAAS,CAAC;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-projects.ts"], "sourcesContent": ["'use client'\r\n\r\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\r\nimport { Database } from '@/lib/supabase'\r\nimport { canCreateProject, updateUsageStats, PLAN_LIMIT_MESSAGES } from '@/lib/plan-limits'\r\nimport {\r\n  validateProjectName,\r\n  formatValidationError\r\n} from '@/lib/project-validation'\r\nimport { checkRateLimit, RateLimitError } from '@/lib/rate-limiter'\r\nimport { OptimizedQueries, QueryPerformanceMonitor } from '@/lib/database-optimization'\r\nimport { CACHE_CONFIGS } from '@/lib/cache-strategies'\r\n\r\ntype Project = Database['public']['Tables']['projects']['Row']\r\ntype ProjectInsert = Database['public']['Tables']['projects']['Insert']\r\ntype ProjectUpdate = Database['public']['Tables']['projects']['Update']\r\n\r\n// Projeleri getir\r\nexport function useProjects() {\r\n  return useQuery({\r\n    queryKey: ['projects'],\r\n    queryFn: async (): Promise<Project[]> => {\r\n      console.log(`📁 [USE_PROJECTS] Fetching projects...`)\r\n\r\n      try {\r\n        // Check if we have a session first\r\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession()\r\n        console.log(`📁 [USE_PROJECTS] Session check:`, {\r\n          hasSession: !!session,\r\n          sessionError: sessionError?.message,\r\n          userId: session?.user?.id\r\n        })\r\n\r\n        const { data, error } = await supabase\r\n          .from('projects')\r\n          .select('*')\r\n          .order('created_at', { ascending: false })\r\n\r\n        if (error) {\r\n          console.error(`❌ [USE_PROJECTS] Error fetching projects:`, error)\r\n          throw new Error(error.message)\r\n        }\r\n\r\n        console.log(`✅ [USE_PROJECTS] Projects fetched:`, data?.length || 0, 'projects')\r\n        return data || []\r\n      } catch (err) {\r\n        console.error(`💥 [USE_PROJECTS] Exception:`, err)\r\n        throw err\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Tek proje getir\r\nexport function useProject(projectId: string | null) {\r\n  return useQuery({\r\n    queryKey: ['project', projectId],\r\n    queryFn: async (): Promise<Project | null> => {\r\n      if (!projectId) return null\r\n\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .select('*')\r\n        .eq('id', projectId)\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    enabled: !!projectId,\r\n  })\r\n}\r\n\r\n// Proje oluştur\r\nexport function useCreateProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (project: Omit<ProjectInsert, 'user_id'>): Promise<Project> => {\r\n      // Plan limiti kontrol et\r\n      const limitCheck = await canCreateProject()\r\n      if (!limitCheck.allowed) {\r\n        throw new Error(limitCheck.reason || PLAN_LIMIT_MESSAGES.PROJECT_LIMIT_REACHED)\r\n      }\r\n\r\n      const { data: { user }, error: userError } = await supabase.auth.getUser()\r\n\r\n      if (userError || !user) {\r\n        throw new Error('Kullanıcı oturumu bulunamadı')\r\n      }\r\n\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .insert({\r\n          ...project,\r\n          user_id: user.id,\r\n        })\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onSuccess: async () => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Proje güncelle\r\nexport function useUpdateProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ id, ...updates }: ProjectUpdate & { id: string }): Promise<Project> => {\r\n      console.log('🔄 [UPDATE_PROJECT] Starting update:', { id, updates })\r\n\r\n      // Önce mevcut kullanıcıyı kontrol et\r\n      const { data: user } = await supabase.auth.getUser()\r\n      if (!user.user) {\r\n        throw new Error('Kullanıcı girişi gerekli')\r\n      }\r\n\r\n      // Güncelleme işlemini yap - RLS politikaları otomatik olarak kontrol edecek\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .update({\r\n          ...updates,\r\n          updated_at: new Date().toISOString()\r\n        })\r\n        .eq('id', id)\r\n        .eq('user_id', user.user.id) // Ekstra güvenlik için user_id kontrolü\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        console.error('❌ [UPDATE_PROJECT] Database error:', error)\r\n        throw new Error(`Proje güncellenirken hata oluştu: ${error.message}`)\r\n      }\r\n\r\n      if (!data) {\r\n        throw new Error('Proje bulunamadı veya güncelleme yetkisi yok')\r\n      }\r\n\r\n      console.log('✅ [UPDATE_PROJECT] Success:', data)\r\n      return data\r\n    },\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      queryClient.invalidateQueries({ queryKey: ['project', data.id] })\r\n    },\r\n    onError: (error) => {\r\n      console.error('❌ [UPDATE_PROJECT] Mutation error:', error)\r\n    }\r\n  })\r\n}\r\n\r\n// Güvenli proje adı güncelleme\r\nexport function useUpdateProjectNameSecure() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      projectId,\r\n      newName\r\n    }: {\r\n      projectId: string;\r\n      newName: string\r\n    }): Promise<Project> => {\r\n      console.log(`🔒 [UPDATE_PROJECT_NAME] Starting secure update:`, { projectId, newName })\r\n\r\n      // Advanced rate limiting check\r\n      const rateLimitResult = await checkRateLimit('project_update')\r\n      if (!rateLimitResult.allowed) {\r\n        throw new RateLimitError(\r\n          'project_update',\r\n          rateLimitResult.retryAfter || 60,\r\n          rateLimitResult.resetTime\r\n        )\r\n      }\r\n\r\n      // Client-side validation\r\n      const validation = validateProjectName(newName)\r\n      if (!validation.isValid) {\r\n        throw new Error(validation.error || 'Geçersiz proje adı')\r\n      }\r\n\r\n      const sanitizedName = validation.sanitizedValue!\r\n\r\n      try {\r\n        // Güvenli database function'ını çağır\r\n        const { data, error } = await supabase.rpc('update_project_name_secure', {\r\n          p_project_id: projectId,\r\n          p_new_name: sanitizedName\r\n        })\r\n\r\n        if (error) {\r\n          console.error(`❌ [UPDATE_PROJECT_NAME] Database error:`, error)\r\n          throw new Error(formatValidationError(error.message))\r\n        }\r\n\r\n        // Function response kontrolü\r\n        if (!data?.success) {\r\n          console.error(`❌ [UPDATE_PROJECT_NAME] Function error:`, data)\r\n          throw new Error(data?.message || 'Proje adı güncellenemedi')\r\n        }\r\n\r\n        console.log(`✅ [UPDATE_PROJECT_NAME] Success:`, data.data)\r\n        return data.data as Project\r\n\r\n      } catch (err) {\r\n        console.error(`💥 [UPDATE_PROJECT_NAME] Exception:`, err)\r\n\r\n        // Error mesajını kullanıcı dostu hale getir\r\n        let errorMessage = err instanceof Error ? err.message : 'Bilinmeyen hata'\r\n\r\n        if (errorMessage.includes('unique_violation') || errorMessage.includes('DuplicateName')) {\r\n          errorMessage = 'Bu isimde bir proje zaten mevcut'\r\n        } else if (errorMessage.includes('check_violation') || errorMessage.includes('InvalidInput')) {\r\n          errorMessage = 'Proje adı geçersiz karakterler içeriyor'\r\n        } else if (errorMessage.includes('RateLimitExceeded')) {\r\n          errorMessage = 'Çok fazla güncelleme isteği. Lütfen bekleyin.'\r\n        }\r\n\r\n        throw new Error(errorMessage)\r\n      }\r\n    },\r\n    onMutate: async ({ projectId, newName }) => {\r\n      console.log(`🚀 [UPDATE_PROJECT_NAME] Starting optimistic update:`, { projectId, newName })\r\n\r\n      // Cancel outgoing refetches\r\n      await queryClient.cancelQueries({ queryKey: ['projects'] })\r\n      await queryClient.cancelQueries({ queryKey: ['project', projectId] })\r\n\r\n      // Snapshot previous values\r\n      const previousProjects = queryClient.getQueryData<Project[]>(['projects'])\r\n      const previousProject = queryClient.getQueryData<Project>(['project', projectId])\r\n\r\n      // Optimistically update projects list\r\n      if (previousProjects) {\r\n        queryClient.setQueryData<Project[]>(['projects'], (old) => {\r\n          if (!old) return old\r\n          return old.map(project =>\r\n            project.id === projectId\r\n              ? { ...project, name: newName.trim(), updated_at: new Date().toISOString() }\r\n              : project\r\n          )\r\n        })\r\n      }\r\n\r\n      // Optimistically update single project\r\n      if (previousProject) {\r\n        queryClient.setQueryData<Project>(['project', projectId], {\r\n          ...previousProject,\r\n          name: newName.trim(),\r\n          updated_at: new Date().toISOString()\r\n        })\r\n      }\r\n\r\n      return { previousProjects, previousProject }\r\n    },\r\n    onSuccess: (data) => {\r\n      console.log(`✅ [UPDATE_PROJECT_NAME] Success, updating cache with server data:`, data.id)\r\n\r\n      // Update with actual server data\r\n      queryClient.setQueryData<Project[]>(['projects'], (old) => {\r\n        if (!old) return old\r\n        return old.map(project =>\r\n          project.id === data.id ? { ...project, ...data } : project\r\n        )\r\n      })\r\n\r\n      queryClient.setQueryData<Project>(['project', data.id], data)\r\n    },\r\n    onError: (error, variables, context) => {\r\n      console.error(`💥 [UPDATE_PROJECT_NAME] Error, rolling back:`, error)\r\n\r\n      // Rollback optimistic updates\r\n      if (context?.previousProjects) {\r\n        queryClient.setQueryData(['projects'], context.previousProjects)\r\n      }\r\n      if (context?.previousProject) {\r\n        queryClient.setQueryData(['project', variables.projectId], context.previousProject)\r\n      }\r\n    },\r\n    onSettled: () => {\r\n      // Always refetch to ensure consistency\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n    }\r\n  })\r\n}\r\n\r\n// Proje sil\r\nexport function useDeleteProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (projectId: string): Promise<void> => {\r\n      const { error } = await supabase\r\n        .from('projects')\r\n        .delete()\r\n        .eq('id', projectId)\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n    },\r\n    onSuccess: async () => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n} "], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AAIA;AAVA;;;;;;AAmBO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAW;QACtB,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC;YAEpD,IAAI;gBACF,mCAAmC;gBACnC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;gBACjF,QAAQ,GAAG,CAAC,CAAC,gCAAgC,CAAC,EAAE;oBAC9C,YAAY,CAAC,CAAC;oBACd,cAAc,cAAc;oBAC5B,QAAQ,SAAS,MAAM;gBACzB;gBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,CAAC,yCAAyC,CAAC,EAAE;oBAC3D,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAC,CAAC,kCAAkC,CAAC,EAAE,MAAM,UAAU,GAAG;gBACrE,OAAO,QAAQ,EAAE;YACnB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;gBAC9C,MAAM;YACR;QACF;IACF;AACF;AAGO,SAAS,WAAW,SAAwB;IACjD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAU;QAChC,SAAS;YACP,IAAI,CAAC,WAAW,OAAO;YAEvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,WACT,MAAM;YAET,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;QACA,SAAS,CAAC,CAAC;IACb;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,yBAAyB;YACzB,MAAM,aAAa,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;YACxC,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,IAAI,MAAM,WAAW,MAAM,IAAI,4HAAA,CAAA,sBAAmB,CAAC,qBAAqB;YAChF;YAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAExE,IAAI,aAAa,CAAC,MAAM;gBACtB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,GAAG,OAAO;gBACV,SAAS,KAAK,EAAE;YAClB,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,qCAAqC;YACrC,IAAI;gBACF,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;gBAC1D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,2CAA2C;YAC1D;QACF;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,EAAE,EAAE,GAAG,SAAyC;YACnE,QAAQ,GAAG,CAAC,wCAAwC;gBAAE;gBAAI;YAAQ;YAElE,qCAAqC;YACrC,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,4EAA4E;YAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,GAAG,OAAO;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,EAAE,wCAAwC;aACpE,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,MAAM,OAAO,EAAE;YACtE;YAEA,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,OAAO;QACT;QACA,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW,KAAK,EAAE;iBAAC;YAAC;QACjE;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,SAAS,EACT,OAAO,EAIR;YACC,QAAQ,GAAG,CAAC,CAAC,gDAAgD,CAAC,EAAE;gBAAE;gBAAW;YAAQ;YAErF,+BAA+B;YAC/B,MAAM,kBAAkB,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;YAC7C,IAAI,CAAC,gBAAgB,OAAO,EAAE;gBAC5B,MAAM,IAAI,6HAAA,CAAA,iBAAc,CACtB,kBACA,gBAAgB,UAAU,IAAI,IAC9B,gBAAgB,SAAS;YAE7B;YAEA,yBAAyB;YACzB,MAAM,aAAa,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD,EAAE;YACvC,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,IAAI,MAAM,WAAW,KAAK,IAAI;YACtC;YAEA,MAAM,gBAAgB,WAAW,cAAc;YAE/C,IAAI;gBACF,sCAAsC;gBACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,8BAA8B;oBACvE,cAAc;oBACd,YAAY;gBACd;gBAEA,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;oBACzD,MAAM,IAAI,MAAM,CAAA,GAAA,mIAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,OAAO;gBACrD;gBAEA,6BAA6B;gBAC7B,IAAI,CAAC,MAAM,SAAS;oBAClB,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;oBACzD,MAAM,IAAI,MAAM,MAAM,WAAW;gBACnC;gBAEA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,CAAC,EAAE,KAAK,IAAI;gBACzD,OAAO,KAAK,IAAI;YAElB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,CAAC,mCAAmC,CAAC,EAAE;gBAErD,4CAA4C;gBAC5C,IAAI,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAExD,IAAI,aAAa,QAAQ,CAAC,uBAAuB,aAAa,QAAQ,CAAC,kBAAkB;oBACvF,eAAe;gBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB,aAAa,QAAQ,CAAC,iBAAiB;oBAC5F,eAAe;gBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB;oBACrD,eAAe;gBACjB;gBAEA,MAAM,IAAI,MAAM;YAClB;QACF;QACA,UAAU,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;YACrC,QAAQ,GAAG,CAAC,CAAC,oDAAoD,CAAC,EAAE;gBAAE;gBAAW;YAAQ;YAEzF,4BAA4B;YAC5B,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACzD,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;oBAAW;iBAAU;YAAC;YAEnE,2BAA2B;YAC3B,MAAM,mBAAmB,YAAY,YAAY,CAAY;gBAAC;aAAW;YACzE,MAAM,kBAAkB,YAAY,YAAY,CAAU;gBAAC;gBAAW;aAAU;YAEhF,sCAAsC;YACtC,IAAI,kBAAkB;gBACpB,YAAY,YAAY,CAAY;oBAAC;iBAAW,EAAE,CAAC;oBACjD,IAAI,CAAC,KAAK,OAAO;oBACjB,OAAO,IAAI,GAAG,CAAC,CAAA,UACb,QAAQ,EAAE,KAAK,YACX;4BAAE,GAAG,OAAO;4BAAE,MAAM,QAAQ,IAAI;4BAAI,YAAY,IAAI,OAAO,WAAW;wBAAG,IACzE;gBAER;YACF;YAEA,uCAAuC;YACvC,IAAI,iBAAiB;gBACnB,YAAY,YAAY,CAAU;oBAAC;oBAAW;iBAAU,EAAE;oBACxD,GAAG,eAAe;oBAClB,MAAM,QAAQ,IAAI;oBAClB,YAAY,IAAI,OAAO,WAAW;gBACpC;YACF;YAEA,OAAO;gBAAE;gBAAkB;YAAgB;QAC7C;QACA,WAAW,CAAC;YACV,QAAQ,GAAG,CAAC,CAAC,iEAAiE,CAAC,EAAE,KAAK,EAAE;YAExF,iCAAiC;YACjC,YAAY,YAAY,CAAY;gBAAC;aAAW,EAAE,CAAC;gBACjD,IAAI,CAAC,KAAK,OAAO;gBACjB,OAAO,IAAI,GAAG,CAAC,CAAA,UACb,QAAQ,EAAE,KAAK,KAAK,EAAE,GAAG;wBAAE,GAAG,OAAO;wBAAE,GAAG,IAAI;oBAAC,IAAI;YAEvD;YAEA,YAAY,YAAY,CAAU;gBAAC;gBAAW,KAAK,EAAE;aAAC,EAAE;QAC1D;QACA,SAAS,CAAC,OAAO,WAAW;YAC1B,QAAQ,KAAK,CAAC,CAAC,6CAA6C,CAAC,EAAE;YAE/D,8BAA8B;YAC9B,IAAI,SAAS,kBAAkB;gBAC7B,YAAY,YAAY,CAAC;oBAAC;iBAAW,EAAE,QAAQ,gBAAgB;YACjE;YACA,IAAI,SAAS,iBAAiB;gBAC5B,YAAY,YAAY,CAAC;oBAAC;oBAAW,UAAU,SAAS;iBAAC,EAAE,QAAQ,eAAe;YACpF;QACF;QACA,WAAW;YACT,uCAAuC;YACvC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;QACF;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,qCAAqC;YACrC,IAAI;gBACF,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;gBAC1D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,2CAA2C;YAC1D;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/project-name-editor.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef, useCallback, memo } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Edit2, Check, X, Loader2, CheckCircle } from 'lucide-react'\nimport { useUpdateProjectNameSecure, useProjects } from '@/hooks/use-projects'\nimport {\n  createAdvancedDebouncedValidator,\n  isSameProjectName\n} from '@/lib/project-validation'\nimport { toast } from 'sonner'\nimport { cn } from '@/lib/utils'\n\ninterface ProjectNameEditorProps {\n  projectId: string\n  currentName: string\n  onNameUpdated?: (newName: string) => void\n  className?: string\n  disabled?: boolean\n}\n\nconst ProjectNameEditor = memo(function ProjectNameEditor({\n  projectId,\n  currentName,\n  onNameUpdated,\n  className,\n  disabled = false\n}: ProjectNameEditorProps) {\n  const [isEditing, setIsEditing] = useState(false)\n  const [editValue, setEditValue] = useState(currentName)\n  const [validationError, setValidationError] = useState<string>('')\n  const [isValidating, setIsValidating] = useState(false)\n  const [validationSuccess, setValidationSuccess] = useState(false)\n  \n  const inputRef = useRef<HTMLInputElement>(null)\n  const updateMutation = useUpdateProjectNameSecure()\n  const { data: projects = [] } = useProjects()\n\n  // Advanced debounced validator with duplicate check\n  const debouncedValidator = useCallback(() => {\n    return createAdvancedDebouncedValidator(projects, projectId, 300)\n  }, [projects, projectId])();\n\n  // Edit mode'a geçiş\n  const startEditing = useCallback(() => {\n    if (disabled || updateMutation.isPending) return\n    \n    setIsEditing(true)\n    setEditValue(currentName)\n    setValidationError('')\n    setIsValidating(false)\n    setValidationSuccess(false)\n  }, [disabled, updateMutation.isPending, currentName])\n\n  // Edit mode'dan çıkış\n  const cancelEditing = useCallback(() => {\n    setIsEditing(false)\n    setEditValue(currentName)\n    setValidationError('')\n    setIsValidating(false)\n    setValidationSuccess(false)\n  }, [currentName])\n\n  // Kaydetme işlemi\n  const saveChanges = useCallback(async () => {\n    if (!editValue.trim() || validationError || isValidating) {\n      return\n    }\n\n    // Değişiklik var mı kontrol et\n    if (isSameProjectName(editValue, currentName)) {\n      cancelEditing()\n      return\n    }\n\n    try {\n      const result = await updateMutation.mutateAsync({\n        projectId,\n        newName: editValue.trim()\n      })\n\n      toast.success('Proje adı başarıyla güncellendi!')\n      setIsEditing(false)\n      onNameUpdated?.(result.name)\n      \n    } catch (error) {\n      console.error('Project name update error:', error)\n      const errorMessage = error instanceof Error ? error.message : 'Proje adı güncellenirken bir hata oluştu'\n      toast.error(errorMessage)\n      \n      // Input'a focus ver ki kullanıcı düzeltebilsin\n      setTimeout(() => {\n        inputRef.current?.focus()\n      }, 100)\n    }\n  }, [editValue, validationError, isValidating, currentName, projectId, updateMutation, onNameUpdated, cancelEditing])\n\n  // Input değişikliği\n  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value\n    setEditValue(value)\n    \n    if (value.trim()) {\n      setIsValidating(true)\n      setValidationSuccess(false)\n      debouncedValidator(value, (result) => {\n        setValidationError(result.isValid ? '' : result.error || '')\n        setValidationSuccess(result.isValid && !isSameProjectName(value, currentName))\n        setIsValidating(false)\n      })\n    } else {\n      setValidationError('Proje adı boş olamaz')\n      setValidationSuccess(false)\n      setIsValidating(false)\n    }\n  }, [debouncedValidator, currentName])\n\n  // Keyboard event handlers\n  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault()\n      saveChanges()\n    } else if (e.key === 'Escape') {\n      e.preventDefault()\n      cancelEditing()\n    }\n  }, [saveChanges, cancelEditing])\n\n  // Auto-focus when editing starts\n  useEffect(() => {\n    if (isEditing && inputRef.current) {\n      inputRef.current.focus()\n      inputRef.current.select()\n    }\n  }, [isEditing])\n\n  // Update editValue when currentName changes\n  useEffect(() => {\n    if (!isEditing) {\n      setEditValue(currentName)\n    }\n  }, [currentName, isEditing])\n\n  // Editing mode UI\n  if (isEditing) {\n    return (\n      <div className={cn(\"flex items-center gap-2 w-full\", className)}>\n        <div className=\"flex-1 relative\">\n          <Input\n            ref={inputRef}\n            value={editValue}\n            onChange={handleInputChange}\n            onKeyDown={handleKeyDown}\n            className={cn(\n              \"text-sm font-medium\",\n              validationError && \"border-red-500 focus:border-red-500\",\n              validationSuccess && \"border-green-500 focus:border-green-500\",\n              \"focus:ring-2 focus:ring-blue-500/20\"\n            )}\n            placeholder=\"Proje adı...\"\n            disabled={updateMutation.isPending}\n            maxLength={50}\n            aria-label=\"Proje adını düzenle\"\n            aria-describedby={`project-name-feedback-${projectId}`}\n            aria-invalid={!!validationError}\n          />\n\n          {/* Character counter */}\n          <div className=\"absolute top-full right-0 mt-1 text-xs text-gray-400 z-10\">\n            <span className=\"sr-only\">Karakter sayısı: </span>\n            {editValue.length}/50\n          </div>\n\n          {/* Validation feedback */}\n          {(validationError || isValidating || validationSuccess) && (\n            <div\n              id={`project-name-feedback-${projectId}`}\n              className=\"absolute top-full left-0 mt-1 text-xs z-10\"\n              role=\"status\"\n              aria-live=\"polite\"\n            >\n              {isValidating ? (\n                <span className=\"text-gray-500 flex items-center gap-1\">\n                  <Loader2 className=\"h-3 w-3 animate-spin\" />\n                  Kontrol ediliyor...\n                </span>\n              ) : validationError ? (\n                <span className=\"text-red-500 flex items-center gap-1\">\n                  <X className=\"h-3 w-3\" />\n                  {validationError}\n                </span>\n              ) : validationSuccess ? (\n                <span className=\"text-green-600 flex items-center gap-1\">\n                  <CheckCircle className=\"h-3 w-3\" />\n                  Proje adı kullanılabilir\n                </span>\n              ) : null}\n            </div>\n          )}\n        </div>\n\n        {/* Action buttons */}\n        <div className=\"flex items-center gap-1\">\n          <Button\n            size=\"sm\"\n            variant=\"ghost\"\n            onClick={saveChanges}\n            disabled={\n              updateMutation.isPending ||\n              !!validationError ||\n              isValidating ||\n              !editValue.trim() ||\n              isSameProjectName(editValue, currentName)\n            }\n            className=\"h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50 touch-target\"\n            title=\"Kaydet (Enter)\"\n            aria-label=\"Proje adını kaydet\"\n          >\n            {updateMutation.isPending ? (\n              <Loader2 className=\"h-4 w-4 animate-spin\" />\n            ) : (\n              <Check className=\"h-4 w-4\" />\n            )}\n          </Button>\n          \n          <Button\n            size=\"sm\"\n            variant=\"ghost\"\n            onClick={cancelEditing}\n            disabled={updateMutation.isPending}\n            className=\"h-8 w-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-50 touch-target\"\n            title=\"İptal (Esc)\"\n            aria-label=\"Düzenlemeyi iptal et\"\n          >\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n    )\n  }\n\n  // Display mode UI\n  return (\n    <div className={cn(\"flex items-center gap-2 group w-full\", className)}>\n      <span\n        className=\"flex-1 text-sm font-medium text-gray-900 truncate\"\n        title={currentName}\n      >\n        {currentName}\n      </span>\n\n      <Button\n        size=\"sm\"\n        variant=\"ghost\"\n        onClick={(e) => {\n          e.stopPropagation(); // Prevent triggering parent click handlers\n          startEditing();\n        }}\n        disabled={disabled || updateMutation.isPending}\n        className={cn(\n          \"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity touch-target\",\n          \"text-gray-400 hover:text-gray-600 hover:bg-gray-50\",\n          \"focus:opacity-100 focus:ring-2 focus:ring-blue-500/20\",\n          \"lg:opacity-0 opacity-100\" // Mobile'da her zaman görünür, desktop'ta sadece hover'da\n        )}\n        title=\"Proje adını düzenle\"\n        aria-label=\"Proje adını düzenle\"\n      >\n        <Edit2 className=\"h-3 w-3\" />\n      </Button>\n    </div>\n  )\n})\n\nexport { ProjectNameEditor }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAIA;AACA;AAZA;;;;;;;;;;AAsBA,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,SAAS,kBAAkB,EACxD,SAAS,EACT,WAAW,EACX,aAAa,EACb,SAAS,EACT,WAAW,KAAK,EACO;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,iBAAiB,CAAA,GAAA,+HAAA,CAAA,6BAA0B,AAAD;IAChD,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAE1C,oDAAoD;IACpD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,OAAO,CAAA,GAAA,mIAAA,CAAA,mCAAgC,AAAD,EAAE,UAAU,WAAW;IAC/D,GAAG;QAAC;QAAU;KAAU;IAExB,oBAAoB;IACpB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,YAAY,eAAe,SAAS,EAAE;QAE1C,aAAa;QACb,aAAa;QACb,mBAAmB;QACnB,gBAAgB;QAChB,qBAAqB;IACvB,GAAG;QAAC;QAAU,eAAe,SAAS;QAAE;KAAY;IAEpD,sBAAsB;IACtB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,aAAa;QACb,aAAa;QACb,mBAAmB;QACnB,gBAAgB;QAChB,qBAAqB;IACvB,GAAG;QAAC;KAAY;IAEhB,kBAAkB;IAClB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,CAAC,UAAU,IAAI,MAAM,mBAAmB,cAAc;YACxD;QACF;QAEA,+BAA+B;QAC/B,IAAI,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,cAAc;YAC7C;YACA;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,eAAe,WAAW,CAAC;gBAC9C;gBACA,SAAS,UAAU,IAAI;YACzB;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,aAAa;YACb,gBAAgB,OAAO,IAAI;QAE7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAEZ,+CAA+C;YAC/C,WAAW;gBACT,SAAS,OAAO,EAAE;YACpB,GAAG;QACL;IACF,GAAG;QAAC;QAAW;QAAiB;QAAc;QAAa;QAAW;QAAgB;QAAe;KAAc;IAEnH,oBAAoB;IACpB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,aAAa;QAEb,IAAI,MAAM,IAAI,IAAI;YAChB,gBAAgB;YAChB,qBAAqB;YACrB,mBAAmB,OAAO,CAAC;gBACzB,mBAAmB,OAAO,OAAO,GAAG,KAAK,OAAO,KAAK,IAAI;gBACzD,qBAAqB,OAAO,OAAO,IAAI,CAAC,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;gBACjE,gBAAgB;YAClB;QACF,OAAO;YACL,mBAAmB;YACnB,qBAAqB;YACrB,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAoB;KAAY;IAEpC,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,EAAE,cAAc;YAChB;QACF;IACF,GAAG;QAAC;QAAa;KAAc;IAE/B,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,SAAS,OAAO,EAAE;YACjC,SAAS,OAAO,CAAC,KAAK;YACtB,SAAS,OAAO,CAAC,MAAM;QACzB;IACF,GAAG;QAAC;KAAU;IAEd,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,aAAa;QACf;IACF,GAAG;QAAC;QAAa;KAAU;IAE3B,kBAAkB;IAClB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;;8BACnD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,KAAK;4BACL,OAAO;4BACP,UAAU;4BACV,WAAW;4BACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uBACA,mBAAmB,uCACnB,qBAAqB,2CACrB;4BAEF,aAAY;4BACZ,UAAU,eAAe,SAAS;4BAClC,WAAW;4BACX,cAAW;4BACX,oBAAkB,CAAC,sBAAsB,EAAE,WAAW;4BACtD,gBAAc,CAAC,CAAC;;;;;;sCAIlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAU;;;;;;gCACzB,UAAU,MAAM;gCAAC;;;;;;;wBAInB,CAAC,mBAAmB,gBAAgB,iBAAiB,mBACpD,8OAAC;4BACC,IAAI,CAAC,sBAAsB,EAAE,WAAW;4BACxC,WAAU;4BACV,MAAK;4BACL,aAAU;sCAET,6BACC,8OAAC;gCAAK,WAAU;;kDACd,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAyB;;;;;;uCAG5C,gCACF,8OAAC;gCAAK,WAAU;;kDACd,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCACZ;;;;;;uCAED,kCACF,8OAAC;gCAAK,WAAU;;kDACd,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;uCAGnC;;;;;;;;;;;;8BAMV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,UACE,eAAe,SAAS,IACxB,CAAC,CAAC,mBACF,gBACA,CAAC,UAAU,IAAI,MACf,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;4BAE/B,WAAU;4BACV,OAAM;4BACN,cAAW;sCAEV,eAAe,SAAS,iBACvB,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAEnB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAIrB,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,eAAe,SAAS;4BAClC,WAAU;4BACV,OAAM;4BACN,cAAW;sCAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAKvB;IAEA,kBAAkB;IAClB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;;0BACzD,8OAAC;gBACC,WAAU;gBACV,OAAO;0BAEN;;;;;;0BAGH,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,SAAS,CAAC;oBACR,EAAE,eAAe,IAAI,2CAA2C;oBAChE;gBACF;gBACA,UAAU,YAAY,eAAe,SAAS;gBAC9C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iFACA,sDACA,yDACA,2BAA2B,0DAA0D;;gBAEvF,OAAM;gBACN,cAAW;0BAEX,cAAA,8OAAC,kMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIzB", "debugId": null}}, {"offset": {"line": 1467, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/pen.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/lucide-react/src/icons/pen.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z',\n      key: '1a8usu',\n    },\n  ],\n];\n\n/**\n * @component @name Pen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuMTc0IDYuODEyYTEgMSAwIDAgMC0zLjk4Ni0zLjk4N0wzLjg0MiAxNi4xNzRhMiAyIDAgMCAwLS41LjgzbC0xLjMyMSA0LjM1MmEuNS41IDAgMCAwIC42MjMuNjIybDQuMzUzLTEuMzJhMiAyIDAgMCAwIC44My0uNDk3eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pen = createLucideIcon('pen', __iconNode);\n\nexport default Pen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/check.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/lucide-react/src/icons/check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M20 6 9 17l-5-5', key: '1gmf2c' }]];\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNiA5IDE3bC01LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('check', __iconNode);\n\nexport default Check;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,iBAAmB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAahF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/%40tanstack/query-core/src/queryObserver.ts"], "sourcesContent": ["import { focusManager } from './focusManager'\nimport { notifyManager } from './notifyManager'\nimport { fetchState } from './query'\nimport { Subscribable } from './subscribable'\nimport { pendingThenable } from './thenable'\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport type { FetchOptions, Query, QueryState } from './query'\nimport type { QueryClient } from './queryClient'\nimport type { PendingThenable, Thenable } from './thenable'\nimport type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\ninterface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  #client: QueryClient\n  #currentQuery: Query<TQueryFnData, TError, TQueryData, TQueryKey> = undefined!\n  #currentQueryInitialState: QueryState<TQueryData, TError> = undefined!\n  #currentResult: QueryObserverResult<TData, TError> = undefined!\n  #currentResultState?: QueryState<TQueryData, TError>\n  #currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  #currentThenable: Thenable<TData>\n  #selectError: TError | null\n  #selectFn?: (data: TQueryData) => TData\n  #selectResult?: TData\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData?: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  #staleTimeoutId?: ReturnType<typeof setTimeout>\n  #refetchIntervalId?: ReturnType<typeof setInterval>\n  #currentRefetchInterval?: number | false\n  #trackedProps = new Set<keyof QueryObserverResult>()\n\n  constructor(\n    client: QueryClient,\n    public options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.#client = client\n    this.#selectError = null\n    this.#currentThenable = pendingThenable()\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(\n        new Error('experimental_prefetchInRender feature flag is not enabled'),\n      )\n    }\n\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch()\n      } else {\n        this.updateResult()\n      }\n\n      this.#updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.#clearStaleTimeout()\n    this.#clearRefetchInterval()\n    this.#currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.#currentQuery\n\n    this.options = this.#client.defaultQueryOptions(options)\n\n    if (\n      this.options.enabled !== undefined &&\n      typeof this.options.enabled !== 'boolean' &&\n      typeof this.options.enabled !== 'function' &&\n      typeof resolveEnabled(this.options.enabled, this.#currentQuery) !==\n        'boolean'\n    ) {\n      throw new Error(\n        'Expected enabled to be a boolean or a callback that returns a boolean',\n      )\n    }\n\n    this.#updateQuery()\n    this.#currentQuery.setOptions(this.options)\n\n    if (\n      prevOptions._defaulted &&\n      !shallowEqualObjects(this.options, prevOptions)\n    ) {\n      this.#client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.#currentQuery,\n        observer: this,\n      })\n    }\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.#currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.#executeFetch()\n    }\n\n    // Update result\n    this.updateResult()\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.#currentQuery !== prevQuery ||\n        resolveEnabled(this.options.enabled, this.#currentQuery) !==\n          resolveEnabled(prevOptions.enabled, this.#currentQuery) ||\n        resolveStaleTime(this.options.staleTime, this.#currentQuery) !==\n          resolveStaleTime(prevOptions.staleTime, this.#currentQuery))\n    ) {\n      this.#updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.#computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.#currentQuery !== prevQuery ||\n        resolveEnabled(this.options.enabled, this.#currentQuery) !==\n          resolveEnabled(prevOptions.enabled, this.#currentQuery) ||\n        nextRefetchInterval !== this.#currentRefetchInterval)\n    ) {\n      this.#updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.#client.getQueryCache().build(this.#client, options)\n\n    const result = this.createResult(query, options)\n\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult every time\n      // an observer reads an optimistic value.\n\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.#currentResult = result\n      this.#currentResultOptions = this.options\n      this.#currentResultState = this.#currentQuery.state\n    }\n    return result\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.#currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n    onPropTracked?: (key: keyof QueryObserverResult) => void,\n  ): QueryObserverResult<TData, TError> {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key as keyof QueryObserverResult)\n        onPropTracked?.(key as keyof QueryObserverResult)\n        return Reflect.get(target, key)\n      },\n    })\n  }\n\n  trackProp(key: keyof QueryObserverResult) {\n    this.#trackedProps.add(key)\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.#currentQuery\n  }\n\n  refetch({ ...options }: RefetchOptions = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.#client.defaultQueryOptions(options)\n\n    const query = this.#client\n      .getQueryCache()\n      .build(this.#client, defaultedOptions)\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.#currentResult\n    })\n  }\n\n  #executeFetch(\n    fetchOptions?: Omit<ObserverFetchOptions, 'initialPromise'>,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.#updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.#currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  #updateStaleTimeout(): void {\n    this.#clearStaleTimeout()\n    const staleTime = resolveStaleTime(\n      this.options.staleTime,\n      this.#currentQuery,\n    )\n\n    if (isServer || this.#currentResult.isStale || !isValidTimeout(staleTime)) {\n      return\n    }\n\n    const time = timeUntilStale(this.#currentResult.dataUpdatedAt, staleTime)\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  #computeRefetchInterval() {\n    return (\n      (typeof this.options.refetchInterval === 'function'\n        ? this.options.refetchInterval(this.#currentQuery)\n        : this.options.refetchInterval) ?? false\n    )\n  }\n\n  #updateRefetchInterval(nextInterval: number | false): void {\n    this.#clearRefetchInterval()\n\n    this.#currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      resolveEnabled(this.options.enabled, this.#currentQuery) === false ||\n      !isValidTimeout(this.#currentRefetchInterval) ||\n      this.#currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.#refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.#executeFetch()\n      }\n    }, this.#currentRefetchInterval)\n  }\n\n  #updateTimers(): void {\n    this.#updateStaleTimeout()\n    this.#updateRefetchInterval(this.#computeRefetchInterval())\n  }\n\n  #clearStaleTimeout(): void {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId)\n      this.#staleTimeoutId = undefined\n    }\n  }\n\n  #clearRefetchInterval(): void {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId)\n      this.#refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.#currentQuery\n    const prevOptions = this.options\n    const prevResult = this.#currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.#currentResultState\n    const prevResultOptions = this.#currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.#currentQueryInitialState\n\n    const { state } = query\n    let newState = { ...state }\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...fetchState(state.data, query.options),\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        newState.fetchStatus = 'idle'\n      }\n    }\n\n    let { error, errorUpdatedAt, status } = newState\n\n    // Per default, use query data\n    data = newState.data as unknown as TData\n    let skipSelect = false\n\n    // use placeholderData if needed\n    if (\n      options.placeholderData !== undefined &&\n      data === undefined &&\n      status === 'pending'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n        // we have to skip select when reading this memoization\n        // because prevResult.data is already \"selected\"\n        skipSelect = true\n      } else {\n        // compute placeholderData\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (\n                options.placeholderData as unknown as PlaceholderDataFunction<TQueryData>\n              )(\n                this.#lastQueryWithDefinedData?.state.data,\n                this.#lastQueryWithDefinedData as any,\n              )\n            : options.placeholderData\n      }\n\n      if (placeholderData !== undefined) {\n        status = 'success'\n        data = replaceData(\n          prevResult?.data,\n          placeholderData as unknown,\n          options,\n        ) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    // Select data if needed\n    // this also runs placeholderData through the select function\n    if (options.select && data !== undefined && !skipSelect) {\n      // Memoize select result\n      if (\n        prevResult &&\n        data === prevResultState?.data &&\n        options.select === this.#selectFn\n      ) {\n        data = this.#selectResult\n      } else {\n        try {\n          this.#selectFn = options.select\n          data = options.select(data as any)\n          data = replaceData(prevResult?.data, data, options)\n          this.#selectResult = data\n          this.#selectError = null\n        } catch (selectError) {\n          this.#selectError = selectError as TError\n        }\n      }\n    }\n\n    if (this.#selectError) {\n      error = this.#selectError as any\n      data = this.#selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = newState.fetchStatus === 'fetching'\n    const isPending = status === 'pending'\n    const isError = status === 'error'\n\n    const isLoading = isPending && isFetching\n    const hasData = data !== undefined\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        newState.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === 'paused',\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable,\n      isEnabled: resolveEnabled(options.enabled, query) !== false,\n    }\n\n    const nextResult = result as QueryObserverResult<TData, TError>\n\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = (thenable: PendingThenable<TData>) => {\n        if (nextResult.status === 'error') {\n          thenable.reject(nextResult.error)\n        } else if (nextResult.data !== undefined) {\n          thenable.resolve(nextResult.data)\n        }\n      }\n\n      /**\n       * Create a new thenable and result promise when the results have changed\n       */\n      const recreateThenable = () => {\n        const pending =\n          (this.#currentThenable =\n          nextResult.promise =\n            pendingThenable())\n\n        finalizeThenableIfPossible(pending)\n      }\n\n      const prevThenable = this.#currentThenable\n      switch (prevThenable.status) {\n        case 'pending':\n          // Finalize the previous thenable if it was pending\n          // and we are still observing the same query\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable)\n          }\n          break\n        case 'fulfilled':\n          if (\n            nextResult.status === 'error' ||\n            nextResult.data !== prevThenable.value\n          ) {\n            recreateThenable()\n          }\n          break\n        case 'rejected':\n          if (\n            nextResult.status !== 'error' ||\n            nextResult.error !== prevThenable.reason\n          ) {\n            recreateThenable()\n          }\n          break\n      }\n    }\n\n    return nextResult\n  }\n\n  updateResult(): void {\n    const prevResult = this.#currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.#currentQuery, this.options)\n\n    this.#currentResultState = this.#currentQuery.state\n    this.#currentResultOptions = this.options\n\n    if (this.#currentResultState.data !== undefined) {\n      this.#lastQueryWithDefinedData = this.#currentQuery\n    }\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.#currentResult = nextResult\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n      const notifyOnChangePropsValue =\n        typeof notifyOnChangeProps === 'function'\n          ? notifyOnChangeProps()\n          : notifyOnChangeProps\n\n      if (\n        notifyOnChangePropsValue === 'all' ||\n        (!notifyOnChangePropsValue && !this.#trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps,\n      )\n\n      if (this.options.throwOnError) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey]\n\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    this.#notify({ listeners: shouldNotifyListeners() })\n  }\n\n  #updateQuery(): void {\n    const query = this.#client.getQueryCache().build(this.#client, this.options)\n\n    if (query === this.#currentQuery) {\n      return\n    }\n\n    const prevQuery = this.#currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.#currentQuery = query\n    this.#currentQueryInitialState = query.state\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(): void {\n    this.updateResult()\n\n    if (this.hasListeners()) {\n      this.#updateTimers()\n    }\n  }\n\n  #notify(notifyOptions: { listeners: boolean }): void {\n    notifyManager.batch(() => {\n      // First, trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: 'observerResultsUpdated',\n      })\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    resolveEnabled(options.enabled, query) !== false &&\n    query.state.data === undefined &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.data !== undefined &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: (typeof options)['refetchOnMount'] &\n    (typeof options)['refetchOnWindowFocus'] &\n    (typeof options)['refetchOnReconnect'],\n) {\n  if (\n    resolveEnabled(options.enabled, query) !== false &&\n    resolveStaleTime(options.staleTime, query) !== 'static'\n  ) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    (query !== prevQuery ||\n      resolveEnabled(prevOptions.enabled, query) === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    resolveEnabled(options.enabled, query) !== false &&\n    query.isStaleByTime(resolveStaleTime(options.staleTime, query))\n  )\n}\n\n// this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\nfunction shouldAssignObserverCurrentProperties<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  optimisticResult: QueryObserverResult<TData, TError>,\n) {\n  // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true\n  }\n\n  // basically, just keep previous properties if nothing changed\n  return false\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAC7B,SAAS,uBAAuB;AAChC;;;;;;;AAiCO,IAAM,gBAAN,6LAMG,eAAA,CAAmD;IAyB3D,YACE,MAAA,EACO,OAAA,CAOP;QACA,KAAA,CAAM;QARC,IAAA,CAAA,OAAA,GAAA;QAUP,IAAA,EAAK,MAAA,GAAU;QACf,IAAA,EAAK,WAAA,GAAe;QACpB,IAAA,EAAK,eAAA,GAAmB,iMAAA,CAAgB;QACxC,IAAI,CAAC,IAAA,CAAK,OAAA,CAAQ,6BAAA,EAA+B;YAC/C,IAAA,EAAK,eAAA,CAAiB,MAAA,CACpB,IAAI,MAAM,2DAA2D;QAEzE;QAEA,IAAA,CAAK,WAAA,CAAY;QACjB,IAAA,CAAK,UAAA,CAAW,OAAO;IACzB;IA/CA,OAAA,CAAA;KACA,YAAA,GAAoE,KAAA,EAAA;KACpE,wBAAA,GAA4D,KAAA,EAAA;KAC5D,aAAA,GAAqD,KAAA,EAAA;KACrD,kBAAA,CAAA;KACA,oBAAA,CAAA;IAOA,gBAAA,CAAA;KACA,WAAA,CAAA;KACA,QAAA,CAAA;KACA,YAAA,CAAA;IAAA,iEAAA;IAAA,mGAAA;KAGA,wBAAA,CAAA;KACA,cAAA,CAAA;KACA,iBAAA,CAAA;KACA,sBAAA,CAAA;KACA,YAAA,GAAgB,aAAA,GAAA,IAAI,IAA+B,EAAA;IA2BzC,cAAoB;QAC5B,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,IAAI;IACvC;IAEU,cAAoB;QAC5B,IAAI,IAAA,CAAK,SAAA,CAAU,IAAA,KAAS,GAAG;YAC7B,IAAA,EAAK,YAAA,CAAc,WAAA,CAAY,IAAI;YAEnC,IAAI,mBAAmB,IAAA,EAAK,YAAA,EAAe,IAAA,CAAK,OAAO,GAAG;gBACxD,IAAA,EAAK,YAAA,CAAc;YACrB,OAAO;gBACL,IAAA,CAAK,YAAA,CAAa;YACpB;YAEA,IAAA,CAAK,aAAA,CAAc;QACrB;IACF;IAEU,gBAAsB;QAC9B,IAAI,CAAC,IAAA,CAAK,YAAA,CAAa,GAAG;YACxB,IAAA,CAAK,OAAA,CAAQ;QACf;IACF;IAEA,yBAAkC;QAChC,OAAO,cACL,IAAA,CAAK,aAAA,EACL,IAAA,CAAK,OAAA,EACL,IAAA,CAAK,OAAA,CAAQ,kBAAA;IAEjB;IAEA,2BAAoC;QAClC,OAAO,cACL,IAAA,EAAK,YAAA,EACL,IAAA,CAAK,OAAA,EACL,IAAA,CAAK,OAAA,CAAQ,oBAAA;IAEjB;IAEA,UAAgB;QACd,IAAA,CAAK,SAAA,GAAY,aAAA,GAAA,IAAI,IAAI;QACzB,IAAA,EAAK,iBAAA,CAAmB;QACxB,IAAA,EAAK,oBAAA,CAAsB;QAC3B,IAAA,EAAK,YAAA,CAAc,cAAA,CAAe,IAAI;IACxC;IAEA,WACE,OAAA,EAOM;QACN,MAAM,cAAc,IAAA,CAAK,OAAA;QACzB,MAAM,YAAY,IAAA,EAAK,YAAA;QAEvB,IAAA,CAAK,OAAA,GAAU,IAAA,EAAK,MAAA,CAAQ,mBAAA,CAAoB,OAAO;QAEvD,IACE,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,KAAA,KACzB,OAAO,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,aAChC,OAAO,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,cAChC,mLAAO,iBAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS,IAAA,EAAK,YAAa,MAC5D,WACF;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,IAAA,EAAK,WAAA,CAAa;QAClB,IAAA,CAAK,aAAA,CAAc,UAAA,CAAW,IAAA,CAAK,OAAO;QAE1C,IACE,YAAY,UAAA,IACZ,CAAC,kMAAA,EAAoB,IAAA,CAAK,OAAA,EAAS,WAAW,GAC9C;YACA,IAAA,EAAK,MAAA,CAAQ,aAAA,CAAc,EAAE,MAAA,CAAO;gBAClC,MAAM;gBACN,OAAO,IAAA,EAAK,YAAA;gBACZ,UAAU,IAAA;YACZ,CAAC;QACH;QAEA,MAAM,UAAU,IAAA,CAAK,YAAA,CAAa;QAGlC,IACE,WACA,sBACE,IAAA,EAAK,YAAA,EACL,WACA,IAAA,CAAK,OAAA,EACL,cAEF;YACA,IAAA,EAAK,YAAA,CAAc;QACrB;QAGA,IAAA,CAAK,YAAA,CAAa;QAGlB,IACE,WAAA,CACC,IAAA,EAAK,YAAA,KAAkB,wLACtB,kBAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS,IAAA,EAAK,YAAa,kLACrD,iBAAA,EAAe,YAAY,OAAA,EAAS,IAAA,EAAK,YAAa,iLACxD,mBAAA,EAAiB,IAAA,CAAK,OAAA,CAAQ,SAAA,EAAW,IAAA,EAAK,YAAa,MACzD,+LAAA,EAAiB,YAAY,SAAA,EAAW,IAAA,EAAK,YAAa,CAAA,GAC9D;YACA,IAAA,CAAK,mBAAA,CAAoB;QAC3B;QAEA,MAAM,sBAAsB,IAAA,EAAK,sBAAA,CAAwB;QAGzD,IACE,WAAA,CACC,IAAA,EAAK,YAAA,KAAkB,wLACtB,kBAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS,IAAA,EAAK,YAAa,iLACrD,kBAAA,EAAe,YAAY,OAAA,EAAS,IAAA,EAAK,YAAa,KACxD,wBAAwB,IAAA,EAAK,sBAAA,GAC/B;YACA,IAAA,CAAK,sBAAA,CAAuB,mBAAmB;QACjD;IACF;IAEA,oBACE,OAAA,EAOoC;QACpC,MAAM,QAAQ,IAAA,EAAK,MAAA,CAAQ,aAAA,CAAc,EAAE,KAAA,CAAM,IAAA,EAAK,MAAA,EAAS,OAAO;QAEtE,MAAM,SAAS,IAAA,CAAK,YAAA,CAAa,OAAO,OAAO;QAE/C,IAAI,sCAAsC,IAAA,EAAM,MAAM,GAAG;YAiBvD,IAAA,EAAK,aAAA,GAAiB;YACtB,IAAA,EAAK,oBAAA,GAAwB,IAAA,CAAK,OAAA;YAClC,IAAA,EAAK,kBAAA,GAAsB,IAAA,EAAK,YAAA,CAAc,KAAA;QAChD;QACA,OAAO;IACT;IAEA,mBAAuD;QACrD,OAAO,IAAA,EAAK,aAAA;IACd;IAEA,YACE,MAAA,EACA,aAAA,EACoC;QACpC,OAAO,IAAI,MAAM,QAAQ;YACvB,KAAK,CAAC,QAAQ,QAAQ;gBACpB,IAAA,CAAK,SAAA,CAAU,GAAgC;gBAC/C,gBAAgB,GAAgC;gBAChD,OAAO,QAAQ,GAAA,CAAI,QAAQ,GAAG;YAChC;QACF,CAAC;IACH;IAEA,UAAU,GAAA,EAAgC;QACxC,IAAA,EAAK,YAAA,CAAc,GAAA,CAAI,GAAG;IAC5B;IAEA,kBAAsE;QACpE,OAAO,IAAA,EAAK,YAAA;IACd;IAEA,QAAQ,EAAE,GAAG,QAAQ,CAAA,GAAoB,CAAC,CAAA,EAExC;QACA,OAAO,IAAA,CAAK,KAAA,CAAM;YAChB,GAAG,OAAA;QACL,CAAC;IACH;IAEA,gBACE,OAAA,EAO6C;QAC7C,MAAM,mBAAmB,IAAA,EAAK,MAAA,CAAQ,mBAAA,CAAoB,OAAO;QAEjE,MAAM,QAAQ,IAAA,EAAK,MAAA,CAChB,aAAA,CAAc,EACd,KAAA,CAAM,IAAA,EAAK,MAAA,EAAS,gBAAgB;QAEvC,OAAO,MAAM,KAAA,CAAM,EAAE,IAAA,CAAK,IAAM,IAAA,CAAK,YAAA,CAAa,OAAO,gBAAgB,CAAC;IAC5E;IAEU,MACR,YAAA,EAC6C;QAC7C,OAAO,IAAA,EAAK,YAAA,CAAc;YACxB,GAAG,YAAA;YACH,eAAe,aAAa,aAAA,IAAiB;QAC/C,CAAC,EAAE,IAAA,CAAK,MAAM;YACZ,IAAA,CAAK,YAAA,CAAa;YAClB,OAAO,IAAA,EAAK,aAAA;QACd,CAAC;IACH;KAEA,YAAA,CACE,YAAA,EACiC;QAEjC,IAAA,EAAK,WAAA,CAAa;QAGlB,IAAI,UAA2C,IAAA,CAAK,aAAA,CAAc,KAAA,CAChE,IAAA,CAAK,OAAA,EACL;QAGF,IAAI,CAAC,cAAc,cAAc;YAC/B,UAAU,QAAQ,KAAA,yKAAM,OAAI;QAC9B;QAEA,OAAO;IACT;KAEA,kBAAA,GAA4B;QAC1B,IAAA,EAAK,iBAAA,CAAmB;QACxB,MAAM,wLAAY,mBAAA,EAChB,IAAA,CAAK,OAAA,CAAQ,SAAA,EACb,IAAA,EAAK,YAAA;QAGP,4KAAI,WAAA,IAAY,IAAA,EAAK,aAAA,CAAe,OAAA,IAAW,CAAC,6LAAA,EAAe,SAAS,GAAG;YACzE;QACF;QAEA,MAAM,QAAO,4LAAA,EAAe,IAAA,EAAK,aAAA,CAAe,aAAA,EAAe,SAAS;QAIxE,MAAM,UAAU,OAAO;QAEvB,IAAA,EAAK,cAAA,GAAkB,WAAW,MAAM;YACtC,IAAI,CAAC,IAAA,EAAK,aAAA,CAAe,OAAA,EAAS;gBAChC,IAAA,CAAK,YAAA,CAAa;YACpB;QACF,GAAG,OAAO;IACZ;KAEA,sBAAA,GAA0B;QACxB,OAAA,CACG,OAAO,IAAA,CAAK,OAAA,CAAQ,eAAA,KAAoB,aACrC,IAAA,CAAK,OAAA,CAAQ,eAAA,CAAgB,IAAA,EAAK,YAAa,IAC/C,IAAA,CAAK,OAAA,CAAQ,eAAA,KAAoB;IAEzC;KAEA,qBAAA,CAAuB,YAAA,EAAoC;QACzD,IAAA,EAAK,oBAAA,CAAsB;QAE3B,IAAA,EAAK,sBAAA,GAA0B;QAE/B,4KACE,WAAA,gLACA,iBAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS,IAAA,EAAK,YAAa,MAAM,SAC7D,6KAAC,iBAAA,EAAe,IAAA,EAAK,sBAAuB,KAC5C,IAAA,EAAK,sBAAA,KAA4B,GACjC;YACA;QACF;QAEA,IAAA,EAAK,iBAAA,GAAqB,YAAY,MAAM;YAC1C,IACE,IAAA,CAAK,OAAA,CAAQ,2BAAA,mLACb,eAAA,CAAa,SAAA,CAAU,GACvB;gBACA,IAAA,EAAK,YAAA,CAAc;YACrB;QACF,GAAG,IAAA,EAAK,sBAAuB;IACjC;KAEA,YAAA,GAAsB;QACpB,IAAA,EAAK,kBAAA,CAAoB;QACzB,IAAA,EAAK,qBAAA,CAAuB,IAAA,EAAK,sBAAA,CAAwB,CAAC;IAC5D;KAEA,iBAAA,GAA2B;QACzB,IAAI,IAAA,EAAK,cAAA,EAAiB;YACxB,aAAa,IAAA,EAAK,cAAe;YACjC,IAAA,CAAK,eAAA,GAAkB,KAAA;QACzB;IACF;KAEA,oBAAA,GAA8B;QAC5B,IAAI,IAAA,EAAK,iBAAA,EAAoB;YAC3B,cAAc,IAAA,EAAK,iBAAkB;YACrC,IAAA,EAAK,iBAAA,GAAqB,KAAA;QAC5B;IACF;IAEU,aACR,KAAA,EACA,OAAA,EAOoC;QACpC,MAAM,YAAY,IAAA,CAAK,aAAA;QACvB,MAAM,cAAc,IAAA,CAAK,OAAA;QACzB,MAAM,aAAa,IAAA,EAAK,aAAA;QAGxB,MAAM,kBAAkB,IAAA,EAAK,kBAAA;QAC7B,MAAM,oBAAoB,IAAA,EAAK,oBAAA;QAC/B,MAAM,cAAc,UAAU;QAC9B,MAAM,oBAAoB,cACtB,MAAM,KAAA,GACN,IAAA,EAAK,wBAAA;QAET,MAAM,EAAE,KAAA,CAAM,CAAA,GAAI;QAClB,IAAI,WAAW;YAAE,GAAG,KAAA;QAAM;QAC1B,IAAI,oBAAoB;QACxB,IAAI;QAGJ,IAAI,QAAQ,kBAAA,EAAoB;YAC9B,MAAM,UAAU,IAAA,CAAK,YAAA,CAAa;YAElC,MAAM,eAAe,CAAC,WAAW,mBAAmB,OAAO,OAAO;YAElE,MAAM,kBACJ,WAAW,sBAAsB,OAAO,WAAW,SAAS,WAAW;YAEzE,IAAI,gBAAgB,iBAAiB;gBACnC,WAAW;oBACT,GAAG,QAAA;oBACH,+KAAG,aAAA,EAAW,MAAM,IAAA,EAAM,MAAM,OAAO,CAAA;gBACzC;YACF;YACA,IAAI,QAAQ,kBAAA,KAAuB,eAAe;gBAChD,SAAS,WAAA,GAAc;YACzB;QACF;QAEA,IAAI,EAAE,KAAA,EAAO,cAAA,EAAgB,MAAA,CAAO,CAAA,GAAI;QAGxC,OAAO,SAAS,IAAA;QAChB,IAAI,aAAa;QAGjB,IACE,QAAQ,eAAA,KAAoB,KAAA,KAC5B,SAAS,KAAA,KACT,WAAW,WACX;YACA,IAAI;YAGJ,IACE,YAAY,qBACZ,QAAQ,eAAA,KAAoB,mBAAmB,iBAC/C;gBACA,kBAAkB,WAAW,IAAA;gBAG7B,aAAa;YACf,OAAO;gBAEL,kBACE,OAAO,QAAQ,eAAA,KAAoB,aAE7B,QAAQ,eAAA,CAER,IAAA,EAAK,wBAAA,EAA2B,MAAM,MACtC,IAAA,EAAK,wBAAA,IAEP,QAAQ,eAAA;YAChB;YAEA,IAAI,oBAAoB,KAAA,GAAW;gBACjC,SAAS;gBACT,mLAAO,cAAA,EACL,YAAY,MACZ,iBACA;gBAEF,oBAAoB;YACtB;QACF;QAIA,IAAI,QAAQ,MAAA,IAAU,SAAS,KAAA,KAAa,CAAC,YAAY;YAEvD,IACE,cACA,SAAS,iBAAiB,QAC1B,QAAQ,MAAA,KAAW,IAAA,CAAK,SAAA,EACxB;gBACA,OAAO,IAAA,EAAK,YAAA;YACd,OAAO;gBACL,IAAI;oBACF,IAAA,EAAK,QAAA,GAAY,QAAQ,MAAA;oBACzB,OAAO,QAAQ,MAAA,CAAO,IAAW;oBACjC,mLAAO,cAAA,EAAY,YAAY,MAAM,MAAM,OAAO;oBAClD,IAAA,EAAK,YAAA,GAAgB;oBACrB,IAAA,EAAK,WAAA,GAAe;gBACtB,EAAA,OAAS,aAAa;oBACpB,IAAA,EAAK,WAAA,GAAe;gBACtB;YACF;QACF;QAEA,IAAI,IAAA,EAAK,WAAA,EAAc;YACrB,QAAQ,IAAA,EAAK,WAAA;YACb,OAAO,IAAA,EAAK,YAAA;YACZ,iBAAiB,KAAK,GAAA,CAAI;YAC1B,SAAS;QACX;QAEA,MAAM,aAAa,SAAS,WAAA,KAAgB;QAC5C,MAAM,YAAY,WAAW;QAC7B,MAAM,UAAU,WAAW;QAE3B,MAAM,YAAY,aAAa;QAC/B,MAAM,UAAU,SAAS,KAAA;QAEzB,MAAM,SAAiD;YACrD;YACA,aAAa,SAAS,WAAA;YACtB;YACA,WAAW,WAAW;YACtB;YACA,kBAAkB;YAClB;YACA;YACA,eAAe,SAAS,aAAA;YACxB;YACA;YACA,cAAc,SAAS,iBAAA;YACvB,eAAe,SAAS,kBAAA;YACxB,kBAAkB,SAAS,gBAAA;YAC3B,WAAW,SAAS,eAAA,GAAkB,KAAK,SAAS,gBAAA,GAAmB;YACvE,qBACE,SAAS,eAAA,GAAkB,kBAAkB,eAAA,IAC7C,SAAS,gBAAA,GAAmB,kBAAkB,gBAAA;YAChD;YACA,cAAc,cAAc,CAAC;YAC7B,gBAAgB,WAAW,CAAC;YAC5B,UAAU,SAAS,WAAA,KAAgB;YACnC;YACA,gBAAgB,WAAW;YAC3B,SAAS,QAAQ,OAAO,OAAO;YAC/B,SAAS,IAAA,CAAK,OAAA;YACd,SAAS,IAAA,EAAK,eAAA;YACd,uLAAW,iBAAA,EAAe,QAAQ,OAAA,EAAS,KAAK,MAAM;QACxD;QAEA,MAAM,aAAa;QAEnB,IAAI,IAAA,CAAK,OAAA,CAAQ,6BAAA,EAA+B;YAC9C,MAAM,6BAA6B,CAAC,aAAqC;gBACvE,IAAI,WAAW,MAAA,KAAW,SAAS;oBACjC,SAAS,MAAA,CAAO,WAAW,KAAK;gBAClC,OAAA,IAAW,WAAW,IAAA,KAAS,KAAA,GAAW;oBACxC,SAAS,OAAA,CAAQ,WAAW,IAAI;gBAClC;YACF;YAKA,MAAM,mBAAmB,MAAM;gBAC7B,MAAM,UACH,IAAA,CAAK,gBAAA,GACN,WAAW,OAAA,kLACT,kBAAA,CAAgB;gBAEpB,2BAA2B,OAAO;YACpC;YAEA,MAAM,eAAe,IAAA,EAAK,eAAA;YAC1B,OAAQ,aAAa,MAAA,EAAQ;gBAC3B,KAAK;oBAGH,IAAI,MAAM,SAAA,KAAc,UAAU,SAAA,EAAW;wBAC3C,2BAA2B,YAAY;oBACzC;oBACA;gBACF,KAAK;oBACH,IACE,WAAW,MAAA,KAAW,WACtB,WAAW,IAAA,KAAS,aAAa,KAAA,EACjC;wBACA,iBAAiB;oBACnB;oBACA;gBACF,KAAK;oBACH,IACE,WAAW,MAAA,KAAW,WACtB,WAAW,KAAA,KAAU,aAAa,MAAA,EAClC;wBACA,iBAAiB;oBACnB;oBACA;YACJ;QACF;QAEA,OAAO;IACT;IAEA,eAAqB;QACnB,MAAM,aAAa,IAAA,EAAK,aAAA;QAIxB,MAAM,aAAa,IAAA,CAAK,YAAA,CAAa,IAAA,EAAK,YAAA,EAAe,IAAA,CAAK,OAAO;QAErE,IAAA,EAAK,kBAAA,GAAsB,IAAA,EAAK,YAAA,CAAc,KAAA;QAC9C,IAAA,EAAK,oBAAA,GAAwB,IAAA,CAAK,OAAA;QAElC,IAAI,IAAA,EAAK,kBAAA,CAAoB,IAAA,KAAS,KAAA,GAAW;YAC/C,IAAA,EAAK,wBAAA,GAA4B,IAAA,EAAK,YAAA;QACxC;QAGA,KAAI,iMAAA,EAAoB,YAAY,UAAU,GAAG;YAC/C;QACF;QAEA,IAAA,EAAK,aAAA,GAAiB;QAEtB,MAAM,wBAAwB,MAAe;YAC3C,IAAI,CAAC,YAAY;gBACf,OAAO;YACT;YAEA,MAAM,EAAE,mBAAA,CAAoB,CAAA,GAAI,IAAA,CAAK,OAAA;YACrC,MAAM,2BACJ,OAAO,wBAAwB,aAC3B,oBAAoB,IACpB;YAEN,IACE,6BAA6B,SAC5B,CAAC,4BAA4B,CAAC,IAAA,EAAK,YAAA,CAAc,IAAA,EAClD;gBACA,OAAO;YACT;YAEA,MAAM,gBAAgB,IAAI,IACxB,4BAA4B,IAAA,EAAK,YAAA;YAGnC,IAAI,IAAA,CAAK,OAAA,CAAQ,YAAA,EAAc;gBAC7B,cAAc,GAAA,CAAI,OAAO;YAC3B;YAEA,OAAO,OAAO,IAAA,CAAK,IAAA,EAAK,aAAc,EAAE,IAAA,CAAK,CAAC,QAAQ;gBACpD,MAAM,WAAW;gBACjB,MAAM,UAAU,IAAA,EAAK,aAAA,CAAe,QAAQ,CAAA,KAAM,UAAA,CAAW,QAAQ,CAAA;gBAErE,OAAO,WAAW,cAAc,GAAA,CAAI,QAAQ;YAC9C,CAAC;QACH;QAEA,IAAA,EAAK,MAAA,CAAQ;YAAE,WAAW,sBAAsB;QAAE,CAAC;IACrD;KAEA,WAAA,GAAqB;QACnB,MAAM,QAAQ,IAAA,EAAK,MAAA,CAAQ,aAAA,CAAc,EAAE,KAAA,CAAM,IAAA,EAAK,MAAA,EAAS,IAAA,CAAK,OAAO;QAE3E,IAAI,UAAU,IAAA,EAAK,YAAA,EAAe;YAChC;QACF;QAEA,MAAM,YAAY,IAAA,EAAK,YAAA;QAGvB,IAAA,EAAK,YAAA,GAAgB;QACrB,IAAA,EAAK,wBAAA,GAA4B,MAAM,KAAA;QAEvC,IAAI,IAAA,CAAK,YAAA,CAAa,GAAG;YACvB,WAAW,eAAe,IAAI;YAC9B,MAAM,WAAA,CAAY,IAAI;QACxB;IACF;IAEA,gBAAsB;QACpB,IAAA,CAAK,YAAA,CAAa;QAElB,IAAI,IAAA,CAAK,YAAA,CAAa,GAAG;YACvB,IAAA,EAAK,YAAA,CAAc;QACrB;IACF;KAEA,MAAA,CAAQ,aAAA,EAA6C;QACnD,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YAExB,IAAI,cAAc,SAAA,EAAW;gBAC3B,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;oBACnC,SAAS,IAAA,CAAK,cAAc;gBAC9B,CAAC;YACH;YAGA,IAAA,EAAK,MAAA,CAAQ,aAAA,CAAc,EAAE,MAAA,CAAO;gBAClC,OAAO,IAAA,EAAK,YAAA;gBACZ,MAAM;YACR,CAAC;QACH,CAAC;IACH;AACF;AAEA,SAAS,kBACP,KAAA,EACA,OAAA,EACS;IACT,mLACE,iBAAA,EAAe,QAAQ,OAAA,EAAS,KAAK,MAAM,SAC3C,MAAM,KAAA,CAAM,IAAA,KAAS,KAAA,KACrB,CAAA,CAAE,MAAM,KAAA,CAAM,MAAA,KAAW,WAAW,QAAQ,YAAA,KAAiB,KAAA;AAEjE;AAEA,SAAS,mBACP,KAAA,EACA,OAAA,EACS;IACT,OACE,kBAAkB,OAAO,OAAO,KAC/B,MAAM,KAAA,CAAM,IAAA,KAAS,KAAA,KACpB,cAAc,OAAO,SAAS,QAAQ,cAAc;AAE1D;AAEA,SAAS,cACP,KAAA,EACA,OAAA,EACA,KAAA,EAGA;IACA,gLACE,iBAAA,EAAe,QAAQ,OAAA,EAAS,KAAK,MAAM,qLAC3C,mBAAA,EAAiB,QAAQ,SAAA,EAAW,KAAK,MAAM,UAC/C;QACA,MAAM,QAAQ,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;QAE3D,OAAO,UAAU,YAAa,UAAU,SAAS,QAAQ,OAAO,OAAO;IACzE;IACA,OAAO;AACT;AAEA,SAAS,sBACP,KAAA,EACA,SAAA,EACA,OAAA,EACA,WAAA,EACS;IACT,OAAA,CACG,UAAU,aACT,6LAAA,EAAe,YAAY,OAAA,EAAS,KAAK,MAAM,KAAA,KAAA,CAChD,CAAC,QAAQ,QAAA,IAAY,MAAM,KAAA,CAAM,MAAA,KAAW,OAAA,KAC7C,QAAQ,OAAO,OAAO;AAE1B;AAEA,SAAS,QACP,KAAA,EACA,OAAA,EACS;IACT,mLACE,iBAAA,EAAe,QAAQ,OAAA,EAAS,KAAK,MAAM,SAC3C,MAAM,aAAA,6KAAc,mBAAA,EAAiB,QAAQ,SAAA,EAAW,KAAK,CAAC;AAElE;AAIA,SAAS,sCAOP,QAAA,EACA,gBAAA,EACA;IAGA,IAAI,6KAAC,sBAAA,EAAoB,SAAS,gBAAA,CAAiB,GAAG,gBAAgB,GAAG;QACvE,OAAO;IACT;IAGA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1975, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/%40tanstack/react-query/src/QueryErrorResetBoundary.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\n// CONTEXT\nexport type QueryErrorResetFunction = () => void\nexport type QueryErrorIsResetFunction = () => boolean\nexport type QueryErrorClearResetFunction = () => void\n\nexport interface QueryErrorResetBoundaryValue {\n  clearReset: QueryErrorClearResetFunction\n  isReset: QueryErrorIsResetFunction\n  reset: QueryErrorResetFunction\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport type QueryErrorResetBoundaryFunction = (\n  value: QueryErrorResetBoundaryValue,\n) => React.ReactNode\n\nexport interface QueryErrorResetBoundaryProps {\n  children: QueryErrorResetBoundaryFunction | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const [value] = React.useState(() => createValue())\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function' ? children(value) : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA,YAAY,WAAW;AAkDnB;;;;AArCJ,SAAS,cAA4C;IACnD,IAAI,UAAU;IACd,OAAO;QACL,YAAY,MAAM;YAChB,UAAU;QACZ;QACA,OAAO,MAAM;YACX,UAAU;QACZ;QACA,SAAS,MAAM;YACb,OAAO;QACT;IACF;AACF;AAEA,IAAM,uOAAuC,gBAAA,CAAc,YAAY,CAAC;AAIjE,IAAM,6BAA6B,0MAClC,aAAA,CAAW,8BAA8B;AAY1C,IAAM,0BAA0B,CAAC,EACtC,QAAA,EACF,KAAoC;IAClC,MAAM,CAAC,KAAK,CAAA,yMAAU,WAAA,CAAS,IAAM,YAAY,CAAC;IAClD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,+BAA+B,QAAA,EAA/B;QAAwC;QACtC,UAAA,OAAO,aAAa,aAAa,SAAS,KAAK,IAAI;IAAA,CACtD;AAEJ", "debugId": null}}, {"offset": {"line": 2014, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/%40tanstack/react-query/src/errorBoundaryUtils.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { shouldThrowError } from '@tanstack/query-core'\nimport type {\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserverResult,\n  ThrowOnError,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const ensurePreventErrorBoundaryRetry = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  if (\n    options.suspense ||\n    options.throwOnError ||\n    options.experimental_prefetchInRender\n  ) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false\n    }\n  }\n}\n\nexport const useClearResetErrorBoundary = (\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset()\n  }, [errorResetBoundary])\n}\n\nexport const getHasError = <\n  TData,\n  TError,\n  TQueryFnData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>({\n  result,\n  errorResetBoundary,\n  throwOnError,\n  query,\n  suspense,\n}: {\n  result: QueryObserverResult<TData, TError>\n  errorResetBoundary: QueryErrorResetBoundaryValue\n  throwOnError: ThrowOnError<TQueryFnData, TError, TQueryData, TQueryKey>\n  query: Query<TQueryFnData, TError, TQueryData, TQueryKey> | undefined\n  suspense: boolean | undefined\n}) => {\n  return (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    query &&\n    ((suspense && result.data === undefined) ||\n      shouldThrowError(throwOnError, [result.error, query]))\n  )\n}\n"], "names": [], "mappings": ";;;;;;AACA,YAAY,WAAW;AACvB,SAAS,wBAAwB;;;;AAU1B,IAAM,kCAAkC,CAO7C,SAOA,uBACG;IACH,IACE,QAAQ,QAAA,IACR,QAAQ,YAAA,IACR,QAAQ,6BAAA,EACR;QAEA,IAAI,CAAC,mBAAmB,OAAA,CAAQ,GAAG;YACjC,QAAQ,YAAA,GAAe;QACzB;IACF;AACF;AAEO,IAAM,6BAA6B,CACxC,uBACG;0MACG,YAAA,CAAU,MAAM;QACpB,mBAAmB,UAAA,CAAW;IAChC,GAAG;QAAC,kBAAkB;KAAC;AACzB;AAEO,IAAM,cAAc,CAMzB,EACA,MAAA,EACA,kBAAA,EACA,YAAA,EACA,KAAA,EACA,QAAA,EACF,KAMM;IACJ,OACE,OAAO,OAAA,IACP,CAAC,mBAAmB,OAAA,CAAQ,KAC5B,CAAC,OAAO,UAAA,IACR,SAAA,CACE,YAAY,OAAO,IAAA,KAAS,KAAA,iLAC5B,mBAAA,EAAiB,cAAc;QAAC,OAAO,KAAA;QAAO,KAAK;KAAC,CAAA;AAE1D", "debugId": null}}, {"offset": {"line": 2051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/%40tanstack/react-query/src/IsRestoringProvider.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nconst IsRestoringContext = React.createContext(false)\n\nexport const useIsRestoring = () => React.useContext(IsRestoringContext)\nexport const IsRestoringProvider = IsRestoringContext.Provider\n"], "names": [], "mappings": ";;;;;AACA,YAAY,WAAW;;;AAEvB,IAAM,2NAA2B,gBAAA,CAAc,KAAK;AAE7C,IAAM,iBAAiB,0MAAY,aAAA,CAAW,kBAAkB;AAChE,IAAM,sBAAsB,mBAAmB,QAAA", "debugId": null}}, {"offset": {"line": 2068, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/%40tanstack/react-query/src/suspense.ts"], "sourcesContent": ["import type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const defaultThrowOnError = <\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  _error: TError,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n) => query.state.data === undefined\n\nexport const ensureSuspenseTimers = (\n  defaultedOptions: DefaultedQueryObserverOptions<any, any, any, any, any>,\n) => {\n  if (defaultedOptions.suspense) {\n    // Handle staleTime to ensure minimum 1000ms in Suspense mode\n    // This prevents unnecessary refetching when components remount after suspending\n\n    const clamp = (value: number | 'static' | undefined) =>\n      value === 'static' ? value : Math.max(value ?? 1000, 1000)\n\n    const originalStaleTime = defaultedOptions.staleTime\n    defaultedOptions.staleTime =\n      typeof originalStaleTime === 'function'\n        ? (...args) => clamp(originalStaleTime(...args))\n        : clamp(originalStaleTime)\n\n    if (typeof defaultedOptions.gcTime === 'number') {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1000)\n    }\n  }\n}\n\nexport const willFetch = (\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => result.isLoading && result.isFetching && !isRestoring\n\nexport const shouldSuspend = (\n  defaultedOptions:\n    | DefaultedQueryObserverOptions<any, any, any, any, any>\n    | undefined,\n  result: QueryObserverResult<any, any>,\n) => defaultedOptions?.suspense && result.isPending\n\nexport const fetchOptimistic = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  defaultedOptions: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) =>\n  observer.fetchOptimistic(defaultedOptions).catch(() => {\n    errorResetBoundary.clearReset()\n  })\n"], "names": [], "mappings": ";;;;;;;;AAUO,IAAM,sBAAsB,CAMjC,QACA,QACG,MAAM,KAAA,CAAM,IAAA,KAAS,KAAA;AAEnB,IAAM,uBAAuB,CAClC,qBACG;IACH,IAAI,iBAAiB,QAAA,EAAU;QAI7B,MAAM,QAAQ,CAAC,QACb,UAAU,WAAW,QAAQ,KAAK,GAAA,CAAI,SAAS,KAAM,GAAI;QAE3D,MAAM,oBAAoB,iBAAiB,SAAA;QAC3C,iBAAiB,SAAA,GACf,OAAO,sBAAsB,aACzB,CAAA,GAAI,OAAS,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAC7C,MAAM,iBAAiB;QAE7B,IAAI,OAAO,iBAAiB,MAAA,KAAW,UAAU;YAC/C,iBAAiB,MAAA,GAAS,KAAK,GAAA,CAAI,iBAAiB,MAAA,EAAQ,GAAI;QAClE;IACF;AACF;AAEO,IAAM,YAAY,CACvB,QACA,cACG,OAAO,SAAA,IAAa,OAAO,UAAA,IAAc,CAAC;AAExC,IAAM,gBAAgB,CAC3B,kBAGA,SACG,kBAAkB,YAAY,OAAO,SAAA;AAEnC,IAAM,kBAAkB,CAO7B,kBAOA,UACA,qBAEA,SAAS,eAAA,CAAgB,gBAAgB,EAAE,KAAA,CAAM,MAAM;QACrD,mBAAmB,UAAA,CAAW;IAChC,CAAC", "debugId": null}}, {"offset": {"line": 2098, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/%40tanstack/react-query/src/useBaseQuery.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport { isServer, noop, notifyManager } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport { useIsRestoring } from './IsRestoringProvider'\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch,\n} from './suspense'\nimport type {\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { UseBaseQueryOptions } from './types'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  <PERSON><PERSON><PERSON>y<PERSON>ey extends QueryKey,\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  Observer: typeof QueryObserver,\n  queryClient?: QueryClient,\n): QueryObserverResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof options !== 'object' || Array.isArray(options)) {\n      throw new Error(\n        'Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object',\n      )\n    }\n  }\n\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const client = useQueryClient(queryClient)\n  const defaultedOptions = client.defaultQueryOptions(options)\n\n  ;(client.getDefaultOptions().queries as any)?._experimental_beforeQuery?.(\n    defaultedOptions,\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (!defaultedOptions.queryFn) {\n      console.error(\n        `[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`,\n      )\n    }\n  }\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions._optimisticResults = isRestoring\n    ? 'isRestoring'\n    : 'optimistic'\n\n  ensureSuspenseTimers(defaultedOptions)\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary)\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  // this needs to be invoked before creating the Observer because that can create a cache entry\n  const isNewCacheEntry = !client\n    .getQueryCache()\n    .get(defaultedOptions.queryHash)\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        client,\n        defaultedOptions,\n      ),\n  )\n\n  // note: this must be called before useSyncExternalStore\n  const result = observer.getOptimisticResult(defaultedOptions)\n\n  const shouldSubscribe = !isRestoring && options.subscribed !== false\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = shouldSubscribe\n          ? observer.subscribe(notifyManager.batchCalls(onStoreChange))\n          : noop\n\n        // Update result to make sure we did not miss any query updates\n        // between creating the observer and subscribing to it.\n        observer.updateResult()\n\n        return unsubscribe\n      },\n      [observer, shouldSubscribe],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(defaultedOptions)\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (shouldSuspend(defaultedOptions, result)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n  }\n\n  // Handle error boundary\n  if (\n    getHasError({\n      result,\n      errorResetBoundary,\n      throwOnError: defaultedOptions.throwOnError,\n      query: client\n        .getQueryCache()\n        .get<\n          TQueryFnData,\n          TError,\n          TQueryData,\n          TQueryKey\n        >(defaultedOptions.queryHash),\n      suspense: defaultedOptions.suspense,\n    })\n  ) {\n    throw result.error\n  }\n\n  ;(client.getDefaultOptions().queries as any)?._experimental_afterQuery?.(\n    defaultedOptions,\n    result,\n  )\n\n  if (\n    defaultedOptions.experimental_prefetchInRender &&\n    !isServer &&\n    willFetch(result, isRestoring)\n  ) {\n    const promise = isNewCacheEntry\n      ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n      : // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        client.getQueryCache().get(defaultedOptions.queryHash)?.promise\n\n    promise?.catch(noop).finally(() => {\n      // `.updateResult()` will trigger `.#currentThenable` to finalize\n      observer.updateResult()\n    })\n  }\n\n  // Handle result property usage tracking\n  return !defaultedOptions.notifyOnChangeProps\n    ? observer.trackResult(result)\n    : result\n}\n"], "names": [], "mappings": ";;;;AACA,YAAY,WAAW;AAEvB,SAAS,UAAU,MAAM,qBAAqB;;AAC9C,SAAS,sBAAsB;AAC/B,SAAS,kCAAkC;AAC3C;AAKA,SAAS,sBAAsB;AAC/B;;;;;;;;;AAcO,SAAS,aAOd,OAAA,EAOA,QAAA,EACA,WAAA,EACoC;IACpC,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAI,OAAO,YAAY,YAAY,MAAM,OAAA,CAAQ,OAAO,GAAG;YACzD,MAAM,IAAI,MACR;QAEJ;IACF;IAEA,MAAM,yMAAc,iBAAA,CAAe;IACnC,MAAM,oNAAqB,6BAAA,CAA2B;IACtD,MAAM,oMAAS,iBAAA,EAAe,WAAW;IACzC,MAAM,mBAAmB,OAAO,mBAAA,CAAoB,OAAO;IAEzD,OAAO,iBAAA,CAAkB,EAAE,OAAA,EAAiB,4BAC5C;IAGF,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAI,CAAC,iBAAiB,OAAA,EAAS;YAC7B,QAAQ,KAAA,CACN,CAAA,CAAA,EAAI,iBAAiB,SAAS,CAAA,kPAAA,CAAA;QAElC;IACF;IAGA,iBAAiB,kBAAA,GAAqB,cAClC,gBACA;IAEJ,CAAA,GAAA,2KAAA,CAAA,uBAAA,EAAqB,gBAAgB;IACrC,CAAA,GAAA,qLAAA,CAAA,kCAAA,EAAgC,kBAAkB,kBAAkB;IAEpE,CAAA,GAAA,qLAAA,CAAA,6BAAA,EAA2B,kBAAkB;IAG7C,MAAM,kBAAkB,CAAC,OACtB,aAAA,CAAc,EACd,GAAA,CAAI,iBAAiB,SAAS;IAEjC,MAAM,CAAC,QAAQ,CAAA,yMAAU,WAAA,CACvB,IACE,IAAI,SACF,QACA;IAKN,MAAM,SAAS,SAAS,mBAAA,CAAoB,gBAAgB;IAE5D,MAAM,kBAAkB,CAAC,eAAe,QAAQ,UAAA,KAAe;0MACzD,uBAAA,uMACE,cAAA,CACJ,CAAC,kBAAkB;QACjB,MAAM,cAAc,kBAChB,SAAS,SAAA,iLAAU,gBAAA,CAAc,UAAA,CAAW,aAAa,CAAC,4KAC1D,OAAA;QAIJ,SAAS,YAAA,CAAa;QAEtB,OAAO;IACT,GACA;QAAC;QAAU,eAAe;KAAA,GAE5B,IAAM,SAAS,gBAAA,CAAiB,GAChC,IAAM,SAAS,gBAAA,CAAiB;0MAG5B,YAAA,CAAU,MAAM;QACpB,SAAS,UAAA,CAAW,gBAAgB;IACtC,GAAG;QAAC;QAAkB,QAAQ;KAAC;IAG/B,KAAI,+LAAA,EAAc,kBAAkB,MAAM,GAAG;QAC3C,sLAAM,kBAAA,EAAgB,kBAAkB,UAAU,kBAAkB;IACtE;IAGA,IACE,wMAAA,EAAY;QACV;QACA;QACA,cAAc,iBAAiB,YAAA;QAC/B,OAAO,OACJ,aAAA,CAAc,EACd,GAAA,CAKC,iBAAiB,SAAS;QAC9B,UAAU,iBAAiB,QAAA;IAC7B,CAAC,GACD;QACA,MAAM,OAAO,KAAA;IACf;;IAEE,OAAO,iBAAA,CAAkB,EAAE,OAAA,EAAiB,2BAC5C,kBACA;IAGF,IACE,iBAAiB,6BAAA,IACjB,yKAAC,WAAA,oLACD,YAAA,EAAU,QAAQ,WAAW,GAC7B;QACA,MAAM,UAAU,kBAAA,2GAAA;wLAEZ,kBAAA,EAAgB,kBAAkB,UAAU,kBAAkB,IAAA,kGAAA;QAE9D,OAAO,aAAA,CAAc,EAAE,GAAA,CAAI,iBAAiB,SAAS,GAAG;QAE5D,SAAS,8KAAM,OAAI,EAAE,QAAQ,MAAM;YAEjC,SAAS,YAAA,CAAa;QACxB,CAAC;IACH;IAGA,OAAO,CAAC,iBAAiB,mBAAA,GACrB,SAAS,WAAA,CAAY,MAAM,IAC3B;AACN", "debugId": null}}, {"offset": {"line": 2186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/%40tanstack/react-query/src/useQuery.ts"], "sourcesContent": ["'use client'\nimport { QueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  DefaultError,\n  NoInfer,\n  QueryClient,\n  QueryKey,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\nimport type {\n  DefinedInitialDataOptions,\n  UndefinedInitialDataOptions,\n} from './queryOptions'\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): DefinedUseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  T<PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryKey,\n>(\n  options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery(options: UseQueryOptions, queryClient?: QueryClient) {\n  return useBaseQuery(options, QueryObserver, queryClient)\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;;;;AA+CtB,SAAS,SAAS,OAAA,EAA0B,WAAA,EAA2B;IAC5E,2LAAO,eAAA,EAAa,yLAAS,gBAAA,EAAe,WAAW;AACzD", "debugId": null}}, {"offset": {"line": 2204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/%40tanstack/query-core/src/mutationObserver.ts"], "sourcesContent": ["import { getDefaultState } from './mutation'\nimport { notify<PERSON>anager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { hashKey, shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  DefaultError,\n  MutateOptions,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  #client: QueryClient\n  #currentResult: MutationObserverResult<TData, TError, TVariables, TContext> =\n    undefined!\n  #currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  #mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.#client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.#updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options as\n      | MutationObserverOptions<TData, TError, TVariables, TContext>\n      | undefined\n    this.options = this.#client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.#currentMutation,\n        observer: this,\n      })\n    }\n\n    if (\n      prevOptions?.mutationKey &&\n      this.options.mutationKey &&\n      hashKey(prevOptions.mutationKey) !== hashKey(this.options.mutationKey)\n    ) {\n      this.reset()\n    } else if (this.#currentMutation?.state.status === 'pending') {\n      this.#currentMutation.setOptions(this.options)\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.#updateResult()\n\n    this.#notify(action)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.#currentResult\n  }\n\n  reset(): void {\n    // reset needs to remove the observer from the mutation because there is no way to \"get it back\"\n    // another mutate call will yield a new mutation!\n    this.#currentMutation?.removeObserver(this)\n    this.#currentMutation = undefined\n    this.#updateResult()\n    this.#notify()\n  }\n\n  mutate(\n    variables: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.#mutateOptions = options\n\n    this.#currentMutation?.removeObserver(this)\n\n    this.#currentMutation = this.#client\n      .getMutationCache()\n      .build(this.#client, this.options)\n\n    this.#currentMutation.addObserver(this)\n\n    return this.#currentMutation.execute(variables)\n  }\n\n  #updateResult(): void {\n    const state =\n      this.#currentMutation?.state ??\n      getDefaultState<TData, TError, TVariables, TContext>()\n\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === 'pending',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    } as MutationObserverResult<TData, TError, TVariables, TContext>\n  }\n\n  #notify(action?: Action<TData, TError, TVariables, TContext>): void {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables!\n        const context = this.#currentResult.context\n\n        if (action?.type === 'success') {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context!)\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context)\n        } else if (action?.type === 'error') {\n          this.#mutateOptions.onError?.(action.error, variables, context)\n          this.#mutateOptions.onSettled?.(\n            undefined,\n            action.error,\n            variables,\n            context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult)\n      })\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,uBAAuB;AAChC,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;AAC7B,SAAS,SAAS,2BAA2B;;;;;AAkBtC,IAAM,mBAAN,6LAKG,eAAA,CAER;KAGA,MAAA,CAAA;KACA,aAAA,GACE,KAAA,EAAA;KACF,eAAA,CAAA;IACA,cAAA,CAAA;IAEA,YACE,MAAA,EACA,OAAA,CACA;QACA,KAAA,CAAM;QAEN,IAAA,EAAK,MAAA,GAAU;QACf,IAAA,CAAK,UAAA,CAAW,OAAO;QACvB,IAAA,CAAK,WAAA,CAAY;QACjB,IAAA,EAAK,YAAA,CAAc;IACrB;IAEU,cAAoB;QAC5B,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,IAAI;QACnC,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAI;IACnC;IAEA,WACE,OAAA,EACA;QACA,MAAM,cAAc,IAAA,CAAK,OAAA;QAGzB,IAAA,CAAK,OAAA,GAAU,IAAA,EAAK,MAAA,CAAQ,sBAAA,CAAuB,OAAO;QAC1D,IAAI,CAAC,kMAAA,EAAoB,IAAA,CAAK,OAAA,EAAS,WAAW,GAAG;YACnD,IAAA,EAAK,MAAA,CAAQ,gBAAA,CAAiB,EAAE,MAAA,CAAO;gBACrC,MAAM;gBACN,UAAU,IAAA,EAAK,eAAA;gBACf,UAAU,IAAA;YACZ,CAAC;QACH;QAEA,IACE,aAAa,eACb,IAAA,CAAK,OAAA,CAAQ,WAAA,gLACb,UAAA,EAAQ,YAAY,WAAW,MAAM,sLAAA,EAAQ,IAAA,CAAK,OAAA,CAAQ,WAAW,GACrE;YACA,IAAA,CAAK,KAAA,CAAM;QACb,OAAA,IAAW,IAAA,CAAK,gBAAA,EAAkB,MAAM,WAAW,WAAW;YAC5D,IAAA,EAAK,eAAA,CAAiB,UAAA,CAAW,IAAA,CAAK,OAAO;QAC/C;IACF;IAEU,gBAAsB;QAC9B,IAAI,CAAC,IAAA,CAAK,YAAA,CAAa,GAAG;YACxB,IAAA,EAAK,eAAA,EAAkB,eAAe,IAAI;QAC5C;IACF;IAEA,iBAAiB,MAAA,EAA2D;QAC1E,IAAA,EAAK,YAAA,CAAc;QAEnB,IAAA,CAAK,OAAA,CAAQ,MAAM;IACrB;IAEA,mBAKE;QACA,OAAO,IAAA,EAAK,aAAA;IACd;IAEA,QAAc;QAGZ,IAAA,EAAK,eAAA,EAAkB,eAAe,IAAI;QAC1C,IAAA,EAAK,eAAA,GAAmB,KAAA;QACxB,IAAA,EAAK,YAAA,CAAc;QACnB,IAAA,EAAK,MAAA,CAAQ;IACf;IAEA,OACE,SAAA,EACA,OAAA,EACgB;QAChB,IAAA,EAAK,aAAA,GAAiB;QAEtB,IAAA,EAAK,eAAA,EAAkB,eAAe,IAAI;QAE1C,IAAA,EAAK,eAAA,GAAmB,IAAA,EAAK,MAAA,CAC1B,gBAAA,CAAiB,EACjB,KAAA,CAAM,IAAA,EAAK,MAAA,EAAS,IAAA,CAAK,OAAO;QAEnC,IAAA,EAAK,eAAA,CAAiB,WAAA,CAAY,IAAI;QAEtC,OAAO,IAAA,EAAK,eAAA,CAAiB,OAAA,CAAQ,SAAS;IAChD;KAEA,YAAA,GAAsB;QACpB,MAAM,QACJ,IAAA,EAAK,eAAA,EAAkB,wLACvB,kBAAA,CAAqD;QAEvD,IAAA,EAAK,aAAA,GAAiB;YACpB,GAAG,KAAA;YACH,WAAW,MAAM,MAAA,KAAW;YAC5B,WAAW,MAAM,MAAA,KAAW;YAC5B,SAAS,MAAM,MAAA,KAAW;YAC1B,QAAQ,MAAM,MAAA,KAAW;YACzB,QAAQ,IAAA,CAAK,MAAA;YACb,OAAO,IAAA,CAAK,KAAA;QACd;IACF;KAEA,MAAA,CAAQ,MAAA,EAA4D;QAClE,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YAExB,IAAI,IAAA,EAAK,aAAA,IAAkB,IAAA,CAAK,YAAA,CAAa,GAAG;gBAC9C,MAAM,YAAY,IAAA,EAAK,aAAA,CAAe,SAAA;gBACtC,MAAM,UAAU,IAAA,EAAK,aAAA,CAAe,OAAA;gBAEpC,IAAI,QAAQ,SAAS,WAAW;oBAC9B,IAAA,EAAK,aAAA,CAAe,SAAA,GAAY,OAAO,IAAA,EAAM,WAAW,OAAQ;oBAChE,IAAA,EAAK,aAAA,CAAe,SAAA,GAAY,OAAO,IAAA,EAAM,MAAM,WAAW,OAAO;gBACvE,OAAA,IAAW,QAAQ,SAAS,SAAS;oBACnC,IAAA,EAAK,aAAA,CAAe,OAAA,GAAU,OAAO,KAAA,EAAO,WAAW,OAAO;oBAC9D,IAAA,EAAK,aAAA,CAAe,SAAA,GAClB,KAAA,GACA,OAAO,KAAA,EACP,WACA;gBAEJ;YACF;YAGA,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;gBACnC,SAAS,IAAA,EAAK,aAAc;YAC9B,CAAC;QACH,CAAC;IACH;AACF", "debugId": null}}, {"offset": {"line": 2310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/%40tanstack/react-query/src/useMutation.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport {\n  MutationObserver,\n  noop,\n  notifyManager,\n  shouldThrowError,\n} from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\nimport type { DefaultError, QueryClient } from '@tanstack/query-core'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>,\n  queryClient?: QueryClient,\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const client = useQueryClient(queryClient)\n\n  const [observer] = React.useState(\n    () =>\n      new MutationObserver<TData, TError, TVariables, TContext>(\n        client,\n        options,\n      ),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(options)\n  }, [observer, options])\n\n  const result = React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop)\n    },\n    [observer],\n  )\n\n  if (\n    result.error &&\n    shouldThrowError(observer.options.throwOnError, [result.error])\n  ) {\n    throw result.error\n  }\n\n  return { ...result, mutate, mutateAsync: result.mutate }\n}\n"], "names": [], "mappings": ";;;;AACA,YAAY,WAAW;;;AACvB;AAMA,SAAS,sBAAsB;;;;;AAUxB,SAAS,YAMd,OAAA,EACA,WAAA,EACwD;IACxD,MAAM,oMAAS,iBAAA,EAAe,WAAW;IAEzC,MAAM,CAAC,QAAQ,CAAA,yMAAU,WAAA,CACvB,IACE,uLAAI,mBAAA,CACF,QACA;0MAIA,YAAA,CAAU,MAAM;QACpB,SAAS,UAAA,CAAW,OAAO;IAC7B,GAAG;QAAC;QAAU,OAAO;KAAC;IAEtB,MAAM,+MAAe,uBAAA,uMACb,cAAA,CACJ,CAAC,gBACC,SAAS,SAAA,iLAAU,gBAAA,CAAc,UAAA,CAAW,aAAa,CAAC,GAC5D;QAAC,QAAQ;KAAA,GAEX,IAAM,SAAS,gBAAA,CAAiB,GAChC,IAAM,SAAS,gBAAA,CAAiB;IAGlC,MAAM,+MAAe,cAAA,CAGnB,CAAC,WAAW,kBAAkB;QAC5B,SAAS,MAAA,CAAO,WAAW,aAAa,EAAE,KAAA,CAAM,+KAAI;IACtD,GACA;QAAC,QAAQ;KAAA;IAGX,IACE,OAAO,KAAA,gLACP,mBAAA,EAAiB,SAAS,OAAA,CAAQ,YAAA,EAAc;QAAC,OAAO,KAAK;KAAC,GAC9D;QACA,MAAM,OAAO,KAAA;IACf;IAEA,OAAO;QAAE,GAAG,MAAA;QAAQ;QAAQ,aAAa,OAAO,MAAA;IAAO;AACzD", "debugId": null}}]}