(()=>{var a={};a.id=5105,a.ids=[5105],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},12941:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},14952:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},16259:(a,b,c)=>{"use strict";c.r(b),c.d(b,{ProjectNameEditor:()=>q});var d=c(60687),e=c(43210),f=c(29523),g=c(89667),h=c(41862),i=c(11860),j=c(5336),k=c(13964),l=c(90131),m=c(97421),n=c(26910),o=c(52581),p=c(4780);let q=(0,e.memo)(function({projectId:a,currentName:b,onNameUpdated:c,className:q,disabled:r=!1}){let[s,t]=(0,e.useState)(!1),[u,v]=(0,e.useState)(b),[w,x]=(0,e.useState)(""),[y,z]=(0,e.useState)(!1),[A,B]=(0,e.useState)(!1),C=(0,e.useRef)(null),D=(0,m.eo)(),{data:E=[]}=(0,m.YK)(),F=(0,e.useCallback)(()=>(0,n.qj)(E,a,300),[E,a])(),G=(0,e.useCallback)(()=>{r||D.isPending||(t(!0),v(b),x(""),z(!1),B(!1))},[r,D.isPending,b]),H=(0,e.useCallback)(()=>{t(!1),v(b),x(""),z(!1),B(!1)},[b]),I=(0,e.useCallback)(async()=>{if(u.trim()&&!w&&!y){if((0,n.Br)(u,b))return void H();try{let b=await D.mutateAsync({projectId:a,newName:u.trim()});o.oR.success("Proje adı başarıyla g\xfcncellendi!"),t(!1),c?.(b.name)}catch(b){console.error("Project name update error:",b);let a=b instanceof Error?b.message:"Proje adı g\xfcncellenirken bir hata oluştu";o.oR.error(a),setTimeout(()=>{C.current?.focus()},100)}}},[u,w,y,b,a,D,c,H]),J=(0,e.useCallback)(a=>{let c=a.target.value;v(c),c.trim()?(z(!0),B(!1),F(c,a=>{x(a.isValid?"":a.error||""),B(a.isValid&&!(0,n.Br)(c,b)),z(!1)})):(x("Proje adı boş olamaz"),B(!1),z(!1))},[F,b]),K=(0,e.useCallback)(a=>{"Enter"===a.key?(a.preventDefault(),I()):"Escape"===a.key&&(a.preventDefault(),H())},[I,H]);return((0,e.useEffect)(()=>{s&&C.current&&(C.current.focus(),C.current.select())},[s]),(0,e.useEffect)(()=>{s||v(b)},[b,s]),s)?(0,d.jsxs)("div",{className:(0,p.cn)("flex items-center gap-2 w-full",q),children:[(0,d.jsxs)("div",{className:"flex-1 relative",children:[(0,d.jsx)(g.p,{ref:C,value:u,onChange:J,onKeyDown:K,className:(0,p.cn)("text-sm font-medium",w&&"border-red-500 focus:border-red-500",A&&"border-green-500 focus:border-green-500","focus:ring-2 focus:ring-blue-500/20"),placeholder:"Proje adı...",disabled:D.isPending,maxLength:50,"aria-label":"Proje adını d\xfczenle","aria-describedby":`project-name-feedback-${a}`,"aria-invalid":!!w}),(0,d.jsxs)("div",{className:"absolute top-full right-0 mt-1 text-xs text-gray-400 z-10",children:[(0,d.jsx)("span",{className:"sr-only",children:"Karakter sayısı: "}),u.length,"/50"]}),(w||y||A)&&(0,d.jsx)("div",{id:`project-name-feedback-${a}`,className:"absolute top-full left-0 mt-1 text-xs z-10",role:"status","aria-live":"polite",children:y?(0,d.jsxs)("span",{className:"text-gray-500 flex items-center gap-1",children:[(0,d.jsx)(h.A,{className:"h-3 w-3 animate-spin"}),"Kontrol ediliyor..."]}):w?(0,d.jsxs)("span",{className:"text-red-500 flex items-center gap-1",children:[(0,d.jsx)(i.A,{className:"h-3 w-3"}),w]}):A?(0,d.jsxs)("span",{className:"text-green-600 flex items-center gap-1",children:[(0,d.jsx)(j.A,{className:"h-3 w-3"}),"Proje adı kullanılabilir"]}):null})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(f.$,{size:"sm",variant:"ghost",onClick:I,disabled:D.isPending||!!w||y||!u.trim()||(0,n.Br)(u,b),className:"h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50 touch-target",title:"Kaydet (Enter)","aria-label":"Proje adını kaydet",children:D.isPending?(0,d.jsx)(h.A,{className:"h-4 w-4 animate-spin"}):(0,d.jsx)(k.A,{className:"h-4 w-4"})}),(0,d.jsx)(f.$,{size:"sm",variant:"ghost",onClick:H,disabled:D.isPending,className:"h-8 w-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-50 touch-target",title:"İptal (Esc)","aria-label":"D\xfczenlemeyi iptal et",children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})]})]}):(0,d.jsxs)("div",{className:(0,p.cn)("flex items-center gap-2 group w-full",q),children:[(0,d.jsx)("span",{className:"flex-1 text-sm font-medium text-gray-900 truncate",title:b,children:b}),(0,d.jsx)(f.$,{size:"sm",variant:"ghost",onClick:a=>{a.stopPropagation(),G()},disabled:r||D.isPending,className:(0,p.cn)("h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity touch-target","text-gray-400 hover:text-gray-600 hover:bg-gray-50","focus:opacity-100 focus:ring-2 focus:ring-blue-500/20","lg:opacity-0 opacity-100"),title:"Proje adını d\xfczenle","aria-label":"Proje adını d\xfczenle",children:(0,d.jsx)(l.A,{className:"h-3 w-3"})})]})})},18408:(a,b,c)=>{Promise.resolve().then(c.bind(c,80559))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24711:(a,b,c)=>{"use strict";c.d(b,{F:()=>W});var d=c(60687),e=c(43210),f=c(14163),g=c(46059),h=c(11273),i=c(98599),j=c(13495),k=c(43),l=c(66156),m=c(67969),n=c(70569),o="ScrollArea",[p,q]=(0,h.A)(o),[r,s]=p(o),t=e.forwardRef((a,b)=>{let{__scopeScrollArea:c,type:g="hover",dir:h,scrollHideDelay:j=600,...l}=a,[m,n]=e.useState(null),[o,p]=e.useState(null),[q,s]=e.useState(null),[t,u]=e.useState(null),[v,w]=e.useState(null),[x,y]=e.useState(0),[z,A]=e.useState(0),[B,C]=e.useState(!1),[D,E]=e.useState(!1),F=(0,i.s)(b,a=>n(a)),G=(0,k.jH)(h);return(0,d.jsx)(r,{scope:c,type:g,dir:G,scrollHideDelay:j,scrollArea:m,viewport:o,onViewportChange:p,content:q,onContentChange:s,scrollbarX:t,onScrollbarXChange:u,scrollbarXEnabled:B,onScrollbarXEnabledChange:C,scrollbarY:v,onScrollbarYChange:w,scrollbarYEnabled:D,onScrollbarYEnabledChange:E,onCornerWidthChange:y,onCornerHeightChange:A,children:(0,d.jsx)(f.sG.div,{dir:G,...l,ref:F,style:{position:"relative","--radix-scroll-area-corner-width":x+"px","--radix-scroll-area-corner-height":z+"px",...a.style}})})});t.displayName=o;var u="ScrollAreaViewport",v=e.forwardRef((a,b)=>{let{__scopeScrollArea:c,children:g,nonce:h,...j}=a,k=s(u,c),l=e.useRef(null),m=(0,i.s)(b,l,k.onViewportChange);return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:h}),(0,d.jsx)(f.sG.div,{"data-radix-scroll-area-viewport":"",...j,ref:m,style:{overflowX:k.scrollbarXEnabled?"scroll":"hidden",overflowY:k.scrollbarYEnabled?"scroll":"hidden",...a.style},children:(0,d.jsx)("div",{ref:k.onContentChange,style:{minWidth:"100%",display:"table"},children:g})})]})});v.displayName=u;var w="ScrollAreaScrollbar",x=e.forwardRef((a,b)=>{let{forceMount:c,...f}=a,g=s(w,a.__scopeScrollArea),{onScrollbarXEnabledChange:h,onScrollbarYEnabledChange:i}=g,j="horizontal"===a.orientation;return e.useEffect(()=>(j?h(!0):i(!0),()=>{j?h(!1):i(!1)}),[j,h,i]),"hover"===g.type?(0,d.jsx)(y,{...f,ref:b,forceMount:c}):"scroll"===g.type?(0,d.jsx)(z,{...f,ref:b,forceMount:c}):"auto"===g.type?(0,d.jsx)(A,{...f,ref:b,forceMount:c}):"always"===g.type?(0,d.jsx)(B,{...f,ref:b}):null});x.displayName=w;var y=e.forwardRef((a,b)=>{let{forceMount:c,...f}=a,h=s(w,a.__scopeScrollArea),[i,j]=e.useState(!1);return e.useEffect(()=>{let a=h.scrollArea,b=0;if(a){let c=()=>{window.clearTimeout(b),j(!0)},d=()=>{b=window.setTimeout(()=>j(!1),h.scrollHideDelay)};return a.addEventListener("pointerenter",c),a.addEventListener("pointerleave",d),()=>{window.clearTimeout(b),a.removeEventListener("pointerenter",c),a.removeEventListener("pointerleave",d)}}},[h.scrollArea,h.scrollHideDelay]),(0,d.jsx)(g.C,{present:c||i,children:(0,d.jsx)(A,{"data-state":i?"visible":"hidden",...f,ref:b})})}),z=e.forwardRef((a,b)=>{var c;let{forceMount:f,...h}=a,i=s(w,a.__scopeScrollArea),j="horizontal"===a.orientation,k=T(()=>m("SCROLL_END"),100),[l,m]=(c={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},e.useReducer((a,b)=>c[a][b]??a,"hidden"));return e.useEffect(()=>{if("idle"===l){let a=window.setTimeout(()=>m("HIDE"),i.scrollHideDelay);return()=>window.clearTimeout(a)}},[l,i.scrollHideDelay,m]),e.useEffect(()=>{let a=i.viewport,b=j?"scrollLeft":"scrollTop";if(a){let c=a[b],d=()=>{let d=a[b];c!==d&&(m("SCROLL"),k()),c=d};return a.addEventListener("scroll",d),()=>a.removeEventListener("scroll",d)}},[i.viewport,j,m,k]),(0,d.jsx)(g.C,{present:f||"hidden"!==l,children:(0,d.jsx)(B,{"data-state":"hidden"===l?"hidden":"visible",...h,ref:b,onPointerEnter:(0,n.m)(a.onPointerEnter,()=>m("POINTER_ENTER")),onPointerLeave:(0,n.m)(a.onPointerLeave,()=>m("POINTER_LEAVE"))})})}),A=e.forwardRef((a,b)=>{let c=s(w,a.__scopeScrollArea),{forceMount:f,...h}=a,[i,j]=e.useState(!1),k="horizontal"===a.orientation,l=T(()=>{if(c.viewport){let a=c.viewport.offsetWidth<c.viewport.scrollWidth,b=c.viewport.offsetHeight<c.viewport.scrollHeight;j(k?a:b)}},10);return U(c.viewport,l),U(c.content,l),(0,d.jsx)(g.C,{present:f||i,children:(0,d.jsx)(B,{"data-state":i?"visible":"hidden",...h,ref:b})})}),B=e.forwardRef((a,b)=>{let{orientation:c="vertical",...f}=a,g=s(w,a.__scopeScrollArea),h=e.useRef(null),i=e.useRef(0),[j,k]=e.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),l=O(j.viewport,j.content),m={...f,sizes:j,onSizesChange:k,hasThumb:!!(l>0&&l<1),onThumbChange:a=>h.current=a,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:a=>i.current=a};function n(a,b){return function(a,b,c,d="ltr"){let e=P(c),f=b||e/2,g=c.scrollbar.paddingStart+f,h=c.scrollbar.size-c.scrollbar.paddingEnd-(e-f),i=c.content-c.viewport;return R([g,h],"ltr"===d?[0,i]:[-1*i,0])(a)}(a,i.current,j,b)}return"horizontal"===c?(0,d.jsx)(C,{...m,ref:b,onThumbPositionChange:()=>{if(g.viewport&&h.current){let a=Q(g.viewport.scrollLeft,j,g.dir);h.current.style.transform=`translate3d(${a}px, 0, 0)`}},onWheelScroll:a=>{g.viewport&&(g.viewport.scrollLeft=a)},onDragScroll:a=>{g.viewport&&(g.viewport.scrollLeft=n(a,g.dir))}}):"vertical"===c?(0,d.jsx)(D,{...m,ref:b,onThumbPositionChange:()=>{if(g.viewport&&h.current){let a=Q(g.viewport.scrollTop,j);h.current.style.transform=`translate3d(0, ${a}px, 0)`}},onWheelScroll:a=>{g.viewport&&(g.viewport.scrollTop=a)},onDragScroll:a=>{g.viewport&&(g.viewport.scrollTop=n(a))}}):null}),C=e.forwardRef((a,b)=>{let{sizes:c,onSizesChange:f,...g}=a,h=s(w,a.__scopeScrollArea),[j,k]=e.useState(),l=e.useRef(null),m=(0,i.s)(b,l,h.onScrollbarXChange);return e.useEffect(()=>{l.current&&k(getComputedStyle(l.current))},[l]),(0,d.jsx)(G,{"data-orientation":"horizontal",...g,ref:m,sizes:c,style:{bottom:0,left:"rtl"===h.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===h.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":P(c)+"px",...a.style},onThumbPointerDown:b=>a.onThumbPointerDown(b.x),onDragScroll:b=>a.onDragScroll(b.x),onWheelScroll:(b,c)=>{if(h.viewport){let d=h.viewport.scrollLeft+b.deltaX;a.onWheelScroll(d),function(a,b){return a>0&&a<b}(d,c)&&b.preventDefault()}},onResize:()=>{l.current&&h.viewport&&j&&f({content:h.viewport.scrollWidth,viewport:h.viewport.offsetWidth,scrollbar:{size:l.current.clientWidth,paddingStart:N(j.paddingLeft),paddingEnd:N(j.paddingRight)}})}})}),D=e.forwardRef((a,b)=>{let{sizes:c,onSizesChange:f,...g}=a,h=s(w,a.__scopeScrollArea),[j,k]=e.useState(),l=e.useRef(null),m=(0,i.s)(b,l,h.onScrollbarYChange);return e.useEffect(()=>{l.current&&k(getComputedStyle(l.current))},[l]),(0,d.jsx)(G,{"data-orientation":"vertical",...g,ref:m,sizes:c,style:{top:0,right:"ltr"===h.dir?0:void 0,left:"rtl"===h.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":P(c)+"px",...a.style},onThumbPointerDown:b=>a.onThumbPointerDown(b.y),onDragScroll:b=>a.onDragScroll(b.y),onWheelScroll:(b,c)=>{if(h.viewport){let d=h.viewport.scrollTop+b.deltaY;a.onWheelScroll(d),function(a,b){return a>0&&a<b}(d,c)&&b.preventDefault()}},onResize:()=>{l.current&&h.viewport&&j&&f({content:h.viewport.scrollHeight,viewport:h.viewport.offsetHeight,scrollbar:{size:l.current.clientHeight,paddingStart:N(j.paddingTop),paddingEnd:N(j.paddingBottom)}})}})}),[E,F]=p(w),G=e.forwardRef((a,b)=>{let{__scopeScrollArea:c,sizes:g,hasThumb:h,onThumbChange:k,onThumbPointerUp:l,onThumbPointerDown:m,onThumbPositionChange:o,onDragScroll:p,onWheelScroll:q,onResize:r,...t}=a,u=s(w,c),[v,x]=e.useState(null),y=(0,i.s)(b,a=>x(a)),z=e.useRef(null),A=e.useRef(""),B=u.viewport,C=g.content-g.viewport,D=(0,j.c)(q),F=(0,j.c)(o),G=T(r,10);function H(a){z.current&&p({x:a.clientX-z.current.left,y:a.clientY-z.current.top})}return e.useEffect(()=>{let a=a=>{let b=a.target;v?.contains(b)&&D(a,C)};return document.addEventListener("wheel",a,{passive:!1}),()=>document.removeEventListener("wheel",a,{passive:!1})},[B,v,C,D]),e.useEffect(F,[g,F]),U(v,G),U(u.content,G),(0,d.jsx)(E,{scope:c,scrollbar:v,hasThumb:h,onThumbChange:(0,j.c)(k),onThumbPointerUp:(0,j.c)(l),onThumbPositionChange:F,onThumbPointerDown:(0,j.c)(m),children:(0,d.jsx)(f.sG.div,{...t,ref:y,style:{position:"absolute",...t.style},onPointerDown:(0,n.m)(a.onPointerDown,a=>{0===a.button&&(a.target.setPointerCapture(a.pointerId),z.current=v.getBoundingClientRect(),A.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",u.viewport&&(u.viewport.style.scrollBehavior="auto"),H(a))}),onPointerMove:(0,n.m)(a.onPointerMove,H),onPointerUp:(0,n.m)(a.onPointerUp,a=>{let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),document.body.style.webkitUserSelect=A.current,u.viewport&&(u.viewport.style.scrollBehavior=""),z.current=null})})})}),H="ScrollAreaThumb",I=e.forwardRef((a,b)=>{let{forceMount:c,...e}=a,f=F(H,a.__scopeScrollArea);return(0,d.jsx)(g.C,{present:c||f.hasThumb,children:(0,d.jsx)(J,{ref:b,...e})})}),J=e.forwardRef((a,b)=>{let{__scopeScrollArea:c,style:g,...h}=a,j=s(H,c),k=F(H,c),{onThumbPositionChange:l}=k,m=(0,i.s)(b,a=>k.onThumbChange(a)),o=e.useRef(void 0),p=T(()=>{o.current&&(o.current(),o.current=void 0)},100);return e.useEffect(()=>{let a=j.viewport;if(a){let b=()=>{p(),o.current||(o.current=S(a,l),l())};return l(),a.addEventListener("scroll",b),()=>a.removeEventListener("scroll",b)}},[j.viewport,p,l]),(0,d.jsx)(f.sG.div,{"data-state":k.hasThumb?"visible":"hidden",...h,ref:m,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...g},onPointerDownCapture:(0,n.m)(a.onPointerDownCapture,a=>{let b=a.target.getBoundingClientRect(),c=a.clientX-b.left,d=a.clientY-b.top;k.onThumbPointerDown({x:c,y:d})}),onPointerUp:(0,n.m)(a.onPointerUp,k.onThumbPointerUp)})});I.displayName=H;var K="ScrollAreaCorner",L=e.forwardRef((a,b)=>{let c=s(K,a.__scopeScrollArea),e=!!(c.scrollbarX&&c.scrollbarY);return"scroll"!==c.type&&e?(0,d.jsx)(M,{...a,ref:b}):null});L.displayName=K;var M=e.forwardRef((a,b)=>{let{__scopeScrollArea:c,...g}=a,h=s(K,c),[i,j]=e.useState(0),[k,l]=e.useState(0),m=!!(i&&k);return U(h.scrollbarX,()=>{let a=h.scrollbarX?.offsetHeight||0;h.onCornerHeightChange(a),l(a)}),U(h.scrollbarY,()=>{let a=h.scrollbarY?.offsetWidth||0;h.onCornerWidthChange(a),j(a)}),m?(0,d.jsx)(f.sG.div,{...g,ref:b,style:{width:i,height:k,position:"absolute",right:"ltr"===h.dir?0:void 0,left:"rtl"===h.dir?0:void 0,bottom:0,...a.style}}):null});function N(a){return a?parseInt(a,10):0}function O(a,b){let c=a/b;return isNaN(c)?0:c}function P(a){let b=O(a.viewport,a.content),c=a.scrollbar.paddingStart+a.scrollbar.paddingEnd;return Math.max((a.scrollbar.size-c)*b,18)}function Q(a,b,c="ltr"){let d=P(b),e=b.scrollbar.paddingStart+b.scrollbar.paddingEnd,f=b.scrollbar.size-e,g=b.content-b.viewport,h=(0,m.q)(a,"ltr"===c?[0,g]:[-1*g,0]);return R([0,g],[0,f-d])(h)}function R(a,b){return c=>{if(a[0]===a[1]||b[0]===b[1])return b[0];let d=(b[1]-b[0])/(a[1]-a[0]);return b[0]+d*(c-a[0])}}var S=(a,b=()=>{})=>{let c={left:a.scrollLeft,top:a.scrollTop},d=0;return!function e(){let f={left:a.scrollLeft,top:a.scrollTop},g=c.left!==f.left,h=c.top!==f.top;(g||h)&&b(),c=f,d=window.requestAnimationFrame(e)}(),()=>window.cancelAnimationFrame(d)};function T(a,b){let c=(0,j.c)(a),d=e.useRef(0);return e.useEffect(()=>()=>window.clearTimeout(d.current),[]),e.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(c,b)},[c,b])}function U(a,b){let c=(0,j.c)(b);(0,l.N)(()=>{let b=0;if(a){let d=new ResizeObserver(()=>{cancelAnimationFrame(b),b=window.requestAnimationFrame(c)});return d.observe(a),()=>{window.cancelAnimationFrame(b),d.unobserve(a)}}},[a,c])}var V=c(4780);function W({className:a,children:b,...c}){return(0,d.jsxs)(t,{"data-slot":"scroll-area",className:(0,V.cn)("relative",a),...c,children:[(0,d.jsx)(v,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:b}),(0,d.jsx)(X,{}),(0,d.jsx)(L,{})]})}function X({className:a,orientation:b="vertical",...c}){return(0,d.jsx)(x,{"data-slot":"scroll-area-scrollbar",orientation:b,className:(0,V.cn)("flex touch-none p-px transition-colors select-none","vertical"===b&&"h-full w-2.5 border-l border-l-transparent","horizontal"===b&&"h-2.5 flex-col border-t border-t-transparent",a),...c,children:(0,d.jsx)(I,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28555:(a,b,c)=>{"use strict";c.d(b,{C:()=>h});var d=c(43210);let e=a=>{let b,c=new Set,d=(a,d)=>{let e="function"==typeof a?a(b):a;if(!Object.is(e,b)){let a=b;b=(null!=d?d:"object"!=typeof e||null===e)?e:Object.assign({},b,e),c.forEach(c=>c(b,a))}},e=()=>b,f={setState:d,getState:e,getInitialState:()=>g,subscribe:a=>(c.add(a),()=>c.delete(a))},g=b=a(d,e,f);return f},f=a=>{let b=(a=>a?e(a):e)(a),c=a=>(function(a,b=a=>a){let c=d.useSyncExternalStore(a.subscribe,()=>b(a.getState()),()=>b(a.getInitialState()));return d.useDebugValue(c),c})(b,a);return Object.assign(c,b),c},g=a=>b=>{try{let c=a(b);if(c instanceof Promise)return c;return{then:a=>g(a)(c),catch(a){return this}}}catch(a){return{then(a){return this},catch:b=>g(b)(a)}}},h=f(((a,b)=>(c,d,e)=>{let f,h={storage:function(a,b){let c;try{c=a()}catch(a){return}return{getItem:a=>{var b;let d=a=>null===a?null:JSON.parse(a,void 0),e=null!=(b=c.getItem(a))?b:null;return e instanceof Promise?e.then(d):d(e)},setItem:(a,b)=>c.setItem(a,JSON.stringify(b,void 0)),removeItem:a=>c.removeItem(a)}}(()=>localStorage),partialize:a=>a,version:0,merge:(a,b)=>({...b,...a}),...b},i=!1,j=new Set,k=new Set,l=h.storage;if(!l)return a((...a)=>{console.warn(`[zustand persist middleware] Unable to update item '${h.name}', the given storage is currently unavailable.`),c(...a)},d,e);let m=()=>{let a=h.partialize({...d()});return l.setItem(h.name,{state:a,version:h.version})},n=e.setState;e.setState=(a,b)=>{n(a,b),m()};let o=a((...a)=>{c(...a),m()},d,e);e.getInitialState=()=>o;let p=()=>{var a,b;if(!l)return;i=!1,j.forEach(a=>{var b;return a(null!=(b=d())?b:o)});let e=(null==(b=h.onRehydrateStorage)?void 0:b.call(h,null!=(a=d())?a:o))||void 0;return g(l.getItem.bind(l))(h.name).then(a=>{if(a)if("number"!=typeof a.version||a.version===h.version)return[!1,a.state];else{if(h.migrate){let b=h.migrate(a.state,a.version);return b instanceof Promise?b.then(a=>[!0,a]):[!0,b]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(a=>{var b;let[e,g]=a;if(c(f=h.merge(g,null!=(b=d())?b:o),!0),e)return m()}).then(()=>{null==e||e(f,void 0),f=d(),i=!0,k.forEach(a=>a(f))}).catch(a=>{null==e||e(void 0,a)})};return e.persist={setOptions:a=>{h={...h,...a},a.storage&&(l=a.storage)},clearStorage:()=>{null==l||l.removeItem(h.name)},getOptions:()=>h,rehydrate:()=>p(),hasHydrated:()=>i,onHydrate:a=>(j.add(a),()=>{j.delete(a)}),onFinishHydration:a=>(k.add(a),()=>{k.delete(a)})},h.skipHydration||p(),f||o})(a=>({activeProjectId:null,setActiveProjectId:b=>a({activeProjectId:b}),isContextEnabled:!0,setIsContextEnabled:b=>a({isContextEnabled:b}),isProjectSidebarCollapsed:!1,setIsProjectSidebarCollapsed:b=>a({isProjectSidebarCollapsed:b}),isContextSidebarCollapsed:!1,setIsContextSidebarCollapsed:b=>a({isContextSidebarCollapsed:b})}),{name:"promptflow-app-store"}))},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},37941:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,80559)),"C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,39998)),"C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\dashboard\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},39020:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>aX});var d=c(60687),e=c(43210),f=c(29523),g=c(44493),h=c(89667),i=c(24711),j=c(14952),k=c(47033),l=c(11860),m=c(58869),n=c(40083),o=c(99270),p=c(96474),q=c(82570),r=c(85814),s=c.n(r),t=c(28555),u=c(97421),v=c(31568),w=c(34248),x=c(66432),y=c(96834),z=c(46657),A=c(35950),B=c(45583),C=c(92363),D=c(99891),E=c(43649),F=c(25541),G=c(63503),H=c(70569),I=c(98599),J=c(11273),K=c(14163),L=c(9510),M=c(96963),N=c(13495),O=c(65551),P=c(43),Q="rovingFocusGroup.onEntryFocus",R={bubbles:!1,cancelable:!0},S="RovingFocusGroup",[T,U,V]=(0,L.N)(S),[W,X]=(0,J.A)(S,[V]),[Y,Z]=W(S),$=e.forwardRef((a,b)=>(0,d.jsx)(T.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,d.jsx)(T.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,d.jsx)(_,{...a,ref:b})})}));$.displayName=S;var _=e.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:f,loop:g=!1,dir:h,currentTabStopId:i,defaultCurrentTabStopId:j,onCurrentTabStopIdChange:k,onEntryFocus:l,preventScrollOnEntryFocus:m=!1,...n}=a,o=e.useRef(null),p=(0,I.s)(b,o),q=(0,P.jH)(h),[r,s]=(0,O.i)({prop:i,defaultProp:j??null,onChange:k,caller:S}),[t,u]=e.useState(!1),v=(0,N.c)(l),w=U(c),x=e.useRef(!1),[y,z]=e.useState(0);return e.useEffect(()=>{let a=o.current;if(a)return a.addEventListener(Q,v),()=>a.removeEventListener(Q,v)},[v]),(0,d.jsx)(Y,{scope:c,orientation:f,dir:q,loop:g,currentTabStopId:r,onItemFocus:e.useCallback(a=>s(a),[s]),onItemShiftTab:e.useCallback(()=>u(!0),[]),onFocusableItemAdd:e.useCallback(()=>z(a=>a+1),[]),onFocusableItemRemove:e.useCallback(()=>z(a=>a-1),[]),children:(0,d.jsx)(K.sG.div,{tabIndex:t||0===y?-1:0,"data-orientation":f,...n,ref:p,style:{outline:"none",...a.style},onMouseDown:(0,H.m)(a.onMouseDown,()=>{x.current=!0}),onFocus:(0,H.m)(a.onFocus,a=>{let b=!x.current;if(a.target===a.currentTarget&&b&&!t){let b=new CustomEvent(Q,R);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=w().filter(a=>a.focusable);ad([a.find(a=>a.active),a.find(a=>a.id===r),...a].filter(Boolean).map(a=>a.ref.current),m)}}x.current=!1}),onBlur:(0,H.m)(a.onBlur,()=>u(!1))})})}),aa="RovingFocusGroupItem",ab=e.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:f=!0,active:g=!1,tabStopId:h,children:i,...j}=a,k=(0,M.B)(),l=h||k,m=Z(aa,c),n=m.currentTabStopId===l,o=U(c),{onFocusableItemAdd:p,onFocusableItemRemove:q,currentTabStopId:r}=m;return e.useEffect(()=>{if(f)return p(),()=>q()},[f,p,q]),(0,d.jsx)(T.ItemSlot,{scope:c,id:l,focusable:f,active:g,children:(0,d.jsx)(K.sG.span,{tabIndex:n?0:-1,"data-orientation":m.orientation,...j,ref:b,onMouseDown:(0,H.m)(a.onMouseDown,a=>{f?m.onItemFocus(l):a.preventDefault()}),onFocus:(0,H.m)(a.onFocus,()=>m.onItemFocus(l)),onKeyDown:(0,H.m)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void m.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return ac[e]}(a,m.orientation,m.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=o().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=m.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>ad(c))}}),children:"function"==typeof i?i({isCurrentTabStop:n,hasTabStop:null!=r}):i})})});ab.displayName=aa;var ac={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function ad(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var ae=c(18853),af=c(83721),ag=c(46059),ah="Radio",[ai,aj]=(0,J.A)(ah),[ak,al]=ai(ah),am=e.forwardRef((a,b)=>{let{__scopeRadio:c,name:f,checked:g=!1,required:h,disabled:i,value:j="on",onCheck:k,form:l,...m}=a,[n,o]=e.useState(null),p=(0,I.s)(b,a=>o(a)),q=e.useRef(!1),r=!n||l||!!n.closest("form");return(0,d.jsxs)(ak,{scope:c,checked:g,disabled:i,children:[(0,d.jsx)(K.sG.button,{type:"button",role:"radio","aria-checked":g,"data-state":aq(g),"data-disabled":i?"":void 0,disabled:i,value:j,...m,ref:p,onClick:(0,H.m)(a.onClick,a=>{g||k?.(),r&&(q.current=a.isPropagationStopped(),q.current||a.stopPropagation())})}),r&&(0,d.jsx)(ap,{control:n,bubbles:!q.current,name:f,value:j,checked:g,required:h,disabled:i,form:l,style:{transform:"translateX(-100%)"}})]})});am.displayName=ah;var an="RadioIndicator",ao=e.forwardRef((a,b)=>{let{__scopeRadio:c,forceMount:e,...f}=a,g=al(an,c);return(0,d.jsx)(ag.C,{present:e||g.checked,children:(0,d.jsx)(K.sG.span,{"data-state":aq(g.checked),"data-disabled":g.disabled?"":void 0,...f,ref:b})})});ao.displayName=an;var ap=e.forwardRef(({__scopeRadio:a,control:b,checked:c,bubbles:f=!0,...g},h)=>{let i=e.useRef(null),j=(0,I.s)(i,h),k=(0,af.Z)(c),l=(0,ae.X)(b);return e.useEffect(()=>{let a=i.current;if(!a)return;let b=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(k!==c&&b){let d=new Event("click",{bubbles:f});b.call(a,c),a.dispatchEvent(d)}},[k,c,f]),(0,d.jsx)(K.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:c,...g,tabIndex:-1,ref:j,style:{...g.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function aq(a){return a?"checked":"unchecked"}ap.displayName="RadioBubbleInput";var ar=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],as="RadioGroup",[at,au]=(0,J.A)(as,[X,aj]),av=X(),aw=aj(),[ax,ay]=at(as),az=e.forwardRef((a,b)=>{let{__scopeRadioGroup:c,name:e,defaultValue:f,value:g,required:h=!1,disabled:i=!1,orientation:j,dir:k,loop:l=!0,onValueChange:m,...n}=a,o=av(c),p=(0,P.jH)(k),[q,r]=(0,O.i)({prop:g,defaultProp:f??null,onChange:m,caller:as});return(0,d.jsx)(ax,{scope:c,name:e,required:h,disabled:i,value:q,onValueChange:r,children:(0,d.jsx)($,{asChild:!0,...o,orientation:j,dir:p,loop:l,children:(0,d.jsx)(K.sG.div,{role:"radiogroup","aria-required":h,"aria-orientation":j,"data-disabled":i?"":void 0,dir:p,...n,ref:b})})})});az.displayName=as;var aA="RadioGroupItem",aB=e.forwardRef((a,b)=>{let{__scopeRadioGroup:c,disabled:f,...g}=a,h=ay(aA,c),i=h.disabled||f,j=av(c),k=aw(c),l=e.useRef(null),m=(0,I.s)(b,l),n=h.value===g.value,o=e.useRef(!1);return e.useEffect(()=>{let a=a=>{ar.includes(a.key)&&(o.current=!0)},b=()=>o.current=!1;return document.addEventListener("keydown",a),document.addEventListener("keyup",b),()=>{document.removeEventListener("keydown",a),document.removeEventListener("keyup",b)}},[]),(0,d.jsx)(ab,{asChild:!0,...j,focusable:!i,active:n,children:(0,d.jsx)(am,{disabled:i,required:h.required,checked:n,...k,...g,name:h.name,ref:m,onCheck:()=>h.onValueChange(g.value),onKeyDown:(0,H.m)(a=>{"Enter"===a.key&&a.preventDefault()}),onFocus:(0,H.m)(g.onFocus,()=>{o.current&&l.current?.click()})})})});aB.displayName=aA;var aC=e.forwardRef((a,b)=>{let{__scopeRadioGroup:c,...e}=a,f=aw(c);return(0,d.jsx)(ao,{...f,...e,ref:b})});aC.displayName="RadioGroupIndicator";var aD=c(65822),aE=c(4780);let aF=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(az,{className:(0,aE.cn)("grid gap-2",a),...b,ref:c}));aF.displayName=az.displayName;let aG=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(aB,{ref:c,className:(0,aE.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),...b,children:(0,d.jsx)(aC,{className:"flex items-center justify-center",children:(0,d.jsx)(aD.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));aG.displayName=aB.displayName;var aH=c(80013),aI=c(13964),aJ=c(52581);function aK({open:a,onOpenChange:b,currentPlan:c,planTypes:h}){let[i,j]=(0,e.useState)(""),[k,m]=(0,e.useState)("monthly"),n=(0,w.Jd)(),o=async()=>{if(!i)return void aJ.oR.error("L\xfctfen bir plan se\xe7in");try{await n.mutateAsync({planName:i,billingCycle:k}),aJ.oR.success("Planınız başarıyla g\xfcncellendi!"),b(!1)}catch(a){aJ.oR.error(a instanceof Error?a.message:"Plan g\xfcncellenirken hata oluştu")}},p=a=>c?.plan_name===a;return(0,d.jsx)(G.lG,{open:a,onOpenChange:b,children:(0,d.jsxs)(G.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,d.jsxs)(G.c7,{children:[(0,d.jsx)(G.L3,{className:"text-2xl",children:"Planınızı Y\xfckseltin"}),(0,d.jsx)(G.rr,{children:"İhtiya\xe7larınıza en uygun planı se\xe7in ve daha fazla \xf6zellikten yararlanın"})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(aH.J,{className:"text-base font-medium",children:"Faturalama D\xf6ng\xfcs\xfc"}),(0,d.jsxs)(aF,{value:k,onValueChange:a=>m(a),className:"flex gap-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(aG,{value:"monthly",id:"monthly"}),(0,d.jsx)(aH.J,{htmlFor:"monthly",children:"Aylık"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(aG,{value:"yearly",id:"yearly"}),(0,d.jsxs)(aH.J,{htmlFor:"yearly",className:"flex items-center gap-2",children:["Yıllık",(0,d.jsx)(y.E,{variant:"secondary",className:"text-xs",children:"%17 İndirim"})]})]})]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:h.map(a=>{var b;let c=(a=>{if("free"===a.name||"enterprise"===a.name)return null;let b=12*a.price_monthly,c=b-a.price_yearly,d=Math.round(c/b*100);return{amount:c,percentage:d}})(a),e=p(a.name),f="enterprise"!==(b=a.name)&&!p(b);return(0,d.jsxs)(g.Zp,{className:(0,aE.cn)("relative cursor-pointer transition-all duration-200",(a=>{switch(a){case"free":return"border-blue-200 bg-blue-50";case"professional":return"border-yellow-200 bg-yellow-50";case"enterprise":return"border-purple-200 bg-purple-50";default:return"border-gray-200 bg-gray-50"}})(a.name),i===a.name&&"ring-2 ring-blue-500",e&&"ring-2 ring-green-500",!f&&"opacity-60 cursor-not-allowed"),onClick:()=>f&&j(a.name),children:[e&&(0,d.jsx)(y.E,{className:"absolute -top-2 left-1/2 transform -translate-x-1/2 bg-green-500",children:"Mevcut Plan"}),"professional"===a.name&&(0,d.jsx)(y.E,{className:"absolute -top-2 right-4 bg-blue-500",children:"Pop\xfcler"}),(0,d.jsxs)(g.aR,{className:"text-center pb-2",children:[(0,d.jsx)("div",{className:"flex justify-center mb-2",children:(a=>{switch(a){case"free":return(0,d.jsx)(B.A,{className:"h-6 w-6 text-blue-500"});case"professional":return(0,d.jsx)(C.A,{className:"h-6 w-6 text-yellow-500"});case"enterprise":return(0,d.jsx)(D.A,{className:"h-6 w-6 text-purple-500"});default:return(0,d.jsx)(B.A,{className:"h-6 w-6 text-gray-500"})}})(a.name)}),(0,d.jsx)(g.ZB,{className:"text-xl",children:a.display_name}),(0,d.jsx)(g.BT,{className:"text-sm",children:a.description}),(0,d.jsxs)("div",{className:"pt-2",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-gray-900",children:(a=>{if("enterprise"===a.name)return"\xd6zel Fiyat";if("free"===a.name)return"\xdccretsiz";let b="monthly"===k?a.price_monthly:a.price_yearly,c="monthly"===k?"ay":"yıl";return`₺${b}/${c}`})(a)}),"yearly"===k&&c&&(0,d.jsxs)("div",{className:"text-sm text-green-600 font-medium",children:["Yıllık ₺",c.amount," tasarruf"]})]})]}),(0,d.jsxs)(g.Wu,{className:"space-y-4",children:[(0,d.jsx)(A.w,{}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsx)("span",{children:"Proje Limiti"}),(0,d.jsx)("span",{className:"font-medium",children:-1===a.max_projects?"Sınırsız":a.max_projects})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsx)("span",{children:"Prompt Limiti"}),(0,d.jsx)("span",{className:"font-medium",children:-1===a.max_prompts_per_project?"Sınırsız":`${a.max_prompts_per_project}/proje`})]})]}),(0,d.jsx)(A.w,{}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h4",{className:"font-medium text-sm",children:"\xd6zellikler"}),(0,d.jsx)("div",{className:"space-y-1",children:Object.entries(a.features).map(([a,b])=>(0,d.jsxs)("div",{className:"flex items-center gap-2 text-xs",children:[b?(0,d.jsx)(aI.A,{className:"h-3 w-3 text-green-500"}):(0,d.jsx)(l.A,{className:"h-3 w-3 text-gray-400"}),(0,d.jsxs)("span",{className:b?"text-gray-900":"text-gray-500",children:["context_gallery"===a&&"Context Gallery","api_access"===a&&"API Erişimi","support"===a&&`${b} Destek`,"team_features"===a&&"Takım \xd6zellikleri","advanced_analytics"===a&&"Gelişmiş Analitik","sso"===a&&"SSO Entegrasyonu","custom_deployment"===a&&"\xd6zel Deployment"]})]},a))})]}),"enterprise"===a.name&&(0,d.jsx)("div",{className:"text-xs text-gray-600 bg-gray-50 p-2 rounded",children:"Kurumsal plan i\xe7in satış ekibimizle iletişime ge\xe7in"})]})]},a.id)})}),(0,d.jsxs)("div",{className:"flex justify-end gap-3 pt-4",children:[(0,d.jsx)(f.$,{variant:"outline",onClick:()=>b(!1),children:"İptal"}),(0,d.jsx)(f.$,{onClick:o,disabled:!i||n.isPending,children:n.isPending?"G\xfcncelleniyor...":"Planı G\xfcncelle"})]})]})]})})}function aL(){let[a,b]=(0,e.useState)(!1),{data:c,isLoading:h}=(0,w.EU)(),{data:i,isLoading:j}=(0,w.p$)(),{data:k}=(0,w.C)();if(h||j)return(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-2/3"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-3/4"})]})})]});let l=(a,b)=>{if(-1===b)return"text-green-600";let c=a/b*100;return c>=90?"text-red-600":c>=70?"text-yellow-600":"text-green-600"},m=(a,b)=>{if(-1===b)return"bg-green-500";let c=a/b*100;return c>=90?"bg-red-500":c>=70?"bg-yellow-500":"bg-green-500"},n=()=>{if(!i||!c||"enterprise"===c.plan_name)return!1;let a=-1===i.max_projects?0:i.current_projects/i.max_projects*100,b=-1===i.max_prompts_per_project?0:i.current_prompts/i.max_prompts_per_project*100;return a>=80||b>=80};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(g.Zp,{className:"w-full",children:[(0,d.jsx)(g.aR,{className:"pb-3",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[c&&(a=>{switch(a){case"free":return(0,d.jsx)(B.A,{className:"h-5 w-5 text-blue-500"});case"professional":return(0,d.jsx)(C.A,{className:"h-5 w-5 text-yellow-500"});case"enterprise":return(0,d.jsx)(D.A,{className:"h-5 w-5 text-purple-500"});default:return(0,d.jsx)(B.A,{className:"h-5 w-5 text-gray-500"})}})(c.plan_name),(0,d.jsxs)("div",{children:[(0,d.jsx)(g.ZB,{className:"text-lg",children:c?.display_name||"Plan Y\xfckleniyor..."}),(0,d.jsx)(g.BT,{children:"Mevcut planınız ve kullanım durumunuz"})]})]}),c&&(0,d.jsx)(y.E,{variant:"outline",className:(0,aE.cn)("font-medium",(a=>{switch(a){case"free":return"bg-blue-50 text-blue-700 border-blue-200";case"professional":return"bg-yellow-50 text-yellow-700 border-yellow-200";case"enterprise":return"bg-purple-50 text-purple-700 border-purple-200";default:return"bg-gray-50 text-gray-700 border-gray-200"}})(c.plan_name)),children:"active"===c.status?"Aktif":c.status})]})}),(0,d.jsxs)(g.Wu,{className:"space-y-4",children:[i&&(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsx)("span",{className:"font-medium",children:"Projeler"}),(0,d.jsxs)("span",{className:l(i.current_projects,i.max_projects),children:[i.current_projects," / ",-1===i.max_projects?"∞":i.max_projects]})]}),(0,d.jsx)(z.k,{value:-1===i.max_projects?0:i.current_projects/i.max_projects*100,className:"h-2",style:{"--progress-background":m(i.current_projects,i.max_projects)}})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsx)("span",{className:"font-medium",children:"Prompt'lar (Proje başına max)"}),(0,d.jsxs)("span",{className:l(i.current_prompts,i.max_prompts_per_project),children:[i.current_prompts," / ",-1===i.max_prompts_per_project?"∞":i.max_prompts_per_project]})]}),(0,d.jsx)(z.k,{value:-1===i.max_prompts_per_project?0:i.current_prompts/i.max_prompts_per_project*100,className:"h-2",style:{"--progress-background":m(i.current_prompts,i.max_prompts_per_project)}})]})]}),n()&&(0,d.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,d.jsx)(E.A,{className:"h-4 w-4 text-yellow-600"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-yellow-800",children:"Limitinize yaklaşıyorsunuz"}),(0,d.jsx)("p",{className:"text-xs text-yellow-700",children:"Daha fazla \xf6zellik i\xe7in planınızı y\xfckseltin"})]})]}),(0,d.jsx)(A.w,{}),c?.features&&(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Plan \xd6zellikleri"}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[c.features.context_gallery&&(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),(0,d.jsx)("span",{children:"Context Gallery"})]}),c.features.api_access&&(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),(0,d.jsx)("span",{children:"API Erişimi"})]}),c.features.team_features&&(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),(0,d.jsx)("span",{children:"Takım \xd6zellikleri"})]}),c.features.advanced_analytics&&(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),(0,d.jsx)("span",{children:"Gelişmiş Analitik"})]})]})]}),c?.plan_name!=="enterprise"&&(0,d.jsxs)(f.$,{onClick:()=>b(!0),className:"w-full",variant:n()?"default":"outline",children:[(0,d.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"Planı Y\xfckselt"]})]})]}),(0,d.jsx)(aK,{open:a,onOpenChange:b,currentPlan:c,planTypes:k||[]})]})}var aM=c(16259);function aN({onClose:a,isCollapsed:b=!1,onToggleCollapse:c}){let[r,y]=(0,e.useState)(""),[z,A]=(0,e.useState)(!1),[B,C]=(0,e.useState)(""),{activeProjectId:D,setActiveProjectId:E}=(0,t.C)(),{data:F=[],isLoading:G,error:H}=(0,u.YK)(),{data:I}=(0,v.Jd)(),{data:J}=(0,w.p$)(),K=(0,u.bL)(),L=(0,v.Rt)();console.log(`📁 [PROJECT_SIDEBAR] Projects:`,{count:F.length,loading:G,error:H,projects:F.map(a=>({id:a.id,name:a.name}))});let M=F.filter(a=>a.name.toLowerCase().includes(r.toLowerCase())),N=async()=>{if(B.trim()){if(J&&!J.can_create_project)return void aJ.oR.error(`Proje oluşturma limitinize ulaştınız (${J.current_projects}/${J.max_projects}). Planınızı y\xfckseltin.`);try{let a=await K.mutateAsync({name:B.trim(),context_text:""});E(a.id),C(""),A(!1)}catch(a){console.error("Proje oluşturma hatası:",a)}}},O=async()=>{try{console.log("Logout button clicked"),await L.mutateAsync()}catch(a){console.error("Logout error in component:",a)}};return(0,d.jsxs)("div",{className:"flex flex-col h-full",children:[(0,d.jsxs)("div",{className:`border-b border-gray-200 ${b?"p-2":"p-4 lg:p-6"}`,children:[(0,d.jsxs)("div",{className:`flex items-center ${b?"justify-center mb-2":"justify-between mb-4"}`,children:[!b&&(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("img",{src:"/logo.png",alt:"Promptbir Logo",className:"h-6 w-auto"}),(0,d.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Promptbir"})]}),b&&(0,d.jsx)("img",{src:"/logo.png",alt:"Promptbir Logo",className:"h-6 w-auto",title:"Promptbir"}),(0,d.jsxs)("div",{className:`flex items-center gap-2 ${b?"flex-col":""}`,children:[c&&(0,d.jsx)(f.$,{variant:"ghost",size:"sm",onClick:c,className:"hidden lg:flex",title:b?"Proje panelini genişlet":"Proje panelini daralt",children:b?(0,d.jsx)(j.A,{className:"h-4 w-4"}):(0,d.jsx)(k.A,{className:"h-4 w-4"})}),a&&(0,d.jsx)(f.$,{variant:"ghost",size:"sm",onClick:a,className:"lg:hidden",children:(0,d.jsx)(l.A,{className:"h-4 w-4"})}),(0,d.jsx)(s(),{href:"/profile",children:(0,d.jsx)(f.$,{variant:"ghost",size:"sm",title:I?.email||"Kullanıcı",children:(0,d.jsx)(m.A,{className:"h-4 w-4"})})}),(0,d.jsx)(f.$,{variant:"ghost",size:"sm",onClick:O,disabled:L.isPending,title:"\xc7ıkış Yap",children:(0,d.jsx)(n.A,{className:"h-4 w-4"})})]})]}),!b&&(0,d.jsxs)("div",{className:"relative mb-4",children:[(0,d.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,d.jsx)(h.p,{placeholder:"Projelerde ara...",value:r,onChange:a=>y(a.target.value),className:"pl-10"})]}),!b&&J&&(0,d.jsx)(x.LD,{type:"project",current:J.current_projects,max:J.max_projects,onUpgrade:()=>{}}),(0,d.jsxs)(f.$,{onClick:()=>A(!0),className:`${b?"w-8 h-8 p-0":"w-full"}`,size:"sm",title:b?"Yeni Proje":void 0,disabled:J&&!J.can_create_project,children:[(0,d.jsx)(p.A,{className:"h-4 w-4"}),!b&&(0,d.jsx)("span",{className:"ml-2",children:"Yeni Proje"})]})]}),(0,d.jsx)(i.F,{className:`flex-1 ${b?"p-2":"p-4"}`,children:(0,d.jsxs)("div",{className:"space-y-2",children:[z&&!b&&(0,d.jsx)(g.Zp,{className:"border-blue-200 bg-blue-50",children:(0,d.jsxs)(g.Wu,{className:"p-3",children:[(0,d.jsx)(h.p,{placeholder:"Proje adı...",value:B,onChange:a=>C(a.target.value),onKeyPress:a=>"Enter"===a.key&&N(),className:"mb-2",autoFocus:!0}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(f.$,{size:"sm",onClick:N,children:"Oluştur"}),(0,d.jsx)(f.$,{size:"sm",variant:"outline",onClick:()=>{A(!1),C("")},children:"İptal"})]})]})}),M.map(c=>(0,d.jsx)(g.Zp,{className:`transition-all hover:shadow-md cursor-pointer ${D===c.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"} ${b?"p-2":""}`,onClick:b?()=>{E(c.id),a?.()}:()=>{E(c.id),a?.()},title:b?c.name:void 0,children:b?(0,d.jsx)("div",{className:"flex items-center justify-center",children:(0,d.jsx)(q.A,{className:`h-4 w-4 ${D===c.id?"text-blue-600":"text-gray-600"}`})}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.aR,{className:"pb-2",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 text-blue-600 flex-shrink-0"}),(0,d.jsx)(aM.ProjectNameEditor,{projectId:c.id,currentName:c.name,onNameUpdated:a=>{console.log(`📝 [PROJECT_SIDEBAR] Project name updated:`,{projectId:c.id,oldName:c.name,newName:a})},className:"flex-1 min-w-0"})]})}),(0,d.jsx)(g.Wu,{className:"pt-0",children:(0,d.jsx)("p",{className:"text-xs text-gray-500",children:new Date(c.created_at).toLocaleDateString("tr-TR")})})]})},c.id))]})}),!b&&(0,d.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,d.jsx)(aL,{})})]})}var aO=c(77154),aP=c(28637),aQ=c(12941),aR=c(84027);let aS=new Map,aT=async(a,b)=>{if(aS.has(a))return aS.get(a);let c=Date.now(),d=b().then(b=>(aW(a,c),b));return aS.set(a,d),d};(0,e.lazy)(()=>aT("context-gallery",()=>Promise.all([c.e(2762),c.e(4617),c.e(9450),c.e(4103),c.e(6257),c.e(5107)]).then(c.bind(c,56257)).then(a=>({default:a.default||a}))));let aU=(0,e.lazy)(()=>aT("prompt-workspace",()=>Promise.all([c.e(4103),c.e(6861)]).then(c.bind(c,76861)).then(a=>({default:a.default||a})))),aV=(0,e.lazy)(()=>aT("context-sidebar",()=>c.e(9225).then(c.bind(c,19225)).then(a=>({default:a.default||a}))));(0,e.lazy)(()=>aT("project-name-editor",()=>Promise.resolve().then(c.bind(c,16259)).then(a=>({default:a.ProjectNameEditor})))),(0,e.lazy)(()=>aT("context-creation-modal",()=>Promise.all([c.e(2762),c.e(4617),c.e(4103)]).then(c.bind(c,34617)).then(a=>({default:a.default||a})))),(0,e.lazy)(()=>aT("context-edit-modal",()=>Promise.all([c.e(2762),c.e(9450),c.e(815)]).then(c.bind(c,79450)).then(a=>({default:a.default||a})))),(0,e.lazy)(()=>aT("dashboard-page",()=>Promise.resolve().then(c.bind(c,39020)).then(a=>({default:a.default||a})))),(0,e.lazy)(()=>aT("profile-page",()=>c.e(6200).then(c.bind(c,86200)).then(a=>({default:a.default||a})))),(0,e.lazy)(()=>aT("auth-page",()=>c.e(5795).then(c.bind(c,55795)).then(a=>({default:a.default||a}))));let aW=(a,b)=>{let c=Date.now()-b;console.log(`🚀 [DYNAMIC_IMPORT] ${a} loaded in ${c}ms`)},aX=(0,e.memo)(function(){let[a,b]=(0,e.useState)(!1),[c,g]=(0,e.useState)(!1),[h,i]=(0,e.useState)(!1),{isMobile:j,orientation:k}=function(){let[a,b]=(0,e.useState)(()=>({width:1024,height:768,breakpoint:"lg",deviceType:"desktop",orientation:"landscape",isMobile:!1,isTablet:!1,isDesktop:!0,isTouch:!1,pixelRatio:1}));return(0,e.useCallback)(()=>{},[]),a}(),{isProjectSidebarCollapsed:l,setIsProjectSidebarCollapsed:m,isContextSidebarCollapsed:n,setIsContextSidebarCollapsed:o}=(0,t.C)();return(0,e.useEffect)(()=>{},[]),(0,e.useEffect)(()=>{j&&(b(!1),g(!1))},[k,j]),(0,d.jsx)(aO.q,{children:(0,d.jsx)(aP.ErrorBoundary,{level:"page",showDetails:!1,children:(0,d.jsxs)("div",{className:`
        flex full-height-mobile bg-gray-50 relative
        ${j?"flex-col":"flex-row"}
      `,children:[(a||c)&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black/50 z-40 lg:hidden mobile-transition",onClick:()=>{b(!1),g(!1)}}),(0,d.jsx)("div",{className:`
          fixed lg:relative inset-y-0 left-0 z-50
          w-[85vw] sm:w-80
          ${l?"lg:w-16":"lg:w-80"}
          transform transition-all duration-300 ease-in-out
          lg:transform-none lg:translate-x-0
          border-r border-gray-200 bg-white
          ${a?"translate-x-0":"-translate-x-full lg:translate-x-0"}
          ${a?"block":"hidden lg:block"}
        `,children:(0,d.jsx)(aN,{onClose:()=>b(!1),isCollapsed:l,onToggleCollapse:()=>m(!l)})}),(0,d.jsxs)("div",{className:"flex-1 flex flex-col min-w-0",children:[(0,d.jsxs)("div",{className:"lg:hidden flex items-center justify-between p-4 bg-white border-b border-gray-200 safe-area-top",children:[(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>b(!0),className:"flex items-center gap-2 touch-target focus-visible-enhanced",children:[(0,d.jsx)(aQ.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium mobile-text-base",children:"Projeler"})]}),(0,d.jsx)("h1",{className:"text-lg font-semibold text-gray-900 mobile-text-base",children:"Promptbir"}),(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>g(!0),className:"flex items-center gap-2 touch-target focus-visible-enhanced",children:[(0,d.jsx)(aR.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium mobile-text-base",children:"Ayarlar"})]})]}),(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),children:(0,d.jsx)(aU,{isContextGalleryOpen:h,onToggleContextGallery:()=>i(!h)})})]}),(0,d.jsx)("div",{className:`
          fixed xl:relative inset-y-0 right-0 z-50
          w-[90vw] sm:w-96
          ${n?"xl:w-16":"xl:w-96"}
          transform transition-all duration-300 ease-in-out
          xl:transform-none xl:translate-x-0
          border-l border-gray-200 bg-white
          ${c?"translate-x-0":"translate-x-full xl:translate-x-0"}
          ${c?"block":"hidden xl:block"}
        `,children:(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),children:(0,d.jsx)(aV,{onClose:()=>g(!1),isCollapsed:n,onToggleCollapse:()=>o(!n),isContextGalleryOpen:h,onToggleContextGallery:()=>i(!h)})})})]})})})})},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46657:(a,b,c)=>{"use strict";c.d(b,{k:()=>v});var d=c(60687),e=c(43210),f=c(11273),g=c(14163),h="Progress",[i,j]=(0,f.A)(h),[k,l]=i(h),m=e.forwardRef((a,b)=>{var c,e;let{__scopeProgress:f,value:h=null,max:i,getValueLabel:j=p,...l}=a;(i||0===i)&&!s(i)&&console.error((c=`${i}`,`Invalid prop \`max\` of value \`${c}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=s(i)?i:100;null===h||t(h,m)||console.error((e=`${h}`,`Invalid prop \`value\` of value \`${e}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let n=t(h,m)?h:null,o=r(n)?j(n,m):void 0;return(0,d.jsx)(k,{scope:f,value:n,max:m,children:(0,d.jsx)(g.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":r(n)?n:void 0,"aria-valuetext":o,role:"progressbar","data-state":q(n,m),"data-value":n??void 0,"data-max":m,...l,ref:b})})});m.displayName=h;var n="ProgressIndicator",o=e.forwardRef((a,b)=>{let{__scopeProgress:c,...e}=a,f=l(n,c);return(0,d.jsx)(g.sG.div,{"data-state":q(f.value,f.max),"data-value":f.value??void 0,"data-max":f.max,...e,ref:b})});function p(a,b){return`${Math.round(a/b*100)}%`}function q(a,b){return null==a?"indeterminate":a===b?"complete":"loading"}function r(a){return"number"==typeof a}function s(a){return r(a)&&!isNaN(a)&&a>0}function t(a,b){return r(a)&&!isNaN(a)&&a<=b&&a>=0}o.displayName=n;var u=c(4780);let v=e.forwardRef(({className:a,value:b,indicatorClassName:c,...e},f)=>(0,d.jsx)(m,{ref:f,className:(0,u.cn)("relative h-2 w-full overflow-hidden rounded-full bg-gray-100",a),...e,children:(0,d.jsx)(o,{className:(0,u.cn)("h-full w-full flex-1 bg-gray-900 transition-all",c),style:{transform:`translateX(-${100-(b||0)}%)`}})}));v.displayName=m.displayName},47033:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65822:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},66432:(a,b,c)=>{"use strict";c.d(b,{LD:()=>h});var d=c(60687),e=c(43649),f=c(25541),g=c(29523);function h({type:a,current:b,max:c,onUpgrade:h}){return -1===c||b<c?null:(0,d.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded text-red-800 text-sm",children:[(0,d.jsx)(e.A,{className:"h-4 w-4 text-red-500"}),(0,d.jsx)("span",{className:"flex-1",children:"project"===a?"Proje limiti doldu":"Prompt limiti doldu"}),h&&(0,d.jsxs)(g.$,{size:"sm",variant:"outline",onClick:h,className:"h-6 text-xs",children:[(0,d.jsx)(f.A,{className:"h-3 w-3 mr-1"}),"Y\xfckselt"]})]})}c(44493),c(96834),c(43210)},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},80559:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Masa\xfcst\xfc\\\\Promptbir\\\\promptflow\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\dashboard\\page.tsx","default")},81630:a=>{"use strict";a.exports=require("http")},82570:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]])},84027:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88904:(a,b,c)=>{Promise.resolve().then(c.bind(c,39020))},90131:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]])},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99270:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,904,5814,3303,7210,2358,3646,1093,2086],()=>b(b.s=37941));module.exports=c})();