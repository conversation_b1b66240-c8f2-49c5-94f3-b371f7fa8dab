import { Metadata } from 'next'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Sparkles, 
  Users, 
  Rocket, 
  Heart, 
  Code,
  Palette,
  BarChart3,
  Headphones,
  MapPin,
  Clock,
  DollarSign,
  Star,
  Coffee,
  Zap
} from 'lucide-react'

export const metadata: Metadata = {
  title: 'Kariyer - PromptBir | İş İlanları ve Kariyer Fırsatları',
  description: 'PromptBir ekibine katılın! AI teknolojileri alanında kariyer fırsatları, açık pozisyonlar ve çalışma kültürümüz hakkında bilgi edinin.',
  keywords: [
    'PromptBir kariyer',
    'iş ilanları',
    'AI şirketi iş',
    'yazılım geliştirici iş',
    'startup kariyer',
    'teknoloji işleri'
  ],
  openGraph: {
    title: 'Kariyer - PromptBir',
    description: 'AI teknolojileri alanında kariyer fırsatları ve açık pozisyonlar',
    type: 'website',
    url: 'https://promptbir.com/careers'
  }
}

export default function CareersPage() {
  const openPositions = [
    {
      id: 1,
      title: 'Senior Frontend Developer',
      department: 'Engineering',
      location: 'Remote / İstanbul',
      type: 'Full-time',
      experience: '3+ yıl',
      skills: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS'],
      description: 'Modern web teknolojileri ile kullanıcı deneyimini geliştiren bir frontend developer arıyoruz.'
    },
    {
      id: 2,
      title: 'Backend Developer',
      department: 'Engineering',
      location: 'Remote / İstanbul',
      type: 'Full-time',
      experience: '2+ yıl',
      skills: ['Node.js', 'PostgreSQL', 'Supabase', 'API Design'],
      description: 'Ölçeklenebilir backend sistemleri geliştiren deneyimli bir developer arıyoruz.'
    },
    {
      id: 3,
      title: 'Product Designer',
      department: 'Design',
      location: 'Remote / İstanbul',
      type: 'Full-time',
      experience: '2+ yıl',
      skills: ['Figma', 'UI/UX', 'Design Systems', 'User Research'],
      description: 'Kullanıcı odaklı tasarımlar yapan yaratıcı bir product designer arıyoruz.'
    },
    {
      id: 4,
      title: 'DevOps Engineer',
      department: 'Engineering',
      location: 'Remote / İstanbul',
      type: 'Full-time',
      experience: '3+ yıl',
      skills: ['Docker', 'Kubernetes', 'AWS', 'CI/CD'],
      description: 'Altyapı ve deployment süreçlerini optimize eden bir DevOps engineer arıyoruz.'
    }
  ]

  const benefits = [
    {
      icon: <DollarSign className="h-6 w-6 text-green-600" />,
      title: 'Rekabetçi Maaş',
      description: 'Sektör standartlarının üzerinde maaş ve performans bonusu'
    },
    {
      icon: <MapPin className="h-6 w-6 text-blue-600" />,
      title: 'Esnek Çalışma',
      description: 'Remote çalışma imkanı ve esnek çalışma saatleri'
    },
    {
      icon: <Rocket className="h-6 w-6 text-purple-600" />,
      title: 'Gelişim Fırsatları',
      description: 'Eğitim bütçesi, konferanslar ve sertifikasyon desteği'
    },
    {
      icon: <Heart className="h-6 w-6 text-red-600" />,
      title: 'Sağlık Sigortası',
      description: 'Kapsamlı sağlık sigortası ve wellness programları'
    },
    {
      icon: <Coffee className="h-6 w-6 text-orange-600" />,
      title: 'Ofis İmkanları',
      description: 'Modern ofis, ücretsiz yemek ve içecek'
    },
    {
      icon: <Star className="h-6 w-6 text-yellow-600" />,
      title: 'Hisse Senedi',
      description: 'Şirket büyümesinden pay alma fırsatı'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link 
              href="/" 
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Ana Sayfaya Dön</span>
            </Link>
            <div className="flex items-center space-x-2">
              <Sparkles className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">PromptBir</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Users className="h-8 w-8 text-purple-600" />
          </div>
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
            Ekibimize Katılın
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            AI teknolojilerinin geleceğini şekillendiren bir ekibin parçası olun. 
            PromptBir'de yenilikçi projeler üzerinde çalışın ve kariyerinizi geliştirin.
          </p>
        </div>

        {/* Company Culture */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Çalışma Kültürümüz</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="shadow-lg text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <Zap className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle className="text-lg">İnovasyon Odaklı</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Sürekli öğrenme ve yenilik yapma kültürü ile teknolojinin 
                  sınırlarını zorluyoruz.
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-lg text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle className="text-lg">Takım Ruhu</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Birlikte başarıya ulaşan, birbirini destekleyen ve 
                  güçlü bir takım ruhu olan ekip.
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-lg text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                  <Heart className="h-6 w-6 text-purple-600" />
                </div>
                <CardTitle className="text-lg">İş-Yaşam Dengesi</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Çalışanlarımızın mutluluğunu ve refahını önemseyen, 
                  esnek çalışma imkanları sunan ortam.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Benefits */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Çalışan Avantajları</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {benefits.map((benefit, index) => (
              <Card key={index} className="shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      {benefit.icon}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">
                        {benefit.title}
                      </h3>
                      <p className="text-gray-600 text-sm">
                        {benefit.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Open Positions */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Açık Pozisyonlar</h2>
          <div className="space-y-6">
            {openPositions.map((position) => (
              <Card key={position.id} className="shadow-lg">
                <CardHeader>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                      <CardTitle className="text-xl mb-2">{position.title}</CardTitle>
                      <CardDescription className="text-base">
                        {position.description}
                      </CardDescription>
                    </div>
                    <Button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 md:flex-shrink-0">
                      Başvur
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">{position.department}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">{position.location}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">{position.type}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <BarChart3 className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">{position.experience}</span>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {position.skills.map((skill, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* No Open Position */}
        <Card className="shadow-lg mb-16 bg-gradient-to-r from-gray-50 to-blue-50 border-blue-200">
          <CardContent className="p-8 text-center">
            <Users className="h-16 w-16 text-blue-600 mx-auto mb-6" />
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Aradığınız Pozisyon Yok mu?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Şu anda açık olmayan bir pozisyonda çalışmak istiyorsanız, 
              CV'nizi gönderin. Uygun fırsat çıktığında sizinle iletişime geçelim.
            </p>
            <Button 
              variant="outline" 
              size="lg"
              className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
            >
              Spontan Başvuru Yap
            </Button>
          </CardContent>
        </Card>

        {/* Application Process */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Başvuru Süreci</h2>
          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 font-bold">1</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Başvuru</h3>
              <p className="text-gray-600 text-sm">
                CV ve motivasyon mektubunuzu gönderin
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-purple-600 font-bold">2</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">İnceleme</h3>
              <p className="text-gray-600 text-sm">
                Başvurunuzu detaylı olarak inceleriz
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-green-600 font-bold">3</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Görüşme</h3>
              <p className="text-gray-600 text-sm">
                Teknik ve kültürel uyum görüşmeleri
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-orange-600 font-bold">4</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Karar</h3>
              <p className="text-gray-600 text-sm">
                Sonucu 1 hafta içinde bildiririz
              </p>
            </div>
          </div>
        </section>

        {/* Team Stats */}
        <section className="mb-16">
          <Card className="shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="py-12">
              <h2 className="text-3xl font-bold text-center mb-12">Ekibimiz Rakamlarla</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div>
                  <div className="text-3xl font-bold mb-2">15+</div>
                  <div className="text-blue-100">Takım Üyesi</div>
                </div>
                <div>
                  <div className="text-3xl font-bold mb-2">5+</div>
                  <div className="text-blue-100">Farklı Uzmanlık</div>
                </div>
                <div>
                  <div className="text-3xl font-bold mb-2">100%</div>
                  <div className="text-blue-100">Remote Uyumlu</div>
                </div>
                <div>
                  <div className="text-3xl font-bold mb-2">4.8/5</div>
                  <div className="text-blue-100">Çalışan Memnuniyeti</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Contact */}
        <section className="text-center">
          <Card className="shadow-lg">
            <CardContent className="py-12">
              <Headphones className="h-16 w-16 text-blue-600 mx-auto mb-6" />
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Sorularınız mı var?
              </h2>
              <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
                Kariyer fırsatları, çalışma koşulları veya başvuru süreci hakkında 
                sorularınız varsa bizimle iletişime geçin.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="mailto:<EMAIL>">
                  <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700">
                    <EMAIL>
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button variant="outline" size="lg">
                    İletişim Formu
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-400">
            © 2024 PromptBir. Tüm hakları saklıdır.
          </p>
        </div>
      </footer>
    </div>
  )
}
