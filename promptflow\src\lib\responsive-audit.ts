/**
 * Mobile-First Responsive Design Audit
 * Comprehensive responsive design analysis and optimization tools
 */

import { BREAKPOINTS, DeviceDetection } from '@/lib/responsive-utils'

// Audit result types
interface ResponsiveIssue {
  type: 'critical' | 'warning' | 'info'
  category: 'layout' | 'typography' | 'touch' | 'performance' | 'accessibility'
  element: string
  description: string
  recommendation: string
  breakpoint?: string
}

interface ResponsiveAuditResult {
  score: number
  issues: ResponsiveIssue[]
  summary: {
    critical: number
    warnings: number
    info: number
  }
  breakpointAnalysis: Record<string, {
    score: number
    issues: number
    optimized: boolean
  }>
}

// Responsive Design Auditor
export class ResponsiveAuditor {
  private issues: ResponsiveIssue[] = []

  // Main audit function
  static async performAudit(): Promise<ResponsiveAuditResult> {
    const auditor = new ResponsiveAuditor()
    
    // Run all audit checks
    await auditor.auditViewport()
    await auditor.auditTouchTargets()
    await auditor.auditTypography()
    await auditor.auditLayout()
    await auditor.auditImages()
    await auditor.auditPerformance()
    await auditor.auditAccessibility()
    
    return auditor.generateReport()
  }

  // Viewport and meta tag audit
  private async auditViewport() {
    const viewportMeta = document.querySelector('meta[name="viewport"]')
    
    if (!viewportMeta) {
      this.addIssue({
        type: 'critical',
        category: 'layout',
        element: 'head',
        description: 'Viewport meta tag eksik',
        recommendation: 'Viewport meta tag ekleyin: <meta name="viewport" content="width=device-width, initial-scale=1">'
      })
    } else {
      const content = viewportMeta.getAttribute('content') || ''
      
      if (!content.includes('width=device-width')) {
        this.addIssue({
          type: 'critical',
          category: 'layout',
          element: 'meta[name="viewport"]',
          description: 'Viewport width device-width olarak ayarlanmamış',
          recommendation: 'width=device-width ekleyin'
        })
      }
      
      if (!content.includes('initial-scale=1')) {
        this.addIssue({
          type: 'warning',
          category: 'layout',
          element: 'meta[name="viewport"]',
          description: 'Initial scale ayarlanmamış',
          recommendation: 'initial-scale=1 ekleyin'
        })
      }
    }
  }

  // Touch target audit
  private async auditTouchTargets() {
    const interactiveElements = document.querySelectorAll(
      'button, a, input, select, textarea, [role="button"], [tabindex]'
    )

    interactiveElements.forEach(element => {
      const rect = element.getBoundingClientRect()
      const minSize = 44 // WCAG AA minimum

      if (rect.width < minSize || rect.height < minSize) {
        this.addIssue({
          type: 'critical',
          category: 'touch',
          element: this.getElementSelector(element as HTMLElement),
          description: `Touch target çok küçük: ${Math.round(rect.width)}x${Math.round(rect.height)}px`,
          recommendation: `Minimum ${minSize}x${minSize}px boyutunda olmalı`
        })
      }
    })
  }

  // Typography audit
  private async auditTypography() {
    const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, a, button, label')
    
    textElements.forEach(element => {
      const styles = window.getComputedStyle(element)
      const fontSize = parseFloat(styles.fontSize)
      const lineHeight = parseFloat(styles.lineHeight)

      // Check minimum font size for mobile
      if (DeviceDetection.isMobile() && fontSize < 16) {
        this.addIssue({
          type: 'warning',
          category: 'typography',
          element: this.getElementSelector(element as HTMLElement),
          description: `Mobile'da font boyutu çok küçük: ${fontSize}px`,
          recommendation: 'Mobile için minimum 16px font boyutu kullanın'
        })
      }

      // Check line height
      if (lineHeight && lineHeight < fontSize * 1.2) {
        this.addIssue({
          type: 'info',
          category: 'typography',
          element: this.getElementSelector(element as HTMLElement),
          description: 'Line height çok düşük',
          recommendation: 'Minimum 1.2 line-height kullanın'
        })
      }
    })
  }

  // Layout audit
  private async auditLayout() {
    // Check for horizontal scrolling
    if (document.body.scrollWidth > window.innerWidth) {
      this.addIssue({
        type: 'critical',
        category: 'layout',
        element: 'body',
        description: 'Horizontal scrolling mevcut',
        recommendation: 'Layout genişliğini viewport\'a sığacak şekilde ayarlayın'
      })
    }

    // Check for fixed widths
    const elementsWithFixedWidth = document.querySelectorAll('*')
    elementsWithFixedWidth.forEach(element => {
      const styles = window.getComputedStyle(element)
      const width = styles.width
      
      if (width && width.includes('px') && !width.includes('max-width')) {
        const widthValue = parseFloat(width)
        if (widthValue > window.innerWidth) {
          this.addIssue({
            type: 'warning',
            category: 'layout',
            element: this.getElementSelector(element as HTMLElement),
            description: `Fixed width viewport\'dan büyük: ${width}`,
            recommendation: 'Responsive units (%, vw, rem) kullanın'
          })
        }
      }
    })

    // Check for proper container usage
    const containers = document.querySelectorAll('.container, .max-w-')
    if (containers.length === 0) {
      this.addIssue({
        type: 'info',
        category: 'layout',
        element: 'body',
        description: 'Container class\'ları kullanılmamış',
        recommendation: 'Content için container class\'ları kullanın'
      })
    }
  }

  // Image audit
  private async auditImages() {
    const images = document.querySelectorAll('img')
    
    images.forEach(img => {
      // Check for responsive images
      if (!img.hasAttribute('srcset') && !img.style.maxWidth) {
        this.addIssue({
          type: 'warning',
          category: 'performance',
          element: this.getElementSelector(img),
          description: 'Responsive image optimizasyonu eksik',
          recommendation: 'srcset attribute veya max-width CSS kullanın'
        })
      }

      // Check for alt text
      if (!img.hasAttribute('alt')) {
        this.addIssue({
          type: 'critical',
          category: 'accessibility',
          element: this.getElementSelector(img),
          description: 'Alt text eksik',
          recommendation: 'Tüm görseller için alt text ekleyin'
        })
      }

      // Check for loading attribute
      if (!img.hasAttribute('loading')) {
        this.addIssue({
          type: 'info',
          category: 'performance',
          element: this.getElementSelector(img),
          description: 'Lazy loading eksik',
          recommendation: 'loading="lazy" attribute ekleyin'
        })
      }
    })
  }

  // Performance audit
  private async auditPerformance() {
    // Check for large images
    const images = document.querySelectorAll('img')
    images.forEach(img => {
      if (img.naturalWidth > 1920 || img.naturalHeight > 1080) {
        this.addIssue({
          type: 'warning',
          category: 'performance',
          element: this.getElementSelector(img),
          description: `Görsel boyutu çok büyük: ${img.naturalWidth}x${img.naturalHeight}`,
          recommendation: 'Görselleri optimize edin ve responsive boyutlar kullanın'
        })
      }
    })

    // Check for unused CSS
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]')
    if (stylesheets.length > 5) {
      this.addIssue({
        type: 'info',
        category: 'performance',
        element: 'head',
        description: `Çok fazla CSS dosyası: ${stylesheets.length}`,
        recommendation: 'CSS dosyalarını birleştirin ve minimize edin'
      })
    }
  }

  // Accessibility audit
  private async auditAccessibility() {
    // Check for proper heading structure
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
    let previousLevel = 0
    
    headings.forEach(heading => {
      const level = parseInt(heading.tagName.charAt(1))
      
      if (level > previousLevel + 1) {
        this.addIssue({
          type: 'warning',
          category: 'accessibility',
          element: this.getElementSelector(heading as HTMLElement),
          description: `Heading seviyesi atlandı: h${previousLevel} -> h${level}`,
          recommendation: 'Heading seviyelerini sıralı kullanın'
        })
      }
      
      previousLevel = level
    })

    // Check for focus indicators
    const focusableElements = document.querySelectorAll(
      'button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    focusableElements.forEach(element => {
      const styles = window.getComputedStyle(element)
      if (!styles.outline && !styles.boxShadow) {
        this.addIssue({
          type: 'warning',
          category: 'accessibility',
          element: this.getElementSelector(element as HTMLElement),
          description: 'Focus indicator eksik',
          recommendation: 'Keyboard navigation için focus indicator ekleyin'
        })
      }
    })
  }

  // Helper methods
  private addIssue(issue: ResponsiveIssue) {
    this.issues.push(issue)
  }

  private getElementSelector(element: HTMLElement): string {
    if (element.id) return `#${element.id}`
    if (element.className) return `.${element.className.split(' ')[0]}`
    return element.tagName.toLowerCase()
  }

  private generateReport(): ResponsiveAuditResult {
    const summary = {
      critical: this.issues.filter(i => i.type === 'critical').length,
      warnings: this.issues.filter(i => i.type === 'warning').length,
      info: this.issues.filter(i => i.type === 'info').length
    }

    // Calculate score (100 - penalties)
    const score = Math.max(0, 100 - (summary.critical * 10) - (summary.warnings * 5) - (summary.info * 1))

    // Breakpoint analysis
    const breakpointAnalysis: Record<string, any> = {}
    Object.keys(BREAKPOINTS).forEach(bp => {
      const breakpointIssues = this.issues.filter(i => i.breakpoint === bp)
      breakpointAnalysis[bp] = {
        score: Math.max(0, 100 - (breakpointIssues.length * 5)),
        issues: breakpointIssues.length,
        optimized: breakpointIssues.length === 0
      }
    })

    return {
      score,
      issues: this.issues,
      summary,
      breakpointAnalysis
    }
  }
}

// Responsive testing utilities
export class ResponsiveTester {
  // Test different viewport sizes
  static async testViewports(viewports: Array<{ width: number; height: number; name: string }>) {
    const results: Array<{ viewport: string; issues: ResponsiveIssue[] }> = []

    for (const viewport of viewports) {
      // Simulate viewport resize
      Object.defineProperty(window, 'innerWidth', { value: viewport.width, writable: true })
      Object.defineProperty(window, 'innerHeight', { value: viewport.height, writable: true })
      
      // Trigger resize event
      window.dispatchEvent(new Event('resize'))
      
      // Wait for layout to settle
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Run audit
      const audit = await ResponsiveAuditor.performAudit()
      
      results.push({
        viewport: viewport.name,
        issues: audit.issues
      })
    }

    return results
  }

  // Common device presets
  static getDevicePresets() {
    return [
      { width: 375, height: 667, name: 'iPhone SE' },
      { width: 390, height: 844, name: 'iPhone 12' },
      { width: 768, height: 1024, name: 'iPad' },
      { width: 1024, height: 768, name: 'iPad Landscape' },
      { width: 1440, height: 900, name: 'Desktop' },
      { width: 1920, height: 1080, name: 'Large Desktop' }
    ]
  }
}

// Auto-audit on page load
export function enableAutoAudit() {
  if (typeof window === 'undefined') return

  let auditTimeout: NodeJS.Timeout

  const runAudit = async () => {
    clearTimeout(auditTimeout)
    auditTimeout = setTimeout(async () => {
      const result = await ResponsiveAuditor.performAudit()
      
      if (process.env.NODE_ENV === 'development') {
        console.group('🔍 Responsive Design Audit')
        console.log(`Score: ${result.score}/100`)
        console.log(`Issues: ${result.issues.length}`)
        console.table(result.summary)
        
        if (result.issues.length > 0) {
          console.group('Issues')
          result.issues.forEach(issue => {
            const emoji = issue.type === 'critical' ? '🚨' : issue.type === 'warning' ? '⚠️' : 'ℹ️'
            console.log(`${emoji} ${issue.element}: ${issue.description}`)
          })
          console.groupEnd()
        }
        
        console.groupEnd()
      }
    }, 1000)
  }

  // Run audit on load and resize
  window.addEventListener('load', runAudit)
  window.addEventListener('resize', runAudit)
  
  // Run initial audit if page is already loaded
  if (document.readyState === 'complete') {
    runAudit()
  }
}
