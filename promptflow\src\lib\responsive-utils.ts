/**
 * Responsive Design Utilities
 * Mobile-first responsive design tools and helpers
 */

// Breakpoint definitions (mobile-first)
export const BREAKPOINTS = {
  xs: 0,      // Extra small devices (phones)
  sm: 640,    // Small devices (large phones)
  md: 768,    // Medium devices (tablets)
  lg: 1024,   // Large devices (laptops)
  xl: 1280,   // Extra large devices (desktops)
  '2xl': 1536 // 2X large devices (large desktops)
} as const

export type Breakpoint = keyof typeof BREAKPOINTS

// Container max widths
export const CONTAINER_WIDTHS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
} as const

// Touch target sizes (WCAG AA compliance)
export const TOUCH_TARGETS = {
  minimum: 44,    // 44px minimum for WCAG AA
  comfortable: 48, // 48px for comfortable touch
  large: 56       // 56px for primary actions
} as const

// Responsive spacing scale
export const SPACING_SCALE = {
  xs: {
    padding: { x: 4, y: 2 },
    margin: { x: 2, y: 1 },
    gap: 2
  },
  sm: {
    padding: { x: 6, y: 3 },
    margin: { x: 3, y: 2 },
    gap: 3
  },
  md: {
    padding: { x: 8, y: 4 },
    margin: { x: 4, y: 3 },
    gap: 4
  },
  lg: {
    padding: { x: 12, y: 6 },
    margin: { x: 6, y: 4 },
    gap: 6
  },
  xl: {
    padding: { x: 16, y: 8 },
    margin: { x: 8, y: 6 },
    gap: 8
  },
  '2xl': {
    padding: { x: 20, y: 10 },
    margin: { x: 10, y: 8 },
    gap: 10
  }
} as const

// Responsive typography scale
export const TYPOGRAPHY_SCALE = {
  xs: {
    fontSize: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem'   // 24px
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75
    }
  },
  sm: {
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem'
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75
    }
  },
  md: {
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem'
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75
    }
  }
} as const

// Device detection utilities
export class DeviceDetection {
  static isMobile(): boolean {
    if (typeof window === 'undefined') return false
    return window.innerWidth < BREAKPOINTS.md
  }

  static isTablet(): boolean {
    if (typeof window === 'undefined') return false
    return window.innerWidth >= BREAKPOINTS.md && window.innerWidth < BREAKPOINTS.lg
  }

  static isDesktop(): boolean {
    if (typeof window === 'undefined') return false
    return window.innerWidth >= BREAKPOINTS.lg
  }

  static getCurrentBreakpoint(): Breakpoint {
    if (typeof window === 'undefined') return 'lg'
    
    const width = window.innerWidth
    
    if (width >= BREAKPOINTS['2xl']) return '2xl'
    if (width >= BREAKPOINTS.xl) return 'xl'
    if (width >= BREAKPOINTS.lg) return 'lg'
    if (width >= BREAKPOINTS.md) return 'md'
    if (width >= BREAKPOINTS.sm) return 'sm'
    return 'xs'
  }

  static isTouchDevice(): boolean {
    if (typeof window === 'undefined') return false
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  }

  static getOrientation(): 'portrait' | 'landscape' {
    if (typeof window === 'undefined') return 'landscape'
    return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'
  }

  static getViewportSize(): { width: number; height: number } {
    if (typeof window === 'undefined') return { width: 1024, height: 768 }
    return {
      width: window.innerWidth,
      height: window.innerHeight
    }
  }

  static getSafeAreaInsets(): { top: number; right: number; bottom: number; left: number } {
    if (typeof window === 'undefined') return { top: 0, right: 0, bottom: 0, left: 0 }
    
    const style = getComputedStyle(document.documentElement)
    
    return {
      top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
      right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0'),
      bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
      left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0')
    }
  }
}

// Responsive class generator
export class ResponsiveClasses {
  static spacing(property: 'p' | 'm' | 'gap', values: Partial<Record<Breakpoint, number>>): string {
    const classes: string[] = []
    
    Object.entries(values).forEach(([breakpoint, value]) => {
      const bp = breakpoint as Breakpoint
      const prefix = bp === 'xs' ? '' : `${bp}:`
      classes.push(`${prefix}${property}-${value}`)
    })
    
    return classes.join(' ')
  }

  static grid(columns: Partial<Record<Breakpoint, number>>): string {
    const classes: string[] = []
    
    Object.entries(columns).forEach(([breakpoint, cols]) => {
      const bp = breakpoint as Breakpoint
      const prefix = bp === 'xs' ? '' : `${bp}:`
      classes.push(`${prefix}grid-cols-${cols}`)
    })
    
    return classes.join(' ')
  }

  static flex(direction: Partial<Record<Breakpoint, 'row' | 'col'>>): string {
    const classes: string[] = []
    
    Object.entries(direction).forEach(([breakpoint, dir]) => {
      const bp = breakpoint as Breakpoint
      const prefix = bp === 'xs' ? '' : `${bp}:`
      classes.push(`${prefix}flex-${dir}`)
    })
    
    return classes.join(' ')
  }

  static text(sizes: Partial<Record<Breakpoint, string>>): string {
    const classes: string[] = []
    
    Object.entries(sizes).forEach(([breakpoint, size]) => {
      const bp = breakpoint as Breakpoint
      const prefix = bp === 'xs' ? '' : `${bp}:`
      classes.push(`${prefix}text-${size}`)
    })
    
    return classes.join(' ')
  }

  static hidden(breakpoints: Breakpoint[]): string {
    return breakpoints.map(bp => bp === 'xs' ? 'hidden' : `${bp}:hidden`).join(' ')
  }

  static block(breakpoints: Breakpoint[]): string {
    return breakpoints.map(bp => bp === 'xs' ? 'block' : `${bp}:block`).join(' ')
  }
}

// Responsive image utilities
export class ResponsiveImages {
  static generateSrcSet(baseUrl: string, sizes: number[]): string {
    return sizes.map(size => `${baseUrl}?w=${size} ${size}w`).join(', ')
  }

  static generateSizes(breakpoints: Partial<Record<Breakpoint, string>>): string {
    const sizeEntries = Object.entries(breakpoints).map(([bp, size]) => {
      const breakpoint = bp as Breakpoint
      const minWidth = BREAKPOINTS[breakpoint]
      return minWidth > 0 ? `(min-width: ${minWidth}px) ${size}` : size
    })
    
    return sizeEntries.join(', ')
  }

  static getOptimalImageSize(containerWidth: number, devicePixelRatio: number = 1): number {
    const targetWidth = containerWidth * devicePixelRatio
    const standardSizes = [320, 640, 768, 1024, 1280, 1536, 1920]
    
    return standardSizes.find(size => size >= targetWidth) || standardSizes[standardSizes.length - 1]
  }
}

// Layout utilities
export class LayoutUtils {
  static getContainerPadding(breakpoint: Breakpoint): number {
    const spacing = SPACING_SCALE[breakpoint] || SPACING_SCALE.md
    return spacing.padding.x * 4 // Convert to px (assuming 1 unit = 4px)
  }

  static getOptimalColumns(itemWidth: number, containerWidth: number, gap: number = 16): number {
    const availableWidth = containerWidth - gap
    const itemWithGap = itemWidth + gap
    return Math.max(1, Math.floor(availableWidth / itemWithGap))
  }

  static calculateAspectRatio(width: number, height: number): string {
    const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b)
    const divisor = gcd(width, height)
    return `${width / divisor}/${height / divisor}`
  }

  static getViewportUnits(): { vw: number; vh: number; vmin: number; vmax: number } {
    if (typeof window === 'undefined') return { vw: 0, vh: 0, vmin: 0, vmax: 0 }
    
    const { width, height } = DeviceDetection.getViewportSize()
    
    return {
      vw: width / 100,
      vh: height / 100,
      vmin: Math.min(width, height) / 100,
      vmax: Math.max(width, height) / 100
    }
  }
}

// Performance utilities for responsive design
export class ResponsivePerformance {
  static preloadCriticalImages(urls: string[]): void {
    if (typeof window === 'undefined') return
    
    urls.forEach(url => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      link.href = url
      document.head.appendChild(link)
    })
  }

  static lazyLoadImages(selector: string = 'img[data-src]'): void {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) return
    
    const images = document.querySelectorAll(selector)
    
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          const src = img.dataset.src
          
          if (src) {
            img.src = src
            img.removeAttribute('data-src')
            imageObserver.unobserve(img)
          }
        }
      })
    })
    
    images.forEach(img => imageObserver.observe(img))
  }

  static optimizeForTouch(): void {
    if (typeof window === 'undefined') return
    
    // Add touch-friendly styles
    const style = document.createElement('style')
    style.textContent = `
      .touch-target {
        min-height: ${TOUCH_TARGETS.minimum}px;
        min-width: ${TOUCH_TARGETS.minimum}px;
      }
      
      .touch-target-comfortable {
        min-height: ${TOUCH_TARGETS.comfortable}px;
        min-width: ${TOUCH_TARGETS.comfortable}px;
      }
      
      .touch-target-large {
        min-height: ${TOUCH_TARGETS.large}px;
        min-width: ${TOUCH_TARGETS.large}px;
      }
      
      @media (hover: none) and (pointer: coarse) {
        .hover\\:scale-105:hover {
          transform: scale(1.02);
        }
        
        .hover\\:shadow-lg:hover {
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
      }
    `
    
    document.head.appendChild(style)
  }
}
