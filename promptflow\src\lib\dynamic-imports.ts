/**
 * Dynamic Imports for Performance Optimization
 * Lazy loading heavy components and libraries
 */

import { lazy } from 'react'

// Cache for dynamic imports to avoid re-loading
const importCache = new Map<string, Promise<any>>()

/**
 * Cached dynamic import to avoid re-loading with performance tracking
 */
export const cachedDynamicImport = async <T>(
  cacheKey: string,
  importFn: () => Promise<T>
): Promise<T> => {
  // Create performance start mark
  if (typeof window !== 'undefined' && window.performance) {
    try {
      performance.mark(`dynamic-import-${cacheKey}-start`)
    } catch (error) {
      // Ignore performance marking errors
    }
  }

  if (importCache.has(cacheKey)) {
    return importCache.get(cacheKey)
  }

  const startTime = Date.now()
  const importPromise = importFn().then((result) => {
    // Track import completion
    trackDynamicImport(cacheKey, startTime)
    return result
  })

  importCache.set(cacheKey, importPromise)

  return importPromise
}

// Heavy UI Components - Lazy loaded
export const LazyContextGallery = lazy(() =>
  cachedDynamicImport('context-gallery', () =>
    import('@/components/context-gallery').then((module: any) => ({ default: module.default || module }))
  )
)

export const LazyPromptWorkspace = lazy(() =>
  cachedDynamicImport('prompt-workspace', () =>
    import('@/components/prompt-workspace').then((module: any) => ({ default: module.default || module }))
  )
)

export const LazyContextSidebar = lazy(() =>
  cachedDynamicImport('context-sidebar', () =>
    import('@/components/context-sidebar').then((module: any) => ({ default: module.default || module }))
  )
)

export const LazyProjectNameEditor = lazy(() => 
  cachedDynamicImport('project-name-editor', () => 
    import('@/components/project-name-editor').then(module => ({
      default: module.ProjectNameEditor
    }))
  )
)

// Modal Components - Only load when needed
export const LazyContextCreationModal = lazy(() =>
  cachedDynamicImport('context-creation-modal', () =>
    import('@/components/context-creation-modal').then((module: any) => ({ default: module.default || module }))
  )
)

export const LazyContextEditModal = lazy(() =>
  cachedDynamicImport('context-edit-modal', () =>
    import('@/components/context-edit-modal').then((module: any) => ({ default: module.default || module }))
  )
)

// Chart and Visualization Libraries
export const loadChartLibrary = () => 
  cachedDynamicImport('recharts', () => import('recharts'))

export const loadDateUtils = () => 
  cachedDynamicImport('date-fns', async () => {
    const { format, parseISO, formatDistanceToNow } = await import('date-fns')
    const { tr } = await import('date-fns/locale')
    return { format, parseISO, formatDistanceToNow, tr }
  })

// Form and Validation Libraries
export const loadFormValidation = () => 
  cachedDynamicImport('zod', async () => {
    const { z } = await import('zod')
    return { z }
  })

// File Processing Libraries
export const loadFileProcessing = () => 
  cachedDynamicImport('file-processing', async () => {
    const Papa = await import('papaparse')
    return { Papa: Papa.default }
  })

// Rich Text Editor - Heavy component
export const loadRichTextEditor = () => 
  cachedDynamicImport('rich-text-editor', async () => {
    // Placeholder for future rich text editor
    return { RichTextEditor: null }
  })

// Code Syntax Highlighting
export const loadSyntaxHighlighter = () => 
  cachedDynamicImport('syntax-highlighter', async () => {
    const { Prism } = await import('react-syntax-highlighter')
    const { tomorrow } = await import('react-syntax-highlighter/dist/esm/styles/prism')
    return { Prism, tomorrow }
  })

// Image Processing
export const loadImageProcessing = () => 
  cachedDynamicImport('image-processing', async () => {
    // Placeholder for future image processing
    return { ImageProcessor: null }
  })

// Animation Libraries
export const loadAnimationLibrary = () => 
  cachedDynamicImport('framer-motion', () => import('framer-motion'))

// Utility Functions
export const preloadCriticalImports = () => {
  // Preload commonly used utilities
  loadDateUtils()
  loadFormValidation()
}

export const preloadPageImports = (page: string) => {
  switch (page) {
    case 'dashboard':
      LazyPromptWorkspace
      LazyContextSidebar
      break
    case 'profile':
      loadFormValidation()
      break
    case 'templates':
      LazyContextGallery
      break
  }
}

// Route-based code splitting
export const LazyDashboardPage = lazy(() =>
  cachedDynamicImport('dashboard-page', () =>
    import('@/app/dashboard/page').then((module: any) => ({ default: module.default || module }))
  )
)

export const LazyProfilePage = lazy(() =>
  cachedDynamicImport('profile-page', () =>
    import('@/app/profile/page').then((module: any) => ({ default: module.default || module }))
  )
)

export const LazyAuthPage = lazy(() =>
  cachedDynamicImport('auth-page', () =>
    import('@/app/auth/page').then((module: any) => ({ default: module.default || module }))
  )
)

// Templates page - will be created later
// export const LazyTemplatesPage = lazy(() =>
//   cachedDynamicImport('templates-page', () =>
//     import('@/app/templates/page')
//   )
// )

// Performance monitoring for dynamic imports
export const trackDynamicImport = (componentName: string, startTime: number) => {
  const loadTime = Date.now() - startTime

  if (typeof window !== 'undefined' && window.performance) {
    try {
      // Track loading performance with defensive programming
      const startMarkName = `dynamic-import-${componentName}-start`
      const endMarkName = `dynamic-import-${componentName}-end`
      const measureName = `dynamic-import-${componentName}`

      // Create end mark
      performance.mark(endMarkName)

      // Check if start mark exists before measuring - use safer approach
      try {
        const marks = performance.getEntriesByName(startMarkName, 'mark')
        if (marks.length > 0) {
          performance.measure(measureName, startMarkName, endMarkName)
        } else {
          // Create a simple measure without deprecated navigationStart
          const startTime = performance.timeOrigin || Date.now()
          performance.measure(measureName, { start: startTime, end: performance.now() })
        }
      } catch (error) {
        // Ignore measurement errors to prevent deprecated API warnings
        console.debug('Performance measurement skipped:', error)
      }
    } catch (error) {
      // Graceful degradation - performance tracking fails silently
      console.warn(`Performance tracking failed for ${componentName}:`, error)
    }
  }

  console.log(`🚀 [DYNAMIC_IMPORT] ${componentName} loaded in ${loadTime}ms`)
}

// Preload strategy based on user interaction
export const preloadOnHover = (importFn: () => Promise<any>) => {
  let preloaded = false
  
  return {
    onMouseEnter: () => {
      if (!preloaded) {
        preloaded = true
        importFn()
      }
    }
  }
}

// Intersection Observer based preloading
export const preloadOnVisible = (importFn: () => Promise<any>) => {
  if (typeof window === 'undefined') return
  
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          importFn()
          observer.disconnect()
        }
      })
    },
    { threshold: 0.1 }
  )
  
  return observer
}

// Bundle size monitoring
export const getBundleInfo = () => {
  if (typeof window === 'undefined') return null
  
  return {
    cacheSize: importCache.size,
    loadedComponents: Array.from(importCache.keys()),
    memoryUsage: (performance as any).memory ? {
      used: (performance as any).memory.usedJSHeapSize,
      total: (performance as any).memory.totalJSHeapSize,
      limit: (performance as any).memory.jsHeapSizeLimit
    } : null
  }
}
