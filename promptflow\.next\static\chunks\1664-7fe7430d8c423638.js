"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1664],{22100:(e,r,o)=>{o.d(r,{Jd:()=>g,Rt:()=>f,a7:()=>h,rU:()=>d,wF:()=>p});var t=o(26715),n=o(32960),s=o(5041),i=o(70478),a=o(12115),u=o(35695),l=o(56671);function c(e,r){let o=e instanceof Error?e.message:String(e);return!!(o.includes("Invalid Refresh Token")||o.includes("Refresh Token Not Found")||o.includes("refresh_token_not_found"))&&(console.warn("Refresh token hatası, oturum temizleniyor:",o),i.L.auth.signOut({scope:"local"}),r.clear(),!0)}function d(){let e=(0,t.jE)(),r=(0,u.useRouter)();(0,a.useEffect)(()=>{console.log("\uD83C\uDFA7 [AUTH_LISTENER] Setting up auth state listener");let{data:{subscription:o}}=i.L.auth.onAuthStateChange((o,t)=>{var n,s;console.log("\uD83D\uDD04 [AUTH_LISTENER] Auth state change: ".concat(o),{userId:null==t||null==(n=t.user)?void 0:n.id,email:null==t||null==(s=t.user)?void 0:s.email,hasSession:!!t}),("SIGNED_OUT"===o||"TOKEN_REFRESHED"===o)&&(console.log("\uD83D\uDD04 [AUTH_LISTENER] Invalidating queries for event: ".concat(o)),e.invalidateQueries({queryKey:["user"]}),e.invalidateQueries({queryKey:["session"]})),"SIGNED_OUT"===o&&(console.log("\uD83D\uDEAA [AUTH_LISTENER] User signed out - clearing cache and redirecting"),e.clear(),localStorage.removeItem("promptflow-app-store"),setTimeout(()=>{console.log("\uD83D\uDEAA [AUTH_LISTENER] Redirecting to /auth"),r.push("/auth"),r.refresh()},100)),"SIGNED_IN"===o&&(console.log("\uD83D\uDD11 [AUTH_LISTENER] User signed in - refreshing queries and redirecting"),e.invalidateQueries({queryKey:["user"]}),e.invalidateQueries({queryKey:["session"]}),setTimeout(()=>{let e=window.location.pathname;("/auth"===e||"/"===e)&&(console.log("\uD83D\uDE80 [AUTH_LISTENER] Redirecting from ".concat(e," to /dashboard")),r.push("/dashboard"))},100))});return()=>{console.log("\uD83C\uDFA7 [AUTH_LISTENER] Cleaning up auth state listener"),o.unsubscribe()}},[e,r])}function g(){let e=(0,t.jE)();return(0,n.I)({queryKey:["user"],queryFn:async()=>{console.log("\uD83D\uDD10 [USE_USER] Getting user...");try{let{data:{session:r},error:o}=await i.L.auth.getSession();if(o)return console.error("❌ [USE_USER] Session error:",o),null;if(!r)return console.log("\uD83D\uDD10 [USE_USER] No session found"),null;console.log("\uD83D\uDD10 [USE_USER] Session found, getting user...");let{data:{user:t},error:n}=await i.L.auth.getUser();if(n){if(console.error("❌ [USE_USER] Error getting user:",n),n.message.includes("Auth session missing"))return console.log("\uD83D\uDD04 [USE_USER] Auth session missing, returning null"),null;if(c(n,e))return console.log("\uD83D\uDD04 [USE_USER] Handled auth error, returning null"),null;throw Error(n.message)}return console.log("✅ [USE_USER] User retrieved:",(null==t?void 0:t.email)||"null"),t}catch(r){if(console.error("\uD83D\uDCA5 [USE_USER] Exception:",r),(r instanceof Error?r.message:String(r)).includes("Auth session missing"))return console.log("\uD83D\uDD04 [USE_USER] Auth session missing exception, returning null"),null;if(c(r,e))return console.log("\uD83D\uDD04 [USE_USER] Handled auth error exception, returning null"),null;throw r}},staleTime:3e5,retry:(e,r)=>{let o=r instanceof Error?r.message:String(r);return(console.log("\uD83D\uDD04 [USE_USER] Retry attempt ".concat(e,", error: ").concat(o)),o.includes("Invalid Refresh Token")||o.includes("Refresh Token Not Found")||o.includes("Auth session missing"))?(console.log("\uD83D\uDEAB [USE_USER] Not retrying auth error: ".concat(o)),!1):e<3}})}function h(){let e=(0,t.jE)();return(0,s.n)({mutationFn:async e=>{var r,o;let{email:t,password:n}=e;console.log("\uD83D\uDD11 [SIGN_IN] Attempting login for: ".concat(t));let{data:s,error:a}=await i.L.auth.signInWithPassword({email:t,password:n});if(a)throw console.error("❌ [SIGN_IN] Login failed:",a),Error(a.message);return console.log("✅ [SIGN_IN] Login successful:",{userId:null==(r=s.user)?void 0:r.id,email:null==(o=s.user)?void 0:o.email,hasSession:!!s.session}),s},onSuccess:()=>{console.log("\uD83C\uDF89 [SIGN_IN] onSuccess triggered, invalidating queries"),e.invalidateQueries({queryKey:["user"]}),e.invalidateQueries({queryKey:["session"]})},onError:e=>{console.error("\uD83D\uDCA5 [SIGN_IN] onError triggered:",e)}})}function p(){let e=(0,t.jE)();return(0,s.n)({mutationFn:async e=>{let{email:r,password:o}=e,{data:t,error:n}=await i.L.auth.signUp({email:r,password:o});if(n)throw Error(n.message);return t},onSuccess:()=>{e.invalidateQueries({queryKey:["user"]}),e.invalidateQueries({queryKey:["session"]})}})}function f(){let e=(0,t.jE)(),r=(0,u.useRouter)();return(0,s.n)({mutationFn:async()=>{console.log("Starting logout process...");let{error:e}=await i.L.auth.signOut({scope:"global"});if(e)throw console.error("Logout error:",e),Error(e.message);console.log("Logout successful")},onSuccess:()=>{console.log("Logout onSuccess triggered"),l.oR.success("Başarıyla \xe7ıkış yapıldı",{description:"Giriş sayfasına y\xf6nlendiriliyorsunuz..."}),e.clear(),localStorage.removeItem("promptflow-app-store"),sessionStorage.clear(),setTimeout(()=>{console.log("Redirecting to auth page..."),r.push("/auth"),r.refresh(),window.location.href="/auth"},1e3)},onError:o=>{console.error("Logout failed:",o),l.oR.error("\xc7ıkış yapılırken hata oluştu",{description:"Yine de oturum temizleniyor..."}),e.clear(),localStorage.removeItem("promptflow-app-store"),sessionStorage.clear(),setTimeout(()=>{r.push("/auth"),r.refresh()},1e3)}})}},30285:(e,r,o)=>{o.d(r,{$:()=>u});var t=o(95155);o(12115);var n=o(99708),s=o(74466),i=o(59434);let a=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function u(e){let{className:r,variant:o,size:s,asChild:u=!1,...l}=e,c=u?n.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,i.cn)(a({variant:o,size:s,className:r})),...l})}},59434:(e,r,o)=>{o.d(r,{cn:()=>s});var t=o(52596),n=o(39688);function s(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return(0,n.QP)((0,t.$)(r))}},62523:(e,r,o)=>{o.d(r,{p:()=>s});var t=o(95155);o(12115);var n=o(59434);function s(e){let{className:r,type:o,...s}=e;return(0,t.jsx)("input",{type:o,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...s})}},66695:(e,r,o)=>{o.d(r,{BT:()=>u,Wu:()=>l,ZB:()=>a,Zp:()=>s,aR:()=>i});var t=o(95155);o(12115);var n=o(59434);function s(e){let{className:r,...o}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...o})}function i(e){let{className:r,...o}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...o})}function a(e){let{className:r,...o}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",r),...o})}function u(e){let{className:r,...o}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",r),...o})}function l(e){let{className:r,...o}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",r),...o})}},70478:(e,r,o)=>{o.d(r,{L:()=>t});let t=(0,o(98616).createBrowserClient)("https://iqehopwgrczylqliajww.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlxZWhvcHdncmN6eWxxbGlhand3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2OTQyNzksImV4cCI6MjA2ODI3MDI3OX0.9zrZ8Zot6SvsjxTGMyh3IYFqrr_QXQkKSNU4rJNz0VM",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0,flowType:"pkce",debug:!1,storageKey:"sb-iqehopwgrczylqliajww-auth-token",storage:window.localStorage},db:{schema:"public"},realtime:{params:{eventsPerSecond:10}},global:{headers:{"X-Client-Info":"promptflow-web","X-Client-Version":"1.0.0"}}});t.auth.onAuthStateChange((e,r)=>{var o,t;if(console.log("\uD83D\uDD10 [SUPABASE_BROWSER] Auth state change: ".concat(e),{hasSession:!!r,userId:null==r||null==(o=r.user)?void 0:o.id,email:null==r||null==(t=r.user)?void 0:t.email,expiresAt:(null==r?void 0:r.expires_at)?new Date(1e3*r.expires_at).toISOString():null}),r){let e=Math.round((1e3*r.expires_at-Date.now())/1e3);document.cookie="sb-iqehopwgrczylqliajww-auth-token=".concat(JSON.stringify(r),"; path=/; max-age=").concat(e,"; SameSite=Lax; secure=").concat("https:"===location.protocol),console.log("\uD83C\uDF6A [SUPABASE_BROWSER] Set auth cookie with maxAge: ".concat(e,"s"))}else document.cookie="sb-iqehopwgrczylqliajww-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",console.log("\uD83C\uDF6A [SUPABASE_BROWSER] Cleared auth cookie")})},85057:(e,r,o)=>{o.d(r,{J:()=>i});var t=o(95155);o(12115);var n=o(40968),s=o(59434);function i(e){let{className:r,...o}=e;return(0,t.jsx)(n.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...o})}}}]);