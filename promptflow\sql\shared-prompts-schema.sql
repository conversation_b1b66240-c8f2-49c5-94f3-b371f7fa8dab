-- Shared Prompts Schema
-- Prompt paylaşım sistemi için veritabanı şeması

-- 1. Shared Prompts Tablosu
CREATE TABLE IF NOT EXISTS shared_prompts (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  prompt_id uuid REFERENCES prompts(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  share_token text NOT NULL UNIQUE, -- Benz<PERSON>iz paylaşım token'ı
  title text, -- Payla<PERSON>ım için özel başlık (opsiyonel)
  description text, -- <PERSON><PERSON>ş<PERSON><PERSON> için özel açıklama (opsiyonel)
  is_public boolean DEFAULT true, -- <PERSON><PERSON><PERSON> açık mı?
  password_hash text, -- <PERSON><PERSON><PERSON> payla<PERSON>ı<PERSON> (opsiyonel)
  expires_at timestamp with time zone, -- <PERSON> kullan<PERSON> tarihi (opsiyonel)
  view_count integer DEFAULT 0, -- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sayısı
  copy_count integer DEFAULT 0, -- <PERSON><PERSON><PERSON><PERSON> sayısı
  is_active boolean DEFAULT true, -- Aktif mi?
  metadata jsonb DEFAULT '{}', -- Ek bilgiler
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- 2. Shared Prompt Views Tablosu (Analytics için)
CREATE TABLE IF NOT EXISTS shared_prompt_views (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  shared_prompt_id uuid REFERENCES shared_prompts(id) ON DELETE CASCADE,
  viewer_ip inet, -- IP adresi (anonim analytics için)
  viewer_user_agent text, -- User agent
  referrer text, -- Nereden geldi
  viewed_at timestamp with time zone DEFAULT now(),
  session_id text -- Session tracking için
);

-- 3. İndeksler
CREATE INDEX IF NOT EXISTS idx_shared_prompts_token ON shared_prompts(share_token);
CREATE INDEX IF NOT EXISTS idx_shared_prompts_user_id ON shared_prompts(user_id);
CREATE INDEX IF NOT EXISTS idx_shared_prompts_prompt_id ON shared_prompts(prompt_id);
CREATE INDEX IF NOT EXISTS idx_shared_prompts_active ON shared_prompts(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_shared_prompts_public ON shared_prompts(is_public) WHERE is_public = true;
CREATE INDEX IF NOT EXISTS idx_shared_prompts_expires ON shared_prompts(expires_at) WHERE expires_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_shared_prompt_views_shared_prompt_id ON shared_prompt_views(shared_prompt_id);
CREATE INDEX IF NOT EXISTS idx_shared_prompt_views_viewed_at ON shared_prompt_views(viewed_at);

-- 4. RLS Policies
ALTER TABLE shared_prompts ENABLE ROW LEVEL SECURITY;
ALTER TABLE shared_prompt_views ENABLE ROW LEVEL SECURITY;

-- Kullanıcılar sadece kendi paylaştıkları prompt'ları yönetebilir
CREATE POLICY "Users can manage their own shared prompts" ON shared_prompts
  FOR ALL USING (auth.uid() = user_id);

-- Herkes aktif ve public paylaşımları görüntüleyebilir
CREATE POLICY "Anyone can view active public shared prompts" ON shared_prompts
  FOR SELECT USING (is_active = true AND is_public = true);

-- View kayıtları için policy (analytics)
CREATE POLICY "Anyone can insert view records" ON shared_prompt_views
  FOR INSERT WITH CHECK (true);

-- Sadece paylaşım sahibi view kayıtlarını görebilir
CREATE POLICY "Users can view their shared prompt analytics" ON shared_prompt_views
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM shared_prompts sp 
      WHERE sp.id = shared_prompt_id AND sp.user_id = auth.uid()
    )
  );

-- 5. Fonksiyonlar

-- Paylaşım token'ı oluşturma fonksiyonu
CREATE OR REPLACE FUNCTION generate_share_token()
RETURNS text AS $$
BEGIN
  RETURN encode(gen_random_bytes(16), 'base64url');
END;
$$ LANGUAGE plpgsql;

-- Paylaşım oluşturma fonksiyonu
CREATE OR REPLACE FUNCTION create_shared_prompt(
  p_prompt_id uuid,
  p_title text DEFAULT NULL,
  p_description text DEFAULT NULL,
  p_is_public boolean DEFAULT true,
  p_password text DEFAULT NULL,
  p_expires_at timestamp with time zone DEFAULT NULL
)
RETURNS json AS $$
DECLARE
  v_user_id uuid;
  v_share_token text;
  v_password_hash text;
  v_shared_prompt shared_prompts;
BEGIN
  -- Kullanıcı kontrolü
  v_user_id := auth.uid();
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'Unauthorized';
  END IF;

  -- Prompt'un kullanıcıya ait olduğunu kontrol et
  IF NOT EXISTS (
    SELECT 1 FROM prompts 
    WHERE id = p_prompt_id AND user_id = v_user_id
  ) THEN
    RAISE EXCEPTION 'Prompt not found or access denied';
  END IF;

  -- Token oluştur
  v_share_token := generate_share_token();
  
  -- Şifre hash'le
  IF p_password IS NOT NULL THEN
    v_password_hash := crypt(p_password, gen_salt('bf'));
  END IF;

  -- Paylaşım oluştur
  INSERT INTO shared_prompts (
    prompt_id,
    user_id,
    share_token,
    title,
    description,
    is_public,
    password_hash,
    expires_at
  ) VALUES (
    p_prompt_id,
    v_user_id,
    v_share_token,
    p_title,
    p_description,
    p_is_public,
    v_password_hash,
    p_expires_at
  ) RETURNING * INTO v_shared_prompt;

  RETURN json_build_object(
    'id', v_shared_prompt.id,
    'share_token', v_shared_prompt.share_token,
    'share_url', 'https://promptbir.com/share/' || v_shared_prompt.share_token,
    'created_at', v_shared_prompt.created_at
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Paylaşım görüntüleme fonksiyonu
CREATE OR REPLACE FUNCTION get_shared_prompt(
  p_share_token text,
  p_password text DEFAULT NULL
)
RETURNS json AS $$
DECLARE
  v_shared_prompt shared_prompts;
  v_prompt prompts;
  v_project projects;
  v_user_email text;
BEGIN
  -- Paylaşımı bul
  SELECT * INTO v_shared_prompt
  FROM shared_prompts
  WHERE share_token = p_share_token
    AND is_active = true
    AND (expires_at IS NULL OR expires_at > now());

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Shared prompt not found or expired';
  END IF;

  -- Şifre kontrolü
  IF v_shared_prompt.password_hash IS NOT NULL THEN
    IF p_password IS NULL OR NOT (v_shared_prompt.password_hash = crypt(p_password, v_shared_prompt.password_hash)) THEN
      RAISE EXCEPTION 'Password required or incorrect';
    END IF;
  END IF;

  -- Prompt bilgilerini al
  SELECT p.*, pr.name as project_name, u.email
  INTO v_prompt, v_project, v_user_email
  FROM prompts p
  JOIN projects pr ON p.project_id = pr.id
  JOIN auth.users u ON p.user_id = u.id
  WHERE p.id = v_shared_prompt.prompt_id;

  -- Görüntülenme sayısını artır
  UPDATE shared_prompts 
  SET view_count = view_count + 1,
      updated_at = now()
  WHERE id = v_shared_prompt.id;

  RETURN json_build_object(
    'shared_prompt', row_to_json(v_shared_prompt),
    'prompt', row_to_json(v_prompt),
    'project_name', v_project.name,
    'author_email', v_user_email
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- View kaydı ekleme fonksiyonu
CREATE OR REPLACE FUNCTION record_shared_prompt_view(
  p_share_token text,
  p_viewer_ip inet DEFAULT NULL,
  p_viewer_user_agent text DEFAULT NULL,
  p_referrer text DEFAULT NULL,
  p_session_id text DEFAULT NULL
)
RETURNS void AS $$
DECLARE
  v_shared_prompt_id uuid;
BEGIN
  -- Shared prompt ID'sini al
  SELECT id INTO v_shared_prompt_id
  FROM shared_prompts
  WHERE share_token = p_share_token AND is_active = true;

  IF FOUND THEN
    INSERT INTO shared_prompt_views (
      shared_prompt_id,
      viewer_ip,
      viewer_user_agent,
      referrer,
      session_id
    ) VALUES (
      v_shared_prompt_id,
      p_viewer_ip,
      p_viewer_user_agent,
      p_referrer,
      p_session_id
    );
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for updated_at
CREATE OR REPLACE FUNCTION update_shared_prompts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER shared_prompts_updated_at
  BEFORE UPDATE ON shared_prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_shared_prompts_updated_at();
