import { NextRequest, NextResponse } from 'next/server'
import { supabaseBrowser } from '@/lib/supabase-browser'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      share_token, 
      viewer_user_agent, 
      referrer, 
      session_id 
    } = body

    if (!share_token) {
      return NextResponse.json(
        { error: 'Share token is required' },
        { status: 400 }
      )
    }

    // IP adresini al
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const viewer_ip = forwarded?.split(',')[0] || realIp || null

    // Shared prompt ID'sini al
    const { data: sharedPrompt, error: findError } = await supabaseBrowser
      .from('shared_prompts')
      .select('id')
      .eq('share_token', share_token)
      .eq('is_active', true)
      .single()

    if (findError || !sharedPrompt) {
      return NextResponse.json(
        { error: 'Shared prompt not found' },
        { status: 404 }
      )
    }

    // View kaydı ekle
    const { error: insertError } = await supabaseBrowser
      .from('shared_prompt_views')
      .insert({
        shared_prompt_id: sharedPrompt.id,
        viewer_ip,
        viewer_user_agent,
        referrer,
        session_id,
        viewed_at: new Date().toISOString()
      })

    if (insertError) {
      console.error('View record insert error:', insertError)
      // View kaydı başarısız olsa bile 200 döndür
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Record view error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
