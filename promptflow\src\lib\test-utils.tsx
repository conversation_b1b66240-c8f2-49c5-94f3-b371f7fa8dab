/**
 * Test Utilities
 * Comprehensive testing helpers and utilities
 */

import React, { ReactElement } from 'react'
// Testing library imports - only available in test environment
// import { render, RenderOptions, RenderResult } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
// import userEvent from '@testing-library/user-event'
// import { jest } from '@jest/globals'

// Mock data generators
export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

export const mockProject = {
  id: 'test-project-id',
  name: 'Test Project',
  description: 'Test project description',
  user_id: mockUser.id,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

export const mockPrompt = {
  id: 'test-prompt-id',
  title: 'Test Prompt',
  content: 'Test prompt content',
  hashtags: ['test', 'prompt'],
  project_id: mockProject.id,
  user_id: mockUser.id,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

// Test providers wrapper
interface TestProvidersProps {
  children: React.ReactNode
  queryClient?: QueryClient
}

function TestProviders({ children, queryClient }: TestProvidersProps) {
  const testQueryClient = queryClient || new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={testQueryClient}>
      {children}
    </QueryClientProvider>
  )
}

/* Custom render function - only available in test environment
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient
  initialEntries?: string[]
} */

/* Testing function - only available in test environment
export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult & {
  user: ReturnType<typeof userEvent.setup>
  queryClient: QueryClient
} {
  const { queryClient, ...renderOptions } = options

  const testQueryClient = queryClient || new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestProviders queryClient={testQueryClient}>
      {children}
    </TestProviders>
  )

  const user = userEvent.setup()

  return {
    user,
    queryClient: testQueryClient,
    ...render(ui, { wrapper: Wrapper, ...renderOptions })
  }
} */

/* Mock implementations - only available in test environment
export const mockSupabase = {
  auth: {
    getUser: jest.fn(),
    signInWithPassword: jest.fn(),
    signUp: jest.fn(),
    signOut: jest.fn(),
    onAuthStateChange: jest.fn(() => ({
      data: { subscription: { unsubscribe: jest.fn() } }
    }))
  },
  from: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    single: jest.fn(),
    then: jest.fn()
  }))
}

// Mock fetch responses
export function mockFetchResponse(data: unknown, ok = true, status = 200) {
  return Promise.resolve({
    ok,
    status,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data))
  } as Response)
}

// Mock localStorage
export const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}

// Mock IntersectionObserver
export const mockIntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}))

// Mock ResizeObserver
export const mockResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}))

// Test helpers
export const waitForLoadingToFinish = () => 
  new Promise(resolve => setTimeout(resolve, 0))

export const createMockEvent = (overrides = {}) => ({
  preventDefault: jest.fn(),
  stopPropagation: jest.fn(),
  target: { value: '' },
  ...overrides
})

export const createMockFile = (name = 'test.txt', type = 'text/plain', size = 1024) => 
  new File(['test content'], name, { type, lastModified: Date.now() })

// Accessibility testing helpers
export const axeMatchers = {
  toHaveNoViolations: expect.extend({
    async toHaveNoViolations(received) {
      // This would integrate with @axe-core/react in a real implementation
      return {
        pass: true,
        message: () => 'No accessibility violations found'
      }
    }
  })
}

// Performance testing helpers
export const measurePerformance = async (fn: () => Promise<void> | void) => {
  const start = performance.now()
  await fn()
  const end = performance.now()
  return end - start
}

// Mock timers helpers
export const advanceTimersByTime = (ms: number) => {
  jest.advanceTimersByTime(ms)
}

export const runAllTimers = () => {
  jest.runAllTimers()
}

// Error boundary testing
export const ThrowError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error')
  }
  return <div>No error</div>
}

// Custom matchers
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace jest {
    interface Matchers<R> {
      toHaveNoViolations(): R
    }
  }
}

// Test data factories
export const createTestUser = (overrides = {}) => ({
  ...mockUser,
  ...overrides
})

export const createTestProject = (overrides = {}) => ({
  ...mockProject,
  ...overrides
})

export const createTestPrompt = (overrides = {}) => ({
  ...mockPrompt,
  ...overrides
})

// Mock API responses
export const mockApiResponses = {
  getProjects: {
    data: [createTestProject()],
    count: 1,
    hasMore: false
  },
  
  getPrompts: {
    data: [createTestPrompt()],
    count: 1,
    hasMore: false
  },
  
  getUserStats: {
    projectCount: 1,
    promptCount: 1
  }
}

// Setup and teardown helpers
export const setupTest = () => {
  // Reset all mocks
  jest.clearAllMocks()
  
  // Reset localStorage
  mockLocalStorage.clear()
  
  // Reset fetch mock
  global.fetch = jest.fn()
  
  // Setup default mocks
  Object.defineProperty(window, 'localStorage', {
    value: mockLocalStorage
  })
  
  Object.defineProperty(window, 'IntersectionObserver', {
    value: mockIntersectionObserver
  })
  
  Object.defineProperty(window, 'ResizeObserver', {
    value: mockResizeObserver
  })
}

export const teardownTest = () => {
  jest.restoreAllMocks()
}

// Component testing utilities
export const getByTestId = (container: HTMLElement, testId: string) =>
  container.querySelector(`[data-testid="${testId}"]`)

export const getAllByTestId = (container: HTMLElement, testId: string) =>
  container.querySelectorAll(`[data-testid="${testId}"]`)

// Form testing helpers
export const fillForm = async (
  user: ReturnType<typeof userEvent.setup>,
  form: HTMLElement,
  data: Record<string, string>
) => {
  for (const [name, value] of Object.entries(data)) {
    const field = form.querySelector(`[name="${name}"]`) as HTMLInputElement
    if (field) {
      await user.clear(field)
      await user.type(field, value)
    }
  }
}

export const submitForm = async (
  user: ReturnType<typeof userEvent.setup>,
  form: HTMLElement
) => {
  const submitButton = form.querySelector('[type="submit"]') as HTMLButtonElement
  if (submitButton) {
    await user.click(submitButton)
  }
}

// Re-export everything from testing-library - only available in test environment
// export * from '@testing-library/react'
// export { userEvent } */
