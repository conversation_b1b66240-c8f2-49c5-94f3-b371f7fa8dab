(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_58378e91._.js",
  "static/chunks/node_modules_@tanstack_query-devtools_build_DevtoolsComponent_HH7B3BHX_4c84b83e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_bd0a3a12._.js",
  "static/chunks/dd92d_modules_@tanstack_query-devtools_build_DevtoolsPanelComponent_JZI2RDCT_4c84b83e.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js [app-client] (ecmascript)");
    });
});
}),
"[project]/src/components/ui/performance-monitor.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_f436f799._.js",
  "static/chunks/node_modules_4cc006a1._.js",
  "static/chunks/src_870fcee8._.js",
  "static/chunks/src_components_ui_performance-monitor_tsx_4c84b83e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/ui/performance-monitor.tsx [app-client] (ecmascript)");
    });
});
}),
}]);