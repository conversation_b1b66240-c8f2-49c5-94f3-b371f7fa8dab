/**
 * Form Feedback Components
 * Real-time form validation, error handling, and user guidance system
 */

'use client'

import React, { useState, useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'
import { CheckCircle, AlertCircle, Info, X, Loader2 } from 'lucide-react'
import { useAccessibility } from '@/hooks/use-accessibility'

// Feedback types
export type FeedbackType = 'success' | 'error' | 'warning' | 'info' | 'loading'

interface FeedbackMessage {
  id: string
  type: FeedbackType
  message: string
  field?: string
  timestamp: number
  duration?: number
  persistent?: boolean
}

// Form feedback context
interface FormFeedbackContextType {
  messages: FeedbackMessage[]
  addMessage: (message: Omit<FeedbackMessage, 'id' | 'timestamp'>) => void
  removeMessage: (id: string) => void
  clearMessages: (field?: string) => void
  clearAllMessages: () => void
}

const FormFeedbackContext = React.createContext<FormFeedbackContextType | null>(null)

// Form feedback provider
export function FormFeedbackProvider({ children }: { children: React.ReactNode }) {
  const [messages, setMessages] = useState<FeedbackMessage[]>([])
  const { announce } = useAccessibility()

  const addMessage = (message: Omit<FeedbackMessage, 'id' | 'timestamp'>) => {
    const id = `feedback-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const newMessage: FeedbackMessage = {
      ...message,
      id,
      timestamp: Date.now(),
      duration: message.duration || (message.type === 'error' ? 0 : 5000) // Errors persist, others auto-dismiss
    }

    setMessages(prev => [...prev, newMessage])

    // Announce to screen readers
    const priority = message.type === 'error' ? 'assertive' : 'polite'
    announce(message.message, priority)

    // Auto-remove non-persistent messages
    if (newMessage.duration && newMessage.duration > 0) {
      setTimeout(() => {
        setMessages(prev => prev.filter(m => m.id !== id))
      }, newMessage.duration)
    }
  }

  const removeMessage = (id: string) => {
    setMessages(prev => prev.filter(m => m.id !== id))
  }

  const clearMessages = (field?: string) => {
    if (field) {
      setMessages(prev => prev.filter(m => m.field !== field))
    } else {
      setMessages([])
    }
  }

  const clearAllMessages = () => {
    setMessages([])
  }

  return (
    <FormFeedbackContext.Provider value={{
      messages,
      addMessage,
      removeMessage,
      clearMessages,
      clearAllMessages
    }}>
      {children}
    </FormFeedbackContext.Provider>
  )
}

// Hook to use form feedback
export function useFormFeedback() {
  const context = React.useContext(FormFeedbackContext)
  if (!context) {
    throw new Error('useFormFeedback must be used within FormFeedbackProvider')
  }
  return context
}

// Individual feedback message component
export function FeedbackMessage({ 
  message, 
  onDismiss,
  className 
}: { 
  message: FeedbackMessage
  onDismiss?: () => void
  className?: string 
}) {
  const getIcon = () => {
    switch (message.type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case 'info':
        return <Info className="h-4 w-4 text-blue-600" />
      case 'loading':
        return <Loader2 className="h-4 w-4 text-gray-600 animate-spin" />
      default:
        return null
    }
  }

  const getStyles = () => {
    switch (message.type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800'
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800'
      case 'loading':
        return 'bg-gray-50 border-gray-200 text-gray-800'
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800'
    }
  }

  return (
    <div
      className={cn(
        'flex items-start gap-2 p-3 rounded-md border text-sm',
        getStyles(),
        className
      )}
      role="alert"
      aria-live={message.type === 'error' ? 'assertive' : 'polite'}
    >
      {getIcon()}
      <div className="flex-1 min-w-0">
        <p className="break-words">{message.message}</p>
      </div>
      {onDismiss && (
        <button
          onClick={onDismiss}
          className="flex-shrink-0 p-1 hover:bg-black/5 rounded"
          aria-label="Mesajı kapat"
        >
          <X className="h-3 w-3" />
        </button>
      )}
    </div>
  )
}

// Field-specific feedback component
export function FieldFeedback({ 
  field, 
  className 
}: { 
  field: string
  className?: string 
}) {
  const { messages, removeMessage } = useFormFeedback()
  const fieldMessages = messages.filter(m => m.field === field)

  if (fieldMessages.length === 0) return null

  return (
    <div className={cn('space-y-2 mt-1', className)}>
      {fieldMessages.map(message => (
        <FeedbackMessage
          key={message.id}
          message={message}
          onDismiss={() => removeMessage(message.id)}
        />
      ))}
    </div>
  )
}

// Global feedback container
export function GlobalFeedback({ 
  className,
  position = 'top-right' 
}: { 
  className?: string
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center'
}) {
  const { messages, removeMessage } = useFormFeedback()
  const globalMessages = messages.filter(m => !m.field)

  const getPositionStyles = () => {
    switch (position) {
      case 'top-right':
        return 'fixed top-4 right-4 z-50'
      case 'top-left':
        return 'fixed top-4 left-4 z-50'
      case 'bottom-right':
        return 'fixed bottom-4 right-4 z-50'
      case 'bottom-left':
        return 'fixed bottom-4 left-4 z-50'
      case 'top-center':
        return 'fixed top-4 left-1/2 transform -translate-x-1/2 z-50'
      default:
        return 'fixed top-4 right-4 z-50'
    }
  }

  if (globalMessages.length === 0) return null

  return (
    <div className={cn(getPositionStyles(), 'w-96 max-w-[calc(100vw-2rem)]', className)}>
      <div className="space-y-2">
        {globalMessages.map(message => (
          <FeedbackMessage
            key={message.id}
            message={message}
            onDismiss={() => removeMessage(message.id)}
            className="shadow-lg"
          />
        ))}
      </div>
    </div>
  )
}

// Form validation hook with feedback integration
export function useFormValidation<T extends Record<string, any>>(
  initialValues: T,
  validationRules: Record<keyof T, (value: any) => string | null>
) {
  const [values, setValues] = useState<T>(initialValues)
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({})
  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({})
  const [isValidating, setIsValidating] = useState(false)
  const { addMessage, clearMessages } = useFormFeedback()

  const validateField = (field: keyof T, value: any) => {
    const rule = validationRules[field]
    if (!rule) return null

    const error = rule(value)
    return error
  }

  const validateAllFields = () => {
    const newErrors: Partial<Record<keyof T, string>> = {}
    let hasErrors = false

    Object.keys(values).forEach(key => {
      const field = key as keyof T
      const error = validateField(field, values[field])
      if (error) {
        newErrors[field] = error
        hasErrors = true
      }
    })

    setErrors(newErrors)
    return !hasErrors
  }

  const setValue = (field: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [field]: value }))
    
    // Clear previous field messages
    clearMessages(field as string)
    
    // Validate if field has been touched
    if (touched[field]) {
      const error = validateField(field, value)
      if (error) {
        setErrors(prev => ({ ...prev, [field]: error }))
        addMessage({
          type: 'error',
          message: error,
          field: field as string
        })
      } else {
        setErrors(prev => {
          const newErrors = { ...prev }
          delete newErrors[field]
          return newErrors
        })
        addMessage({
          type: 'success',
          message: 'Geçerli',
          field: field as string,
          duration: 2000
        })
      }
    }
  }

  const markFieldTouched = (field: keyof T) => {
    setTouched(prev => ({ ...prev, [field]: true }))
  }

  const handleSubmit = async (onSubmit: (values: T) => Promise<void> | void) => {
    setIsValidating(true)
    
    // Mark all fields as touched
    const allTouched = Object.keys(values).reduce((acc, key) => {
      acc[key as keyof T] = true
      return acc
    }, {} as Partial<Record<keyof T, boolean>>)
    setTouched(allTouched)

    // Validate all fields
    const isValid = validateAllFields()
    
    if (!isValid) {
      addMessage({
        type: 'error',
        message: 'Lütfen form hatalarını düzeltin',
        duration: 5000
      })
      setIsValidating(false)
      return
    }

    try {
      await onSubmit(values)
      addMessage({
        type: 'success',
        message: 'Form başarıyla gönderildi',
        duration: 3000
      })
    } catch (error) {
      addMessage({
        type: 'error',
        message: error instanceof Error ? error.message : 'Bir hata oluştu',
        duration: 0 // Persist error messages
      })
    } finally {
      setIsValidating(false)
    }
  }

  const reset = () => {
    setValues(initialValues)
    setErrors({})
    setTouched({})
    clearMessages()
  }

  return {
    values,
    errors,
    touched,
    isValidating,
    setValue,
    setTouched: markFieldTouched,
    validateField,
    validateAllFields,
    handleSubmit,
    reset,
    isValid: Object.keys(errors).length === 0
  }
}

// Progress indicator for multi-step forms
export function FormProgress({ 
  currentStep, 
  totalSteps, 
  stepLabels,
  className 
}: {
  currentStep: number
  totalSteps: number
  stepLabels?: string[]
  className?: string
}) {
  const progress = (currentStep / totalSteps) * 100

  return (
    <div className={cn('w-full', className)}>
      <div className="flex justify-between text-sm text-gray-600 mb-2">
        <span>Adım {currentStep} / {totalSteps}</span>
        <span>{Math.round(progress)}% tamamlandı</span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${progress}%` }}
          role="progressbar"
          aria-valuenow={currentStep}
          aria-valuemin={1}
          aria-valuemax={totalSteps}
          aria-label={`Form ilerlemesi: ${currentStep} / ${totalSteps}`}
        />
      </div>

      {stepLabels && (
        <div className="flex justify-between">
          {stepLabels.map((label, index) => (
            <div
              key={index}
              className={cn(
                'text-xs text-center flex-1',
                index + 1 <= currentStep ? 'text-blue-600 font-medium' : 'text-gray-400'
              )}
            >
              {label}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
