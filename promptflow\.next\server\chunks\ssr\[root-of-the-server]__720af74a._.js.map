{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/app/terms/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport Link from 'next/link'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { \n  ArrowLeft, \n  Sparkles, \n  FileText, \n  Scale, \n  AlertTriangle, \n  CheckCircle,\n  XCircle,\n  DollarSign,\n  Shield,\n  Users\n} from 'lucide-react'\n\nexport const metadata: Metadata = {\n  title: 'Kullanım Şartları - PromptBir | Hizmet Şartları ve Koşulları',\n  description: 'PromptBir kullanım şartları ve hizmet koşulları. Platform kullanımına dair kurallar, haklar ve sorumluluklar hakkında detaylı bilgi.',\n  keywords: [\n    'kullanım şartları',\n    'hizmet koşulları',\n    'kullanıcı sözleşmesi',\n    'platform kuralları',\n    'yasal şartlar',\n    'hizmet sözleşmesi'\n  ],\n  openGraph: {\n    title: '<PERSON>llan<PERSON><PERSON> Şartları - PromptBir',\n    description: 'Platform kullanımına dair kurallar, haklar ve sorumluluklar',\n    type: 'website',\n    url: 'https://promptbir.com/terms'\n  }\n}\n\nexport default function TermsPage() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      {/* Header */}\n      <header className=\"border-b border-gray-200 bg-white/80 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <Link \n              href=\"/\" \n              className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <ArrowLeft className=\"h-5 w-5\" />\n              <span>Ana Sayfaya Dön</span>\n            </Link>\n            <div className=\"flex items-center space-x-2\">\n              <Sparkles className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">PromptBir</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Hero Section */}\n        <div className=\"text-center mb-12\">\n          <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n            <Scale className=\"h-8 w-8 text-blue-600\" />\n          </div>\n          <h1 className=\"text-4xl sm:text-5xl font-bold text-gray-900 mb-6\">\n            Kullanım Şartları\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            PromptBir platformunu kullanarak aşağıdaki şart ve koşulları kabul etmiş olursunuz. \n            Lütfen bu belgeyi dikkatlice okuyun.\n          </p>\n          <p className=\"text-sm text-gray-500 mt-4\">\n            Son güncelleme: 1 Ocak 2024 | Yürürlük tarihi: 1 Ocak 2024\n          </p>\n        </div>\n\n        {/* Quick Summary */}\n        <Card className=\"shadow-lg mb-8 bg-gradient-to-r from-green-50 to-blue-50 border-green-200\">\n          <CardContent className=\"p-6\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4 flex items-center gap-2\">\n              <CheckCircle className=\"h-5 w-5 text-green-600\" />\n              Özet\n            </h2>\n            <div className=\"grid md:grid-cols-2 gap-4 text-sm\">\n              <div className=\"flex items-start gap-2\">\n                <CheckCircle className=\"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0\" />\n                <span>Platform'u yasal amaçlarla kullanın</span>\n              </div>\n              <div className=\"flex items-start gap-2\">\n                <CheckCircle className=\"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0\" />\n                <span>Hesap güvenliğinizden siz sorumlusunuz</span>\n              </div>\n              <div className=\"flex items-start gap-2\">\n                <CheckCircle className=\"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0\" />\n                <span>İçeriklerinizin telif hakkı size aittir</span>\n              </div>\n              <div className=\"flex items-start gap-2\">\n                <CheckCircle className=\"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0\" />\n                <span>Hizmet kesintileri olabilir</span>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Main Content */}\n        <div className=\"space-y-8\">\n          {/* Acceptance */}\n          <Card className=\"shadow-lg\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3\">\n                <FileText className=\"h-6 w-6 text-blue-600\" />\n                1. Şartların Kabulü\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"prose prose-gray max-w-none\">\n              <p className=\"mb-4\">\n                PromptBir platformunu (\"Hizmet\") kullanarak, bu Kullanım Şartları'nı (\"Şartlar\") \n                kabul etmiş olursunuz. Bu şartları kabul etmiyorsanız, lütfen hizmeti kullanmayın.\n              </p>\n              <ul className=\"list-disc pl-6 space-y-2\">\n                <li>Bu şartlar tüm kullanıcılar için geçerlidir</li>\n                <li>Şartlar zaman zaman güncellenebilir</li>\n                <li>Güncellemeler platform üzerinden duyurulur</li>\n                <li>Devam eden kullanım güncellemeleri kabul anlamına gelir</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          {/* Service Description */}\n          <Card className=\"shadow-lg\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3\">\n                <Sparkles className=\"h-6 w-6 text-purple-600\" />\n                2. Hizmet Tanımı\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"prose prose-gray max-w-none\">\n              <p className=\"mb-4\">PromptBir aşağıdaki hizmetleri sunar:</p>\n              <ul className=\"list-disc pl-6 space-y-2\">\n                <li><strong>Prompt Yönetimi:</strong> AI prompt'larınızı organize etme</li>\n                <li><strong>Proje Yönetimi:</strong> Prompt'ları projeler halinde gruplandırma</li>\n                <li><strong>Paylaşım:</strong> Prompt'ları güvenli şekilde paylaşma</li>\n                <li><strong>Context Gallery:</strong> Hazır prompt şablonları</li>\n                <li><strong>Analitik:</strong> Kullanım istatistikleri</li>\n                <li><strong>API Erişimi:</strong> Programatik erişim (ücretli planlarda)</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          {/* User Accounts */}\n          <Card className=\"shadow-lg\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3\">\n                <Users className=\"h-6 w-6 text-green-600\" />\n                3. Kullanıcı Hesapları\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"prose prose-gray max-w-none\">\n              <h4 className=\"font-semibold mb-2\">Hesap Oluşturma:</h4>\n              <ul className=\"list-disc pl-6 mb-4 space-y-1\">\n                <li>18 yaşından büyük olmalısınız</li>\n                <li>Doğru ve güncel bilgiler vermelisiniz</li>\n                <li>Hesap başına bir kişi sorumludur</li>\n                <li>Şirket hesapları yetkili kişi tarafından açılmalıdır</li>\n              </ul>\n\n              <h4 className=\"font-semibold mb-2\">Hesap Güvenliği:</h4>\n              <ul className=\"list-disc pl-6 space-y-1\">\n                <li>Güçlü şifre kullanın</li>\n                <li>Hesap bilgilerinizi kimseyle paylaşmayın</li>\n                <li>Şüpheli aktiviteleri derhal bildirin</li>\n                <li>Hesabınızdan yapılan tüm işlemlerden siz sorumlusunuz</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          {/* Acceptable Use */}\n          <Card className=\"shadow-lg\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3\">\n                <CheckCircle className=\"h-6 w-6 text-green-600\" />\n                4. Kabul Edilebilir Kullanım\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"prose prose-gray max-w-none\">\n              <h4 className=\"font-semibold mb-2 text-green-700\">İzin Verilen Kullanımlar:</h4>\n              <ul className=\"list-disc pl-6 mb-4 space-y-1\">\n                <li>Yasal AI prompt'larını yönetme</li>\n                <li>Takım çalışması ve işbirliği</li>\n                <li>Eğitim ve araştırma amaçlı kullanım</li>\n                <li>Ticari projeler (plan limitleri dahilinde)</li>\n              </ul>\n\n              <h4 className=\"font-semibold mb-2 text-red-700\">Yasak Kullanımlar:</h4>\n              <ul className=\"list-disc pl-6 space-y-1\">\n                <li>Yasadışı içerik oluşturma veya paylaşma</li>\n                <li>Telif hakkı ihlali yapan içerikler</li>\n                <li>Zararlı, tehditkar veya taciz edici içerikler</li>\n                <li>Spam veya istenmeyen içerik gönderme</li>\n                <li>Sistemi hackleme veya zarar verme girişimleri</li>\n                <li>Başkalarının hesaplarına yetkisiz erişim</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          {/* Content Ownership */}\n          <Card className=\"shadow-lg\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3\">\n                <Shield className=\"h-6 w-6 text-orange-600\" />\n                5. İçerik Sahipliği ve Lisans\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"prose prose-gray max-w-none\">\n              <h4 className=\"font-semibold mb-2\">Sizin İçerikleriniz:</h4>\n              <ul className=\"list-disc pl-6 mb-4 space-y-1\">\n                <li>Oluşturduğunuz prompt'ların telif hakkı size aittir</li>\n                <li>İçeriklerinizi istediğiniz zaman silebilirsiniz</li>\n                <li>Paylaştığınız içeriklerden siz sorumlusunuz</li>\n                <li>Başkalarının telif haklarını ihlal etmemelisiniz</li>\n              </ul>\n\n              <h4 className=\"font-semibold mb-2\">Platform Lisansı:</h4>\n              <ul className=\"list-disc pl-6 space-y-1\">\n                <li>Hizmeti sunmak için içeriklerinizi işleme hakkımız vardır</li>\n                <li>İçeriklerinizi satmayız veya üçüncü taraflarla paylaşmayız</li>\n                <li>Yedekleme ve güvenlik amaçlı kopyalama yapabiliriz</li>\n                <li>Hesap silindikten sonra içerikler 30 gün içinde silinir</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          {/* Payment Terms */}\n          <Card className=\"shadow-lg\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3\">\n                <DollarSign className=\"h-6 w-6 text-green-600\" />\n                6. Ödeme Şartları\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"prose prose-gray max-w-none\">\n              <h4 className=\"font-semibold mb-2\">Ücretli Planlar:</h4>\n              <ul className=\"list-disc pl-6 mb-4 space-y-1\">\n                <li>Aylık veya yıllık ödeme seçenekleri mevcuttur</li>\n                <li>Ödemeler peşin olarak tahsil edilir</li>\n                <li>Fiyatlar KDV dahildir</li>\n                <li>Otomatik yenileme varsayılan olarak açıktır</li>\n              </ul>\n\n              <h4 className=\"font-semibold mb-2\">İptal ve İade:</h4>\n              <ul className=\"list-disc pl-6 space-y-1\">\n                <li>Aboneliği istediğiniz zaman iptal edebilirsiniz</li>\n                <li>İptal sonrası mevcut dönem sonuna kadar hizmet devam eder</li>\n                <li>İade politikası için destek ekibiyle iletişime geçin</li>\n                <li>Kötüye kullanım durumunda iade yapılmayabilir</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          {/* Service Availability */}\n          <Card className=\"shadow-lg\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3\">\n                <AlertTriangle className=\"h-6 w-6 text-yellow-600\" />\n                7. Hizmet Kullanılabilirliği\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"prose prose-gray max-w-none\">\n              <ul className=\"list-disc pl-6 space-y-2\">\n                <li><strong>Uptime Hedefi:</strong> %99.9 kullanılabilirlik hedefliyoruz</li>\n                <li><strong>Bakım:</strong> Planlı bakımlar önceden duyurulur</li>\n                <li><strong>Kesintiler:</strong> Beklenmeyen kesintiler olabilir</li>\n                <li><strong>Yedekleme:</strong> Düzenli veri yedeklemesi yapılır</li>\n                <li><strong>Destek:</strong> Teknik destek iş saatlerinde sağlanır</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          {/* Termination */}\n          <Card className=\"shadow-lg\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-3\">\n                <XCircle className=\"h-6 w-6 text-red-600\" />\n                8. Hesap Sonlandırma\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"prose prose-gray max-w-none\">\n              <h4 className=\"font-semibold mb-2\">Kullanıcı Tarafından:</h4>\n              <ul className=\"list-disc pl-6 mb-4 space-y-1\">\n                <li>Hesabınızı istediğiniz zaman silebilirsiniz</li>\n                <li>Silme işlemi geri alınamaz</li>\n                <li>Verileriniz 30 gün içinde tamamen silinir</li>\n              </ul>\n\n              <h4 className=\"font-semibold mb-2\">Platform Tarafından:</h4>\n              <ul className=\"list-disc pl-6 space-y-1\">\n                <li>Şartları ihlal eden hesaplar askıya alınabilir</li>\n                <li>Yasadışı aktivite durumunda hesap kapatılır</li>\n                <li>Uzun süre inaktif hesaplar silinebilir</li>\n                <li>Sonlandırma öncesi uyarı gönderilir</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          {/* Liability */}\n          <Card className=\"shadow-lg\">\n            <CardHeader>\n              <CardTitle>9. Sorumluluk Sınırlaması</CardTitle>\n            </CardHeader>\n            <CardContent className=\"prose prose-gray max-w-none\">\n              <ul className=\"list-disc pl-6 space-y-2\">\n                <li>Hizmet \"olduğu gibi\" sunulur</li>\n                <li>Veri kaybından doğan zararlardan sorumlu değiliz</li>\n                <li>Üçüncü taraf hizmetlerinden sorumlu değiliz</li>\n                <li>Maksimum sorumluluk ödenen ücretle sınırlıdır</li>\n                <li>Dolaylı zararlar kapsamında değildir</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          {/* Governing Law */}\n          <Card className=\"shadow-lg\">\n            <CardHeader>\n              <CardTitle>10. Uygulanacak Hukuk</CardTitle>\n            </CardHeader>\n            <CardContent className=\"prose prose-gray max-w-none\">\n              <ul className=\"list-disc pl-6 space-y-2\">\n                <li>Bu şartlar Türkiye Cumhuriyeti hukukuna tabidir</li>\n                <li>Uyuşmazlıklar Türkiye mahkemelerinde çözülür</li>\n                <li>KVKK ve diğer yerel mevzuat uygulanır</li>\n                <li>Uluslararası kullanıcılar için yerel yasalar da geçerlidir</li>\n              </ul>\n            </CardContent>\n          </Card>\n\n          {/* Contact */}\n          <Card className=\"shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white\">\n            <CardContent className=\"p-8 text-center\">\n              <h2 className=\"text-2xl font-bold mb-4\">Sorularınız mı var?</h2>\n              <p className=\"mb-6 text-blue-100\">\n                Kullanım şartları hakkında sorularınız varsa bizimle iletişime geçin.\n              </p>\n              <div className=\"space-y-2\">\n                <p><strong>Hukuki Sorular:</strong> <EMAIL></p>\n                <p><strong>Genel Sorular:</strong> <EMAIL></p>\n                <p><strong>Adres:</strong> Türkiye</p>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Updates */}\n          <Card className=\"shadow-lg border-blue-200 bg-blue-50\">\n            <CardContent className=\"p-6\">\n              <h3 className=\"font-bold text-blue-900 mb-2\">Şart Güncellemeleri</h3>\n              <p className=\"text-blue-800 text-sm\">\n                Bu kullanım şartları gerektiğinde güncellenebilir. Önemli değişiklikler \n                e-posta ile bildirilecek ve platform üzerinde duyurulacaktır. \n                Güncellemeleri düzenli olarak kontrol etmenizi öneririz.\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-8 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 PromptBir. Tüm hakları saklıdır.\n          </p>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAaO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,KAAK;IACP;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;0CAIvE,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAM5C,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAA2B;;;;;;;8CAGpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAA0B;;;;;;;;;;;;kDAIlD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAE,WAAU;0DAAO;;;;;;0DAIpB,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAMV,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAA4B;;;;;;;;;;;;kDAIpD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAE,WAAU;0DAAO;;;;;;0DACpB,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;;0EAAG,8OAAC;0EAAO;;;;;;4DAAyB;;;;;;;kEACrC,8OAAC;;0EAAG,8OAAC;0EAAO;;;;;;4DAAwB;;;;;;;kEACpC,8OAAC;;0EAAG,8OAAC;0EAAO;;;;;;4DAAkB;;;;;;;kEAC9B,8OAAC;;0EAAG,8OAAC;0EAAO;;;;;;4DAAyB;;;;;;;kEACrC,8OAAC;;0EAAG,8OAAC;0EAAO;;;;;;4DAAkB;;;;;;;kEAC9B,8OAAC;;0EAAG,8OAAC;0EAAO;;;;;;4DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;0CAMvC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;;;;;;kDAIhD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;0DAGN,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAMV,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;;;;;;kDAItD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;0DAGN,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAMV,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAA4B;;;;;;;;;;;;kDAIlD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;0DAGN,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAMV,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;;;;;;kDAIrD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;0DAGN,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAMV,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAA4B;;;;;;;;;;;;kDAIzD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;;sEAAG,8OAAC;sEAAO;;;;;;wDAAuB;;;;;;;8DACnC,8OAAC;;sEAAG,8OAAC;sEAAO;;;;;;wDAAe;;;;;;;8DAC3B,8OAAC;;sEAAG,8OAAC;sEAAO;;;;;;wDAAoB;;;;;;;8DAChC,8OAAC;;sEAAG,8OAAC;sEAAO;;;;;;wDAAmB;;;;;;;8DAC/B,8OAAC;;sEAAG,8OAAC;sEAAO;;;;;;wDAAgB;;;;;;;;;;;;;;;;;;;;;;;;0CAMlC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,4MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAyB;;;;;;;;;;;;kDAIhD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;0DAGN,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAMV,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;0CAMV,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;0CAMV,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAwB;;;;;;;8DACnC,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAuB;;;;;;;8DAClC,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAe;;;;;;;;;;;;;;;;;;;;;;;;0CAMhC,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW7C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}]}