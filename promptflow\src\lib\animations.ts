/**
 * Animation System
 * Micro-interactions and animation optimization for enhanced UX
 */

import { useReducedMotion } from '@/hooks/use-accessibility'

// Animation presets
export const ANIMATION_PRESETS = {
  // Entrance animations
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.2, ease: 'easeOut' }
  },
  
  slideInUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3, ease: 'easeOut' }
  },
  
  slideInDown: {
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 20 },
    transition: { duration: 0.3, ease: 'easeOut' }
  },
  
  slideInLeft: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 },
    transition: { duration: 0.3, ease: 'easeOut' }
  },
  
  slideInRight: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
    transition: { duration: 0.3, ease: 'easeOut' }
  },
  
  scaleIn: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
    transition: { duration: 0.2, ease: 'easeOut' }
  },
  
  // Hover animations
  hover: {
    scale: 1.02,
    transition: { duration: 0.15, ease: 'easeOut' }
  },
  
  hoverLift: {
    y: -2,
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    transition: { duration: 0.15, ease: 'easeOut' }
  },
  
  // Button animations
  buttonPress: {
    scale: 0.98,
    transition: { duration: 0.1, ease: 'easeInOut' }
  },
  
  // Loading animations
  pulse: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: 'easeInOut'
    }
  },
  
  bounce: {
    y: [0, -10, 0],
    transition: {
      duration: 0.6,
      repeat: Infinity,
      ease: 'easeInOut'
    }
  },
  
  // Notification animations
  notification: {
    initial: { opacity: 0, x: 300, scale: 0.9 },
    animate: { opacity: 1, x: 0, scale: 1 },
    exit: { opacity: 0, x: 300, scale: 0.9 },
    transition: { duration: 0.3, ease: 'easeOut' }
  },
  
  // Modal animations
  modal: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 },
    transition: { duration: 0.2, ease: 'easeOut' }
  },
  
  modalBackdrop: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.2 }
  }
} as const

// Animation utilities
export class AnimationUtils {
  // Check if animations should be reduced
  static shouldReduceMotion(): boolean {
    if (typeof window === 'undefined') return false
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  }

  // Get animation with reduced motion support
  static getAnimation(preset: keyof typeof ANIMATION_PRESETS, reduceMotion = false) {
    const animation = ANIMATION_PRESETS[preset]
    
    if (reduceMotion || this.shouldReduceMotion()) {
      return {
        ...animation,
        transition: { duration: 0.01 } // Nearly instant for reduced motion
      }
    }
    
    return animation
  }

  // Create staggered animation for lists
  static createStaggered(
    baseAnimation: any,
    staggerDelay = 0.1,
    maxStagger = 0.5
  ) {
    return {
      ...baseAnimation,
      transition: {
        ...baseAnimation.transition,
        staggerChildren: Math.min(staggerDelay, maxStagger)
      }
    }
  }

  // Create spring animation
  static createSpring(config: {
    stiffness?: number
    damping?: number
    mass?: number
  } = {}) {
    return {
      type: 'spring',
      stiffness: config.stiffness || 300,
      damping: config.damping || 30,
      mass: config.mass || 1
    }
  }
}

// CSS-in-JS animations for non-React contexts
export const CSS_ANIMATIONS = {
  fadeIn: `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .animate-fade-in {
      animation: fadeIn 0.2s ease-out;
    }
  `,
  
  slideInUp: `
    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    .animate-slide-in-up {
      animation: slideInUp 0.3s ease-out;
    }
  `,
  
  pulse: `
    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }
    .animate-pulse-custom {
      animation: pulse 1.5s ease-in-out infinite;
    }
  `,
  
  bounce: `
    @keyframes bounce {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-10px); }
    }
    .animate-bounce-custom {
      animation: bounce 0.6s ease-in-out infinite;
    }
  `,
  
  shake: `
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      25% { transform: translateX(-5px); }
      75% { transform: translateX(5px); }
    }
    .animate-shake {
      animation: shake 0.5s ease-in-out;
    }
  `,
  
  wiggle: `
    @keyframes wiggle {
      0%, 100% { transform: rotate(0deg); }
      25% { transform: rotate(-3deg); }
      75% { transform: rotate(3deg); }
    }
    .animate-wiggle {
      animation: wiggle 0.5s ease-in-out;
    }
  `
}

// Micro-interaction helpers
export class MicroInteractions {
  // Button click feedback
  static buttonClick(element: HTMLElement) {
    if (AnimationUtils.shouldReduceMotion()) return

    element.style.transform = 'scale(0.98)'
    element.style.transition = 'transform 0.1s ease-in-out'
    
    setTimeout(() => {
      element.style.transform = 'scale(1)'
    }, 100)
  }

  // Hover effect
  static addHoverEffect(element: HTMLElement, type: 'lift' | 'scale' | 'glow' = 'lift') {
    if (AnimationUtils.shouldReduceMotion()) return

    const originalTransform = element.style.transform
    const originalBoxShadow = element.style.boxShadow
    
    element.addEventListener('mouseenter', () => {
      switch (type) {
        case 'lift':
          element.style.transform = 'translateY(-2px)'
          element.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)'
          break
        case 'scale':
          element.style.transform = 'scale(1.02)'
          break
        case 'glow':
          element.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.3)'
          break
      }
      element.style.transition = 'all 0.15s ease-out'
    })
    
    element.addEventListener('mouseleave', () => {
      element.style.transform = originalTransform
      element.style.boxShadow = originalBoxShadow
    })
  }

  // Success feedback animation
  static successFeedback(element: HTMLElement) {
    if (AnimationUtils.shouldReduceMotion()) return

    element.style.transform = 'scale(1.1)'
    element.style.transition = 'transform 0.2s ease-out'
    
    setTimeout(() => {
      element.style.transform = 'scale(1)'
    }, 200)
  }

  // Error shake animation
  static errorShake(element: HTMLElement) {
    if (AnimationUtils.shouldReduceMotion()) return

    element.classList.add('animate-shake')
    setTimeout(() => {
      element.classList.remove('animate-shake')
    }, 500)
  }

  // Loading pulse
  static loadingPulse(element: HTMLElement, start = true) {
    if (AnimationUtils.shouldReduceMotion()) return

    if (start) {
      element.classList.add('animate-pulse-custom')
    } else {
      element.classList.remove('animate-pulse-custom')
    }
  }

  // Attention grabber
  static grabAttention(element: HTMLElement, type: 'wiggle' | 'bounce' = 'wiggle') {
    if (AnimationUtils.shouldReduceMotion()) return

    const className = type === 'wiggle' ? 'animate-wiggle' : 'animate-bounce-custom'
    element.classList.add(className)
    
    setTimeout(() => {
      element.classList.remove(className)
    }, 500)
  }
}

// Performance optimized animation hook
export function useOptimizedAnimation() {
  const prefersReducedMotion = useReducedMotion()

  const getAnimation = (preset: keyof typeof ANIMATION_PRESETS) => {
    return AnimationUtils.getAnimation(preset, prefersReducedMotion)
  }

  const createStaggered = (baseAnimation: any, delay = 0.1) => {
    return AnimationUtils.createStaggered(baseAnimation, delay)
  }

  const createSpring = (config?: Parameters<typeof AnimationUtils.createSpring>[0]) => {
    if (prefersReducedMotion) {
      return { duration: 0.01 }
    }
    return AnimationUtils.createSpring(config)
  }

  return {
    getAnimation,
    createStaggered,
    createSpring,
    prefersReducedMotion
  }
}

// Inject CSS animations into document
export function injectAnimationStyles() {
  if (typeof document === 'undefined') return

  const styleId = 'promptflow-animations'
  if (document.getElementById(styleId)) return

  const style = document.createElement('style')
  style.id = styleId
  style.textContent = Object.values(CSS_ANIMATIONS).join('\n')
  
  // Respect reduced motion preference
  if (AnimationUtils.shouldReduceMotion()) {
    style.textContent += `
      @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
    `
  }
  
  document.head.appendChild(style)
}

// Auto-inject animations on module load
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', injectAnimationStyles)
  } else {
    injectAnimationStyles()
  }
}
