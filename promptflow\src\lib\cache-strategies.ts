/**
 * Advanced Caching Strategies
 * Multi-layer caching for optimal performance
 */

import { QueryClient } from '@tanstack/react-query'

// Cache configurations for different data types
export const CACHE_CONFIGS = {
  // User data - rarely changes
  user: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  },
  
  // Projects - moderate changes
  projects: {
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
  },
  
  // Prompts - frequent changes
  prompts: {
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  },
  
  // Context templates - rarely changes
  templates: {
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  },
  
  // User plans - rarely changes
  plans: {
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  },
  
  // Real-time data - very fresh
  realtime: {
    staleTime: 0, // Always stale
    gcTime: 1 * 60 * 1000, // 1 minute
  }
} as const

// Enhanced Query Client with optimized defaults
export const createOptimizedQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Default cache settings
        staleTime: 1 * 60 * 1000, // 1 minute
        gcTime: 5 * 60 * 1000, // 5 minutes
        
        // Retry configuration
        retry: (failureCount, error: any) => {
          // Don't retry on 4xx errors
          if (error?.status >= 400 && error?.status < 500) {
            return false
          }
          // Retry up to 3 times for other errors
          return failureCount < 3
        },
        
        // Retry delay with exponential backoff
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        
        // Background refetch settings
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
        refetchOnMount: true,
      },
      
      mutations: {
        // Retry failed mutations
        retry: 1,
        retryDelay: 1000,
      }
    }
  })
}

// Browser cache utilities
export class BrowserCache {
  private static readonly PREFIX = 'promptflow_'
  
  // Local Storage cache (persistent)
  static setLocal<T>(key: string, data: T, ttl?: number): void {
    try {
      const item = {
        data,
        timestamp: Date.now(),
        ttl: ttl || 24 * 60 * 60 * 1000 // 24 hours default
      }
      localStorage.setItem(this.PREFIX + key, JSON.stringify(item))
    } catch (error) {
      console.warn('Failed to set localStorage cache:', error)
    }
  }
  
  static getLocal<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(this.PREFIX + key)
      if (!item) return null
      
      const parsed = JSON.parse(item)
      const now = Date.now()
      
      // Check if expired
      if (now - parsed.timestamp > parsed.ttl) {
        localStorage.removeItem(this.PREFIX + key)
        return null
      }
      
      return parsed.data
    } catch (error) {
      console.warn('Failed to get localStorage cache:', error)
      return null
    }
  }
  
  // Session Storage cache (session-only)
  static setSession<T>(key: string, data: T): void {
    try {
      const item = {
        data,
        timestamp: Date.now()
      }
      sessionStorage.setItem(this.PREFIX + key, JSON.stringify(item))
    } catch (error) {
      console.warn('Failed to set sessionStorage cache:', error)
    }
  }
  
  static getSession<T>(key: string): T | null {
    try {
      const item = sessionStorage.getItem(this.PREFIX + key)
      if (!item) return null
      
      const parsed = JSON.parse(item)
      return parsed.data
    } catch (error) {
      console.warn('Failed to get sessionStorage cache:', error)
      return null
    }
  }
  
  // Memory cache (in-memory, fastest)
  private static memoryCache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  
  static setMemory<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
  
  static getMemory<T>(key: string): T | null {
    const item = this.memoryCache.get(key)
    if (!item) return null
    
    const now = Date.now()
    if (now - item.timestamp > item.ttl) {
      this.memoryCache.delete(key)
      return null
    }
    
    return item.data
  }
  
  // Clear all caches
  static clearAll(): void {
    // Clear localStorage
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(this.PREFIX)) {
        localStorage.removeItem(key)
      }
    })
    
    // Clear sessionStorage
    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith(this.PREFIX)) {
        sessionStorage.removeItem(key)
      }
    })
    
    // Clear memory cache
    this.memoryCache.clear()
  }
  
  // Cache size monitoring
  static getCacheInfo() {
    const localStorageSize = Object.keys(localStorage)
      .filter(key => key.startsWith(this.PREFIX))
      .reduce((size, key) => size + localStorage.getItem(key)!.length, 0)
    
    const sessionStorageSize = Object.keys(sessionStorage)
      .filter(key => key.startsWith(this.PREFIX))
      .reduce((size, key) => size + sessionStorage.getItem(key)!.length, 0)
    
    return {
      localStorage: {
        size: localStorageSize,
        items: Object.keys(localStorage).filter(key => key.startsWith(this.PREFIX)).length
      },
      sessionStorage: {
        size: sessionStorageSize,
        items: Object.keys(sessionStorage).filter(key => key.startsWith(this.PREFIX)).length
      },
      memory: {
        items: this.memoryCache.size
      }
    }
  }
}

// Service Worker cache utilities
export class ServiceWorkerCache {
  private static readonly CACHE_NAME = 'promptflow-v1'
  
  static async cacheResources(urls: string[]): Promise<void> {
    if (!('serviceWorker' in navigator)) return
    
    try {
      const cache = await caches.open(this.CACHE_NAME)
      await cache.addAll(urls)
      console.log('✅ [SW_CACHE] Resources cached:', urls.length)
    } catch (error) {
      console.warn('Failed to cache resources:', error)
    }
  }
  
  static async getCachedResponse(url: string): Promise<Response | null> {
    if (!('caches' in window)) return null
    
    try {
      const cache = await caches.open(this.CACHE_NAME)
      const response = await cache.match(url)
      return response || null
    } catch (error) {
      console.warn('Failed to get cached response:', error)
      return null
    }
  }
  
  static async clearCache(): Promise<void> {
    if (!('caches' in window)) return
    
    try {
      await caches.delete(this.CACHE_NAME)
      console.log('✅ [SW_CACHE] Cache cleared')
    } catch (error) {
      console.warn('Failed to clear cache:', error)
    }
  }
}

// Optimistic updates helper
export class OptimisticUpdates {
  static updateQueryData<T>(
    queryClient: QueryClient,
    queryKey: string[],
    updater: (oldData: T | undefined) => T
  ): T | undefined {
    const previousData = queryClient.getQueryData<T>(queryKey)
    
    queryClient.setQueryData<T>(queryKey, updater)
    
    return previousData
  }
  
  static rollbackQueryData<T>(
    queryClient: QueryClient,
    queryKey: string[],
    previousData: T | undefined
  ): void {
    queryClient.setQueryData<T>(queryKey, previousData)
  }
}

// Cache invalidation strategies
export class CacheInvalidation {
  static invalidateRelatedQueries(
    queryClient: QueryClient,
    entityType: string,
    entityId?: string
  ): void {
    const patterns = {
      project: ['projects', 'prompts', 'user-stats'],
      prompt: ['prompts', 'project-prompts'],
      user: ['user', 'user-plans', 'user-stats'],
      template: ['templates', 'context-templates']
    }
    
    const relatedPatterns = patterns[entityType as keyof typeof patterns] || []
    
    relatedPatterns.forEach(pattern => {
      queryClient.invalidateQueries({
        queryKey: entityId ? [pattern, entityId] : [pattern]
      })
    })
  }
  
  static prefetchRelatedData(
    queryClient: QueryClient,
    entityType: string,
    entityId: string
  ): void {
    // Prefetch related data based on entity type
    switch (entityType) {
      case 'project':
        queryClient.prefetchQuery({
          queryKey: ['prompts', entityId],
          staleTime: CACHE_CONFIGS.prompts.staleTime
        })
        break
      case 'user':
        queryClient.prefetchQuery({
          queryKey: ['user-plans', entityId],
          staleTime: CACHE_CONFIGS.plans.staleTime
        })
        break
    }
  }
}

// Performance monitoring
export class CachePerformance {
  private static metrics = new Map<string, { hits: number; misses: number; totalTime: number }>()
  
  static recordCacheHit(key: string, responseTime: number): void {
    const metric = this.metrics.get(key) || { hits: 0, misses: 0, totalTime: 0 }
    metric.hits++
    metric.totalTime += responseTime
    this.metrics.set(key, metric)
  }
  
  static recordCacheMiss(key: string, responseTime: number): void {
    const metric = this.metrics.get(key) || { hits: 0, misses: 0, totalTime: 0 }
    metric.misses++
    metric.totalTime += responseTime
    this.metrics.set(key, metric)
  }
  
  static getCacheMetrics() {
    const results = Array.from(this.metrics.entries()).map(([key, metric]) => ({
      key,
      hitRate: metric.hits / (metric.hits + metric.misses),
      avgResponseTime: metric.totalTime / (metric.hits + metric.misses),
      totalRequests: metric.hits + metric.misses
    }))
    
    return results.sort((a, b) => b.totalRequests - a.totalRequests)
  }
}
