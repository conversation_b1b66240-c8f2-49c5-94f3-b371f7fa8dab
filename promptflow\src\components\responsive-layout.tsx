'use client'

import { ReactNode, useMemo } from 'react'
import { cn } from '@/lib/utils'
import { useResponsive, useBreakpointValue, useResponsiveGrid } from '@/hooks/use-responsive'

// Responsive container component
interface ResponsiveContainerProps {
  children: ReactNode
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: boolean | 'sm' | 'md' | 'lg'
  className?: string
}

export function ResponsiveContainer({
  children,
  maxWidth = 'xl',
  padding = true,
  className
}: ResponsiveContainerProps) {
  const { isMobile, isTablet } = useResponsive()

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  }

  const paddingClasses = useMemo(() => {
    if (padding === false) return ''
    
    if (typeof padding === 'string') {
      const paddingMap = {
        sm: isMobile ? 'px-3 py-2' : 'px-4 py-3',
        md: isMobile ? 'px-4 py-3' : 'px-6 py-4',
        lg: isMobile ? 'px-6 py-4' : 'px-8 py-6'
      }
      return paddingMap[padding]
    }

    // Default responsive padding
    return isMobile ? 'px-4 py-3' : isTablet ? 'px-6 py-4' : 'px-8 py-6'
  }, [padding, isMobile, isTablet])

  return (
    <div className={cn(
      'mx-auto w-full',
      maxWidthClasses[maxWidth],
      paddingClasses,
      className
    )}>
      {children}
    </div>
  )
}

// Responsive grid component
interface ResponsiveGridProps {
  children: ReactNode
  columns?: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
    '2xl'?: number
  }
  gap?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

export function ResponsiveGrid({
  children,
  columns = { xs: 1, sm: 2, md: 3, lg: 4 },
  gap = 'md',
  className
}: ResponsiveGridProps) {
  const currentColumns = useBreakpointValue(columns) || 1

  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8'
  }

  return (
    <div
      className={cn(
        'grid',
        gapClasses[gap],
        className
      )}
      style={{
        gridTemplateColumns: `repeat(${currentColumns}, minmax(0, 1fr))`
      }}
    >
      {children}
    </div>
  )
}

// Responsive stack component (vertical layout)
interface ResponsiveStackProps {
  children: ReactNode
  spacing?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  align?: 'start' | 'center' | 'end' | 'stretch'
  className?: string
}

export function ResponsiveStack({
  children,
  spacing = 'md',
  align = 'stretch',
  className
}: ResponsiveStackProps) {
  const { isMobile } = useResponsive()

  const spacingClasses = {
    xs: isMobile ? 'space-y-1' : 'space-y-2',
    sm: isMobile ? 'space-y-2' : 'space-y-3',
    md: isMobile ? 'space-y-3' : 'space-y-4',
    lg: isMobile ? 'space-y-4' : 'space-y-6',
    xl: isMobile ? 'space-y-6' : 'space-y-8'
  }

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch'
  }

  return (
    <div className={cn(
      'flex flex-col',
      spacingClasses[spacing],
      alignClasses[align],
      className
    )}>
      {children}
    </div>
  )
}

// Responsive flex component
interface ResponsiveFlexProps {
  children: ReactNode
  direction?: {
    xs?: 'row' | 'col'
    sm?: 'row' | 'col'
    md?: 'row' | 'col'
    lg?: 'row' | 'col'
  }
  align?: 'start' | 'center' | 'end' | 'stretch'
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly'
  wrap?: boolean
  gap?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

export function ResponsiveFlex({
  children,
  direction = { xs: 'col', md: 'row' },
  align = 'start',
  justify = 'start',
  wrap = false,
  gap = 'md',
  className
}: ResponsiveFlexProps) {
  const currentDirection = useBreakpointValue(direction) || 'row'

  const directionClasses = {
    row: 'flex-row',
    col: 'flex-col'
  }

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch'
  }

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly'
  }

  const gapClasses = {
    xs: currentDirection === 'row' ? 'gap-x-1' : 'gap-y-1',
    sm: currentDirection === 'row' ? 'gap-x-2' : 'gap-y-2',
    md: currentDirection === 'row' ? 'gap-x-4' : 'gap-y-4',
    lg: currentDirection === 'row' ? 'gap-x-6' : 'gap-y-6',
    xl: currentDirection === 'row' ? 'gap-x-8' : 'gap-y-8'
  }

  return (
    <div className={cn(
      'flex',
      directionClasses[currentDirection],
      alignClasses[align],
      justifyClasses[justify],
      wrap && 'flex-wrap',
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  )
}

// Responsive sidebar layout
interface ResponsiveSidebarLayoutProps {
  sidebar: ReactNode
  main: ReactNode
  sidebarWidth?: 'sm' | 'md' | 'lg'
  collapsible?: boolean
  defaultCollapsed?: boolean
  className?: string
}

export function ResponsiveSidebarLayout({
  sidebar,
  main,
  sidebarWidth = 'md',
  collapsible = true,
  defaultCollapsed = false,
  className
}: ResponsiveSidebarLayoutProps) {
  const { isMobile, isTablet } = useResponsive()

  const sidebarWidthClasses = {
    sm: 'w-64',
    md: 'w-80',
    lg: 'w-96'
  }

  if (isMobile) {
    // Mobile: Stack layout
    return (
      <div className={cn('flex flex-col h-full', className)}>
        <div className="flex-1 overflow-hidden">
          {main}
        </div>
        <div className="border-t bg-white">
          {sidebar}
        </div>
      </div>
    )
  }

  if (isTablet && collapsible) {
    // Tablet: Collapsible sidebar
    return (
      <div className={cn('flex h-full', className)}>
        <div className={cn(
          'flex-shrink-0 border-r bg-white transition-all duration-300',
          defaultCollapsed ? 'w-16' : sidebarWidthClasses[sidebarWidth]
        )}>
          {sidebar}
        </div>
        <div className="flex-1 overflow-hidden">
          {main}
        </div>
      </div>
    )
  }

  // Desktop: Full sidebar
  return (
    <div className={cn('flex h-full', className)}>
      <div className={cn(
        'flex-shrink-0 border-r bg-white',
        sidebarWidthClasses[sidebarWidth]
      )}>
        {sidebar}
      </div>
      <div className="flex-1 overflow-hidden">
        {main}
      </div>
    </div>
  )
}

// Responsive modal/drawer
interface ResponsiveModalProps {
  children: ReactNode
  isOpen: boolean
  onClose: () => void
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  className?: string
}

export function ResponsiveModal({
  children,
  isOpen,
  onClose,
  title,
  size = 'md',
  className
}: ResponsiveModalProps) {
  const { isMobile } = useResponsive()

  if (!isOpen) return null

  if (isMobile) {
    // Mobile: Full-screen drawer from bottom
    return (
      <div className="fixed inset-0 z-50 flex items-end">
        <div
          className="absolute inset-0 bg-black/50"
          onClick={onClose}
        />
        <div className={cn(
          'relative w-full bg-white rounded-t-lg max-h-[90vh] overflow-hidden',
          'animate-in slide-in-from-bottom duration-300',
          className
        )}>
          {title && (
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">{title}</h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                ×
              </button>
            </div>
          )}
          <div className="overflow-auto max-h-full">
            {children}
          </div>
        </div>
      </div>
    )
  }

  // Desktop: Centered modal
  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'max-w-full mx-4'
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div
        className="absolute inset-0 bg-black/50"
        onClick={onClose}
      />
      <div className={cn(
        'relative bg-white rounded-lg shadow-xl w-full',
        sizeClasses[size],
        'animate-in fade-in zoom-in-95 duration-300',
        className
      )}>
        {title && (
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-xl font-semibold">{title}</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              ×
            </button>
          </div>
        )}
        <div className="p-6">
          {children}
        </div>
      </div>
    </div>
  )
}
