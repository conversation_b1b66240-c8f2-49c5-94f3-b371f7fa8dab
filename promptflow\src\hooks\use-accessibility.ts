/**
 * Accessibility Hooks
 * React hooks for accessibility features and WCAG compliance
 */

'use client'

import { useEffect, useRef, useState, useCallback } from 'react'
import { 
  AriaLiveRegion, 
  KeyboardNavigation, 
  ScreenReader, 
  FormAccessibility,
  AccessibilityTester 
} from '@/lib/accessibility-utils'

// Hook for ARIA live announcements
export function useAriaLive() {
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    AriaLiveRegion.announce(message, priority)
  }, [])

  const clear = useCallback((priority: 'polite' | 'assertive' = 'polite') => {
    AriaLiveRegion.clear(priority)
  }, [])

  return { announce, clear }
}

// Hook for focus management
export function useFocusManagement() {
  const [focusedElement, setFocusedElement] = useState<HTMLElement | null>(null)
  
  const saveFocus = useCallback(() => {
    setFocusedElement(document.activeElement as HTMLElement)
  }, [])

  const restoreFocus = useCallback(() => {
    if (focusedElement && focusedElement.focus) {
      focusedElement.focus()
    }
  }, [focusedElement])

  const focusFirst = useCallback((container: HTMLElement) => {
    const focusableElements = KeyboardNavigation.getFocusableElements(container)
    if (focusableElements.length > 0) {
      focusableElements[0].focus()
    }
  }, [])

  return {
    saveFocus,
    restoreFocus,
    focusFirst,
    focusedElement
  }
}

// Hook for focus trap (modals, dialogs)
export function useFocusTrap(isActive: boolean = false) {
  const containerRef = useRef<HTMLElement>(null)
  const cleanupRef = useRef<(() => void) | null>(null)

  useEffect(() => {
    if (isActive && containerRef.current) {
      const cleanup = KeyboardNavigation.trapFocus(containerRef.current)
      cleanupRef.current = cleanup || null
    }

    return () => {
      if (cleanupRef.current) {
        cleanupRef.current()
        cleanupRef.current = null
      }
    }
  }, [isActive])

  return containerRef
}

// Hook for keyboard navigation
export function useKeyboardNavigation(handlers: {
  onEscape?: () => void
  onEnter?: () => void
  onArrowUp?: () => void
  onArrowDown?: () => void
  onArrowLeft?: () => void
  onArrowRight?: () => void
  onTab?: (e: KeyboardEvent) => void
} = {}) {
  const elementRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          handlers.onEscape?.()
          break
        case 'Enter':
          handlers.onEnter?.()
          break
        case 'ArrowUp':
          e.preventDefault()
          handlers.onArrowUp?.()
          break
        case 'ArrowDown':
          e.preventDefault()
          handlers.onArrowDown?.()
          break
        case 'ArrowLeft':
          handlers.onArrowLeft?.()
          break
        case 'ArrowRight':
          handlers.onArrowRight?.()
          break
        case 'Tab':
          handlers.onTab?.(e)
          break
      }
    }

    element.addEventListener('keydown', handleKeyDown)
    return () => element.removeEventListener('keydown', handleKeyDown)
  }, [handlers])

  return elementRef
}

// Hook for form accessibility
export function useFormAccessibility() {
  const addError = useCallback((field: HTMLElement, message: string) => {
    return FormAccessibility.addFieldError(field, message)
  }, [])

  const removeError = useCallback((field: HTMLElement, errorId: string) => {
    FormAccessibility.removeFieldError(field, errorId)
  }, [])

  const addSuccess = useCallback((field: HTMLElement, message: string) => {
    return FormAccessibility.addFieldSuccess(field, message)
  }, [])

  return {
    addError,
    removeError,
    addSuccess
  }
}

// Hook for screen reader announcements
export function useScreenReader() {
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    ScreenReader.announceStatus(message, priority)
  }, [])

  const createDescribedBy = useCallback((element: HTMLElement, description: string) => {
    return ScreenReader.createDescribedBy(element, description)
  }, [])

  const createLabelledBy = useCallback((element: HTMLElement, labelText: string) => {
    return ScreenReader.createLabelledBy(element, labelText)
  }, [])

  return {
    announce,
    createDescribedBy,
    createLabelledBy
  }
}

// Hook for accessibility testing
export function useAccessibilityTesting() {
  const [report, setReport] = useState<ReturnType<typeof AccessibilityTester.generateReport> | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const runTests = useCallback(async () => {
    setIsLoading(true)
    
    // Wait for DOM to be ready
    await new Promise(resolve => setTimeout(resolve, 100))
    
    try {
      const testReport = AccessibilityTester.generateReport()
      setReport(testReport)
    } catch (error) {
      console.error('Accessibility testing failed:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const getScore = useCallback(() => {
    if (!report) return null

    const colorContrastPassed = report.colorContrast.filter(test => test.passes).length
    const colorContrastTotal = report.colorContrast.length
    const colorContrastScore = colorContrastTotal > 0 ? (colorContrastPassed / colorContrastTotal) * 100 : 100

    const keyboardIssues = report.keyboardNavigation.issues.length
    const keyboardScore = Math.max(0, 100 - (keyboardIssues * 10))

    const ariaScore = report.ariaLabels > 0 ? 100 : 50

    // Check heading structure
    let headingScore = 100
    for (let i = 1; i < report.headingStructure.length; i++) {
      const current = report.headingStructure[i]
      const previous = report.headingStructure[i - 1]
      if (current.level > previous.level + 1) {
        headingScore -= 20 // Penalty for skipping heading levels
      }
    }

    const overallScore = (colorContrastScore + keyboardScore + ariaScore + headingScore) / 4

    return {
      overall: Math.round(overallScore),
      colorContrast: Math.round(colorContrastScore),
      keyboard: Math.round(keyboardScore),
      aria: Math.round(ariaScore),
      headings: Math.round(headingScore)
    }
  }, [report])

  return {
    report,
    isLoading,
    runTests,
    getScore
  }
}

// Hook for reduced motion preference
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    if (typeof window === 'undefined') return

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersReducedMotion
}

// Hook for high contrast preference
export function useHighContrast() {
  const [prefersHighContrast, setPrefersHighContrast] = useState(false)

  useEffect(() => {
    if (typeof window === 'undefined') return

    const mediaQuery = window.matchMedia('(prefers-contrast: high)')
    setPrefersHighContrast(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersHighContrast(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersHighContrast
}

// Hook for color scheme preference
export function useColorScheme() {
  const [colorScheme, setColorScheme] = useState<'light' | 'dark'>('light')

  useEffect(() => {
    if (typeof window === 'undefined') return

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    setColorScheme(mediaQuery.matches ? 'dark' : 'light')

    const handleChange = (e: MediaQueryListEvent) => {
      setColorScheme(e.matches ? 'dark' : 'light')
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return colorScheme
}

// Hook for comprehensive accessibility state
export function useAccessibility() {
  const { announce, clear } = useAriaLive()
  const { saveFocus, restoreFocus, focusFirst } = useFocusManagement()
  const { announce: screenReaderAnnounce } = useScreenReader()
  const prefersReducedMotion = useReducedMotion()
  const prefersHighContrast = useHighContrast()
  const colorScheme = useColorScheme()

  return {
    // Announcements
    announce,
    clear,
    screenReaderAnnounce,
    
    // Focus management
    saveFocus,
    restoreFocus,
    focusFirst,
    
    // User preferences
    prefersReducedMotion,
    prefersHighContrast,
    colorScheme,
    
    // Utilities
    generateId: ScreenReader.generateId
  }
}
