import { LandingPage } from "@/components/landing-page";
import { Metadata } from "next";
import { SEOGenerator } from "@/lib/seo-utils";

// Enhanced SEO metadata for home page
export const metadata: Metadata = SEOGenerator.generateMetadata({
  title: "PromptFlow - AI Destekli Prompt Yönetim Platformu",
  description: "AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformu. Prompt'larınızı organize edin, takımınızla paylaşın ve verimliliğinizi 10x artırın. 10,000+ geliştirici tarafından güvenilir.",
  keywords: [
    "prompt yönetimi",
    "AI araçları",
    "yapay zeka",
    "geliştirici araçları",
    "prompt engineering",
    "AI destekli geliştirme",
    "kod optimizasyonu",
    "proje yönetimi",
    "ChatGPT prompts",
    "AI productivity",
    "prompt organizasyonu",
    "takım çalışması",
    "context gallery",
    "API erişimi",
    "gerçek zamanlı senkronizasyon"
  ],
  url: "/",
  type: "website"
});

export default function Home() {
  return <LandingPage />;
}
