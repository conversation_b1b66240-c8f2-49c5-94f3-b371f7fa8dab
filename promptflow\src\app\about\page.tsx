import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  ArrowLeft, 
  Sparkles, 
  Users, 
  Target, 
  Lightbulb, 
  Shield, 
  Zap,
  Heart,
  Globe,
  TrendingUp
} from 'lucide-react'

export const metadata: Metadata = {
  title: 'Hakkımızda - PromptBir | AI Destekli Prompt Yönetim Platformu',
  description: 'PromptBir\'in hikayesi, misyonu ve vizyonu. AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformumuz hakkında bilgi edinin.',
  keywords: [
    'PromptBir hakkında',
    'AI prompt yönetimi',
    'şirket hikayesi',
    'misyon vizyon',
    'yapay zeka araçları',
    'geliştirici araçları'
  ],
  openGraph: {
    title: 'Hakkımızda - PromptBir',
    description: 'AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformu',
    type: 'website',
    url: 'https://promptbir.com/about'
  }
}

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link 
              href="/" 
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Ana Sayfaya Dön</span>
            </Link>
            <div className="flex items-center space-x-2">
              <Sparkles className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">PromptBir</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
            Hakkımızda
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            PromptBir, AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformudur. 
            Geliştiricilerin ve AI kullanıcılarının verimliliğini artırmak için tasarlandı.
          </p>
        </div>

        {/* Story Section */}
        <section className="mb-16">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-2xl">
                <Lightbulb className="h-6 w-6 text-yellow-500" />
                Hikayemiz
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-lg max-w-none">
              <p className="text-gray-700 leading-relaxed mb-4">
                2024 yılında, AI teknolojilerinin hızla geliştiği bir dönemde, geliştiricilerin ve içerik üreticilerinin 
                prompt yönetiminde yaşadığı zorlukları fark ettik. Karmaşık prompt'ları organize etmek, takımla paylaşmak 
                ve verimli bir şekilde yönetmek büyük bir ihtiyaç haline gelmişti.
              </p>
              <p className="text-gray-700 leading-relaxed mb-4">
                Bu ihtiyaçtan yola çıkarak PromptBir'i geliştirdik. Amacımız, AI ile çalışan herkesin prompt'larını 
                profesyonelce yönetebileceği, takımıyla paylaşabileceği ve verimliliğini artırabileceği bir platform 
                oluşturmaktı.
              </p>
              <p className="text-gray-700 leading-relaxed">
                Bugün binlerce kullanıcı PromptBir ile AI projelerini daha verimli bir şekilde yönetiyor ve 
                prompt'larını organize ediyor.
              </p>
            </CardContent>
          </Card>
        </section>

        {/* Mission & Vision */}
        <section className="mb-16">
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-xl">
                  <Target className="h-6 w-6 text-blue-600" />
                  Misyonumuz
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">
                  AI destekli geliştirme süreçlerini herkes için erişilebilir, verimli ve organize hale getirmek. 
                  Geliştiricilerin ve içerik üreticilerinin AI araçlarını daha etkili kullanmalarını sağlamak.
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-xl">
                  <Globe className="h-6 w-6 text-purple-600" />
                  Vizyonumuz
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">
                  AI prompt yönetiminde dünya çapında lider platform olmak ve AI teknolojilerinin demokratikleşmesine 
                  katkıda bulunmak. Her seviyeden kullanıcının AI'dan maksimum verim almasını sağlamak.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Values */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Değerlerimiz</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="shadow-lg text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <Shield className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle className="text-lg">Güvenlik</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Kullanıcı verilerinin güvenliği ve gizliliği bizim için önceliktir. 
                  En yüksek güvenlik standartlarını uygularız.
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-lg text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                  <Zap className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle className="text-lg">Verimlilik</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Kullanıcılarımızın zamanını değerli görür, onların daha verimli 
                  çalışmalarını sağlayacak araçlar geliştiririz.
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-lg text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                  <Heart className="h-6 w-6 text-purple-600" />
                </div>
                <CardTitle className="text-lg">Kullanıcı Odaklılık</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Tüm kararlarımızı kullanıcılarımızın ihtiyaçları doğrultusunda alır, 
                  onların geri bildirimlerini dinleriz.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Stats */}
        <section className="mb-16">
          <Card className="shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="py-12">
              <h2 className="text-3xl font-bold text-center mb-12">Rakamlarla PromptBir</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div>
                  <div className="text-3xl font-bold mb-2">1000+</div>
                  <div className="text-blue-100">Aktif Kullanıcı</div>
                </div>
                <div>
                  <div className="text-3xl font-bold mb-2">50K+</div>
                  <div className="text-blue-100">Yönetilen Prompt</div>
                </div>
                <div>
                  <div className="text-3xl font-bold mb-2">99.9%</div>
                  <div className="text-blue-100">Uptime</div>
                </div>
                <div>
                  <div className="text-3xl font-bold mb-2">24/7</div>
                  <div className="text-blue-100">Destek</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Team */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Ekibimiz</h2>
          <Card className="shadow-lg">
            <CardContent className="py-12 text-center">
              <Users className="h-16 w-16 text-blue-600 mx-auto mb-6" />
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Tutkulu Bir Ekip</h3>
              <p className="text-gray-600 max-w-2xl mx-auto leading-relaxed">
                PromptBir ekibi, AI teknolojileri, yazılım geliştirme ve kullanıcı deneyimi konularında 
                uzman profesyonellerden oluşur. Amacımız, kullanıcılarımıza en iyi deneyimi sunmak için 
                sürekli yenilik yapmak ve gelişmektir.
              </p>
            </CardContent>
          </Card>
        </section>

        {/* CTA */}
        <section className="text-center">
          <Card className="shadow-lg">
            <CardContent className="py-12">
              <TrendingUp className="h-16 w-16 text-green-600 mx-auto mb-6" />
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Bizimle Büyüyün
              </h2>
              <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
                PromptBir ile AI projelerinizi bir üst seviyeye taşıyın. 
                Hemen ücretsiz hesabınızı oluşturun ve farkı yaşayın.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth">
                  <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700">
                    Ücretsiz Başlayın
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button variant="outline" size="lg">
                    İletişime Geçin
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-400">
            © 2024 PromptBir. Tüm hakları saklıdır.
          </p>
        </div>
      </footer>
    </div>
  )
}
