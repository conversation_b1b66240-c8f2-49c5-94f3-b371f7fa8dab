(()=>{var a={};a.id=5953,a.ids=[5953],a.modules={163:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=c(71042).unstable_rethrow;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5150:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21401:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>u,generateMetadata:()=>t});var d=c(37413),e=c(4536),f=c.n(e),g=c(97576),h=c(78963),i=c(30084),j=c(23469),k=c(51465),l=c(71891),m=c(88971),n=c(40918),o=c(53148);let p=(0,c(26373).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var q=c(75234),r=c(36440);let s={"promptbir-ile-ai-verimlilik":{id:1,title:"PromptBir ile AI Verimliliğinizi 10 Kat Artırın",slug:"promptbir-ile-ai-verimlilik",excerpt:"AI prompt'larınızı organize ederek nasıl daha verimli \xe7alışabileceğinizi \xf6ğrenin. PromptBir'in sunduğu \xf6zellikler ile iş akışınızı optimize edin.",author:"PromptBir Ekibi",publishDate:"2024-01-15",readTime:"8 dakika",category:"Verimlilik",tags:["AI","Verimlilik","Prompt Y\xf6netimi"],featured:!0,content:`
# PromptBir ile AI Verimliliğinizi 10 Kat Artırın

AI teknolojilerinin hızla geliştiği g\xfcn\xfcm\xfczde, doğru prompt y\xf6netimi kritik \xf6nem taşıyor. PromptBir, AI prompt'larınızı organize etmenizi ve verimliliğinizi maksimize etmenizi sağlayan g\xfc\xe7l\xfc bir platform.

## Neden Prompt Y\xf6netimi \xd6nemli?

Modern iş d\xfcnyasında AI ara\xe7ları g\xfcnl\xfck rutinimizin ayrılmaz bir par\xe7ası haline geldi. ChatGPT, Claude, Gemini gibi AI asistanları ile s\xfcrekli etkileşim halindeyiz. Ancak \xe7oğu zaman:

- Daha \xf6nce kullandığımız etkili prompt'ları hatırlayamıyoruz
- Benzer g\xf6revler i\xe7in s\xfcrekli yeni prompt'lar yazıyoruz
- Takım arkadaşlarımızla prompt'ları paylaşamıyoruz
- Hangi prompt'un hangi durumda daha etkili olduğunu bilmiyoruz

## PromptBir'in Sunduğu \xc7\xf6z\xfcmler

### 1. Merkezi Prompt K\xfct\xfcphanesi

PromptBir ile t\xfcm prompt'larınızı tek bir yerde toplayabilirsiniz:

- **Kategorilere Ayırma**: Prompt'larınızı konularına g\xf6re organize edin
- **Etiketleme Sistemi**: Hızlı arama i\xe7in etiketler kullanın
- **Favoriler**: En \xe7ok kullandığınız prompt'ları favorilerinize ekleyin

### 2. Takım \xc7alışması

Ekip halinde \xe7alışırken prompt paylaşımı kritik \xf6nem taşır:

- **Proje Bazlı Organizasyon**: Her proje i\xe7in ayrı prompt koleksiyonları
- **Yetki Y\xf6netimi**: Kimin hangi prompt'lara erişebileceğini belirleyin
- **Ger\xe7ek Zamanlı Senkronizasyon**: Değişiklikler anında t\xfcm ekibe yansır

### 3. Akıllı \xd6neriler

PromptBir'in AI destekli \xf6zellikleri:

- **Benzer Prompt \xd6nerileri**: Yazdığınız prompt'a benzer olanları g\xf6sterir
- **Performans Analizi**: Hangi prompt'ların daha etkili olduğunu analiz eder
- **Otomatik Kategorilendirme**: Yeni prompt'ları otomatik olarak kategoriler

## Pratik Kullanım \xd6rnekleri

### İ\xe7erik \xdcretimi

\`\`\`
Prompt: "Aşağıdaki konuda 500 kelimelik SEO uyumlu bir blog yazısı yaz:
- Hedef kitle: [HEDEF_KITLE]
- Ana anahtar kelime: [ANAHTAR_KELIME]
- Ton: Profesyonel ama samimi
- CTA: [CALL_TO_ACTION]"
\`\`\`

### Kod İncelemesi

\`\`\`
Prompt: "Bu kodu incele ve şu a\xe7ılardan değerlendir:
1. Performans optimizasyonu
2. G\xfcvenlik a\xe7ıkları
3. Kod kalitesi ve okunabilirlik
4. Best practice uyumu
\xd6nerilerini madde madde listele."
\`\`\`

### E-posta Yazımı

\`\`\`
Prompt: "Aşağıdaki bilgileri kullanarak profesyonel bir e-posta yaz:
- Alıcı: [ALICI_ADI]
- Konu: [KONU]
- Ama\xe7: [AMAC]
- Ton: [TON]
- \xd6nemli noktalar: [ONEMLI_NOKTALAR]"
\`\`\`

## Verimlilik Artırma Stratejileri

### 1. Template Kullanımı

Sık kullandığınız prompt t\xfcrleri i\xe7in şablonlar oluşturun:

- **Değişken Alanlar**: [DEGISKEN] formatında placeholder'lar kullanın
- **Standart Yapı**: Her prompt t\xfcr\xfc i\xe7in tutarlı format
- **Hızlı Doldurma**: Değişkenleri tek tıkla doldurun

### 2. Versiyon Kontrol\xfc

Prompt'larınızın farklı versiyonlarını takip edin:

- **Değişiklik Ge\xe7mişi**: Her değişikliği kaydedin
- **A/B Testing**: Farklı versiyonları test edin
- **Geri Alma**: \xd6nceki versiyonlara kolayca d\xf6n\xfcn

### 3. Performans Takibi

Hangi prompt'ların daha etkili olduğunu \xf6l\xe7\xfcn:

- **Kullanım Sıklığı**: En \xe7ok kullanılan prompt'ları belirleyin
- **Başarı Oranı**: Hangi prompt'ların daha iyi sonu\xe7 verdiğini takip edin
- **Zaman Tasarrufu**: Ne kadar zaman kazandığınızı hesaplayın

## Başlangı\xe7 İ\xe7in İpu\xe7ları

### 1. Mevcut Prompt'larınızı Toplayın

- Ge\xe7miş sohbet ge\xe7mişlerinizi inceleyin
- Etkili bulduğunuz prompt'ları kaydedin
- Kategorilere ayırarak organize edin

### 2. Takımınızla Paylaşın

- En iyi prompt'larınızı ekibinizle paylaşın
- Ortak bir prompt k\xfct\xfcphanesi oluşturun
- D\xfczenli olarak yeni prompt'lar ekleyin

### 3. S\xfcrekli İyileştirin

- Prompt'larınızı d\xfczenli olarak g\xf6zden ge\xe7irin
- Yeni AI ara\xe7ları i\xe7in uyarlayın
- Geri bildirimler doğrultusunda g\xfcncelleyin

## Sonu\xe7

PromptBir ile AI verimliliğinizi artırmak artık \xe7ok kolay. Doğru prompt y\xf6netimi stratejileri ile:

- **%300 Zaman Tasarrufu**: Hazır prompt'lar ile hızlı \xe7alışın
- **%500 Kalite Artışı**: Test edilmiş prompt'lar ile daha iyi sonu\xe7lar
- **%200 Takım Verimliliği**: Paylaşılan bilgi ile ekip performansı

Hemen bug\xfcn PromptBir'i denemeye başlayın ve AI verimliliğinizi bir \xfcst seviyeye taşıyın!

---

*Bu yazı PromptBir ekibi tarafından hazırlanmıştır. Platform hakkında daha fazla bilgi i\xe7in [PromptBir.com](https://promptbir.com) adresini ziyaret edebilirsiniz.*
    `},"prompt-engineering-rehber":{id:2,title:"Prompt Engineering: Başlangı\xe7tan İleri Seviyeye Rehber",slug:"prompt-engineering-rehber",excerpt:"Etkili prompt yazma sanatını \xf6ğrenin. Temel prensiplerden ileri tekniklere kadar kapsamlı rehber.",author:"Dr. Ahmet Yılmaz",publishDate:"2024-01-10",readTime:"12 dakika",category:"Eğitim",tags:["Prompt Engineering","AI","Eğitim"],featured:!0,content:`
# Prompt Engineering: Başlangı\xe7tan İleri Seviyeye Rehber

Prompt engineering, AI ile etkileşimde bulunmanın en \xf6nemli becerilerinden biri haline geldi. Bu kapsamlı rehberde, etkili prompt yazma sanatını baştan sona \xf6ğreneceksiniz.

## Prompt Engineering Nedir?

Prompt engineering, AI modellerinden istediğiniz sonu\xe7ları elde etmek i\xe7in optimize edilmiş talimatlar yazma sanatıdır. Doğru prompt, AI'dan aldığınız yanıtın kalitesini dramatik şekilde artırabilir.

## Temel Prensipler

### 1. A\xe7ık ve Net Olun

**K\xf6t\xfc \xd6rnek:**
\`\`\`
"Bir yazı yaz"
\`\`\`

**İyi \xd6rnek:**
\`\`\`
"E-ticaret şirketleri i\xe7in sosyal medya pazarlama stratejileri hakkında 
500 kelimelik, SEO uyumlu bir blog yazısı yaz. Hedef kitle: KOBİ sahipleri."
\`\`\`

### 2. Bağlam Sağlayın

AI'ya yeterli bağlam vermek kritik \xf6nem taşır:

\`\`\`
"Sen deneyimli bir pazarlama uzmanısın. 10 yıllık tecriben var.
Aşağıdaki şirket i\xe7in bir pazarlama stratejisi geliştir:
- Şirket: Organik gıda e-ticaret
- Hedef: 25-45 yaş arası sağlık bilincine sahip bireyler
- B\xfct\xe7e: Aylık 50.000 TL
- S\xfcre: 6 ay"
\`\`\`

### 3. \xd6rnekler Verin

Few-shot learning tekniği ile daha iyi sonu\xe7lar alın:

\`\`\`
"Aşağıdaki \xf6rneklere benzer \xfcr\xfcn a\xe7ıklamaları yaz:

\xd6rnek 1:
\xdcr\xfcn: Kablosuz Kulaklık
A\xe7ıklama: "G\xfcr\xfclt\xfc \xf6nleyici teknoloji ile kristal berraklığında ses deneyimi. 
30 saate kadar kesintisiz m\xfczik keyfi."

\xd6rnek 2:
\xdcr\xfcn: Akıllı Saat
A\xe7ıklama: "Sağlığınızı 7/24 takip eden akıllı asistanınız. 
Su ge\xe7irmez tasarım ile her ortamda yanınızda."

Şimdi bu \xfcr\xfcn i\xe7in a\xe7ıklama yaz:
\xdcr\xfcn: Bluetooth Hoparl\xf6r"
\`\`\`

## İleri Seviye Teknikler

### 1. Chain of Thought (CoT)

AI'ya adım adım d\xfcş\xfcnmesini s\xf6yleyin:

\`\`\`
"Bu problemi adım adım \xe7\xf6z:

Problem: Bir şirketin aylık geliri 100.000 TL, giderleri 75.000 TL. 
Gelirini %20 artırıp, giderlerini %10 azaltırsa, 
yeni kar marjı ne olur?

Adımlar:
1. Mevcut kar hesapla
2. Yeni geliri hesapla
3. Yeni giderleri hesapla
4. Yeni karı hesapla
5. Kar marjını hesapla"
\`\`\`

### 2. Role Playing

AI'ya belirli bir rol verin:

\`\`\`
"Sen deneyimli bir UX/UI tasarımcısısın. 
Aşağıdaki mobil uygulama i\xe7in kullanıcı deneyimi analizi yap:

Uygulama: Yemek sipariş uygulaması
Problem: Kullanıcılar sipariş tamamlama oranı d\xfcş\xfck
Veri: %60 kullanıcı sepete \xfcr\xfcn ekliyor ama sadece %25'i sipariş tamamlıyor

Analiz et ve \xe7\xf6z\xfcm \xf6nerileri sun."
\`\`\`

### 3. Constraint-Based Prompting

Sınırlamalar belirleyin:

\`\`\`
"Aşağıdaki kısıtlamalara uyarak bir blog yazısı yaz:
- Maksimum 300 kelime
- En az 3 madde listesi
- Başlık H1, alt başlıklar H2 formatında
- Her paragraf maksimum 2 c\xfcmle
- Sonunda CTA bulunsun
- SEO i\xe7in 'dijital pazarlama' kelimesi 5 kez ge\xe7sin"
\`\`\`

## Farklı AI Modelleri İ\xe7in Optimizasyon

### ChatGPT İ\xe7in

- Uzun ve detaylı prompt'lar tercih edin
- Bağlam penceresi geniş, \xf6nceki konuşmaları referans alabilir
- Yaratıcı g\xf6revlerde \xe7ok başarılı

### Claude İ\xe7in

- Yapılandırılmış prompt'lar daha etkili
- Analitik g\xf6revlerde g\xfc\xe7l\xfc
- G\xfcvenlik konularında daha dikkatli

### Gemini İ\xe7in

- \xc7ok modlu (metin + g\xf6rsel) prompt'lar destekler
- Google hizmetleri ile entegrasyon
- Ger\xe7ek zamanlı bilgi erişimi

## Yaygın Hatalar ve \xc7\xf6z\xfcmleri

### 1. Belirsiz Talimatlar

**Hata:**
\`\`\`
"İyi bir sunum hazırla"
\`\`\`

**\xc7\xf6z\xfcm:**
\`\`\`
"Startup şirketimiz i\xe7in yatırımcı sunumu hazırla:
- 10 slayt
- Problem, \xe7\xf6z\xfcm, pazar, iş modeli, takım, finansal projeksiyonlar
- Her slayt i\xe7in konuşma notları
- G\xf6rsel \xf6neriler"
\`\`\`

### 2. \xc7ok Karmaşık Talimatlar

**Hata:**
\`\`\`
"Bir e-ticaret sitesi i\xe7in SEO stratejisi geliştir, aynı zamanda sosyal medya 
planı yap, influencer listesi oluştur, b\xfct\xe7e planla, timeline hazırla..."
\`\`\`

**\xc7\xf6z\xfcm:**
G\xf6revleri par\xe7alara ayırın ve sırayla isteyin.

### 3. Bağlam Eksikliği

**Hata:**
\`\`\`
"Bu kodu optimize et: [kod]"
\`\`\`

**\xc7\xf6z\xfcm:**
\`\`\`
"Bu Python kodunu performans a\xe7ısından optimize et:
- Hedef: API yanıt s\xfcresini %50 azalt
- Kısıtlar: Mevcut fonksiyonalite değişmemeli
- Ortam: Django, PostgreSQL
- Kod: [kod]"
\`\`\`

## Prompt Şablonları

### İ\xe7erik \xdcretimi Şablonu

\`\`\`
"[ROL] olarak hareket et.

G\xf6rev: [G\xd6REV_TANIMI]
Hedef Kitle: [HEDEF_KITLE]
Ton: [TON]
Uzunluk: [UZUNLUK]
Format: [FORMAT]
Anahtar Kelimeler: [ANAHTAR_KELIMELER]

Ek Gereksinimler:
- [GEREKSINIM_1]
- [GEREKSINIM_2]
- [GEREKSINIM_3]

\xc7ıktı formatı:
[ISTENEN_FORMAT]"
\`\`\`

### Problem \xc7\xf6zme Şablonu

\`\`\`
"Problem: [PROBLEM_TANIMI]

Bağlam:
- [BAGLAM_1]
- [BAGLAM_2]
- [BAGLAM_3]

Kısıtlar:
- [KISIT_1]
- [KISIT_2]

Beklenen \xc7ıktı:
1. Problem analizi
2. Alternatif \xe7\xf6z\xfcmler (en az 3)
3. Her \xe7\xf6z\xfcm i\xe7in artı/eksi
4. \xd6nerilen \xe7\xf6z\xfcm ve gerek\xe7esi
5. Uygulama adımları"
\`\`\`

## Performans \xd6l\xe7\xfcm\xfc

Prompt'larınızın etkinliğini \xf6l\xe7mek i\xe7in:

### 1. A/B Testing

Aynı g\xf6rev i\xe7in farklı prompt'lar deneyin ve sonu\xe7ları karşılaştırın.

### 2. Kalite Metrikleri

- **Doğruluk**: Verilen bilgiler ne kadar doğru?
- **Relevans**: Cevap soruya ne kadar uygun?
- **Yaratıcılık**: \xd6zg\xfcn ve yaratıcı mı?
- **Kullanılabilirlik**: Pratik olarak kullanılabilir mi?

### 3. Zaman Tasarrufu

- Prompt yazma s\xfcresi
- İstenen sonucu alma s\xfcresi
- D\xfczeltme ihtiyacı

## Gelecek Trendleri

### 1. Multimodal Prompting

Metin + g\xf6rsel + ses kombinasyonları

### 2. Automated Prompt Optimization

AI'ın kendi prompt'larını optimize etmesi

### 3. Domain-Specific Prompting

Sekt\xf6re \xf6zel prompt k\xfct\xfcphaneleri

## Sonu\xe7

Prompt engineering, AI \xe7ağında en değerli becerilerden biri. Bu rehberdeki teknikleri uygulayarak:

- Daha etkili AI etkileşimleri
- Zaman tasarrufu
- Kaliteli \xe7ıktılar
- Profesyonel avantaj

S\xfcrekli pratik yapın ve yeni teknikleri deneyin. AI teknolojisi hızla gelişiyor, prompt engineering becerinizi de s\xfcrekli g\xfcncel tutun.

---

*Dr. Ahmet Yılmaz, AI ve Makine \xd6ğrenmesi alanında 15 yıllık deneyime sahip akademisyen ve danışmandır.*
    `},"takim-calismasi-ai-prompts":{id:3,title:"Takım \xc7alışmasında AI Prompt'ları: İşbirliği Rehberi",slug:"takim-calismasi-ai-prompts",excerpt:"Takım halinde \xe7alışırken AI prompt'larını nasıl etkili şekilde paylaşabileceğinizi ve y\xf6netebileceğinizi \xf6ğrenin.",author:"Elif Kaya",publishDate:"2024-01-05",readTime:"10 dakika",category:"Takım \xc7alışması",tags:["Takım \xc7alışması","İşbirliği","Prompt Paylaşımı"],featured:!1,content:`
# Takım \xc7alışmasında AI Prompt'ları: İşbirliği Rehberi

Modern iş d\xfcnyasında takımlar, AI ara\xe7larını g\xfcnl\xfck iş akışlarına entegre ediyor. Ancak prompt'ları etkili şekilde paylaşmak ve y\xf6netmek, takım verimliliği i\xe7in kritik \xf6nem taşıyor.

## Takım Prompt Y\xf6netiminin \xd6nemi

### Yaygın Sorunlar
- Her takım \xfcyesi kendi prompt'larını yazıyor
- Etkili prompt'lar kaybolup gidiyor
- Tutarsız sonu\xe7lar alınıyor
- Zaman kaybı yaşanıyor

### \xc7\xf6z\xfcm: Merkezi Prompt K\xfct\xfcphanesi
PromptBir ile takımınız i\xe7in merkezi bir prompt k\xfct\xfcphanesi oluşturabilirsiniz.

## Takım Prompt Stratejileri

### 1. Rol Bazlı Prompt Organizasyonu

**Pazarlama Takımı:**
\`\`\`
"[\xdcR\xdcN_ADI] i\xe7in sosyal medya g\xf6nderisi oluştur:
- Platform: [PLATFORM]
- Hedef kitle: [HEDEF_KİTLE]
- Ton: [TON]
- Hashtag sayısı: [SAYI]
- CTA dahil et"
\`\`\`

**Geliştirici Takımı:**
\`\`\`
"[PROGRAMLAMA_DİLİ] ile [A\xc7IKLAMA] yapan kod yaz:
- Clean code prensipleri
- Error handling
- Unit test \xf6rnekleri
- Dok\xfcmantasyon"
\`\`\`

**Satış Takımı:**
\`\`\`
"[M\xdcŞTERİ_PROFİLİ] i\xe7in kişiselleştirilmiş satış e-postası:
- \xdcr\xfcn: [\xdcR\xdcN]
- M\xfcşteri ihtiyacı: [İHTİYA\xc7]
- Ton: profesyonel ama samimi
- CTA: demo talep et"
\`\`\`

### 2. Proje Bazlı Prompt Setleri

Her proje i\xe7in \xf6zel prompt koleksiyonları oluşturun:
- Proje başlangı\xe7 prompt'ları
- S\xfcre\xe7 y\xf6netimi prompt'ları
- Kalite kontrol prompt'ları
- Raporlama prompt'ları

### 3. Versiyon Kontrol\xfc

Prompt'larınızı versiyonlayın:
- v1.0: İlk versiyon
- v1.1: K\xfc\xe7\xfck iyileştirmeler
- v2.0: B\xfcy\xfck değişiklikler

## İşbirliği Best Practice'leri

### Prompt Paylaşım Kuralları

1. **A\xe7ık İsimlendirme**
   - "Email_Template_v2" ❌
   - "M\xfcşteri_Onboarding_Email_Şablonu_v2.1" ✅

2. **A\xe7ıklama Ekleme**
   - Prompt'un amacı
   - Kullanım senaryoları
   - Beklenen sonu\xe7lar

3. **Etiketleme Sistemi**
   - #pazarlama #email #onboarding
   - #geliştirme #python #api
   - #satış #demo #b2b

### Takım Eğitimi

**Haftalık Prompt Review:**
- En etkili prompt'ları paylaşın
- Yeni teknikleri tartışın
- Başarı hikayelerini anlatın

**Prompt Workshop'ları:**
- Yeni takım \xfcyeleri i\xe7in eğitim
- İleri seviye teknikler
- Sekt\xf6rel \xf6rnekler

## Kalite Kontrol S\xfcreci

### Prompt Değerlendirme Kriterleri

1. **Netlik** (1-5 puan)
   - Talimatlar a\xe7ık mı?
   - Beklentiler net mi?

2. **Tutarlılık** (1-5 puan)
   - Sonu\xe7lar tutarlı mı?
   - Farklı kullanıcılarda aynı sonu\xe7 mu?

3. **Verimlilik** (1-5 puan)
   - Zaman tasarrufu sağlıyor mu?
   - İş akışını hızlandırıyor mu?

### Geri Bildirim D\xf6ng\xfcs\xfc

1. Prompt kullanımı
2. Sonu\xe7 değerlendirmesi
3. Geri bildirim toplama
4. İyileştirme \xf6nerileri
5. Prompt g\xfcncelleme

## G\xfcvenlik ve Gizlilik

### Hassas Bilgi Y\xf6netimi

- M\xfcşteri bilgilerini prompt'larda kullanmayın
- Placeholder'lar kullanın: [M\xdcŞTERİ_ADI]
- Şirket sırlarını koruyun

### Erişim Kontrol\xfc

- Takım bazlı erişim yetkileri
- Rol bazlı prompt g\xf6r\xfcn\xfcrl\xfcğ\xfc
- D\xfczenleme yetkilerini sınırlayın

## Performans Metrikleri

### Takım Verimliliği KPI'ları

1. **Prompt Kullanım Oranı**
   - Takım \xfcyelerinin prompt kullanım sıklığı
   - En \xe7ok kullanılan prompt'lar

2. **Zaman Tasarrufu**
   - G\xf6rev tamamlama s\xfcreleri
   - Prompt \xf6ncesi vs sonrası karşılaştırma

3. **Kalite Artışı**
   - \xc7ıktı kalitesi değerlendirmeleri
   - M\xfcşteri memnuniyeti

### Raporlama

Aylık takım raporları:
- En etkili prompt'lar
- Kullanım istatistikleri
- İyileştirme \xf6nerileri
- Başarı hikayeleri

## Gelecek Planlaması

### Prompt K\xfct\xfcphanesi B\xfcy\xfctme

1. **S\xfcrekli İyileştirme**
   - Kullanıcı geri bildirimlerini toplayın
   - A/B test yapın
   - Yeni teknikleri deneyin

2. **Bilgi Paylaşımı**
   - Diğer takımlarla işbirliği
   - Sekt\xf6r best practice'lerini takip edin
   - Konferans ve eğitimlere katılın

3. **Teknoloji Entegrasyonu**
   - API entegrasyonları
   - Otomasyon ara\xe7ları
   - İş akışı optimizasyonu

## Sonu\xe7

Takım halinde AI prompt y\xf6netimi, modern iş d\xfcnyasında rekabet avantajı sağlar. PromptBir ile:

- Merkezi prompt k\xfct\xfcphanesi
- Etkili işbirliği
- Kalite kontrol\xfc
- S\xfcrekli iyileştirme

Takımınızın AI verimliliğini artırmak i\xe7in bug\xfcn başlayın!

---

*Elif Kaya, 10 yıllık deneyime sahip proje y\xf6neticisi ve takım liderliği uzmanıdır.*
    `},"ai-prompt-guvenlik":{id:4,title:"AI Prompt G\xfcvenliği: Veri Koruma ve Gizlilik Rehberi",slug:"ai-prompt-guvenlik",excerpt:"AI prompt'larınızı kullanırken veri g\xfcvenliğini nasıl sağlayacağınızı ve gizliliği nasıl koruyacağınızı \xf6ğrenin.",author:"Mehmet Demir",publishDate:"2023-12-28",readTime:"9 dakika",category:"G\xfcvenlik",tags:["G\xfcvenlik","Gizlilik","Veri Koruma"],featured:!1,content:`
# AI Prompt G\xfcvenliği: Veri Koruma ve Gizlilik Rehberi

AI ara\xe7larının yaygınlaşmasıyla birlikte, prompt g\xfcvenliği kritik bir konu haline geldi. Bu rehberde, AI prompt'larınızı kullanırken veri g\xfcvenliğini nasıl sağlayacağınızı \xf6ğreneceksiniz.

## G\xfcvenlik Riskleri

### Yaygın G\xfcvenlik Tehditleri

1. **Veri Sızıntısı**
   - Hassas bilgilerin prompt'larda kullanılması
   - M\xfcşteri verilerinin yanlışlıkla paylaşılması
   - Şirket sırlarının ifşa edilmesi

2. **Prompt Injection Saldırıları**
   - K\xf6t\xfc niyetli kullanıcıların sistem prompt'larını manip\xfcle etmesi
   - Yetkisiz erişim girişimleri
   - Sistem g\xfcvenliğinin aşılması

3. **Model Poisoning**
   - Zararlı verilerle model eğitiminin bozulması
   - \xd6nyargılı sonu\xe7ların \xfcretilmesi
   - Sistem g\xfcvenilirliğinin azalması

## G\xfcvenli Prompt Yazma Prensipleri

### 1. Veri Anonimleştirme

**Yanlış Yaklaşım:**
\`\`\`
"Ahmet Yılmaz (TC: 12345678901) i\xe7in kredi başvurusu değerlendir"
\`\`\`

**Doğru Yaklaşım:**
\`\`\`
"[M\xdcŞTERİ_ADI] i\xe7in kredi başvurusu değerlendir:
- Yaş: [YAŞ]
- Gelir: [GELİR]
- Kredi ge\xe7mişi: [KREDİ_SKORU]"
\`\`\`

### 2. Placeholder Kullanımı

Hassas bilgiler yerine placeholder'lar kullanın:
- [M\xdcŞTERİ_ADI] → Ger\xe7ek isim yerine
- [ŞIRKET_ADI] → Şirket adı yerine
- [PROJE_KODU] → Proje detayları yerine
- [FİNANSAL_VERİ] → Mali bilgiler yerine

### 3. Minimum Veri Prensibi

Sadece gerekli bilgileri paylaşın:
- G\xf6rev i\xe7in gerekli minimum veri
- Bağlam i\xe7in yeterli bilgi
- Gereksiz detaylardan ka\xe7ının

## Kurumsal G\xfcvenlik Politikaları

### Veri Sınıflandırması

**A\xe7ık Veri (Public)**
- Genel bilgiler
- Pazarlama materyalleri
- Halka a\xe7ık d\xf6k\xfcmanlar

**İ\xe7 Veri (Internal)**
- Şirket s\xfcre\xe7leri
- İ\xe7 iletişim
- Operasyonel bilgiler

**Gizli Veri (Confidential)**
- M\xfcşteri bilgileri
- Finansal veriler
- Stratejik planlar

**\xc7ok Gizli Veri (Restricted)**
- Ticari sırlar
- Kişisel veriler
- Yasal belgeler

### Erişim Kontrol\xfc

1. **Rol Bazlı Erişim**
   - Departman bazlı yetkilendirme
   - G\xf6rev bazlı sınırlamalar
   - Proje bazlı erişim

2. **İki Fakt\xf6rl\xfc Doğrulama**
   - SMS doğrulama
   - Authenticator uygulamaları
   - Biometrik doğrulama

3. **Oturum Y\xf6netimi**
   - Otomatik oturum sonlandırma
   - Eşzamanlı oturum sınırları
   - IP bazlı kısıtlamalar

## KVKK ve GDPR Uyumluluğu

### Kişisel Veri Koruma

**KVKK Gereklilikleri:**
- A\xe7ık rıza alınması
- Veri işleme amacının belirtilmesi
- Veri saklama s\xfcrelerinin belirlenmesi
- Veri g\xfcvenliği tedbirlerinin alınması

**GDPR Gereklilikleri:**
- Veri taşınabilirliği hakkı
- Unutulma hakkı
- Veri işleme faaliyetlerinin kayıt altına alınması
- Veri koruma etki değerlendirmesi

### Uygulama \xd6rnekleri

**M\xfcşteri Hizmetleri Prompt'u:**
\`\`\`
"M\xfcşteri şikayeti analizi yap:
- Şikayet kategorisi: [KATEGORİ]
- Aciliyet seviyesi: [ACİLİYET]
- \xc7\xf6z\xfcm \xf6nerisi sun
- M\xfcşteri memnuniyeti i\xe7in adımlar

Not: Kişisel bilgileri kullanma, sadece şikayet i\xe7eriğini analiz et."
\`\`\`

## Teknik G\xfcvenlik \xd6nlemleri

### Şifreleme

1. **Veri Şifreleme**
   - AES-256 şifreleme
   - End-to-end encryption
   - Database şifreleme

2. **İletişim Şifreleme**
   - HTTPS protokol\xfc
   - TLS 1.3 kullanımı
   - Certificate pinning

### G\xfcvenlik Denetimi

**D\xfczenli Denetimler:**
- Penetrasyon testleri
- G\xfcvenlik a\xe7ığı taramaları
- Kod g\xfcvenlik analizi
- Erişim log incelemesi

**Olay M\xfcdahale Planı:**
1. G\xfcvenlik ihlali tespiti
2. Etki analizi
3. M\xfcdahale ekibinin devreye girmesi
4. İyileştirme \xf6nlemlerinin alınması

## G\xfcvenli Prompt Şablonları

### Genel G\xfcvenlik Şablonu

\`\`\`
"[G\xd6REV_A\xc7IKLAMASI] i\xe7in analiz yap:

Giriş Verileri:
- [VERİ_TİPİ_1]: [PLACEHOLDER_1]
- [VERİ_TİPİ_2]: [PLACEHOLDER_2]

G\xfcvenlik Notları:
- Kişisel bilgileri kullanma
- Hassas verileri kaydetme
- Sadece analiz sonu\xe7larını d\xf6nd\xfcr

Beklenen \xc7ıktı:
- [\xc7IKTI_FORMAT]
- [\xc7IKTI_DETAY]"
\`\`\`

### Finansal Veri Analizi Şablonu

\`\`\`
"Finansal performans analizi:

Veriler (Anonimleştirilmiş):
- Gelir: [GELİR_ARALIĞI]
- Gider: [GİDER_KATEGORİSİ]
- D\xf6nem: [ZAMAN_ARALIĞI]

Analiz Kriterleri:
- Trend analizi
- Karşılaştırmalı analiz
- Risk değerlendirmesi

G\xfcvenlik: Ger\xe7ek rakamları kullanma, sadece oransal analiz yap."
\`\`\`

## Eğitim ve Farkındalık

### Personel Eğitimi

**Temel G\xfcvenlik Eğitimi:**
- G\xfcvenli prompt yazma
- Veri sınıflandırması
- Olay raporlama

**İleri Seviye Eğitim:**
- G\xfcvenlik testleri
- Saldırı sim\xfclasyonları
- Best practice workshop'ları

### S\xfcrekli İyileştirme

1. **G\xfcvenlik Metrikleri**
   - G\xfcvenlik ihlali sayısı
   - Eğitim tamamlama oranları
   - G\xfcvenlik denetim sonu\xe7ları

2. **Geri Bildirim D\xf6ng\xfcs\xfc**
   - Kullanıcı raporları
   - G\xfcvenlik ekibi değerlendirmeleri
   - S\xfcre\xe7 iyileştirmeleri

## Sonu\xe7

AI prompt g\xfcvenliği, modern iş d\xfcnyasında kritik \xf6nem taşır. Bu rehberdeki prensipleri uygulayarak:

- Veri g\xfcvenliğini sağlayabilir
- Yasal gerekliliklere uyum g\xf6sterebilir
- Kurumsal g\xfcveni koruyabilir
- Rekabet avantajı elde edebilirsiniz

G\xfcvenlik, s\xfcrekli bir s\xfcre\xe7tir. D\xfczenli eğitim, denetim ve iyileştirme ile g\xfcvenlik seviyenizi y\xfcksek tutun.

---

*Mehmet Demir, siber g\xfcvenlik alanında 12 yıllık deneyime sahip uzman ve sertifikalı etik hacker'dır.*
    `},"promptbir-api-rehber":{id:5,title:"PromptBir API: Geliştiriciler i\xe7in Kapsamlı Rehber",slug:"promptbir-api-rehber",excerpt:"PromptBir API'sini kullanarak kendi uygulamalarınızda prompt y\xf6netimi nasıl entegre edebileceğinizi \xf6ğrenin.",author:"PromptBir Geliştirici Ekibi",publishDate:"2023-12-20",readTime:"15 dakika",category:"Geliştirici",tags:["API","Geliştirici","Entegrasyon"],featured:!1,content:`
# PromptBir API: Geliştiriciler i\xe7in Kapsamlı Rehber

PromptBir API, geliştiricilerin kendi uygulamalarında prompt y\xf6netimi yapabilmelerini sağlayan g\xfc\xe7l\xfc bir RESTful API'dir. Bu rehberde, API'yi nasıl kullanacağınızı adım adım \xf6ğreneceksiniz.

## API'ye Başlangı\xe7

### Kimlik Doğrulama

PromptBir API, JWT token tabanlı kimlik doğrulama kullanır:

\`\`\`javascript
// API anahtarınızı alın
const API_KEY = 'your-api-key-here';
const BASE_URL = 'https://api.promptbir.com/v1';

// Headers
const headers = {
  'Authorization': \`Bearer \${API_KEY}\`,
  'Content-Type': 'application/json'
};
\`\`\`

### Rate Limiting

API kullanımında aşağıdaki limitler ge\xe7erlidir:
- **Free Plan**: 100 istek/saat
- **Pro Plan**: 1,000 istek/saat
- **Enterprise Plan**: 10,000 istek/saat

## Temel API Endpoint'leri

### 1. Projeler (Projects)

**T\xfcm Projeleri Listele**
\`\`\`javascript
const getProjects = async () => {
  const response = await fetch(\`\${BASE_URL}/projects\`, {
    method: 'GET',
    headers: headers
  });

  const projects = await response.json();
  return projects;
};
\`\`\`

**Yeni Proje Oluştur**
\`\`\`javascript
const createProject = async (projectData) => {
  const response = await fetch(\`\${BASE_URL}/projects\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      name: projectData.name,
      description: projectData.description,
      tags: projectData.tags
    })
  });

  const newProject = await response.json();
  return newProject;
};
\`\`\`

### 2. Prompt'lar (Prompts)

**Prompt'ları Listele**
\`\`\`javascript
const getPrompts = async (projectId, filters = {}) => {
  const queryParams = new URLSearchParams({
    project_id: projectId,
    ...filters
  });

  const response = await fetch(\`\${BASE_URL}/prompts?\${queryParams}\`, {
    method: 'GET',
    headers: headers
  });

  const prompts = await response.json();
  return prompts;
};
\`\`\`

**Yeni Prompt Oluştur**
\`\`\`javascript
const createPrompt = async (promptData) => {
  const response = await fetch(\`\${BASE_URL}/prompts\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      project_id: promptData.projectId,
      title: promptData.title,
      content: promptData.content,
      category: promptData.category,
      hashtags: promptData.hashtags
    })
  });

  const newPrompt = await response.json();
  return newPrompt;
};
\`\`\`

**Prompt G\xfcncelle**
\`\`\`javascript
const updatePrompt = async (promptId, updateData) => {
  const response = await fetch(\`\${BASE_URL}/prompts/\${promptId}\`, {
    method: 'PUT',
    headers: headers,
    body: JSON.stringify(updateData)
  });

  const updatedPrompt = await response.json();
  return updatedPrompt;
};
\`\`\`

### 3. Kategoriler (Categories)

**Kategorileri Listele**
\`\`\`javascript
const getCategories = async (projectId) => {
  const response = await fetch(\`\${BASE_URL}/categories?project_id=\${projectId}\`, {
    method: 'GET',
    headers: headers
  });

  const categories = await response.json();
  return categories;
};
\`\`\`

## İleri Seviye \xd6zellikler

### Toplu İşlemler (Batch Operations)

**Toplu Prompt Oluşturma**
\`\`\`javascript
const createBatchPrompts = async (prompts) => {
  const response = await fetch(\`\${BASE_URL}/prompts/batch\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      prompts: prompts
    })
  });

  const result = await response.json();
  return result;
};
\`\`\`

### Arama ve Filtreleme

**Gelişmiş Arama**
\`\`\`javascript
const searchPrompts = async (searchParams) => {
  const response = await fetch(\`\${BASE_URL}/prompts/search\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      query: searchParams.query,
      filters: {
        category: searchParams.category,
        hashtags: searchParams.hashtags,
        date_range: searchParams.dateRange
      },
      sort: searchParams.sort,
      limit: searchParams.limit,
      offset: searchParams.offset
    })
  });

  const searchResults = await response.json();
  return searchResults;
};
\`\`\`

### Webhook'lar

**Webhook Kurulumu**
\`\`\`javascript
const setupWebhook = async (webhookConfig) => {
  const response = await fetch(\`\${BASE_URL}/webhooks\`, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify({
      url: webhookConfig.url,
      events: webhookConfig.events, // ['prompt.created', 'prompt.updated', 'prompt.deleted']
      secret: webhookConfig.secret
    })
  });

  const webhook = await response.json();
  return webhook;
};
\`\`\`

## SDK'lar ve K\xfct\xfcphaneler

### JavaScript/Node.js SDK

\`\`\`bash
npm install @promptbir/sdk
\`\`\`

\`\`\`javascript
import { PromptBirClient } from '@promptbir/sdk';

const client = new PromptBirClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.promptbir.com/v1'
});

// Kullanım
const projects = await client.projects.list();
const prompts = await client.prompts.list(projectId);
\`\`\`

### Python SDK

\`\`\`bash
pip install promptbir-python
\`\`\`

\`\`\`python
from promptbir import PromptBirClient

client = PromptBirClient(api_key='your-api-key')

# Kullanım
projects = client.projects.list()
prompts = client.prompts.list(project_id=project_id)
\`\`\`

## Hata Y\xf6netimi

### HTTP Durum Kodları

- **200**: Başarılı
- **201**: Oluşturuldu
- **400**: Hatalı istek
- **401**: Yetkisiz erişim
- **403**: Yasak
- **404**: Bulunamadı
- **429**: Rate limit aşıldı
- **500**: Sunucu hatası

### Hata Yanıt Formatı

\`\`\`json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Gerekli alan eksik: title",
    "details": {
      "field": "title",
      "reason": "required"
    }
  }
}
\`\`\`

### Hata Y\xf6netimi \xd6rneği

\`\`\`javascript
const handleApiCall = async (apiFunction) => {
  try {
    const result = await apiFunction();
    return { success: true, data: result };
  } catch (error) {
    if (error.status === 429) {
      // Rate limit aşıldı, bekle ve tekrar dene
      await new Promise(resolve => setTimeout(resolve, 60000));
      return handleApiCall(apiFunction);
    } else if (error.status === 401) {
      // Token yenile
      await refreshToken();
      return handleApiCall(apiFunction);
    } else {
      return { success: false, error: error.message };
    }
  }
};
\`\`\`

## Performans Optimizasyonu

### \xd6nbellekleme (Caching)

\`\`\`javascript
class PromptBirCache {
  constructor() {
    this.cache = new Map();
    this.ttl = 5 * 60 * 1000; // 5 dakika
  }

  get(key) {
    const item = this.cache.get(key);
    if (item && Date.now() - item.timestamp < this.ttl) {
      return item.data;
    }
    this.cache.delete(key);
    return null;
  }

  set(key, data) {
    this.cache.set(key, {
      data: data,
      timestamp: Date.now()
    });
  }
}

const cache = new PromptBirCache();
\`\`\`

### Pagination

\`\`\`javascript
const getAllPrompts = async (projectId) => {
  let allPrompts = [];
  let offset = 0;
  const limit = 100;

  while (true) {
    const response = await getPrompts(projectId, { limit, offset });
    allPrompts = allPrompts.concat(response.data);

    if (response.data.length < limit) {
      break; // Son sayfa
    }

    offset += limit;
  }

  return allPrompts;
};
\`\`\`

## G\xfcvenlik Best Practice'leri

### API Anahtarı G\xfcvenliği

1. **\xc7evre Değişkenleri Kullanın**
\`\`\`javascript
const API_KEY = process.env.PROMPTBIR_API_KEY;
\`\`\`

2. **API Anahtarını Asla Frontend'de Kullanmayın**
\`\`\`javascript
// ❌ Yanlış - Frontend'de API anahtarı
const apiKey = 'pk_live_123456789';

// ✅ Doğru - Backend proxy kullanın
const response = await fetch('/api/prompts', {
  method: 'GET',
  headers: {
    'Authorization': \`Bearer \${userToken}\`
  }
});
\`\`\`

3. **HTTPS Kullanın**
T\xfcm API \xe7ağrılarında HTTPS protokol\xfcn\xfc kullanın.

## \xd6rnek Uygulamalar

### React Entegrasyonu

\`\`\`jsx
import React, { useState, useEffect } from 'react';
import { PromptBirClient } from '@promptbir/sdk';

const PromptList = ({ projectId }) => {
  const [prompts, setPrompts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPrompts = async () => {
      try {
        const client = new PromptBirClient({
          apiKey: process.env.REACT_APP_PROMPTBIR_API_KEY
        });

        const promptData = await client.prompts.list(projectId);
        setPrompts(promptData);
      } catch (error) {
        console.error('Prompt'lar y\xfcklenemedi:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPrompts();
  }, [projectId]);

  if (loading) return <div>Y\xfckleniyor...</div>;

  return (
    <div>
      {prompts.map(prompt => (
        <div key={prompt.id}>
          <h3>{prompt.title}</h3>
          <p>{prompt.content}</p>
        </div>
      ))}
    </div>
  );
};
\`\`\`

### Node.js Backend Entegrasyonu

\`\`\`javascript
const express = require('express');
const { PromptBirClient } = require('@promptbir/sdk');

const app = express();
const client = new PromptBirClient({
  apiKey: process.env.PROMPTBIR_API_KEY
});

app.get('/api/prompts/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const prompts = await client.prompts.list(projectId);
    res.json(prompts);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.listen(3000, () => {
  console.log('Server 3000 portunda \xe7alışıyor');
});
\`\`\`

## Sonu\xe7

PromptBir API, g\xfc\xe7l\xfc ve esnek bir prompt y\xf6netim \xe7\xf6z\xfcm\xfc sunar. Bu rehberdeki \xf6rnekleri kullanarak:

- Kendi uygulamalarınızda prompt y\xf6netimi
- Otomatik prompt senkronizasyonu
- Gelişmiş arama ve filtreleme
- G\xfcvenli API entegrasyonu

API dok\xfcmantasyonunun tamamına [api.promptbir.com](https://api.promptbir.com) adresinden ulaşabilirsiniz.

---

*PromptBir Geliştirici Ekibi, modern web teknolojileri ve API tasarımı konularında uzman geliştiricilerden oluşur.*
    `}};async function t({params:a}){let{slug:b}=await a,c=s[b];return c?{title:`${c.title} - PromptBir Blog`,description:c.excerpt,keywords:c.tags,openGraph:{title:c.title,description:c.excerpt,type:"article",publishedTime:c.publishDate,authors:[c.author],url:`https://promptbir.com/blog/${c.slug}`}}:{title:"Blog Yazısı Bulunamadı - PromptBir",description:"Aradığınız blog yazısı bulunamadı."}}async function u({params:a}){let{slug:b}=await a,c=s[b];c||(0,g.notFound)();let e=Object.values(s).filter(a=>a.id!==c.id&&a.tags.some(a=>c.tags.includes(a))).slice(0,2);return(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,d.jsx)("header",{className:"border-b border-gray-200 bg-white/80 backdrop-blur-sm",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)(f(),{href:"/blog",className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[(0,d.jsx)(k.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"Blog'a D\xf6n"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(l.A,{className:"h-8 w-8 text-blue-600"}),(0,d.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"PromptBir"})]})]})})}),(0,d.jsxs)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,d.jsxs)("article",{children:[(0,d.jsxs)("header",{className:"mb-12",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[c.featured&&(0,d.jsx)(i.E,{variant:"secondary",className:"bg-orange-100 text-orange-700",children:"\xd6ne \xc7ıkan"}),(0,d.jsx)(i.E,{variant:"outline",children:c.category})]}),(0,d.jsx)("h1",{className:"text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight",children:c.title}),(0,d.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:c.excerpt}),(0,d.jsxs)("div",{className:"flex flex-wrap items-center gap-6 text-gray-500 mb-8",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(m.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:c.author})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(n.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:new Date(c.publishDate).toLocaleDateString("tr-TR")})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(o.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:c.readTime})]}),(0,d.jsxs)(j.$,{variant:"outline",size:"sm",className:"ml-auto",children:[(0,d.jsx)(p,{className:"h-4 w-4 mr-2"}),"Paylaş"]})]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:c.tags.map((a,b)=>(0,d.jsx)(i.E,{variant:"secondary",children:a},b))})]}),(0,d.jsx)("div",{className:"prose prose-lg prose-gray max-w-none",children:(0,d.jsx)("div",{dangerouslySetInnerHTML:{__html:c.content.replace(/\n/g,"<br>").replace(/```([\s\S]*?)```/g,"<pre><code>$1</code></pre>")}})})]}),e.length>0&&(0,d.jsxs)("section",{className:"mt-16 pt-16 border-t border-gray-200",children:[(0,d.jsxs)("h2",{className:"text-3xl font-bold text-gray-900 mb-8 flex items-center gap-3",children:[(0,d.jsx)(q.A,{className:"h-8 w-8 text-blue-600"}),"İlgili Yazılar"]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 gap-6",children:e.map(a=>(0,d.jsxs)(h.Zp,{className:"shadow-lg hover:shadow-xl transition-shadow group",children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)("div",{className:"flex items-center gap-2 mb-3",children:(0,d.jsx)(i.E,{variant:"outline",children:a.category})}),(0,d.jsx)("h3",{className:"text-xl font-semibold group-hover:text-blue-600 transition-colors",children:(0,d.jsx)(f(),{href:`/blog/${a.slug}`,children:a.title})}),(0,d.jsx)("p",{className:"text-gray-600 leading-relaxed",children:a.excerpt})]}),(0,d.jsxs)(h.Wu,{children:[(0,d.jsx)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(n.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:new Date(a.publishDate).toLocaleDateString("tr-TR")})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(o.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:a.readTime})]})]})}),(0,d.jsxs)(f(),{href:`/blog/${a.slug}`,className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium group-hover:gap-3 transition-all",children:["Devamını Oku",(0,d.jsx)(r.A,{className:"h-4 w-4"})]})]})]},a.id))})]}),(0,d.jsx)("section",{className:"mt-16",children:(0,d.jsx)(h.Zp,{className:"shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white",children:(0,d.jsxs)(h.Wu,{className:"p-8 text-center",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Daha Fazla İ\xe7erik İ\xe7in Abone Olun"}),(0,d.jsx)("p",{className:"text-blue-100 mb-6",children:"AI ve prompt engineering konularındaki en g\xfcncel yazılarımızı ka\xe7ırmayın."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 max-w-md mx-auto",children:[(0,d.jsx)("input",{type:"email",placeholder:"E-posta adresiniz",className:"flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500"}),(0,d.jsx)(j.$,{className:"bg-white text-blue-600 hover:bg-blue-50",children:"Abone Ol"})]})]})})})]}),(0,d.jsx)("footer",{className:"bg-gray-900 text-white py-8 mt-16",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,d.jsx)("p",{className:"text-gray-400",children:"\xa9 2024 PromptBir. T\xfcm hakları saklıdır."})})})]})}},23469:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(37413);c(61120);var e=c(70403),f=c(50662),g=c(10974);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27821:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["blog",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,21401)),"C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\blog\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,39998)),"C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\OneDrive\\Masa\xfcst\xfc\\Promptbir\\promptflow\\src\\app\\blog\\[slug]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/blog/[slug]/page",pathname:"/blog/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/blog/[slug]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30084:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(37413);c(61120);var e=c(70403),f=c(50662),g=c(10974);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}},33873:a=>{"use strict";a.exports=require("path")},36440:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},40918:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48976:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"forbidden",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},50662:(a,b,c)=>{"use strict";c.d(b,{F:()=>g});var d=c(75986);let e=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,f=d.$,g=(a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return f(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:g,defaultVariants:h}=b,i=Object.keys(g).map(a=>{let b=null==c?void 0:c[a],d=null==h?void 0:h[a];if(null===b)return null;let f=e(b)||e(d);return g[a][f]}),j=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return f(a,i,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...h,...j}[b]):({...h,...j})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)}},53148:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},62765:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"notFound",{enumerable:!0,get:function(){return e}});let d=""+c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70403:(a,b,c)=>{"use strict";c.d(b,{DX:()=>g});var d=c(61120);function e(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}var f=c(37413),g=function(a){let b=function(a){let b=d.forwardRef((a,b)=>{let{children:c,...f}=a;if(d.isValidElement(c)){var g;let a,h,i=(g=c,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,c.props);return c.type!==d.Fragment&&(j.ref=b?function(...a){return b=>{let c=!1,d=a.map(a=>{let d=e(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():e(a[b],null)}}}}(b,i):i),d.cloneElement(c,j)}return d.Children.count(c)>1?d.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),c=d.forwardRef((a,c)=>{let{children:e,...g}=a,h=d.Children.toArray(e),j=h.find(i);if(j){let a=j.props.children,e=h.map(b=>b!==j?b:d.Children.count(a)>1?d.Children.only(null):d.isValidElement(a)?a.props.children:null);return(0,f.jsx)(b,{...g,ref:c,children:d.isValidElement(a)?d.cloneElement(a,void 0,e):null})}return(0,f.jsx)(b,{...g,ref:c,children:e})});return c.displayName=`${a}.Slot`,c}("Slot"),h=Symbol("radix.slottable");function i(a){return d.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===h}},70899:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unauthorized",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},71042:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=c(68388),e=c(52637),f=c(51846),g=c(31162),h=c(84971),i=c(98479);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},75234:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},78963:(a,b,c)=>{"use strict";c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(37413);c(61120);var e=c(10974);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86897:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=c(52836),e=c(49026),f=c(19121).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},88971:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},93526:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23))},97576:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_rethrow:function(){return i.unstable_rethrow}});let d=c(86897),e=c(49026),f=c(62765),g=c(48976),h=c(70899),i=c(163);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,904,5814,3978,1093],()=>b(b.s=27821));module.exports=c})();