import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Single Supabase client instance to prevent multiple GoTrueClient warnings
export const supabaseBrowser = createBrowserClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    debug: process.env.NODE_ENV === 'development',
    storageKey: 'sb-iqehopwgrczylqliajww-auth-token',
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
  },
  db: {
    schema: 'public',
  },
  realtime: {
    params: {
      eventsPerSecond: 10, // Rate limit realtime events
    },
  },
  global: {
    headers: {
      'X-Client-Info': 'promptflow-web',
      'X-Client-Version': '1.0.0',
    }
  }
})

// Session debug için
if (typeof window !== 'undefined') {
  supabaseBrowser.auth.onAuthStateChange((event, session) => {
    console.log(`🔐 [SUPABASE_BROWSER] Auth state change: ${event}`, {
      hasSession: !!session,
      userId: session?.user?.id,
      email: session?.user?.email,
      expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null
    })
    
    // Manually set cookies for server-side access
    if (session) {
      const maxAge = Math.round((session.expires_at! * 1000 - Date.now()) / 1000)
      document.cookie = `sb-iqehopwgrczylqliajww-auth-token=${JSON.stringify(session)}; path=/; max-age=${maxAge}; SameSite=Lax; secure=${location.protocol === 'https:'}`
      console.log(`🍪 [SUPABASE_BROWSER] Set auth cookie with maxAge: ${maxAge}s`)
    } else {
      document.cookie = 'sb-iqehopwgrczylqliajww-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
      console.log(`🍪 [SUPABASE_BROWSER] Cleared auth cookie`)
    }
  })
}
