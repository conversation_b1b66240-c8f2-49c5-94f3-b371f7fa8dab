"use strict";exports.id=6200,exports.ids=[6200],exports.modules={16391:(a,b,c)=>{c.d(b,{N:()=>d.L});var d=c(8266)},28559:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},34729:(a,b,c)=>{c.d(b,{T:()=>f});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("textarea",{"data-slot":"textarea",className:(0,e.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...b})}},40228:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41550:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},64398:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},86200:(a,b,c)=>{c.r(b),c.d(b,{default:()=>am});var d=c(60687),e=c(43210),f=c(29523),g=c(44493),h=c(89667),i=c(80013),j=c(96834),k=c(35950),l=c(77154),m=c(31568),n=c(97421),o=c(34248),p=c(16391),q=c(58869),r=c(92363),s=c(64398),t=c(99891),u=c(28559),v=c(40083),w=c(41550),x=c(40228),y=c(62688);let z=(0,y.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);var A=c(5336),B=c(93613);let C=(0,y.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var D=c(85814),E=c.n(D),F=c(63503),G=c(34729),H=c(98599),I=c(11273),J=c(70569),K=c(65551),L=c(83721),M=c(18853),N=c(46059),O=c(14163),P="Checkbox",[Q,R]=(0,I.A)(P),[S,T]=Q(P);function U(a){let{__scopeCheckbox:b,checked:c,children:f,defaultChecked:g,disabled:h,form:i,name:j,onCheckedChange:k,required:l,value:m="on",internal_do_not_use_render:n}=a,[o,p]=(0,K.i)({prop:c,defaultProp:g??!1,onChange:k,caller:P}),[q,r]=e.useState(null),[s,t]=e.useState(null),u=e.useRef(!1),v=!q||!!i||!!q.closest("form"),w={checked:o,disabled:h,setChecked:p,control:q,setControl:r,name:j,form:i,value:m,hasConsumerStoppedPropagationRef:u,required:l,defaultChecked:!aa(g)&&g,isFormControl:v,bubbleInput:s,setBubbleInput:t};return(0,d.jsx)(S,{scope:b,...w,children:"function"==typeof n?n(w):f})}var V="CheckboxTrigger",W=e.forwardRef(({__scopeCheckbox:a,onKeyDown:b,onClick:c,...f},g)=>{let{control:h,value:i,disabled:j,checked:k,required:l,setControl:m,setChecked:n,hasConsumerStoppedPropagationRef:o,isFormControl:p,bubbleInput:q}=T(V,a),r=(0,H.s)(g,m),s=e.useRef(k);return e.useEffect(()=>{let a=h?.form;if(a){let b=()=>n(s.current);return a.addEventListener("reset",b),()=>a.removeEventListener("reset",b)}},[h,n]),(0,d.jsx)(O.sG.button,{type:"button",role:"checkbox","aria-checked":aa(k)?"mixed":k,"aria-required":l,"data-state":ab(k),"data-disabled":j?"":void 0,disabled:j,value:i,...f,ref:r,onKeyDown:(0,J.m)(b,a=>{"Enter"===a.key&&a.preventDefault()}),onClick:(0,J.m)(c,a=>{n(a=>!!aa(a)||!a),q&&p&&(o.current=a.isPropagationStopped(),o.current||a.stopPropagation())})})});W.displayName=V;var X=e.forwardRef((a,b)=>{let{__scopeCheckbox:c,name:e,checked:f,defaultChecked:g,required:h,disabled:i,value:j,onCheckedChange:k,form:l,...m}=a;return(0,d.jsx)(U,{__scopeCheckbox:c,checked:f,defaultChecked:g,disabled:i,required:h,onCheckedChange:k,name:e,form:l,value:j,internal_do_not_use_render:({isFormControl:a})=>(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(W,{...m,ref:b,__scopeCheckbox:c}),a&&(0,d.jsx)(_,{__scopeCheckbox:c})]})})});X.displayName=P;var Y="CheckboxIndicator",Z=e.forwardRef((a,b)=>{let{__scopeCheckbox:c,forceMount:e,...f}=a,g=T(Y,c);return(0,d.jsx)(N.C,{present:e||aa(g.checked)||!0===g.checked,children:(0,d.jsx)(O.sG.span,{"data-state":ab(g.checked),"data-disabled":g.disabled?"":void 0,...f,ref:b,style:{pointerEvents:"none",...a.style}})})});Z.displayName=Y;var $="CheckboxBubbleInput",_=e.forwardRef(({__scopeCheckbox:a,...b},c)=>{let{control:f,hasConsumerStoppedPropagationRef:g,checked:h,defaultChecked:i,required:j,disabled:k,name:l,value:m,form:n,bubbleInput:o,setBubbleInput:p}=T($,a),q=(0,H.s)(c,p),r=(0,L.Z)(h),s=(0,M.X)(f);e.useEffect(()=>{if(!o)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,b=!g.current;if(r!==h&&a){let c=new Event("click",{bubbles:b});o.indeterminate=aa(h),a.call(o,!aa(h)&&h),o.dispatchEvent(c)}},[o,r,h,g]);let t=e.useRef(!aa(h)&&h);return(0,d.jsx)(O.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:i??t.current,required:j,disabled:k,name:l,value:m,form:n,...b,tabIndex:-1,ref:q,style:{...b.style,...s,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function aa(a){return"indeterminate"===a}function ab(a){return aa(a)?"indeterminate":a?"checked":"unchecked"}_.displayName=$;var ac=c(13964),ad=c(4780);function ae({className:a,...b}){return(0,d.jsx)(X,{"data-slot":"checkbox",className:(0,ad.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...b,children:(0,d.jsx)(Z,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,d.jsx)(ac.A,{className:"size-3.5"})})})}let af=(0,c(24224).F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function ag({className:a,variant:b,...c}){return(0,d.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,ad.cn)(af({variant:b}),a),...c})}function ah({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"alert-description",className:(0,ad.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",a),...b})}var ai=c(43649),aj=c(41862),ak=c(52581);function al({isOpen:a,onClose:b,onSuccess:c}){let[g,h]=(0,e.useState)(""),[j,k]=(0,e.useState)(!1),[l,m]=(0,e.useState)(!1),{data:n,isLoading:p}=(0,o.ov)(),{data:q,isLoading:r}=(0,o.P5)(),s=(0,o.pS)(),t=p||r||s.isPending,u=async()=>{if(!l)return void ak.oR.error("L\xfctfen iptal işlemini onaylayın");if(!g.trim())return void ak.oR.error("L\xfctfen iptal nedeninizi belirtin");try{let a=await s.mutateAsync({cancellationReason:g.trim(),requestRefund:j&&q?.is_in_trial});a?.success?(ak.oR.success(a.message||"Plan başarıyla iptal edildi"),c?.(),b(),h(""),k(!1),m(!1)):ak.oR.error(a?.message||"Plan iptal edilemedi")}catch(a){console.error("Plan cancellation error:",a),ak.oR.error("Plan iptal edilirken bir hata oluştu")}},v=()=>{s.isPending||(h(""),k(!1),m(!1),b())};return n?.can_cancel?(0,d.jsx)(F.lG,{open:a,onOpenChange:v,children:(0,d.jsxs)(F.Cf,{className:"sm:max-w-lg",children:[(0,d.jsxs)(F.c7,{children:[(0,d.jsxs)(F.L3,{className:"flex items-center gap-2",children:[(0,d.jsx)(ai.A,{className:"h-5 w-5 text-orange-500"}),"Plan İptal Et"]}),(0,d.jsxs)(F.rr,{children:[n?.plan_display_name," planınızı iptal etmek \xfczeresiniz. Bu işlem geri alınamaz."]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[q?.is_in_trial&&(0,d.jsxs)(ag,{children:[(0,d.jsx)(A.A,{className:"h-4 w-4"}),(0,d.jsxs)(ah,{children:["Deneme s\xfcreniz devam ediyor (",q.days_remaining," g\xfcn kaldı). İptal ederseniz tam iade alabilirsiniz."]})]}),n?.refund_eligible&&(0,d.jsxs)(ag,{children:[(0,d.jsx)(A.A,{className:"h-4 w-4"}),(0,d.jsxs)(ah,{children:["Deneme s\xfcresi i\xe7inde olduğunuz i\xe7in ",n.estimated_refund_amount," TL tam iade alabilirsiniz."]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(i.J,{htmlFor:"cancellation-reason",children:["İptal Nedeni ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)(G.T,{id:"cancellation-reason",placeholder:"Planınızı neden iptal etmek istiyorsunuz? Geri bildiriminiz bizim i\xe7in değerli.",value:g,onChange:a=>h(a.target.value),rows:3,maxLength:500,disabled:t}),(0,d.jsxs)("p",{className:"text-xs text-muted-foreground",children:[g.length,"/500 karakter"]})]}),n?.refund_eligible&&(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(ae,{id:"request-refund",checked:j,onCheckedChange:a=>k(a),disabled:t}),(0,d.jsxs)(i.J,{htmlFor:"request-refund",className:"text-sm",children:["İade talep ediyorum (",n.estimated_refund_amount," TL)"]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(ae,{id:"confirm-cancellation",checked:l,onCheckedChange:a=>m(a),disabled:t}),(0,d.jsx)(i.J,{htmlFor:"confirm-cancellation",className:"text-sm",children:"Plan iptal işlemini onaylıyorum ve bu işlemin geri alınamayacağını anlıyorum"})]}),(0,d.jsxs)(ag,{children:[(0,d.jsx)(ai.A,{className:"h-4 w-4"}),(0,d.jsx)(ah,{children:"Plan iptal edildikten sonra \xfccretsiz plana ge\xe7eceksiniz. Mevcut projeleriniz ve promptlarınız korunacak ancak plan limitleri ge\xe7erli olacak."})]})]}),(0,d.jsxs)(F.Es,{className:"gap-2",children:[(0,d.jsx)(f.$,{onClick:v,variant:"outline",disabled:t,children:"Vazge\xe7"}),(0,d.jsxs)(f.$,{onClick:u,variant:"destructive",disabled:t||!l||!g.trim(),children:[t&&(0,d.jsx)(aj.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Planı İptal Et"]})]})]})}):(0,d.jsx)(F.lG,{open:a,onOpenChange:v,children:(0,d.jsxs)(F.Cf,{className:"sm:max-w-md",children:[(0,d.jsxs)(F.c7,{children:[(0,d.jsxs)(F.L3,{className:"flex items-center gap-2",children:[(0,d.jsx)(C,{className:"h-5 w-5 text-red-500"}),"Plan İptal Edilemiyor"]}),(0,d.jsx)(F.rr,{children:n?.reason||"Bu plan iptal edilemez."})]}),(0,d.jsx)(F.Es,{children:(0,d.jsx)(f.$,{onClick:v,variant:"outline",children:"Kapat"})})]})})}function am(){let[a,b]=(0,e.useState)(""),[c,y]=(0,e.useState)(""),[D,F]=(0,e.useState)(!1),[G,H]=(0,e.useState)(""),[I,J]=(0,e.useState)(null),[K,L]=(0,e.useState)(!1),{data:M}=(0,m.Jd)(),{data:N=[]}=(0,n.YK)(),{data:O}=(0,o.EU)(),{data:P=[]}=(0,o.C)(),{data:Q}=(0,o.ov)(),{data:R}=(0,o.P5)(),S=(0,o.Jd)(),T=(0,m.Rt)(),U=async d=>{if(d.preventDefault(),H(""),J(null),a!==c){H("Yeni şifreler eşleşmiyor!"),J("error");return}if(a.length<6){H("Yeni şifre en az 6 karakter olmalı!"),J("error");return}F(!0);try{let{error:c}=await p.N.auth.updateUser({password:a});if(c)throw c;H("Şifre başarıyla g\xfcncellendi!"),J("success"),b(""),y("")}catch(a){H(a instanceof Error?a.message:"Şifre g\xfcncellenirken bir hata oluştu"),J("error")}finally{F(!1)}},V=async a=>{try{let b=P.find(b=>b.name===a);if(!b)throw Error("Ge\xe7ersiz plan se\xe7imi");if(O&&b.max_projects<O.max_projects&&N.length>b.max_projects)throw Error(`Bu plana ge\xe7mek i\xe7in \xf6nce proje sayınızı ${b.max_projects}'e d\xfcş\xfcrmeniz gerekiyor. Şu anda ${N.length} projeniz var.`);if(O&&b.price_monthly<O.price_monthly&&!window.confirm(`${b.display_name} planına ge\xe7mek istediğinizden emin misiniz? Bu işlem bazı \xf6zelliklerinizi kısıtlayabilir.`))return;await S.mutateAsync({planName:a,billingCycle:"monthly"}),H("Plan başarıyla değiştirildi!"),J("success"),setTimeout(()=>{H(""),J(null)},5e3)}catch(a){H(a instanceof Error?a.message:"Plan değiştirilemedi"),J("error"),setTimeout(()=>{H(""),J(null)},8e3)}},W=a=>{switch(a){case"free":return(0,d.jsx)(q.A,{className:"h-5 w-5"});case"professional":return(0,d.jsx)(r.A,{className:"h-5 w-5"});case"enterprise":return(0,d.jsx)(s.A,{className:"h-5 w-5"});default:return(0,d.jsx)(t.A,{className:"h-5 w-5"})}},X=a=>{switch(a){case"free":default:return"bg-gray-100 text-gray-800";case"professional":return"bg-blue-100 text-blue-800";case"enterprise":return"bg-purple-100 text-purple-800"}};return M?(0,d.jsxs)(l.q,{children:[(0,d.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)(E(),{href:"/",children:(0,d.jsxs)(f.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Ana Sayfaya D\xf6n"]})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Profil Ayarları"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Hesap bilgilerinizi y\xf6netin"})]})]}),(0,d.jsxs)(f.$,{variant:"outline",onClick:()=>{T.mutate()},className:"text-red-600 hover:text-red-700",children:[(0,d.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"\xc7ıkış Yap"]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(q.A,{className:"h-5 w-5"}),"Temel Bilgiler"]}),(0,d.jsx)(g.BT,{children:"Hesap bilgilerinizi g\xf6r\xfcnt\xfcleyin"})]}),(0,d.jsxs)(g.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{htmlFor:"email",children:"E-posta Adresi"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,d.jsx)(w.A,{className:"h-4 w-4 text-gray-400"}),(0,d.jsx)("span",{className:"text-gray-900",children:M.email})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{children:"Kayıt Tarihi"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,d.jsx)(x.A,{className:"h-4 w-4 text-gray-400"}),(0,d.jsx)("span",{className:"text-gray-900",children:new Date(M.created_at).toLocaleDateString("tr-TR")})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{children:"Hesap Durumu"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,d.jsx)(t.A,{className:"h-4 w-4 text-green-500"}),(0,d.jsx)(j.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:"Aktif"})]})]})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(z,{className:"h-5 w-5"}),"Şifre Değiştir"]}),(0,d.jsx)(g.BT,{children:"G\xfcvenliğiniz i\xe7in d\xfczenli olarak şifrenizi değiştirin"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("form",{onSubmit:U,className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{htmlFor:"newPassword",children:"Yeni Şifre"}),(0,d.jsx)(h.p,{id:"newPassword",type:"password",placeholder:"Yeni şifrenizi girin",value:a,onChange:a=>b(a.target.value),disabled:D,autoComplete:"new-password",required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{htmlFor:"confirmPassword",children:"Yeni Şifre (Tekrar)"}),(0,d.jsx)(h.p,{id:"confirmPassword",type:"password",placeholder:"Yeni şifrenizi tekrar girin",value:c,onChange:a=>y(a.target.value),disabled:D,autoComplete:"new-password",required:!0})]}),G&&(0,d.jsxs)("div",{className:`flex items-center gap-2 text-sm p-3 rounded-md ${"success"===I?"bg-green-50 text-green-700":"bg-red-50 text-red-700"}`,children:["success"===I?(0,d.jsx)(A.A,{className:"h-4 w-4"}):(0,d.jsx)(B.A,{className:"h-4 w-4"}),G]}),(0,d.jsx)(f.$,{type:"submit",disabled:D||!a||!c,className:"w-full",children:D?"G\xfcncelleniyor...":"Şifreyi G\xfcncelle"})]})})]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{className:"text-lg",children:"Hesap İstatistikleri"})}),(0,d.jsxs)(g.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Toplam Proje"}),(0,d.jsx)(j.E,{variant:"outline",children:N.length})]}),(0,d.jsx)(k.w,{}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Hesap Tipi"}),(0,d.jsx)(j.E,{variant:"secondary",className:X(O?.plan_name||"free"),children:O?.display_name||"\xdccretsiz"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Son Giriş"}),(0,d.jsx)("span",{className:"text-sm text-gray-900",children:new Date(M.last_sign_in_at||M.created_at).toLocaleDateString("tr-TR")})]})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(r.A,{className:"h-5 w-5 text-blue-600"}),"Plan Y\xf6netimi"]}),(0,d.jsx)(g.BT,{children:"Mevcut planınızı g\xf6r\xfcnt\xfcleyin ve değiştirin"})]}),(0,d.jsxs)(g.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[W(O?.plan_name||"free"),(0,d.jsx)("span",{className:"font-medium text-blue-900",children:O?.display_name||"\xdccretsiz Plan"})]}),(0,d.jsx)(j.E,{className:X(O?.plan_name||"free"),children:"Aktif"})]}),(0,d.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,d.jsxs)("div",{children:["• ",O?.max_projects||5," proje limiti"]}),(0,d.jsxs)("div",{children:["• ",O?.max_prompts_per_project||100," prompt/proje limiti"]}),O?.expires_at&&(0,d.jsxs)("div",{children:["• Bitiş: ",new Date(O.expires_at).toLocaleDateString("tr-TR")]})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(i.J,{className:"text-sm font-medium",children:"Mevcut Planlar"}),P.map(a=>(0,d.jsx)("div",{className:`p-3 border rounded-lg transition-all ${a.name===O?.plan_name?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[W(a.name),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-sm",children:a.display_name}),(0,d.jsxs)("div",{className:"text-xs text-gray-600",children:[-1===a.max_projects?"Sınırsız":a.max_projects," proje, "," ",-1===a.max_prompts_per_project?"Sınırsız":a.max_prompts_per_project," prompt/proje"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:0===a.price_monthly?"\xdccretsiz":`₺${a.price_monthly}/ay`}),a.name!==O?.plan_name&&(0,d.jsx)(f.$,{size:"sm",variant:"free"===a.name?"outline":"default",onClick:()=>V(a.name),disabled:S.isPending,className:"ml-2",children:S.isPending?"Değiştiriliyor...":"Se\xe7"})]})]})},a.id))]}),R?.is_in_trial&&(0,d.jsxs)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,d.jsx)(A.A,{className:"h-4 w-4 text-green-600"}),(0,d.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Deneme S\xfcresi Aktif"})]}),(0,d.jsxs)("p",{className:"text-xs text-green-700",children:[R.days_remaining," g\xfcn deneme s\xfcreniz kaldı. Bu s\xfcre i\xe7inde planınızı iptal ederseniz tam iade alabilirsiniz."]})]}),Q?.can_cancel&&O?.plan_name!=="free"&&(0,d.jsx)("div",{className:"pt-4 border-t border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Plan İptal"}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:R?.is_in_trial?"Deneme s\xfcresi i\xe7inde tam iade alabilirsiniz":"Planınızı istediğiniz zaman iptal edebilirsiniz"})]}),(0,d.jsxs)(f.$,{variant:"outline",size:"sm",onClick:()=>L(!0),className:"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300",children:[(0,d.jsx)(C,{className:"h-4 w-4 mr-1"}),"Planı İptal Et"]})]})}),G&&I&&(0,d.jsxs)("div",{className:`p-3 rounded-lg flex items-center gap-2 ${"success"===I?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"}`,children:["success"===I?(0,d.jsx)(A.A,{className:"h-4 w-4"}):(0,d.jsx)(B.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-sm",children:G})]})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{className:"text-lg",children:"G\xfcvenlik \xd6nerileri"})}),(0,d.jsxs)(g.Wu,{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)(t.A,{className:"h-4 w-4 text-blue-500 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"G\xfc\xe7l\xfc Şifre"}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"En az 8 karakter, b\xfcy\xfck/k\xfc\xe7\xfck harf ve rakam kullanın"})]})]}),(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)(t.A,{className:"h-4 w-4 text-blue-500 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"D\xfczenli G\xfcncelleme"}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:"Şifrenizi 3-6 ayda bir değiştirin"})]})]})]})]})]})]})]})}),(0,d.jsx)(al,{isOpen:K,onClose:()=>L(!1),onSuccess:()=>{H("Plan başarıyla iptal edildi. \xdccretsiz plana ge\xe7tiniz."),J("success")}})]}):null}},93613:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};