-- Performance Optimization Indexes for PromptFlow
-- These indexes improve query performance for common operations

-- ============================================================================
-- PROJECTS TABLE INDEXES
-- ============================================================================

-- Index for user's projects ordered by update time (most common query)
CREATE INDEX IF NOT EXISTS idx_projects_user_updated 
ON projects (user_id, updated_at DESC);

-- Index for project name searches (case-insensitive)
CREATE INDEX IF NOT EXISTS idx_projects_name_search 
ON projects USING gin (to_tsvector('english', name));

-- Index for project description searches
CREATE INDEX IF NOT EXISTS idx_projects_description_search 
ON projects USING gin (to_tsvector('english', description));

-- Composite index for user projects with name filtering
CREATE INDEX IF NOT EXISTS idx_projects_user_name 
ON projects (user_id, name);

-- ============================================================================
-- PROMPTS TABLE INDEXES
-- ============================================================================

-- Index for project prompts ordered by creation time
CREATE INDEX IF NOT EXISTS idx_prompts_project_created 
ON prompts (project_id, created_at DESC);

-- Index for user prompts across all projects
CREATE INDEX IF NOT EXISTS idx_prompts_user_created 
ON prompts (user_id, created_at DESC);

-- Index for prompt title searches (full-text search)
CREATE INDEX IF NOT EXISTS idx_prompts_title_search 
ON prompts USING gin (to_tsvector('english', title));

-- Index for prompt content searches (full-text search)
CREATE INDEX IF NOT EXISTS idx_prompts_content_search 
ON prompts USING gin (to_tsvector('english', content));

-- Index for hashtag searches (GIN index for array operations)
CREATE INDEX IF NOT EXISTS idx_prompts_hashtags 
ON prompts USING gin (hashtags);

-- Composite index for project prompts with hashtag filtering
CREATE INDEX IF NOT EXISTS idx_prompts_project_hashtags 
ON prompts (project_id) INCLUDE (hashtags);

-- Index for prompt updates (for real-time features)
CREATE INDEX IF NOT EXISTS idx_prompts_updated 
ON prompts (updated_at DESC);

-- ============================================================================
-- CONTEXT TEMPLATES TABLE INDEXES
-- ============================================================================

-- Index for public templates by category
CREATE INDEX IF NOT EXISTS idx_context_templates_public_category 
ON context_templates (is_public, category_id, created_at DESC) 
WHERE is_public = true AND status = 'approved';

-- Index for template searches
CREATE INDEX IF NOT EXISTS idx_context_templates_search 
ON context_templates USING gin (to_tsvector('english', title || ' ' || description));

-- Index for featured templates
CREATE INDEX IF NOT EXISTS idx_context_templates_featured 
ON context_templates (is_featured, created_at DESC) 
WHERE is_featured = true AND is_public = true;

-- ============================================================================
-- USER PLANS TABLE INDEXES
-- ============================================================================

-- Index for active user plans
CREATE INDEX IF NOT EXISTS idx_user_plans_active 
ON user_plans (user_id, is_active, created_at DESC) 
WHERE is_active = true;

-- Index for plan type statistics
CREATE INDEX IF NOT EXISTS idx_user_plans_type_stats 
ON user_plans (plan_type_id, is_active) 
WHERE is_active = true;

-- ============================================================================
-- USAGE STATS TABLE INDEXES
-- ============================================================================

-- Index for user usage tracking
CREATE INDEX IF NOT EXISTS idx_usage_stats_user_date 
ON usage_stats (user_id, date DESC);

-- Index for usage type aggregations
CREATE INDEX IF NOT EXISTS idx_usage_stats_type_date 
ON usage_stats (usage_type, date DESC);

-- ============================================================================
-- ADMIN USERS TABLE INDEXES
-- ============================================================================

-- Index for admin role lookups
CREATE INDEX IF NOT EXISTS idx_admin_users_role 
ON admin_users (user_id, role) 
WHERE role IN ('admin', 'super_admin');

-- ============================================================================
-- PERFORMANCE VIEWS
-- ============================================================================

-- View for project statistics (cached aggregation)
CREATE OR REPLACE VIEW project_stats AS
SELECT 
  p.id,
  p.name,
  p.user_id,
  p.created_at,
  p.updated_at,
  COUNT(pr.id) as prompt_count,
  MAX(pr.updated_at) as last_prompt_update
FROM projects p
LEFT JOIN prompts pr ON p.id = pr.project_id
GROUP BY p.id, p.name, p.user_id, p.created_at, p.updated_at;

-- View for user statistics (cached aggregation)
CREATE OR REPLACE VIEW user_stats AS
SELECT 
  u.id,
  u.email,
  u.created_at,
  COUNT(DISTINCT p.id) as project_count,
  COUNT(pr.id) as prompt_count,
  MAX(p.updated_at) as last_project_update,
  MAX(pr.updated_at) as last_prompt_update
FROM auth.users u
LEFT JOIN projects p ON u.id = p.user_id
LEFT JOIN prompts pr ON p.id = pr.project_id
GROUP BY u.id, u.email, u.created_at;

-- ============================================================================
-- MATERIALIZED VIEWS FOR HEAVY AGGREGATIONS
-- ============================================================================

-- Materialized view for dashboard statistics (refreshed periodically)
CREATE MATERIALIZED VIEW IF NOT EXISTS dashboard_stats AS
SELECT 
  COUNT(DISTINCT u.id) as total_users,
  COUNT(DISTINCT p.id) as total_projects,
  COUNT(pr.id) as total_prompts,
  COUNT(DISTINCT ct.id) as total_templates,
  AVG(project_counts.prompt_count) as avg_prompts_per_project
FROM auth.users u
LEFT JOIN projects p ON u.id = p.user_id
LEFT JOIN prompts pr ON p.id = pr.project_id
LEFT JOIN context_templates ct ON ct.is_public = true
LEFT JOIN (
  SELECT project_id, COUNT(*) as prompt_count
  FROM prompts
  GROUP BY project_id
) project_counts ON p.id = project_counts.project_id;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_dashboard_stats_unique 
ON dashboard_stats ((1));

-- ============================================================================
-- FUNCTIONS FOR PERFORMANCE
-- ============================================================================

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_performance_views()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW dashboard_stats;
END;
$$ LANGUAGE plpgsql;

-- Function to get user project count efficiently
CREATE OR REPLACE FUNCTION get_user_project_count(user_uuid uuid)
RETURNS integer AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::integer
    FROM projects
    WHERE user_id = user_uuid
  );
END;
$$ LANGUAGE plpgsql;

-- Function to get user prompt count efficiently
CREATE OR REPLACE FUNCTION get_user_prompt_count(user_uuid uuid)
RETURNS integer AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::integer
    FROM prompts pr
    JOIN projects p ON pr.project_id = p.id
    WHERE p.user_id = user_uuid
  );
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- PERFORMANCE MONITORING
-- ============================================================================

-- Table to track slow queries (for monitoring)
CREATE TABLE IF NOT EXISTS query_performance_log (
  id bigserial PRIMARY KEY,
  query_type text NOT NULL,
  execution_time_ms integer NOT NULL,
  query_params jsonb,
  created_at timestamp with time zone DEFAULT now()
);

-- Index for performance log queries
CREATE INDEX IF NOT EXISTS idx_query_performance_log_type_time 
ON query_performance_log (query_type, created_at DESC);

-- Function to log slow queries
CREATE OR REPLACE FUNCTION log_slow_query(
  p_query_type text,
  p_execution_time_ms integer,
  p_query_params jsonb DEFAULT NULL
)
RETURNS void AS $$
BEGIN
  -- Only log queries slower than 500ms
  IF p_execution_time_ms > 500 THEN
    INSERT INTO query_performance_log (query_type, execution_time_ms, query_params)
    VALUES (p_query_type, p_execution_time_ms, p_query_params);
  END IF;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- MAINTENANCE PROCEDURES
-- ============================================================================

-- Procedure to analyze table statistics (run weekly)
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS void AS $$
BEGIN
  ANALYZE projects;
  ANALYZE prompts;
  ANALYZE context_templates;
  ANALYZE user_plans;
  ANALYZE usage_stats;
  ANALYZE admin_users;
END;
$$ LANGUAGE plpgsql;

-- Procedure to clean old performance logs (run daily)
CREATE OR REPLACE FUNCTION cleanup_performance_logs()
RETURNS void AS $$
BEGIN
  DELETE FROM query_performance_log 
  WHERE created_at < now() - interval '30 days';
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON INDEX idx_projects_user_updated IS 'Optimizes user project listings ordered by update time';
COMMENT ON INDEX idx_prompts_project_created IS 'Optimizes project prompt listings ordered by creation time';
COMMENT ON INDEX idx_prompts_hashtags IS 'Enables fast hashtag-based prompt filtering';
COMMENT ON VIEW project_stats IS 'Cached aggregation of project statistics';
COMMENT ON VIEW user_stats IS 'Cached aggregation of user statistics';
COMMENT ON MATERIALIZED VIEW dashboard_stats IS 'Pre-computed dashboard statistics, refreshed periodically';
COMMENT ON FUNCTION refresh_performance_views IS 'Refreshes all materialized views for performance';
COMMENT ON FUNCTION log_slow_query IS 'Logs queries that exceed performance thresholds';

-- ============================================================================
-- EXECUTION NOTES
-- ============================================================================

-- To apply these optimizations:
-- 1. Run this script on your Supabase database
-- 2. Set up a cron job to refresh materialized views: SELECT cron.schedule('refresh-stats', '0 */6 * * *', 'SELECT refresh_performance_views();');
-- 3. Set up daily cleanup: SELECT cron.schedule('cleanup-logs', '0 2 * * *', 'SELECT cleanup_performance_logs();');
-- 4. Monitor query performance using the query_performance_log table
