"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8611],{4884:(e,t,r)=>{r.d(t,{bL:()=>E,zi:()=>S});var n=r(12115),o=r(85185),l=r(6101),i=r(46081),a=r(5845),s=r(45503),u=r(11275),d=r(63655),c=r(95155),f="Switch",[p,v]=(0,i.A)(f),[h,m]=p(f),w=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:s,defaultChecked:u,required:p,disabled:v,value:m="on",onCheckedChange:w,form:g,...y}=e,[E,S]=n.useState(null),C=(0,l.s)(t,e=>S(e)),R=n.useRef(!1),j=!E||g||!!E.closest("form"),[N,T]=(0,a.i)({prop:s,defaultProp:null!=u&&u,onChange:w,caller:f});return(0,c.jsxs)(h,{scope:r,checked:N,disabled:v,children:[(0,c.jsx)(d.sG.button,{type:"button",role:"switch","aria-checked":N,"aria-required":p,"data-state":x(N),"data-disabled":v?"":void 0,disabled:v,value:m,...y,ref:C,onClick:(0,o.m)(e.onClick,e=>{T(e=>!e),j&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),j&&(0,c.jsx)(b,{control:E,bubbles:!R.current,name:i,value:m,checked:N,required:p,disabled:v,form:g,style:{transform:"translateX(-100%)"}})]})});w.displayName=f;var g="SwitchThumb",y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=m(g,r);return(0,c.jsx)(d.sG.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});y.displayName=g;var b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:o,checked:i,bubbles:a=!0,...d}=e,f=n.useRef(null),p=(0,l.s)(f,t),v=(0,s.Z)(i),h=(0,u.X)(o);return n.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==i&&t){let r=new Event("click",{bubbles:a});t.call(e,i),e.dispatchEvent(r)}},[v,i,a]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...d,tabIndex:-1,ref:p,style:{...d.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var E=w,S=y},5845:(e,t,r)=>{r.d(t,{i:()=>a});var n,o=r(12115),l=r(52712),i=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||l.N;function a({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[l,a,s]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),l=o.useRef(r),a=o.useRef(t);return i(()=>{a.current=t},[t]),o.useEffect(()=>{l.current!==r&&(a.current?.(r),l.current=r)},[r,l]),[r,n,a]}({defaultProp:t,onChange:r}),u=void 0!==e,d=u?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,n])}return[d,o.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&s.current?.(r)}else a(t)},[u,e,a,s])]}Symbol("RADIX:SYNC_STATE")},6101:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>l});var n=r(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(l(...e),e)}},11275:(e,t,r)=>{r.d(t,{X:()=>l});var n=r(12115),o=r(52712);function l(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},15452:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>el,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(12115),o=r(85185),l=r(6101),i=r(46081),a=r(61285),s=r(5845),u=r(19178),d=r(25519),c=r(34378),f=r(28905),p=r(63655),v=r(92293),h=r(37768),m=r(38168),w=r(99708),g=r(95155),y="Dialog",[b,x]=(0,i.A)(y),[E,S]=b(y),C=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:l,onOpenChange:i,modal:u=!0}=e,d=n.useRef(null),c=n.useRef(null),[f,p]=(0,s.i)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:y});return(0,g.jsx)(E,{scope:t,triggerRef:d,contentRef:c,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:u,children:r})};C.displayName=y;var R="DialogTrigger",j=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=S(R,r),a=(0,l.s)(t,i.triggerRef);return(0,g.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":V(i.open),...n,ref:a,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});j.displayName=R;var N="DialogPortal",[T,P]=b(N,{forceMount:void 0}),A=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:l}=e,i=S(N,t);return(0,g.jsx)(T,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,g.jsx)(f.C,{present:r||i.open,children:(0,g.jsx)(c.Z,{asChild:!0,container:l,children:e})}))})};A.displayName=N;var D="DialogOverlay",L=n.forwardRef((e,t)=>{let r=P(D,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,l=S(D,e.__scopeDialog);return l.modal?(0,g.jsx)(f.C,{present:n||l.open,children:(0,g.jsx)(I,{...o,ref:t})}):null});L.displayName=D;var k=(0,w.TL)("DialogOverlay.RemoveScroll"),I=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=S(D,r);return(0,g.jsx)(h.A,{as:k,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(p.sG.div,{"data-state":V(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),O="DialogContent",_=n.forwardRef((e,t)=>{let r=P(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,l=S(O,e.__scopeDialog);return(0,g.jsx)(f.C,{present:n||l.open,children:l.modal?(0,g.jsx)(M,{...o,ref:t}):(0,g.jsx)(F,{...o,ref:t})})});_.displayName=O;var M=n.forwardRef((e,t)=>{let r=S(O,e.__scopeDialog),i=n.useRef(null),a=(0,l.s)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,m.Eq)(e)},[]),(0,g.jsx)(G,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=n.forwardRef((e,t)=>{let r=S(O,e.__scopeDialog),o=n.useRef(!1),l=n.useRef(!1);return(0,g.jsx)(G,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(i=r.triggerRef.current)||i.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var n,i;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let a=t.target;(null==(i=r.triggerRef.current)?void 0:i.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),G=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...s}=e,c=S(O,r),f=n.useRef(null),p=(0,l.s)(t,f);return(0,v.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,g.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":V(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)($,{titleId:c.titleId}),(0,g.jsx)(Z,{contentRef:f,descriptionId:c.descriptionId})]})]})}),H="DialogTitle",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=S(H,r);return(0,g.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});W.displayName=H;var z="DialogDescription",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=S(z,r);return(0,g.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});B.displayName=z;var U="DialogClose",K=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=S(U,r);return(0,g.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function V(e){return e?"open":"closed"}K.displayName=U;var q="DialogTitleWarning",[X,Y]=(0,i.q)(q,{contentName:O,titleName:H,docsSlug:"dialog"}),$=e=>{let{titleId:t}=e,r=Y(q),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},Z=e=>{let{contentRef:t,descriptionId:r}=e,o=Y("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(l))},[l,t,r]),null},J=C,Q=j,ee=A,et=L,er=_,en=W,eo=B,el=K},19178:(e,t,r)=>{r.d(t,{qW:()=>f});var n,o=r(12115),l=r(85185),i=r(63655),a=r(6101),s=r(39033),u=r(95155),d="dismissableLayer.update",c=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var r,f;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:m,onPointerDownOutside:w,onFocusOutside:g,onInteractOutside:y,onDismiss:b,...x}=e,E=o.useContext(c),[S,C]=o.useState(null),R=null!=(f=null==S?void 0:S.ownerDocument)?f:null==(r=globalThis)?void 0:r.document,[,j]=o.useState({}),N=(0,a.s)(t,e=>C(e)),T=Array.from(E.layers),[P]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),A=T.indexOf(P),D=S?T.indexOf(S):-1,L=E.layersWithOutsidePointerEventsDisabled.size>0,k=D>=A,I=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,s.c)(e),l=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!l.current){let t=function(){v("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",i.current),i.current=t,r.addEventListener("click",i.current,{once:!0})):t()}else r.removeEventListener("click",i.current);l.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",i.current)}},[r,n]),{onPointerDownCapture:()=>l.current=!0}}(e=>{let t=e.target,r=[...E.branches].some(e=>e.contains(t));k&&!r&&(null==w||w(e),null==y||y(e),e.defaultPrevented||null==b||b())},R),O=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,s.c)(e),l=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!l.current&&v("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==g||g(e),null==y||y(e),e.defaultPrevented||null==b||b())},R);return!function(e,t=globalThis?.document){let r=(0,s.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{D===E.layers.size-1&&(null==m||m(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},R),o.useEffect(()=>{if(S)return h&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(n=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),p(),()=>{h&&1===E.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=n)}},[S,R,h,E]),o.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),p())},[S,E]),o.useEffect(()=>{let e=()=>j({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,u.jsx)(i.sG.div,{...x,ref:N,style:{pointerEvents:L?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,l.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,l.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,l.m)(e.onPointerDownCapture,I.onPointerDownCapture)})});function p(){let e=new CustomEvent(d);document.dispatchEvent(e)}function v(e,t,r,n){let{discrete:o}=n,l=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&l.addEventListener(e,t,{once:!0}),o?(0,i.hO)(l,a):l.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(c),n=o.useRef(null),l=(0,a.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(i.sG.div,{...e,ref:l})}).displayName="DismissableLayerBranch"},25519:(e,t,r)=>{r.d(t,{n:()=>c});var n=r(12115),o=r(6101),l=r(63655),i=r(39033),a=r(95155),s="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",d={bubbles:!1,cancelable:!0},c=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:c=!1,onMountAutoFocus:m,onUnmountAutoFocus:w,...g}=e,[y,b]=n.useState(null),x=(0,i.c)(m),E=(0,i.c)(w),S=n.useRef(null),C=(0,o.s)(t,e=>b(e)),R=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(c){let e=function(e){if(R.paused||!y)return;let t=e.target;y.contains(t)?S.current=t:v(S.current,{select:!0})},t=function(e){if(R.paused||!y)return;let t=e.relatedTarget;null!==t&&(y.contains(t)||v(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(y)});return y&&r.observe(y,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[c,y,R.paused]),n.useEffect(()=>{if(y){h.add(R);let e=document.activeElement;if(!y.contains(e)){let t=new CustomEvent(s,d);y.addEventListener(s,x),y.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(v(n,{select:t}),document.activeElement!==r)return}(f(y).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(y))}return()=>{y.removeEventListener(s,x),setTimeout(()=>{let t=new CustomEvent(u,d);y.addEventListener(u,E),y.dispatchEvent(t),t.defaultPrevented||v(null!=e?e:document.body,{select:!0}),y.removeEventListener(u,E),h.remove(R)},0)}}},[y,x,E,R]);let j=n.useCallback(e=>{if(!r&&!c||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,l]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&l?e.shiftKey||n!==l?e.shiftKey&&n===o&&(e.preventDefault(),r&&v(l,{select:!0})):(e.preventDefault(),r&&v(o,{select:!0})):n===t&&e.preventDefault()}},[r,c,R.paused]);return(0,a.jsx)(l.sG.div,{tabIndex:-1,...g,ref:C,onKeyDown:j})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function v(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}c.displayName="FocusScope";var h=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=m(e,t)).unshift(t)},remove(t){var r;null==(r=(e=m(e,t))[0])||r.resume()}}}();function m(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},28905:(e,t,r)=>{r.d(t,{C:()=>i});var n=r(12115),o=r(6101),l=r(52712),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),s=n.useRef(null),u=n.useRef(e),d=n.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=a(s.current);d.current="mounted"===c?e:"none"},[c]),(0,l.N)(()=>{let t=s.current,r=u.current;if(r!==e){let n=d.current,o=a(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,l.N)(()=>{if(o){var e;let t,r=null!=(e=o.ownerDocument.defaultView)?e:window,n=e=>{let n=a(s.current).includes(e.animationName);if(e.target===o&&n&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(d.current=a(s.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{s.current=e?getComputedStyle(e):null,i(e)},[])}}(t),s="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),u=(0,o.s)(i.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof r||i.isPresent?n.cloneElement(s,{ref:u}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},34378:(e,t,r)=>{r.d(t,{Z:()=>s});var n=r(12115),o=r(47650),l=r(63655),i=r(52712),a=r(95155),s=n.forwardRef((e,t)=>{var r,s;let{container:u,...d}=e,[c,f]=n.useState(!1);(0,i.N)(()=>f(!0),[]);let p=u||c&&(null==(s=globalThis)||null==(r=s.document)?void 0:r.body);return p?o.createPortal((0,a.jsx)(l.sG.div,{...d,ref:t}),p):null});s.displayName="Portal"},36170:(e,t,r)=>{r.d(t,{UC:()=>eQ,In:()=>eZ,q7:()=>e1,VF:()=>e2,p4:()=>e5,ZL:()=>eJ,bL:()=>eX,wn:()=>e6,PP:()=>e8,l9:()=>eY,WT:()=>e$,LM:()=>e0});var n=r(12115),o=r(47650),l=r(89367),i=r(85185),a=r(82284),s=r(6101),u=r(46081),d=r(94315),c=r(19178),f=r(92293),p=r(25519),v=r(61285),h=r(84945),m=r(41093),w=r(63655),g=r(95155),y=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...l}=e;return(0,g.jsx)(w.sG.svg,{...l,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,g.jsx)("polygon",{points:"0,0 30,0 15,10"})})});y.displayName="Arrow";var b=r(39033),x=r(52712),E=r(11275),S="Popper",[C,R]=(0,u.A)(S),[j,N]=C(S),T=e=>{let{__scopePopper:t,children:r}=e,[o,l]=n.useState(null);return(0,g.jsx)(j,{scope:t,anchor:o,onAnchorChange:l,children:r})};T.displayName=S;var P="PopperAnchor",A=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...l}=e,i=N(P,r),a=n.useRef(null),u=(0,s.s)(t,a);return n.useEffect(()=>{i.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,g.jsx)(w.sG.div,{...l,ref:u})});A.displayName=P;var D="PopperContent",[L,k]=C(D),I=n.forwardRef((e,t)=>{var r,o,l,i,a,u,d,c;let{__scopePopper:f,side:p="bottom",sideOffset:v=0,align:y="center",alignOffset:S=0,arrowPadding:C=0,avoidCollisions:R=!0,collisionBoundary:j=[],collisionPadding:T=0,sticky:P="partial",hideWhenDetached:A=!1,updatePositionStrategy:k="optimized",onPlaced:I,...O}=e,_=N(D,f),[M,W]=n.useState(null),z=(0,s.s)(t,e=>W(e)),[B,U]=n.useState(null),K=(0,E.X)(B),V=null!=(d=null==K?void 0:K.width)?d:0,q=null!=(c=null==K?void 0:K.height)?c:0,X="number"==typeof T?T:{top:0,right:0,bottom:0,left:0,...T},Y=Array.isArray(j)?j:[j],$=Y.length>0,Z={padding:X,boundary:Y.filter(F),altBoundary:$},{refs:J,floatingStyles:Q,placement:ee,isPositioned:et,middlewareData:er}=(0,h.we)({strategy:"fixed",placement:p+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,m.ll)(...t,{animationFrame:"always"===k})},elements:{reference:_.anchor},middleware:[(0,h.cY)({mainAxis:v+q,alignmentAxis:S}),R&&(0,h.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===P?(0,h.ER)():void 0,...Z}),R&&(0,h.UU)({...Z}),(0,h.Ej)({...Z,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:l,height:i}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),B&&(0,h.UE)({element:B,padding:C}),G({arrowWidth:V,arrowHeight:q}),A&&(0,h.jD)({strategy:"referenceHidden",...Z})]}),[en,eo]=H(ee),el=(0,b.c)(I);(0,x.N)(()=>{et&&(null==el||el())},[et,el]);let ei=null==(r=er.arrow)?void 0:r.x,ea=null==(o=er.arrow)?void 0:o.y,es=(null==(l=er.arrow)?void 0:l.centerOffset)!==0,[eu,ed]=n.useState();return(0,x.N)(()=>{M&&ed(window.getComputedStyle(M).zIndex)},[M]),(0,g.jsx)("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...Q,transform:et?Q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eu,"--radix-popper-transform-origin":[null==(i=er.transformOrigin)?void 0:i.x,null==(a=er.transformOrigin)?void 0:a.y].join(" "),...(null==(u=er.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,g.jsx)(L,{scope:f,placedSide:en,onArrowChange:U,arrowX:ei,arrowY:ea,shouldHideArrow:es,children:(0,g.jsx)(w.sG.div,{"data-side":en,"data-align":eo,...O,ref:z,style:{...O.style,animation:et?void 0:"none"}})})})});I.displayName=D;var O="PopperArrow",_={top:"bottom",right:"left",bottom:"top",left:"right"},M=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=k(O,r),l=_[o.placedSide];return(0,g.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,g.jsx)(y,{...n,ref:t,style:{...n.style,display:"block"}})})});function F(e){return null!==e}M.displayName=O;var G=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,l,i;let{placement:a,rects:s,middlewareData:u}=t,d=(null==(r=u.arrow)?void 0:r.centerOffset)!==0,c=d?0:e.arrowWidth,f=d?0:e.arrowHeight,[p,v]=H(a),h={start:"0%",center:"50%",end:"100%"}[v],m=(null!=(l=null==(n=u.arrow)?void 0:n.x)?l:0)+c/2,w=(null!=(i=null==(o=u.arrow)?void 0:o.y)?i:0)+f/2,g="",y="";return"bottom"===p?(g=d?h:"".concat(m,"px"),y="".concat(-f,"px")):"top"===p?(g=d?h:"".concat(m,"px"),y="".concat(s.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),y=d?h:"".concat(w,"px")):"left"===p&&(g="".concat(s.floating.width+f,"px"),y=d?h:"".concat(w,"px")),{data:{x:g,y}}}});function H(e){let[t,r="center"]=e.split("-");return[t,r]}var W=r(34378),z=r(99708),B=r(5845),U=r(45503),K=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,g.jsx)(w.sG.span,{...e,ref:t,style:{...K,...e.style}})).displayName="VisuallyHidden";var V=r(38168),q=r(37768),X=[" ","Enter","ArrowUp","ArrowDown"],Y=[" ","Enter"],$="Select",[Z,J,Q]=(0,a.N)($),[ee,et]=(0,u.A)($,[Q,R]),er=R(),[en,eo]=ee($),[el,ei]=ee($),ea=e=>{let{__scopeSelect:t,children:r,open:o,defaultOpen:l,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:c,name:f,autoComplete:p,disabled:h,required:m,form:w}=e,y=er(t),[b,x]=n.useState(null),[E,S]=n.useState(null),[C,R]=n.useState(!1),j=(0,d.jH)(c),[N,P]=(0,B.i)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:$}),[A,D]=(0,B.i)({prop:a,defaultProp:s,onChange:u,caller:$}),L=n.useRef(null),k=!b||w||!!b.closest("form"),[I,O]=n.useState(new Set),_=Array.from(I).map(e=>e.props.value).join(";");return(0,g.jsx)(T,{...y,children:(0,g.jsxs)(en,{required:m,scope:t,trigger:b,onTriggerChange:x,valueNode:E,onValueNodeChange:S,valueNodeHasChildren:C,onValueNodeHasChildrenChange:R,contentId:(0,v.B)(),value:A,onValueChange:D,open:N,onOpenChange:P,dir:j,triggerPointerDownPosRef:L,disabled:h,children:[(0,g.jsx)(Z.Provider,{scope:t,children:(0,g.jsx)(el,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{O(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),k?(0,g.jsxs)(eU,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:A,onChange:e=>D(e.target.value),disabled:h,form:w,children:[void 0===A?(0,g.jsx)("option",{value:""}):null,Array.from(I)]},_):null]})})};ea.displayName=$;var es="SelectTrigger",eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:o=!1,...l}=e,a=er(r),u=eo(es,r),d=u.disabled||o,c=(0,s.s)(t,u.onTriggerChange),f=J(r),p=n.useRef("touch"),[v,h,m]=eV(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eq(t,e,r);void 0!==n&&u.onValueChange(n.value)}),y=e=>{d||(u.onOpenChange(!0),m()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,g.jsx)(A,{asChild:!0,...a,children:(0,g.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eK(u.value)?"":void 0,...l,ref:c,onClick:(0,i.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&y(e)}),onPointerDown:(0,i.m)(l.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,i.m)(l.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&X.includes(e.key)&&(y(),e.preventDefault())})})})});eu.displayName=es;var ed="SelectValue",ec=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:l,placeholder:i="",...a}=e,u=eo(ed,r),{onValueNodeHasChildrenChange:d}=u,c=void 0!==l,f=(0,s.s)(t,u.onValueNodeChange);return(0,x.N)(()=>{d(c)},[d,c]),(0,g.jsx)(w.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:eK(u.value)?(0,g.jsx)(g.Fragment,{children:i}):l})});ec.displayName=ed;var ef=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,g.jsx)(w.sG.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});ef.displayName="SelectIcon";var ep=e=>(0,g.jsx)(W.Z,{asChild:!0,...e});ep.displayName="SelectPortal";var ev="SelectContent",eh=n.forwardRef((e,t)=>{let r=eo(ev,e.__scopeSelect),[l,i]=n.useState();return((0,x.N)(()=>{i(new DocumentFragment)},[]),r.open)?(0,g.jsx)(ey,{...e,ref:t}):l?o.createPortal((0,g.jsx)(em,{scope:e.__scopeSelect,children:(0,g.jsx)(Z.Slot,{scope:e.__scopeSelect,children:(0,g.jsx)("div",{children:e.children})})}),l):null});eh.displayName=ev;var[em,ew]=ee(ev),eg=(0,z.TL)("SelectContent.RemoveScroll"),ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:a,onPointerDownOutside:u,side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:y,collisionPadding:b,sticky:x,hideWhenDetached:E,avoidCollisions:S,...C}=e,R=eo(ev,r),[j,N]=n.useState(null),[T,P]=n.useState(null),A=(0,s.s)(t,e=>N(e)),[D,L]=n.useState(null),[k,I]=n.useState(null),O=J(r),[_,M]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(j)return(0,V.Eq)(j)},[j]),(0,f.Oh)();let G=n.useCallback(e=>{let[t,...r]=O().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&T&&(T.scrollTop=0),r===n&&T&&(T.scrollTop=T.scrollHeight),null==r||r.focus(),document.activeElement!==o))return},[O,T]),H=n.useCallback(()=>G([D,j]),[G,D,j]);n.useEffect(()=>{_&&H()},[_,H]);let{onOpenChange:W,triggerPointerDownPosRef:z}=R;n.useEffect(()=>{if(j){let e={x:0,y:0},t=t=>{var r,n,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(r=z.current)?void 0:r.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(l=null==(n=z.current)?void 0:n.y)?l:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():j.contains(r.target)||W(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[j,W,z]),n.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[B,U]=eV(e=>{let t=O().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eq(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),K=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==R.value&&R.value===t||n)&&(L(e),n&&(F.current=!0))},[R.value]),X=n.useCallback(()=>null==j?void 0:j.focus(),[j]),Y=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==R.value&&R.value===t||n)&&I(e)},[R.value]),$="popper"===o?ex:eb,Z=$===ex?{side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:y,collisionPadding:b,sticky:x,hideWhenDetached:E,avoidCollisions:S}:{};return(0,g.jsx)(em,{scope:r,content:j,viewport:T,onViewportChange:P,itemRefCallback:K,selectedItem:D,onItemLeave:X,itemTextRefCallback:Y,focusSelectedItem:H,selectedItemText:k,position:o,isPositioned:_,searchRef:B,children:(0,g.jsx)(q.A,{as:eg,allowPinchZoom:!0,children:(0,g.jsx)(p.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.m)(l,e=>{var t;null==(t=R.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,g.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,g.jsx)($,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...C,...Z,onPlaced:()=>M(!0),ref:A,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,i.m)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||U(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>G(t)),e.preventDefault()}})})})})})})});ey.displayName="SelectContentImpl";var eb=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:o,...i}=e,a=eo(ev,r),u=ew(ev,r),[d,c]=n.useState(null),[f,p]=n.useState(null),v=(0,s.s)(t,e=>p(e)),h=J(r),m=n.useRef(!1),y=n.useRef(!0),{viewport:b,selectedItem:E,selectedItemText:S,focusSelectedItem:C}=u,R=n.useCallback(()=>{if(a.trigger&&a.valueNode&&d&&f&&b&&E&&S){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==a.dir){let o=n.left-t.left,i=r.left-o,a=e.left-i,s=e.width+a,u=Math.max(s,t.width),c=window.innerWidth-10,f=(0,l.q)(i,[10,Math.max(10,c-u)]);d.style.minWidth=s+"px",d.style.left=f+"px"}else{let o=t.right-n.right,i=window.innerWidth-r.right-o,a=window.innerWidth-e.right-i,s=e.width+a,u=Math.max(s,t.width),c=window.innerWidth-10,f=(0,l.q)(i,[10,Math.max(10,c-u)]);d.style.minWidth=s+"px",d.style.right=f+"px"}let i=h(),s=window.innerHeight-20,u=b.scrollHeight,c=window.getComputedStyle(f),p=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=p+v+u+parseInt(c.paddingBottom,10)+w,y=Math.min(5*E.offsetHeight,g),x=window.getComputedStyle(b),C=parseInt(x.paddingTop,10),R=parseInt(x.paddingBottom,10),j=e.top+e.height/2-10,N=E.offsetHeight/2,T=p+v+(E.offsetTop+N);if(T<=j){let e=i.length>0&&E===i[i.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-j,N+(e?R:0)+(f.clientHeight-b.offsetTop-b.offsetHeight)+w);d.style.height=T+t+"px"}else{let e=i.length>0&&E===i[0].ref.current;d.style.top="0px";let t=Math.max(j,p+b.offsetTop+(e?C:0)+N);d.style.height=t+(g-T)+"px",b.scrollTop=T-j+b.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=y+"px",d.style.maxHeight=s+"px",null==o||o(),requestAnimationFrame(()=>m.current=!0)}},[h,a.trigger,a.valueNode,d,f,b,E,S,a.dir,o]);(0,x.N)(()=>R(),[R]);let[j,N]=n.useState();(0,x.N)(()=>{f&&N(window.getComputedStyle(f).zIndex)},[f]);let T=n.useCallback(e=>{e&&!0===y.current&&(R(),null==C||C(),y.current=!1)},[R,C]);return(0,g.jsx)(eE,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:T,children:(0,g.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,g.jsx)(w.sG.div,{...i,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});eb.displayName="SelectItemAlignedPosition";var ex=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...l}=e,i=er(r);return(0,g.jsx)(I,{...i,...l,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ex.displayName="SelectPopperPosition";var[eE,eS]=ee(ev,{}),eC="SelectViewport",eR=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:o,...l}=e,a=ew(eC,r),u=eS(eC,r),d=(0,s.s)(t,a.onViewportChange),c=n.useRef(0);return(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,g.jsx)(Z.Slot,{scope:r,children:(0,g.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,i.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let l=o+e,i=Math.min(n,l),a=l-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});eR.displayName=eC;var ej="SelectGroup",[eN,eT]=ee(ej);n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,v.B)();return(0,g.jsx)(eN,{scope:r,id:o,children:(0,g.jsx)(w.sG.div,{role:"group","aria-labelledby":o,...n,ref:t})})}).displayName=ej;var eP="SelectLabel";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=eT(eP,r);return(0,g.jsx)(w.sG.div,{id:o.id,...n,ref:t})}).displayName=eP;var eA="SelectItem",[eD,eL]=ee(eA),ek=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,disabled:l=!1,textValue:a,...u}=e,d=eo(eA,r),c=ew(eA,r),f=d.value===o,[p,h]=n.useState(null!=a?a:""),[m,y]=n.useState(!1),b=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,o,l)}),x=(0,v.B)(),E=n.useRef("touch"),S=()=>{l||(d.onValueChange(o),d.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,g.jsx)(eD,{scope:r,value:o,disabled:l,textId:x,isSelected:f,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,g.jsx)(Z.ItemSlot,{scope:r,value:o,disabled:l,textValue:p,children:(0,g.jsx)(w.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":f&&m,"data-state":f?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...u,ref:b,onFocus:(0,i.m)(u.onFocus,()=>y(!0)),onBlur:(0,i.m)(u.onBlur,()=>y(!1)),onClick:(0,i.m)(u.onClick,()=>{"mouse"!==E.current&&S()}),onPointerUp:(0,i.m)(u.onPointerUp,()=>{"mouse"===E.current&&S()}),onPointerDown:(0,i.m)(u.onPointerDown,e=>{E.current=e.pointerType}),onPointerMove:(0,i.m)(u.onPointerMove,e=>{if(E.current=e.pointerType,l){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===E.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,i.m)(u.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(Y.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ek.displayName=eA;var eI="SelectItemText",eO=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:l,style:i,...a}=e,u=eo(eI,r),d=ew(eI,r),c=eL(eI,r),f=ei(eI,r),[p,v]=n.useState(null),h=(0,s.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null==(t=d.itemTextRefCallback)?void 0:t.call(d,e,c.value,c.disabled)}),m=null==p?void 0:p.textContent,y=n.useMemo(()=>(0,g.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:b,onNativeOptionRemove:E}=f;return(0,x.N)(()=>(b(y),()=>E(y)),[b,E,y]),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(w.sG.span,{id:c.textId,...a,ref:h}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});eO.displayName=eI;var e_="SelectItemIndicator",eM=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return eL(e_,r).isSelected?(0,g.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t}):null});eM.displayName=e_;var eF="SelectScrollUpButton",eG=n.forwardRef((e,t)=>{let r=ew(eF,e.__scopeSelect),o=eS(eF,e.__scopeSelect),[l,i]=n.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,g.jsx)(ez,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eG.displayName=eF;var eH="SelectScrollDownButton",eW=n.forwardRef((e,t)=>{let r=ew(eH,e.__scopeSelect),o=eS(eH,e.__scopeSelect),[l,i]=n.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,g.jsx)(ez,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eW.displayName=eH;var ez=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:o,...l}=e,a=ew("SelectScrollButton",r),s=n.useRef(null),u=J(r),d=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),(0,x.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,g.jsx)(w.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,i.m)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,i.m)(l.onPointerMove,()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,i.m)(l.onPointerLeave,()=>{d()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,g.jsx)(w.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var eB="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=er(r),l=eo(eB,r),i=ew(eB,r);return l.open&&"popper"===i.position?(0,g.jsx)(M,{...o,...n,ref:t}):null}).displayName=eB;var eU=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,...l}=e,i=n.useRef(null),a=(0,s.s)(t,i),u=(0,U.Z)(o);return n.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==o&&t){let r=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(r)}},[u,o]),(0,g.jsx)(w.sG.select,{...l,style:{...K,...l.style},ref:a,defaultValue:o})});function eK(e){return""===e||void 0===e}function eV(e){let t=(0,b.c)(e),r=n.useRef(""),o=n.useRef(0),l=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),i=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,l,i]}function eq(e,t,r){var n,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,a=(n=e,o=Math.max(i,0),n.map((e,t)=>n[(o+t)%n.length]));1===l.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return s!==r?s:void 0}eU.displayName="SelectBubbleInput";var eX=ea,eY=eu,e$=ec,eZ=ef,eJ=ep,eQ=eh,e0=eR,e1=ek,e5=eO,e2=eM,e8=eG,e6=eW},39033:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(12115);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},40968:(e,t,r)=>{r.d(t,{b:()=>a});var n=r(12115),o=r(63655),l=r(95155),i=n.forwardRef((e,t)=>(0,l.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var a=i},45503:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(12115);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},46081:(e,t,r)=>{r.d(t,{A:()=>i,q:()=>l});var n=r(12115),o=r(95155);function l(e,t){let r=n.createContext(t),l=e=>{let{children:t,...l}=e,i=n.useMemo(()=>l,Object.values(l));return(0,o.jsx)(r.Provider,{value:i,children:t})};return l.displayName=e+"Provider",[l,function(o){let l=n.useContext(r);if(l)return l;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],l=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return l.scopeName=e,[function(t,l){let i=n.createContext(l),a=r.length;r=[...r,l];let s=t=>{let{scope:r,children:l,...s}=t,u=r?.[e]?.[a]||i,d=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:d,children:l})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[a]||i,u=n.useContext(s);if(u)return u;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(l,...t)]}},47655:(e,t,r)=>{r.d(t,{LM:()=>X,OK:()=>Y,VM:()=>S,bL:()=>q,lr:()=>I});var n=r(12115),o=r(63655),l=r(28905),i=r(46081),a=r(6101),s=r(39033),u=r(94315),d=r(52712),c=r(89367),f=r(85185),p=r(95155),v="ScrollArea",[h,m]=(0,i.A)(v),[w,g]=h(v),y=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:l="hover",dir:i,scrollHideDelay:s=600,...d}=e,[c,f]=n.useState(null),[v,h]=n.useState(null),[m,g]=n.useState(null),[y,b]=n.useState(null),[x,E]=n.useState(null),[S,C]=n.useState(0),[R,j]=n.useState(0),[N,T]=n.useState(!1),[P,A]=n.useState(!1),D=(0,a.s)(t,e=>f(e)),L=(0,u.jH)(i);return(0,p.jsx)(w,{scope:r,type:l,dir:L,scrollHideDelay:s,scrollArea:c,viewport:v,onViewportChange:h,content:m,onContentChange:g,scrollbarX:y,onScrollbarXChange:b,scrollbarXEnabled:N,onScrollbarXEnabledChange:T,scrollbarY:x,onScrollbarYChange:E,scrollbarYEnabled:P,onScrollbarYEnabledChange:A,onCornerWidthChange:C,onCornerHeightChange:j,children:(0,p.jsx)(o.sG.div,{dir:L,...d,ref:D,style:{position:"relative","--radix-scroll-area-corner-width":S+"px","--radix-scroll-area-corner-height":R+"px",...e.style}})})});y.displayName=v;var b="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:l,nonce:i,...s}=e,u=g(b,r),d=n.useRef(null),c=(0,a.s)(t,d,u.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,p.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:c,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});x.displayName=b;var E="ScrollAreaScrollbar",S=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=g(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,s="horizontal"===e.orientation;return n.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===l.type?(0,p.jsx)(C,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,p.jsx)(R,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,p.jsx)(j,{...o,ref:t,forceMount:r}):"always"===l.type?(0,p.jsx)(N,{...o,ref:t}):null});S.displayName=E;var C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=g(E,e.__scopeScrollArea),[a,s]=n.useState(!1);return n.useEffect(()=>{let e=i.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[i.scrollArea,i.scrollHideDelay]),(0,p.jsx)(l.C,{present:r||a,children:(0,p.jsx)(j,{"data-state":a?"visible":"hidden",...o,ref:t})})}),R=n.forwardRef((e,t)=>{var r;let{forceMount:o,...i}=e,a=g(E,e.__scopeScrollArea),s="horizontal"===e.orientation,u=K(()=>c("SCROLL_END"),100),[d,c]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},"hidden"));return n.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>c("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,a.scrollHideDelay,c]),n.useEffect(()=>{let e=a.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(c("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[a.viewport,s,c,u]),(0,p.jsx)(l.C,{present:o||"hidden"!==d,children:(0,p.jsx)(N,{"data-state":"hidden"===d?"hidden":"visible",...i,ref:t,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>c("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>c("POINTER_LEAVE"))})})}),j=n.forwardRef((e,t)=>{let r=g(E,e.__scopeScrollArea),{forceMount:o,...i}=e,[a,s]=n.useState(!1),u="horizontal"===e.orientation,d=K(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(u?e:t)}},10);return V(r.viewport,d),V(r.content,d),(0,p.jsx)(l.C,{present:o||a,children:(0,p.jsx)(N,{"data-state":a?"visible":"hidden",...i,ref:t})})}),N=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=g(E,e.__scopeScrollArea),i=n.useRef(null),a=n.useRef(0),[s,u]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=H(s.viewport,s.content),c={...o,sizes:s,onSizesChange:u,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function f(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=W(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return B([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===r?(0,p.jsx)(T,{...c,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=z(l.viewport.scrollLeft,s,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=f(e,l.dir))}}):"vertical"===r?(0,p.jsx)(P,{...c,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=z(l.viewport.scrollTop,s);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=f(e))}}):null}),T=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,i=g(E,e.__scopeScrollArea),[s,u]=n.useState(),d=n.useRef(null),c=(0,a.s)(t,d,i.onScrollbarXChange);return n.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,p.jsx)(L,{"data-orientation":"horizontal",...l,ref:c,sizes:r,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":W(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&o({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:G(s.paddingLeft),paddingEnd:G(s.paddingRight)}})}})}),P=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,i=g(E,e.__scopeScrollArea),[s,u]=n.useState(),d=n.useRef(null),c=(0,a.s)(t,d,i.onScrollbarYChange);return n.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,p.jsx)(L,{"data-orientation":"vertical",...l,ref:c,sizes:r,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":W(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&o({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:G(s.paddingTop),paddingEnd:G(s.paddingBottom)}})}})}),[A,D]=h(E),L=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:l,hasThumb:i,onThumbChange:u,onThumbPointerUp:d,onThumbPointerDown:c,onThumbPositionChange:v,onDragScroll:h,onWheelScroll:m,onResize:w,...y}=e,b=g(E,r),[x,S]=n.useState(null),C=(0,a.s)(t,e=>S(e)),R=n.useRef(null),j=n.useRef(""),N=b.viewport,T=l.content-l.viewport,P=(0,s.c)(m),D=(0,s.c)(v),L=K(w,10);function k(e){R.current&&h({x:e.clientX-R.current.left,y:e.clientY-R.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==x?void 0:x.contains(t))&&P(e,T)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[N,x,T,P]),n.useEffect(D,[l,D]),V(x,L),V(b.content,L),(0,p.jsx)(A,{scope:r,scrollbar:x,hasThumb:i,onThumbChange:(0,s.c)(u),onThumbPointerUp:(0,s.c)(d),onThumbPositionChange:D,onThumbPointerDown:(0,s.c)(c),children:(0,p.jsx)(o.sG.div,{...y,ref:C,style:{position:"absolute",...y.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),R.current=x.getBoundingClientRect(),j.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),k(e))}),onPointerMove:(0,f.m)(e.onPointerMove,k),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=j.current,b.viewport&&(b.viewport.style.scrollBehavior=""),R.current=null})})})}),k="ScrollAreaThumb",I=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=D(k,e.__scopeScrollArea);return(0,p.jsx)(l.C,{present:r||o.hasThumb,children:(0,p.jsx)(O,{ref:t,...n})})}),O=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:l,...i}=e,s=g(k,r),u=D(k,r),{onThumbPositionChange:d}=u,c=(0,a.s)(t,e=>u.onThumbChange(e)),v=n.useRef(void 0),h=K(()=>{v.current&&(v.current(),v.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{h(),v.current||(v.current=U(e,d),d())};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,h,d]),(0,p.jsx)(o.sG.div,{"data-state":u.hasThumb?"visible":"hidden",...i,ref:c,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;u.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,f.m)(e.onPointerUp,u.onThumbPointerUp)})});I.displayName=k;var _="ScrollAreaCorner",M=n.forwardRef((e,t)=>{let r=g(_,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,p.jsx)(F,{...e,ref:t}):null});M.displayName=_;var F=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...l}=e,i=g(_,r),[a,s]=n.useState(0),[u,d]=n.useState(0),c=!!(a&&u);return V(i.scrollbarX,()=>{var e;let t=(null==(e=i.scrollbarX)?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),d(t)}),V(i.scrollbarY,()=>{var e;let t=(null==(e=i.scrollbarY)?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),s(t)}),c?(0,p.jsx)(o.sG.div,{...l,ref:t,style:{width:a,height:u,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function G(e){return e?parseInt(e,10):0}function H(e,t){let r=e/t;return isNaN(r)?0:r}function W(e){let t=H(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function z(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=W(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=(0,c.q)(e,"ltr"===r?[0,i]:[-1*i,0]);return B([0,i],[0,l-n])(a)}function B(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var U=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function K(e,t){let r=(0,s.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function V(e,t){let r=(0,s.c)(t);(0,d.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var q=y,X=x,Y=M},52712:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(12115),o=globalThis?.document?n.useLayoutEffect:()=>{}},55863:(e,t,r)=>{r.d(t,{C1:()=>x,bL:()=>b});var n=r(12115),o=r(46081),l=r(63655),i=r(95155),a="Progress",[s,u]=(0,o.A)(a),[d,c]=s(a),f=n.forwardRef((e,t)=>{var r,n,o,a;let{__scopeProgress:s,value:u=null,max:c,getValueLabel:f=h,...p}=e;(c||0===c)&&!g(c)&&console.error((r="".concat(c),n="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(n,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let v=g(c)?c:100;null===u||y(u,v)||console.error((o="".concat(u),a="Progress","Invalid prop `value` of value `".concat(o,"` supplied to `").concat(a,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let b=y(u,v)?u:null,x=w(b)?f(b,v):void 0;return(0,i.jsx)(d,{scope:s,value:b,max:v,children:(0,i.jsx)(l.sG.div,{"aria-valuemax":v,"aria-valuemin":0,"aria-valuenow":w(b)?b:void 0,"aria-valuetext":x,role:"progressbar","data-state":m(b,v),"data-value":null!=b?b:void 0,"data-max":v,...p,ref:t})})});f.displayName=a;var p="ProgressIndicator",v=n.forwardRef((e,t)=>{var r;let{__scopeProgress:n,...o}=e,a=c(p,n);return(0,i.jsx)(l.sG.div,{"data-state":m(a.value,a.max),"data-value":null!=(r=a.value)?r:void 0,"data-max":a.max,...o,ref:t})});function h(e,t){return"".concat(Math.round(e/t*100),"%")}function m(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function w(e){return"number"==typeof e}function g(e){return w(e)&&!isNaN(e)&&e>0}function y(e,t){return w(e)&&!isNaN(e)&&e<=t&&e>=0}v.displayName=p;var b=f,x=v},57701:(e,t,r)=>{r.d(t,{C1:()=>en,q7:()=>er,bL:()=>et});var n=r(12115),o=r(85185),l=r(6101),i=r(46081),a=r(63655),s=r(82284),u=r(61285),d=r(39033),c=r(5845),f=r(94315),p=r(95155),v="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[w,g,y]=(0,s.N)(m),[b,x]=(0,i.A)(m,[y]),[E,S]=b(m),C=n.forwardRef((e,t)=>(0,p.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(R,{...e,ref:t})})}));C.displayName=m;var R=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:s=!1,dir:u,currentTabStopId:w,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:b,onEntryFocus:x,preventScrollOnEntryFocus:S=!1,...C}=e,R=n.useRef(null),j=(0,l.s)(t,R),N=(0,f.jH)(u),[T,A]=(0,c.i)({prop:w,defaultProp:null!=y?y:null,onChange:b,caller:m}),[D,L]=n.useState(!1),k=(0,d.c)(x),I=g(r),O=n.useRef(!1),[_,M]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(v,k),()=>e.removeEventListener(v,k)},[k]),(0,p.jsx)(E,{scope:r,orientation:i,dir:N,loop:s,currentTabStopId:T,onItemFocus:n.useCallback(e=>A(e),[A]),onItemShiftTab:n.useCallback(()=>L(!0),[]),onFocusableItemAdd:n.useCallback(()=>M(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>M(e=>e-1),[]),children:(0,p.jsx)(a.sG.div,{tabIndex:D||0===_?-1:0,"data-orientation":i,...C,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(v,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=I().filter(e=>e.focusable);P([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),S)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>L(!1))})})}),j="RovingFocusGroupItem",N=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:l=!0,active:i=!1,tabStopId:s,children:d,...c}=e,f=(0,u.B)(),v=s||f,h=S(j,r),m=h.currentTabStopId===v,y=g(r),{onFocusableItemAdd:b,onFocusableItemRemove:x,currentTabStopId:E}=h;return n.useEffect(()=>{if(l)return b(),()=>x()},[l,b,x]),(0,p.jsx)(w.ItemSlot,{scope:r,id:v,focusable:l,active:i,children:(0,p.jsx)(a.sG.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{l?h.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>h.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return T[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=h.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>P(r))}}),children:"function"==typeof d?d({isCurrentTabStop:m,hasTabStop:null!=E}):d})})});N.displayName=j;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function P(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var A=r(11275),D=r(45503),L=r(28905),k="Radio",[I,O]=(0,i.A)(k),[_,M]=I(k),F=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:i,checked:s=!1,required:u,disabled:d,value:c="on",onCheck:f,form:v,...h}=e,[m,w]=n.useState(null),g=(0,l.s)(t,e=>w(e)),y=n.useRef(!1),b=!m||v||!!m.closest("form");return(0,p.jsxs)(_,{scope:r,checked:s,disabled:d,children:[(0,p.jsx)(a.sG.button,{type:"button",role:"radio","aria-checked":s,"data-state":z(s),"data-disabled":d?"":void 0,disabled:d,value:c,...h,ref:g,onClick:(0,o.m)(e.onClick,e=>{s||null==f||f(),b&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),b&&(0,p.jsx)(W,{control:m,bubbles:!y.current,name:i,value:c,checked:s,required:u,disabled:d,form:v,style:{transform:"translateX(-100%)"}})]})});F.displayName=k;var G="RadioIndicator",H=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...o}=e,l=M(G,r);return(0,p.jsx)(L.C,{present:n||l.checked,children:(0,p.jsx)(a.sG.span,{"data-state":z(l.checked),"data-disabled":l.disabled?"":void 0,...o,ref:t})})});H.displayName=G;var W=n.forwardRef((e,t)=>{let{__scopeRadio:r,control:o,checked:i,bubbles:s=!0,...u}=e,d=n.useRef(null),c=(0,l.s)(d,t),f=(0,D.Z)(i),v=(0,A.X)(o);return n.useEffect(()=>{let e=d.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(f!==i&&t){let r=new Event("click",{bubbles:s});t.call(e,i),e.dispatchEvent(r)}},[f,i,s]),(0,p.jsx)(a.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:i,...u,tabIndex:-1,ref:c,style:{...u.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function z(e){return e?"checked":"unchecked"}W.displayName="RadioBubbleInput";var B=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],U="RadioGroup",[K,V]=(0,i.A)(U,[x,O]),q=x(),X=O(),[Y,$]=K(U),Z=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:o,value:l,required:i=!1,disabled:s=!1,orientation:u,dir:d,loop:v=!0,onValueChange:h,...m}=e,w=q(r),g=(0,f.jH)(d),[y,b]=(0,c.i)({prop:l,defaultProp:null!=o?o:null,onChange:h,caller:U});return(0,p.jsx)(Y,{scope:r,name:n,required:i,disabled:s,value:y,onValueChange:b,children:(0,p.jsx)(C,{asChild:!0,...w,orientation:u,dir:g,loop:v,children:(0,p.jsx)(a.sG.div,{role:"radiogroup","aria-required":i,"aria-orientation":u,"data-disabled":s?"":void 0,dir:g,...m,ref:t})})})});Z.displayName=U;var J="RadioGroupItem",Q=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:i,...a}=e,s=$(J,r),u=s.disabled||i,d=q(r),c=X(r),f=n.useRef(null),v=(0,l.s)(t,f),h=s.value===a.value,m=n.useRef(!1);return n.useEffect(()=>{let e=e=>{B.includes(e.key)&&(m.current=!0)},t=()=>m.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,p.jsx)(N,{asChild:!0,...d,focusable:!u,active:h,children:(0,p.jsx)(F,{disabled:u,required:s.required,checked:h,...c,...a,name:s.name,ref:v,onCheck:()=>s.onValueChange(a.value),onKeyDown:(0,o.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.m)(a.onFocus,()=>{var e;m.current&&(null==(e=f.current)||e.click())})})})});Q.displayName=J;var ee=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,o=X(r);return(0,p.jsx)(H,{...o,...n,ref:t})});ee.displayName="RadioGroupIndicator";var et=Z,er=Q,en=ee},61285:(e,t,r)=>{r.d(t,{B:()=>s});var n,o=r(12115),l=r(52712),i=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function s(e){let[t,r]=o.useState(i());return(0,l.N)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},63655:(e,t,r)=>{r.d(t,{hO:()=>s,sG:()=>a});var n=r(12115),o=r(47650),l=r(99708),i=r(95155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,l.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?r:t,{...l,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},76981:(e,t,r)=>{r.d(t,{C1:()=>S,bL:()=>x});var n=r(12115),o=r(6101),l=r(46081),i=r(85185),a=r(5845),s=r(45503),u=r(11275),d=r(28905),c=r(63655),f=r(95155),p="Checkbox",[v,h]=(0,l.A)(p),[m,w]=v(p);function g(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:l,disabled:i,form:s,name:u,onCheckedChange:d,required:c,value:v="on",internal_do_not_use_render:h}=e,[w,g]=(0,a.i)({prop:r,defaultProp:null!=l&&l,onChange:d,caller:p}),[y,b]=n.useState(null),[x,E]=n.useState(null),S=n.useRef(!1),C=!y||!!s||!!y.closest("form"),R={checked:w,disabled:i,setChecked:g,control:y,setControl:b,name:u,form:s,value:v,hasConsumerStoppedPropagationRef:S,required:c,defaultChecked:!j(l)&&l,isFormControl:C,bubbleInput:x,setBubbleInput:E};return(0,f.jsx)(m,{scope:t,...R,children:"function"==typeof h?h(R):o})}var y="CheckboxTrigger",b=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:l,onClick:a,...s}=e,{control:u,value:d,disabled:p,checked:v,required:h,setControl:m,setChecked:g,hasConsumerStoppedPropagationRef:b,isFormControl:x,bubbleInput:E}=w(y,r),S=(0,o.s)(t,m),C=n.useRef(v);return n.useEffect(()=>{let e=null==u?void 0:u.form;if(e){let t=()=>g(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[u,g]),(0,f.jsx)(c.sG.button,{type:"button",role:"checkbox","aria-checked":j(v)?"mixed":v,"aria-required":h,"data-state":N(v),"data-disabled":p?"":void 0,disabled:p,value:d,...s,ref:S,onKeyDown:(0,i.m)(l,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(a,e=>{g(e=>!!j(e)||!e),E&&x&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});b.displayName=y;var x=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:l,required:i,disabled:a,value:s,onCheckedChange:u,form:d,...c}=e;return(0,f.jsx)(g,{__scopeCheckbox:r,checked:o,defaultChecked:l,disabled:a,required:i,onCheckedChange:u,name:n,form:d,value:s,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(b,{...c,ref:t,__scopeCheckbox:r}),n&&(0,f.jsx)(R,{__scopeCheckbox:r})]})}})});x.displayName=p;var E="CheckboxIndicator",S=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,l=w(E,r);return(0,f.jsx)(d.C,{present:n||j(l.checked)||!0===l.checked,children:(0,f.jsx)(c.sG.span,{"data-state":N(l.checked),"data-disabled":l.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});S.displayName=E;var C="CheckboxBubbleInput",R=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...l}=e,{control:i,hasConsumerStoppedPropagationRef:a,checked:d,defaultChecked:p,required:v,disabled:h,name:m,value:g,form:y,bubbleInput:b,setBubbleInput:x}=w(C,r),E=(0,o.s)(t,x),S=(0,s.Z)(d),R=(0,u.X)(i);n.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!a.current;if(S!==d&&e){let r=new Event("click",{bubbles:t});b.indeterminate=j(d),e.call(b,!j(d)&&d),b.dispatchEvent(r)}},[b,S,d,a]);let N=n.useRef(!j(d)&&d);return(0,f.jsx)(c.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:N.current,required:v,disabled:h,name:m,value:g,form:y,...l,tabIndex:-1,ref:E,style:{...l.style,...R,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function j(e){return"indeterminate"===e}function N(e){return j(e)?"indeterminate":e?"checked":"unchecked"}R.displayName=C},82284:(e,t,r)=>{r.d(t,{N:()=>f});var n,o=r(94971),l=r(95920),i=r(86266),a=r(12115),s=r(46081),u=r(6101),d=r(99708),c=r(95155);function f(e){let t=e+"CollectionProvider",[r,n]=(0,s.A)(t),[o,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{let{scope:t,children:r}=e,n=a.useRef(null),l=a.useRef(new Map).current;return(0,c.jsx)(o,{scope:t,itemMap:l,collectionRef:n,children:r})};i.displayName=t;let f=e+"CollectionSlot",p=(0,d.TL)(f),v=a.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=l(f,r),i=(0,u.s)(t,o.collectionRef);return(0,c.jsx)(p,{ref:i,children:n})});v.displayName=f;let h=e+"CollectionItemSlot",m="data-radix-collection-item",w=(0,d.TL)(h),g=a.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,i=a.useRef(null),s=(0,u.s)(t,i),d=l(h,r);return a.useEffect(()=>(d.itemMap.set(i,{ref:i,...o}),()=>void d.itemMap.delete(i))),(0,c.jsx)(w,{...{[m]:""},ref:s,children:n})});return g.displayName=h,[{Provider:i,Slot:v,ItemSlot:g},function(t){let r=l(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var p=new WeakMap;function v(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=h(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function h(e){return e!=e||0===e?0:Math.trunc(e)}n=new WeakMap,class e extends Map{set(e,t){return p.get(this)&&(this.has(e)?(0,o._)(this,n)[(0,o._)(this,n).indexOf(e)]=e:(0,o._)(this,n).push(e)),super.set(e,t),this}insert(e,t,r){let l,i=this.has(t),a=(0,o._)(this,n).length,s=h(e),u=s>=0?s:a+s,d=u<0||u>=a?-1:u;if(d===this.size||i&&d===this.size-1||-1===d)return this.set(t,r),this;let c=this.size+ +!i;s<0&&u++;let f=[...(0,o._)(this,n)],p=!1;for(let e=u;e<c;e++)if(u===e){let n=f[e];f[e]===t&&(n=f[e+1]),i&&this.delete(t),l=this.get(n),this.set(t,r)}else{p||f[e-1]!==t||(p=!0);let r=f[p?e:e-1],n=l;l=this.get(r),this.delete(r),this.set(r,n)}return this}with(t,r,n){let o=new e(this);return o.insert(t,r,n),o}before(e){let t=(0,o._)(this,n).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,r){let l=(0,o._)(this,n).indexOf(e);return -1===l?this:this.insert(l,t,r)}after(e){let t=(0,o._)(this,n).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,r){let l=(0,o._)(this,n).indexOf(e);return -1===l?this:this.insert(l+1,t,r)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return(0,i._)(this,n,[]),super.clear()}delete(e){let t=super.delete(e);return t&&(0,o._)(this,n).splice((0,o._)(this,n).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=v((0,o._)(this,n),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=v((0,o._)(this,n),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return(0,o._)(this,n).indexOf(e)}keyAt(e){return v((0,o._)(this,n),e)}from(e,t){let r=this.indexOf(e);if(-1===r)return;let n=r+t;return n<0&&(n=0),n>=this.size&&(n=this.size-1),this.at(n)}keyFrom(e,t){let r=this.indexOf(e);if(-1===r)return;let n=r+t;return n<0&&(n=0),n>=this.size&&(n=this.size-1),this.keyAt(n)}find(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return n;r++}}findIndex(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return r;r++}return -1}filter(t,r){let n=[],o=0;for(let e of this)Reflect.apply(t,r,[e,o,this])&&n.push(e),o++;return new e(n)}map(t,r){let n=[],o=0;for(let e of this)n.push([e[0],Reflect.apply(t,r,[e,o,this])]),o++;return new e(n)}reduce(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,o]=t,l=0,i=null!=o?o:this.at(0);for(let e of this)i=0===l&&1===t.length?e:Reflect.apply(n,this,[i,e,l,this]),l++;return i}reduceRight(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,o]=t,l=null!=o?o:this.at(-1);for(let e=this.size-1;e>=0;e--){let r=this.at(e);l=e===this.size-1&&1===t.length?r:Reflect.apply(n,this,[l,r,e,this])}return l}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let r=this.keyAt(e),n=this.get(r);t.set(r,n)}return t}toSpliced(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let o=[...this.entries()];return o.splice(...r),new e(o)}slice(t,r){let n=new e,o=this.size-1;if(void 0===t)return n;t<0&&(t+=this.size),void 0!==r&&r>0&&(o=r-1);for(let e=t;e<=o;e++){let t=this.keyAt(e),r=this.get(t);n.set(t,r)}return n}every(e,t){let r=0;for(let n of this){if(!Reflect.apply(e,t,[n,r,this]))return!1;r++}return!0}some(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return!0;r++}return!1}constructor(e){super(e),(0,l._)(this,n,{writable:!0,value:void 0}),(0,i._)(this,n,[...super.keys()]),p.set(this,!0)}}},85185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},87489:(e,t,r)=>{r.d(t,{b:()=>u});var n=r(12115),o=r(63655),l=r(95155),i="horizontal",a=["horizontal","vertical"],s=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:s=i,...u}=e,d=(r=s,a.includes(r))?s:i;return(0,l.jsx)(o.sG.div,{"data-orientation":d,...n?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...u,ref:t})});s.displayName="Separator";var u=s},89367:(e,t,r)=>{r.d(t,{q:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},92293:(e,t,r)=>{r.d(t,{Oh:()=>l});var n=r(12115),o=0;function l(){n.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:i()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},94315:(e,t,r)=>{r.d(t,{jH:()=>l});var n=r(12115);r(95155);var o=n.createContext(void 0);function l(e){let t=n.useContext(o);return e||t||"ltr"}},99708:(e,t,r)=>{r.d(t,{DX:()=>a,TL:()=>i});var n=r(12115),o=r(6101),l=r(95155);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){var i;let e,a,s=(i=r,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),u=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,o.t)(t,s):s),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...i}=e,a=n.Children.toArray(o),s=a.find(u);if(s){let e=s.props.children,o=a.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,l.jsx)(t,{...i,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var a=i("Slot"),s=Symbol("radix.slottable");function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}}}]);