'use client'

import { useState } from 'react'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  ArrowLeft, 
  Sparkles, 
  Bug, 
  AlertTriangle,
  Info,
  Zap,
  Send,
  CheckCircle,
  Upload
} from 'lucide-react'

export default function BugReportPage() {
  const [formData, setFormData] = useState({
    title: '',
    email: '',
    priority: '',
    category: '',
    description: '',
    steps: '',
    expected: '',
    actual: '',
    browser: '',
    os: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setIsSubmitted(true)
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false)
      setFormData({
        title: '', email: '', priority: '', category: '', description: '',
        steps: '', expected: '', actual: '', browser: '', os: ''
      })
    }, 3000)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link 
              href="/" 
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Ana Sayfaya Dön</span>
            </Link>
            <div className="flex items-center space-x-2">
              <Sparkles className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">PromptBir</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Bug className="h-8 w-8 text-red-600" />
          </div>
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
            Bug Raporu
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Karşılaştığınız teknik sorunları bildirin. Detaylı bilgi vererek sorunun 
            daha hızlı çözülmesine yardımcı olabilirsiniz.
          </p>
        </div>

        {/* Priority Info */}
        <div className="grid md:grid-cols-3 gap-4 mb-12">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4 text-center">
              <AlertTriangle className="h-8 w-8 text-red-600 mx-auto mb-2" />
              <h3 className="font-semibold text-red-900">Yüksek Öncelik</h3>
              <p className="text-sm text-red-700">Sistem çökmesi, veri kaybı</p>
            </CardContent>
          </Card>
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="p-4 text-center">
              <Zap className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
              <h3 className="font-semibold text-yellow-900">Orta Öncelik</h3>
              <p className="text-sm text-yellow-700">Özellik çalışmıyor</p>
            </CardContent>
          </Card>
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-4 text-center">
              <Info className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-semibold text-blue-900">Düşük Öncelik</h3>
              <p className="text-sm text-blue-700">Görsel sorunlar, öneriler</p>
            </CardContent>
          </Card>
        </div>

        {/* Bug Report Form */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-2xl">
              <Bug className="h-6 w-6 text-red-600" />
              Bug Raporu Formu
            </CardTitle>
            <CardDescription>
              Lütfen formu mümkün olduğunca detaylı doldurun. Bu, sorunu daha hızlı çözmemize yardımcı olacaktır.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isSubmitted ? (
              <div className="text-center py-8">
                <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Bug Raporu Gönderildi!
                </h3>
                <p className="text-gray-600">
                  Raporunuz alındı. En kısa sürede inceleyeceğiz.
                </p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Bug Başlığı *</Label>
                    <Input
                      id="title"
                      name="title"
                      type="text"
                      required
                      value={formData.title}
                      onChange={handleChange}
                      className="mt-1"
                      placeholder="Kısa ve açıklayıcı başlık"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">E-posta *</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="mt-1"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                {/* Priority and Category */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="priority">Öncelik Seviyesi *</Label>
                    <Select onValueChange={(value) => handleSelectChange('priority', value)}>
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Öncelik seviyesi seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">Yüksek - Sistem çökmesi, veri kaybı</SelectItem>
                        <SelectItem value="medium">Orta - Özellik çalışmıyor</SelectItem>
                        <SelectItem value="low">Düşük - Görsel sorunlar, öneriler</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="category">Kategori *</Label>
                    <Select onValueChange={(value) => handleSelectChange('category', value)}>
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Bug kategorisi seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ui">Kullanıcı Arayüzü</SelectItem>
                        <SelectItem value="functionality">İşlevsellik</SelectItem>
                        <SelectItem value="performance">Performans</SelectItem>
                        <SelectItem value="security">Güvenlik</SelectItem>
                        <SelectItem value="data">Veri İşleme</SelectItem>
                        <SelectItem value="auth">Kimlik Doğrulama</SelectItem>
                        <SelectItem value="other">Diğer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <Label htmlFor="description">Bug Açıklaması *</Label>
                  <Textarea
                    id="description"
                    name="description"
                    required
                    value={formData.description}
                    onChange={handleChange}
                    className="mt-1 min-h-[100px]"
                    placeholder="Karşılaştığınız sorunu detaylı olarak açıklayın..."
                  />
                </div>

                {/* Steps to Reproduce */}
                <div>
                  <Label htmlFor="steps">Hatayı Tekrarlama Adımları *</Label>
                  <Textarea
                    id="steps"
                    name="steps"
                    required
                    value={formData.steps}
                    onChange={handleChange}
                    className="mt-1 min-h-[100px]"
                    placeholder="1. İlk adım&#10;2. İkinci adım&#10;3. Üçüncü adım..."
                  />
                </div>

                {/* Expected vs Actual */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="expected">Beklenen Sonuç *</Label>
                    <Textarea
                      id="expected"
                      name="expected"
                      required
                      value={formData.expected}
                      onChange={handleChange}
                      className="mt-1 min-h-[80px]"
                      placeholder="Ne olmasını bekliyordunuz?"
                    />
                  </div>
                  <div>
                    <Label htmlFor="actual">Gerçek Sonuç *</Label>
                    <Textarea
                      id="actual"
                      name="actual"
                      required
                      value={formData.actual}
                      onChange={handleChange}
                      className="mt-1 min-h-[80px]"
                      placeholder="Gerçekte ne oldu?"
                    />
                  </div>
                </div>

                {/* System Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="browser">Tarayıcı</Label>
                    <Input
                      id="browser"
                      name="browser"
                      type="text"
                      value={formData.browser}
                      onChange={handleChange}
                      className="mt-1"
                      placeholder="Chrome 120, Firefox 121, Safari 17..."
                    />
                  </div>
                  <div>
                    <Label htmlFor="os">İşletim Sistemi</Label>
                    <Input
                      id="os"
                      name="os"
                      type="text"
                      value={formData.os}
                      onChange={handleChange}
                      className="mt-1"
                      placeholder="Windows 11, macOS 14, Ubuntu 22.04..."
                    />
                  </div>
                </div>

                {/* File Upload Info */}
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <Upload className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-semibold text-blue-900 mb-1">Ekran Görüntüsü Ekleyin</h4>
                        <p className="text-sm text-blue-700">
                          Sorunu daha iyi anlayabilmemiz için ekran görüntüsü veya video ekleyebilirsiniz. 
                          Dosyaları e-posta ile <strong><EMAIL></strong> adresine gönderebilirsiniz.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Button 
                  type="submit" 
                  className="w-full bg-gradient-to-r from-red-600 to-pink-600 text-white hover:from-red-700 hover:to-pink-700"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Gönderiliyor...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Bug Raporu Gönder
                    </>
                  )}
                </Button>
              </form>
            )}
          </CardContent>
        </Card>

        {/* Tips */}
        <Card className="shadow-lg mt-8">
          <CardHeader>
            <CardTitle className="text-xl">İyi Bir Bug Raporu İçin İpuçları</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-start gap-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Sorunu tekrarlamak için gereken adımları net bir şekilde yazın</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Ekran görüntüsü veya video ekleyin</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Hata mesajlarını tam olarak kopyalayın</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Tarayıcı konsol hatalarını kontrol edin (F12 tuşu)</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Sorunun hangi koşullarda ortaya çıktığını belirtin</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-400">
            © 2024 PromptBir. Tüm hakları saklıdır.
          </p>
        </div>
      </footer>
    </div>
  )
}
