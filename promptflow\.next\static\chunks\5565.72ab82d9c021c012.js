"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5565],{15790:(e,t,a)=>{a.d(t,{IL:()=>m,RH:()=>l,W2:()=>c,YD:()=>x,_v:()=>u,lL:()=>o,sd:()=>d});var s=a(32960),r=a(26715),i=a(5041),n=a(70478);function l(){return(0,s.I)({queryKey:["context-categories"],queryFn:async()=>{let{data:e,error:t}=await n.L.from("context_categories").select("*").eq("is_active",!0).order("sort_order",{ascending:!0});if(t)throw t;return e},staleTime:3e5})}function c(e){return(0,s.I)({queryKey:["contexts",e],queryFn:async()=>{let t=n.L.from("contexts").select("\n          *,\n          category:context_categories(*)\n        ").eq("status","active");(null==e?void 0:e.category_id)&&(t=t.eq("category_id",e.category_id)),(null==e?void 0:e.is_public)!==void 0&&(t=t.eq("is_public",e.is_public)),(null==e?void 0:e.is_template)!==void 0&&(t=t.eq("is_template",e.is_template)),(null==e?void 0:e.is_featured)!==void 0&&(t=t.eq("is_featured",e.is_featured)),(null==e?void 0:e.author_id)&&(t=t.eq("author_id",e.author_id)),(null==e?void 0:e.search)&&(t=t.or("title.ilike.%".concat(e.search,"%,description.ilike.%").concat(e.search,"%,tags.cs.{").concat(e.search,"}"))),t=t.order("is_featured",{ascending:!1}).order("usage_count",{ascending:!1});let{data:a,error:s}=await t;if(s)throw s;return a.map(e=>({id:e.id,title:e.title,description:e.description,content:e.content,category:e.category,author_id:e.author_id,author_name:"Kullanıcı",is_public:e.is_public,is_featured:e.is_featured,is_template:e.is_template,tags:e.tags||[],usage_count:e.usage_count,like_count:e.like_count,view_count:e.view_count,approval_status:e.approval_status||"approved",approved_by:e.approved_by,approved_at:e.approved_at,approval_notes:e.approval_notes,created_at:e.created_at,updated_at:e.updated_at}))},staleTime:12e4})}function o(){let e=(0,r.jE)();return(0,i.n)({mutationFn:async e=>{let{data:t}=await n.L.auth.getUser();if(!t.user)throw Error("Kullanıcı girişi gerekli");let{data:a,error:s}=await n.L.from("contexts").insert({...e,author_id:t.user.id}).select().single();if(s)throw s;return a},onSuccess:()=>{e.invalidateQueries({queryKey:["contexts"]})}})}function d(){let e=(0,r.jE)();return(0,i.n)({mutationFn:async e=>{let{id:t,updates:a}=e,{data:s}=await n.L.auth.getUser();if(!s.user)throw Error("Kullanıcı girişi gerekli");let{data:r,error:i}=await n.L.from("contexts").update({...a,updated_at:new Date().toISOString()}).eq("id",t).eq("author_id",s.user.id).select().single();if(i)throw i;return r},onSuccess:(t,a)=>{e.invalidateQueries({queryKey:["contexts"]}),e.invalidateQueries({queryKey:["context",a.id]})}})}function u(){let e=(0,r.jE)();return(0,i.n)({mutationFn:async e=>{let{contextId:t,projectId:a}=e,{data:s}=await n.L.auth.getUser();if(!s.user)throw Error("Kullanıcı girişi gerekli");let{error:r}=await n.L.rpc("increment_context_usage",{context_id_param:t,user_id_param:s.user.id,project_id_param:a||null});if(r)throw r},onSuccess:()=>{e.invalidateQueries({queryKey:["contexts"]})}})}function x(){let e=(0,r.jE)();return(0,i.n)({mutationFn:async e=>{let{data:t}=await n.L.auth.getUser();if(!t.user)throw Error("Kullanıcı girişi gerekli");let{data:a,error:s}=await n.L.rpc("toggle_context_like",{context_id_param:e,user_id_param:t.user.id});if(s)throw s;return a},onSuccess:()=>{e.invalidateQueries({queryKey:["contexts"]})}})}function m(){return(0,s.I)({queryKey:["user-liked-contexts"],queryFn:async()=>{let{data:e}=await n.L.auth.getUser();if(!e.user)throw Error("Kullanıcı girişi gerekli");let{data:t,error:a}=await n.L.from("context_likes").select("context_id").eq("user_id",e.user.id);if(a)throw a;return t.map(e=>e.context_id)}})}},35565:(e,t,a)=>{a.r(t),a.d(t,{ContextCreationModal:()=>N});var s=a(95155),r=a(12115),i=a(30285),n=a(62523),l=a(85057),c=a(88539),o=a(26126),d=a(54165),u=a(59409),x=a(57434),m=a(85339),p=a(84616),h=a(43332),g=a(54416),f=a(32919),y=a(34869),v=a(51154),j=a(15790),_=a(53230),b=a(67238),k=a(56671);function N(e){let{open:t,onOpenChange:a,onSuccess:N,showAddToProject:w=!0}=e,[C,K]=(0,r.useState)({title:"",description:"",content:"",category_id:"",is_public:!1,is_template:!1,tags:[]}),[q,L]=(0,r.useState)(""),[A,z]=(0,r.useState)({}),[E,F]=(0,r.useState)(!1),{data:S=[],isLoading:I,error:R}=(0,j.RH)(),{data:P=[]}=(0,b.YK)(),U=(0,j.lL)(),{addContext:Q,isLoading:T}=(0,_.KU)(),J=async e=>{if(e.preventDefault(),!(()=>{let e={};return C.title.trim()||(e.title="Başlık gereklidir"),C.content.trim()||(e.content="İ\xe7erik gereklidir"),C.category_id||(e.category_id="Kategori se\xe7imi gereklidir"),z(e),0===Object.keys(e).length})())return void k.oR.error("L\xfctfen t\xfcm gerekli alanları doldurun");try{let e=await U.mutateAsync({title:C.title.trim(),description:C.description.trim()||void 0,content:C.content.trim(),category_id:C.category_id,is_public:C.is_public,is_template:C.is_template,tags:C.tags});if(E&&w)try{await Q(e,C.title.trim()),k.oR.success("Context oluşturuldu ve projeye eklendi!")}catch(e){console.error("Failed to add to project:",e),k.oR.warning("Context oluşturuldu ancak projeye eklenirken hata oluştu")}else k.oR.success("Context başarıyla oluşturuldu!");K({title:"",description:"",content:"",category_id:"",is_public:!1,is_template:!1,tags:[]}),z({}),F(!1),null==N||N(),a(!1)}catch(e){console.error("Context creation error:",e),k.oR.error("Context oluşturulurken bir hata oluştu")}},O=()=>{q.trim()&&!C.tags.includes(q.trim())&&(K(e=>({...e,tags:[...e.tags,q.trim()]})),L(""))};return(0,s.jsx)(d.lG,{open:t,onOpenChange:a,children:(0,s.jsxs)(d.Cf,{className:"max-w-2xl max-h-[90vh] overflow-hidden flex flex-col z-[60]",children:[(0,s.jsxs)(d.c7,{className:"flex-shrink-0",children:[(0,s.jsxs)(d.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-5 w-5"}),"Yeni Context Oluştur"]}),(0,s.jsx)(d.rr,{children:"Yeni bir context oluşturun. Herkese a\xe7ık contextler admin onayı gerektirir."})]}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,s.jsxs)("form",{onSubmit:J,className:"space-y-6 p-1",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(l.J,{htmlFor:"title",children:["Başlık ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(n.p,{id:"title",placeholder:"Context başlığını girin...",value:C.title,onChange:e=>K(t=>({...t,title:e.target.value})),className:A.title?"border-red-500":""}),A.title&&(0,s.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,s.jsx)(m.A,{className:"h-3 w-3"}),A.title]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"description",children:"A\xe7ıklama"}),(0,s.jsx)(n.p,{id:"description",placeholder:"Context a\xe7ıklaması (isteğe bağlı)...",value:C.description,onChange:e=>K(t=>({...t,description:e.target.value}))})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(l.J,{htmlFor:"category",children:["Kategori ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)(u.l6,{value:C.category_id,onValueChange:e=>K(t=>({...t,category_id:e})),children:[(0,s.jsx)(u.bq,{className:A.category_id?"border-red-500":"",children:(0,s.jsx)(u.yv,{placeholder:"Kategori se\xe7in..."})}),(0,s.jsx)(u.gC,{className:"z-[70]",children:I?(0,s.jsx)(u.eb,{value:"loading",disabled:!0,children:"Kategoriler y\xfckleniyor..."}):R?(0,s.jsxs)(u.eb,{value:"error",disabled:!0,children:["Kategori y\xfckleme hatası: ",R.message]}):0===S.length?(0,s.jsx)(u.eb,{value:"empty",disabled:!0,children:"Kategori bulunamadı"}):S.map(e=>(0,s.jsx)(u.eb,{value:e.id,children:(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:e.icon||"\uD83D\uDCC1"}),e.name]})},e.id))})]}),A.category_id&&(0,s.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,s.jsx)(m.A,{className:"h-3 w-3"}),A.category_id]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(l.J,{htmlFor:"content",children:["İ\xe7erik ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(c.T,{id:"content",placeholder:"Context i\xe7eriğini girin...",value:C.content,onChange:e=>K(t=>({...t,content:e.target.value})),className:"min-h-[120px] resize-none ".concat(A.content?"border-red-500":"")}),A.content&&(0,s.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,s.jsx)(m.A,{className:"h-3 w-3"}),A.content]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"tags",children:"Etiketler"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n.p,{id:"tags",placeholder:"Etiket ekle...",value:q,onChange:e=>L(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),O())},className:"flex-1"}),(0,s.jsx)(i.$,{type:"button",variant:"outline",size:"sm",onClick:O,disabled:!q.trim(),children:(0,s.jsx)(p.A,{className:"h-4 w-4"})})]}),C.tags.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:C.tags.map(e=>(0,s.jsxs)(o.E,{variant:"secondary",className:"flex items-center gap-1",children:[(0,s.jsx)(h.A,{className:"h-3 w-3"}),e,(0,s.jsx)("button",{type:"button",onClick:()=>{K(t=>({...t,tags:t.tags.filter(t=>t!==e)}))},className:"ml-1 hover:text-red-500",children:(0,s.jsx)(g.A,{className:"h-3 w-3"})})]},e))})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(l.J,{children:"G\xf6r\xfcn\xfcrl\xfck Ayarları"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,s.jsx)("input",{type:"radio",name:"visibility",checked:!C.is_public,onChange:()=>K(e=>({...e,is_public:!1})),className:"w-4 h-4"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-gray-500"}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"text-sm",children:"\xd6zel"}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"Sadece ben g\xf6rebilirim"})]})]})]}),(0,s.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,s.jsx)("input",{type:"radio",name:"visibility",checked:C.is_public,onChange:()=>K(e=>({...e,is_public:!0})),className:"w-4 h-4"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 text-blue-500"}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"text-sm",children:"Herkese A\xe7ık"}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"T\xfcm kullanıcılar g\xf6rebilir"})]})]})]})]})]}),(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:C.is_template,onChange:e=>K(t=>({...t,is_template:e.target.checked})),className:"w-4 h-4"}),(0,s.jsx)("span",{className:"text-sm",children:"Bu contexti şablon olarak işaretle"})]})}),w&&P.length>0&&(0,s.jsx)("div",{className:"space-y-2 p-3 bg-blue-50 rounded-lg border border-blue-200",children:(0,s.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:E,onChange:e=>F(e.target.checked),className:"w-4 h-4 text-blue-600"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-blue-600"}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"Mevcut projeye ekle"}),(0,s.jsx)("span",{className:"text-xs text-blue-700",children:"Context oluşturulduktan sonra aktif projeye prompt olarak eklenecek"})]})]})]})}),(0,s.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,s.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>a(!1),className:"flex-1",children:"İptal"}),(0,s.jsxs)(i.$,{type:"submit",disabled:U.isPending||T,className:"flex-1",children:[(U.isPending||T)&&(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4 animate-spin"}),E?"Oluştur ve Ekle":"Oluştur"]})]})]})})]})})}},53230:(e,t,a)=>{a.d(t,{KU:()=>c});var s=a(26715),r=a(5041),i=a(15501),n=a(67238),l=a(56671);function c(){let e=function(){let e=(0,s.jE)(),t=(0,i.sW)(),{data:a}=(0,n.YK)();return(0,r.n)({mutationFn:async s=>{let{context:r,options:i={}}=s;try{let s=i.projectId||function(e){var t;if(!e||0===e.length)return null;let a=localStorage.getItem("activeProjectId");return a&&e.find(e=>(null==e?void 0:e.id)===a)?a:(null==(t=e[0])?void 0:t.id)||null}(a||[]);if(!s)throw Error("L\xfctfen \xf6nce bir proje se\xe7in");let n={project_id:s,prompt_text:r.content,title:i.customTitle||r.title||"Context Gallery Prompt",category:i.customCategory||function(e){if(e.category)return e.category.name||e.category.toString();if(e.tags&&e.tags.length>0){let t=e.tags[0];if(t.startsWith("/"))return t}}(r),tags:function(e,t){let a=e.tags||[];return Array.from(new Set([...a,...t||[]])).filter(e=>e.trim().length>0)}(r,i.additionalTags),order_index:await o(s),is_used:!1,is_favorite:i.markAsFavorite||!1},l=await t.mutateAsync(n);return await d(r.id,s),e.invalidateQueries({queryKey:["prompts",s]}),e.invalidateQueries({queryKey:["projects"]}),{success:!0,promptId:l.id}}catch(e){return console.error("Context to prompt conversion error:",e),{success:!1,error:e instanceof Error?e.message:"Context prompt olarak eklenirken hata oluştu"}}},onSuccess:e=>{e.success?l.oR.success("Context başarıyla projeye eklendi!"):l.oR.error(e.error||"Bir hata oluştu")},onError:e=>{console.error("Context to prompt mutation error:",e),l.oR.error("Context eklenirken beklenmeyen bir hata oluştu")}})}();return{addContext:async(t,a)=>e.mutateAsync({context:t,options:{customTitle:a}}),isLoading:e.isPending,error:e.error}}async function o(e){return Date.now()}async function d(e,t){try{console.log("Context ".concat(e," used in project ").concat(t))}catch(e){console.warn("Failed to track context usage:",e)}}},59409:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>o,yv:()=>d});var s=a(95155);a(12115);var r=a(36170),i=a(66474),n=a(5196),l=a(47863),c=a(59434);function o(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:n,...l}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[n,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:a,position:i="popper",...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,s.jsx)(p,{}),(0,s.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(h,{})]})})}function m(e){let{className:t,children:a,...i}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(i.A,{className:"size-4"})})}}}]);