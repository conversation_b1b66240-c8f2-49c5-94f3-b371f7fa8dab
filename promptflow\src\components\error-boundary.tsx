/**
 * Error Boundary Component
 * Comprehensive error handling with fallback UI and error reporting
 */

'use client'

import React, { Compo<PERSON>, ErrorInfo, ReactNode } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showDetails?: boolean
  level?: 'page' | 'component' | 'critical'
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string | null
}

export class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0
  private maxRetries = 3

  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('🚨 [ERROR_BOUNDARY] Error caught:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      level: this.props.level || 'component',
      timestamp: new Date().toISOString()
    })

    // Update state with error info
    this.setState({
      errorInfo
    })

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // Report error to monitoring service
    this.reportError(error, errorInfo)
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // In production, send to error monitoring service
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry, LogRocket, etc.
      try {
        // window.Sentry?.captureException(error, {
        //   contexts: {
        //     react: {
        //       componentStack: errorInfo.componentStack
        //     }
        //   },
        //   tags: {
        //     errorBoundary: true,
        //     level: this.props.level || 'component'
        //   }
        // })
      } catch (reportingError) {
        console.error('Failed to report error:', reportingError)
      }
    }
  }

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++
      console.log(`🔄 [ERROR_BOUNDARY] Retry attempt ${this.retryCount}/${this.maxRetries}`)
      
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null
      })
    } else {
      console.warn('⚠️ [ERROR_BOUNDARY] Max retries exceeded')
    }
  }

  private handleReload = () => {
    window.location.reload()
  }

  private handleGoHome = () => {
    window.location.href = '/'
  }

  private getErrorLevel = (): 'low' | 'medium' | 'high' => {
    const { level } = this.props
    const { error } = this.state

    if (level === 'critical') return 'high'
    if (level === 'page') return 'medium'
    
    // Analyze error type
    if (error?.name === 'ChunkLoadError') return 'medium'
    if (error?.message?.includes('Network')) return 'medium'
    if (error?.message?.includes('TypeError')) return 'low'
    
    return 'low'
  }

  private renderErrorDetails = () => {
    const { error, errorInfo, errorId } = this.state
    const { showDetails = process.env.NODE_ENV === 'development' } = this.props

    if (!showDetails || !error) return null

    return (
      <details className="mt-4 p-4 bg-gray-50 rounded-lg">
        <summary className="cursor-pointer font-medium text-gray-700 mb-2">
          Teknik Detaylar
        </summary>
        <div className="space-y-2 text-sm font-mono">
          <div>
            <strong>Error ID:</strong> {errorId}
          </div>
          <div>
            <strong>Error:</strong> {error.name}
          </div>
          <div>
            <strong>Message:</strong> {error.message}
          </div>
          {error.stack && (
            <div>
              <strong>Stack Trace:</strong>
              <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32">
                {error.stack}
              </pre>
            </div>
          )}
          {errorInfo?.componentStack && (
            <div>
              <strong>Component Stack:</strong>
              <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32">
                {errorInfo.componentStack}
              </pre>
            </div>
          )}
        </div>
      </details>
    )
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      const errorLevel = this.getErrorLevel()
      const canRetry = this.retryCount < this.maxRetries
      const { error } = this.state

      return (
        <div className="min-h-[400px] flex items-center justify-center p-4">
          <Card className="w-full max-w-lg">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4">
                <AlertTriangle 
                  className={`h-12 w-12 ${
                    errorLevel === 'high' ? 'text-red-500' :
                    errorLevel === 'medium' ? 'text-yellow-500' :
                    'text-orange-500'
                  }`} 
                />
              </div>
              <CardTitle className="text-xl">
                {errorLevel === 'high' ? 'Kritik Hata' :
                 errorLevel === 'medium' ? 'Bir Sorun Oluştu' :
                 'Beklenmeyen Hata'}
              </CardTitle>
              <CardDescription>
                {error?.name === 'ChunkLoadError' 
                  ? 'Uygulama güncellenmiş olabilir. Sayfayı yenilemeyi deneyin.'
                  : error?.message?.includes('Network')
                    ? 'Ağ bağlantısı sorunu. İnternet bağlantınızı kontrol edin.'
                    : 'Bir hata oluştu. Lütfen tekrar deneyin veya sayfayı yenileyin.'
                }
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-2">
                {canRetry && (
                  <Button 
                    onClick={this.handleRetry}
                    className="flex-1"
                    variant="default"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Tekrar Dene ({this.maxRetries - this.retryCount} kalan)
                  </Button>
                )}
                
                <Button 
                  onClick={this.handleReload}
                  variant="outline"
                  className="flex-1"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Sayfayı Yenile
                </Button>
                
                <Button 
                  onClick={this.handleGoHome}
                  variant="outline"
                  className="flex-1"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Ana Sayfa
                </Button>
              </div>

              {process.env.NODE_ENV === 'development' && (
                <Button 
                  onClick={() => console.log('Error details:', this.state)}
                  variant="ghost"
                  size="sm"
                  className="w-full"
                >
                  <Bug className="h-4 w-4 mr-2" />
                  Console'da Detayları Gör
                </Button>
              )}

              {this.renderErrorDetails()}
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

// HOC for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Hook for error reporting
export function useErrorHandler() {
  const reportError = (error: Error, context?: string) => {
    console.error(`🚨 [ERROR_HANDLER] ${context || 'Unhandled error'}:`, error)
    
    // Report to monitoring service
    if (process.env.NODE_ENV === 'production') {
      // window.Sentry?.captureException(error, { tags: { context } })
    }
  }

  return { reportError }
}
