/**
 * Optimized Projects Hook
 * High-performance project queries with caching and monitoring
 */

'use client'

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query'
import { supabaseBrowser as supabase } from '@/lib/supabase-browser'
import { Database } from '@/lib/supabase'
import { OptimizedQueries, QueryPerformanceMonitor } from '@/lib/database-optimization'
import { CACHE_CONFIGS, BrowserCache, OptimisticUpdates } from '@/lib/cache-strategies'
import { toast } from 'sonner'

type Project = Database['public']['Tables']['projects']['Row']
type ProjectInsert = Database['public']['Tables']['projects']['Insert']
type ProjectUpdate = Database['public']['Tables']['projects']['Update']

// Optimized projects list with pagination
export function useOptimizedProjects(options: {
  search?: string
  limit?: number
} = {}) {
  const { search, limit = 20 } = options

  return useQuery({
    queryKey: ['projects-optimized', { search, limit }],
    queryFn: async () => {
      console.log('🚀 [OPTIMIZED_PROJECTS] Fetching projects...')
      const endTimer = QueryPerformanceMonitor.startQuery('optimizedProjects')
      
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          throw new Error('User not authenticated')
        }

        const result = await OptimizedQueries.getProjects({
          userId: user.id,
          limit,
          search,
          orderBy: 'updated_at',
          ascending: false
        })

        console.log(`✅ [OPTIMIZED_PROJECTS] Fetched ${result.data?.length || 0} projects`)
        return result
      } finally {
        endTimer()
      }
    },
    staleTime: CACHE_CONFIGS.projects.staleTime,
    gcTime: CACHE_CONFIGS.projects.gcTime,
    enabled: true,
  })
}

// Infinite scroll projects
export function useInfiniteProjects(options: {
  search?: string
  limit?: number
} = {}) {
  const { search, limit = 20 } = options

  return useInfiniteQuery({
    queryKey: ['projects-infinite', { search, limit }],
    queryFn: async ({ pageParam = 0 }) => {
      const endTimer = QueryPerformanceMonitor.startQuery('infiniteProjects')
      
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          throw new Error('User not authenticated')
        }

        const result = await OptimizedQueries.getProjects({
          userId: user.id,
          limit,
          offset: pageParam * limit,
          search,
          orderBy: 'updated_at',
          ascending: false
        })

        return {
          data: result.data || [],
          nextPage: result.hasMore ? pageParam + 1 : undefined,
          hasMore: result.hasMore
        }
      } finally {
        endTimer()
      }
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    staleTime: CACHE_CONFIGS.projects.staleTime,
    gcTime: CACHE_CONFIGS.projects.gcTime,
    initialPageParam: 0,
  })
}

// Single project with optimized fields
export function useOptimizedProject(projectId: string | null) {
  return useQuery({
    queryKey: ['project-optimized', projectId],
    queryFn: async () => {
      if (!projectId) return null

      const endTimer = QueryPerformanceMonitor.startQuery('optimizedProject')
      
      try {
        // Check cache first
        const cacheKey = `project-${projectId}`
        const cached = BrowserCache.getMemory<Project>(cacheKey)
        if (cached) {
          endTimer()
          return cached
        }

        const { data, error } = await supabase
          .from('projects')
          .select(`
            id, name, description, created_at, updated_at, user_id,
            prompts(count),
            user:users(email)
          `)
          .eq('id', projectId)
          .single()

        if (error) {
          throw new Error(error.message)
        }

        // Cache for 5 minutes
        BrowserCache.setMemory(cacheKey, data, 5 * 60 * 1000)

        return data as unknown as Project
      } finally {
        endTimer()
      }
    },
    staleTime: CACHE_CONFIGS.projects.staleTime,
    gcTime: CACHE_CONFIGS.projects.gcTime,
    enabled: !!projectId,
  })
}

// User statistics with caching
export function useUserStats() {
  return useQuery({
    queryKey: ['user-stats'],
    queryFn: async () => {
      const endTimer = QueryPerformanceMonitor.startQuery('userStats')
      
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          throw new Error('User not authenticated')
        }

        return await OptimizedQueries.getUserStats(user.id)
      } finally {
        endTimer()
      }
    },
    staleTime: CACHE_CONFIGS.user.staleTime,
    gcTime: CACHE_CONFIGS.user.gcTime,
  })
}

// Optimized project creation
export function useCreateOptimizedProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (projectData: Omit<ProjectInsert, 'id' | 'created_at' | 'updated_at'>) => {
      const endTimer = QueryPerformanceMonitor.startQuery('createProject')
      
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          throw new Error('User not authenticated')
        }

        const { data, error } = await supabase
          .from('projects')
          .insert({
            ...projectData,
            user_id: user.id
          })
          .select()
          .single()

        if (error) {
          throw new Error(error.message)
        }

        return data as Project
      } finally {
        endTimer()
      }
    },
    onMutate: async (newProject) => {
      // Optimistic update
      await queryClient.cancelQueries({ queryKey: ['projects-optimized'] })
      
      const previousData = OptimisticUpdates.updateQueryData(
        queryClient,
        ['projects-optimized'],
        (old: any) => {
          if (!old) return { data: [], count: 1, hasMore: false }
          
          const tempProject = {
            id: 'temp-' + Date.now(),
            ...newProject,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            user_id: 'temp'
          }
          
          return {
            ...old,
            data: [tempProject, ...(old.data || [])],
            count: (old.count || 0) + 1
          }
        }
      )

      return { previousData }
    },
    onError: (err, newProject, context) => {
      // Rollback optimistic update
      if (context?.previousData) {
        OptimisticUpdates.rollbackQueryData(
          queryClient,
          ['projects-optimized'],
          context.previousData
        )
      }
      
      toast.error('Proje oluşturulamadı: ' + (err as Error).message)
    },
    onSuccess: (data) => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['projects-optimized'] })
      queryClient.invalidateQueries({ queryKey: ['user-stats'] })
      
      // Clear relevant caches
      BrowserCache.setMemory(`project-${data.id}`, data, 5 * 60 * 1000)
      
      toast.success('Proje başarıyla oluşturuldu!')
    },
  })
}

// Optimized project update
export function useUpdateOptimizedProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, ...updates }: ProjectUpdate & { id: string }) => {
      const endTimer = QueryPerformanceMonitor.startQuery('updateProject')
      
      try {
        const { data, error } = await supabase
          .from('projects')
          .update(updates)
          .eq('id', id)
          .select()
          .single()

        if (error) {
          throw new Error(error.message)
        }

        return data as Project
      } finally {
        endTimer()
      }
    },
    onSuccess: (data) => {
      // Update caches
      queryClient.setQueryData(['project-optimized', data.id], data)
      queryClient.invalidateQueries({ queryKey: ['projects-optimized'] })
      
      // Update browser cache
      BrowserCache.setMemory(`project-${data.id}`, data, 5 * 60 * 1000)
      
      toast.success('Proje güncellendi!')
    },
    onError: (err) => {
      toast.error('Proje güncellenemedi: ' + (err as Error).message)
    },
  })
}

// Optimized project deletion
export function useDeleteOptimizedProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (projectId: string) => {
      const endTimer = QueryPerformanceMonitor.startQuery('deleteProject')
      
      try {
        const { error } = await supabase
          .from('projects')
          .delete()
          .eq('id', projectId)

        if (error) {
          throw new Error(error.message)
        }

        return projectId
      } finally {
        endTimer()
      }
    },
    onSuccess: (projectId) => {
      // Remove from caches
      queryClient.removeQueries({ queryKey: ['project-optimized', projectId] })
      queryClient.invalidateQueries({ queryKey: ['projects-optimized'] })
      queryClient.invalidateQueries({ queryKey: ['user-stats'] })
      
      // Clear browser cache
      BrowserCache.setMemory(`project-${projectId}`, null, 0)
      
      toast.success('Proje silindi!')
    },
    onError: (err) => {
      toast.error('Proje silinemedi: ' + (err as Error).message)
    },
  })
}
