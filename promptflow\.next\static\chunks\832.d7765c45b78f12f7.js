"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[832,8451],{15501:(e,t,r)=>{r.d(t,{$I:()=>l,F$:()=>u,GQ:()=>c,Qu:()=>p,sW:()=>d});var a=r(32960),i=r(26715),o=r(5041),s=r(70478),n=r(86489);function u(e){return(0,a.I)({queryKey:["prompts",e],queryFn:async()=>{if(!e)return console.log("\uD83D\uDCDD [USE_PROMPTS] No project ID provided"),[];console.log("\uD83D\uDCDD [USE_PROMPTS] Fetching prompts for project:",e);try{var t;let{data:{session:r},error:a}=await s.L.auth.getSession();console.log("\uD83D\uDCDD [USE_PROMPTS] Session check:",{hasSession:!!r,sessionError:null==a?void 0:a.message,userId:null==r||null==(t=r.user)?void 0:t.id});let{data:i,error:o}=await s.L.from("prompts").select("*").eq("project_id",e).order("order_index",{ascending:!0});if(o)throw console.error("❌ [USE_PROMPTS] Error fetching prompts:",o),Error(o.message);return console.log("✅ [USE_PROMPTS] Prompts fetched:",(null==i?void 0:i.length)||0,"prompts"),i||[]}catch(e){throw console.error("\uD83D\uDCA5 [USE_PROMPTS] Exception:",e),e}},enabled:!!e})}function d(){let e=(0,i.jE)();return(0,o.n)({mutationFn:async e=>{let t=await (0,n.vw)(e.project_id);if(!t.allowed)throw Error(t.reason||n.x5.PROMPT_LIMIT_REACHED);let{data:{user:r},error:a}=await s.L.auth.getUser();if(a||!r)throw Error("Kullanıcı oturumu bulunamadı");let i=e.task_code||"task-".concat(e.order_index),{data:o,error:u}=await s.L.from("prompts").insert({...e,user_id:r.id,task_code:i,tags:e.tags||[],is_favorite:e.is_favorite||!1,usage_count:e.usage_count||0}).select().single();if(u)throw Error(u.message);return o},onMutate:async t=>{await e.cancelQueries({queryKey:["prompts",t.project_id]});let r=e.getQueryData(["prompts",t.project_id]),{data:{user:a}}=await s.L.auth.getUser();if(!a)return{previousPrompts:r};let i=e.getQueryData(["prompts",t.project_id])||[],o=(i.length>0?Math.max(...i.map(e=>e.order_index||0)):0)+1,n={id:"temp-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),...t,user_id:a.id,order_index:o,task_code:t.task_code||"task-".concat(o),tags:t.tags||[],is_favorite:t.is_favorite||!1,usage_count:0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};return e.setQueryData(["prompts",t.project_id],e=>Array.isArray(e)?[n,...e]:[n]),{previousPrompts:r,optimisticPrompt:n}},onError:(t,r,a)=>{(null==a?void 0:a.previousPrompts)&&e.setQueryData(["prompts",r.project_id],a.previousPrompts)},onSuccess:async(t,r,a)=>{e.setQueryData(["prompts",t.project_id],e=>{if(!Array.isArray(e))return[t];let r=e.filter(e=>{var r;return e.id!==(null==a||null==(r=a.optimisticPrompt)?void 0:r.id)&&e.id!==t.id});return[t,...r]}),setTimeout(()=>{e.invalidateQueries({queryKey:["popular-hashtags",t.project_id]}),e.invalidateQueries({queryKey:["all-hashtags",t.project_id]}),e.invalidateQueries({queryKey:["popular-categories",t.project_id]}),e.invalidateQueries({queryKey:["all-categories",t.project_id]}),(0,n.O3)().then(()=>{e.invalidateQueries({queryKey:["user-limits"]}),e.invalidateQueries({queryKey:["usage-stats"]})}).catch(e=>{console.warn("Kullanım istatistikleri g\xfcncellenemedi:",e)})},100)}})}function l(){let e=(0,i.jE)();return(0,o.n)({mutationFn:async e=>{let{id:t,...r}=e,{data:a,error:i}=await s.L.from("prompts").update({...r,updated_at:new Date().toISOString()}).eq("id",t).select().single();if(i)throw Error(i.message);return a},onSuccess:t=>{e.invalidateQueries({queryKey:["prompts",t.project_id]}),e.invalidateQueries({queryKey:["popular-hashtags",t.project_id]}),e.invalidateQueries({queryKey:["all-hashtags",t.project_id]}),e.invalidateQueries({queryKey:["popular-categories",t.project_id]}),e.invalidateQueries({queryKey:["all-categories",t.project_id]})}})}function p(){let e=(0,i.jE)();return(0,o.n)({mutationFn:async e=>{let{data:t,error:r}=await s.L.from("prompts").update({is_used:!0}).eq("id",e).select().single();if(r)throw Error(r.message);return t},onMutate:async t=>{let r=["prompts"];await e.cancelQueries({queryKey:r});let a=e.getQueriesData({queryKey:r});return e.setQueriesData({queryKey:r},e=>e&&Array.isArray(e)?e.map(e=>e.id===t?{...e,is_used:!0}:e):e),{previousPrompts:a}},onError:(t,r,a)=>{(null==a?void 0:a.previousPrompts)&&a.previousPrompts.forEach(t=>{let[r,a]=t;e.setQueryData(r,a)})},onSettled:t=>{t&&e.invalidateQueries({queryKey:["prompts",t.project_id]})}})}function c(){let e=(0,i.jE)();return(0,o.n)({mutationFn:async e=>{let t=e.filter(e=>void 0!==e.order_index||void 0!==e.task_code),r=e.filter(e=>void 0===e.order_index&&void 0===e.task_code),a=[];if(t.length>0)try{let{data:e,error:r}=await s.L.rpc("bulk_update_prompts_order",{prompt_updates:t});if(r){console.warn("RPC function failed, falling back to individual updates:",r);let e=t.map(async e=>{let{data:t,error:r}=await s.L.from("prompts").update({...e,updated_at:new Date().toISOString()}).eq("id",e.id).select().single();if(r)throw Error("Failed to update prompt ".concat(e.id,": ").concat(r.message));return t}),i=await Promise.all(e);a.push(...i)}else if(e){let t=e.map(e=>e.id),{data:r,error:i}=await s.L.from("prompts").select("*").in("id",t);if(i)throw Error("Failed to fetch updated prompts: ".concat(i.message));a.push(...r||[])}}catch(e){throw console.error("Bulk update error:",e),e}if(r.length>0){let e=r.map(async e=>{let{data:t,error:r}=await s.L.from("prompts").update({...e,updated_at:new Date().toISOString()}).eq("id",e.id).select().single();if(r)throw Error("Failed to update prompt ".concat(e.id,": ").concat(r.message));return t}),t=await Promise.all(e);a.push(...t)}return a},onMutate:async t=>{let r=["prompts"];await e.cancelQueries({queryKey:r});let a=e.getQueriesData({queryKey:r});return e.setQueriesData({queryKey:r},e=>e&&Array.isArray(e)?e.map(e=>{let r=t.find(t=>t.id===e.id);return r?{...e,...r}:e}):e),{previousPrompts:a}},onError:(t,r,a)=>{(null==a?void 0:a.previousPrompts)&&a.previousPrompts.forEach(t=>{let[r,a]=t;e.setQueryData(r,a)})},onSettled:t=>{t&&t.length>0&&e.invalidateQueries({queryKey:["prompts",t[0].project_id]})}})}},88539:(e,t,r)=>{r.d(t,{T:()=>o});var a=r(95155);r(12115);var i=r(59434);function o(e){let{className:t,...r}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,i.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}}}]);