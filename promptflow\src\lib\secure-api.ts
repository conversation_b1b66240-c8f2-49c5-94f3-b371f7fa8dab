/**
 * Secure API Wrapper
 * Comprehensive security layer for API calls
 */

import { AdvancedValidator } from './advanced-validation'
import { RateLimitType } from './rate-limiting'

export interface SecureApiOptions extends RequestInit {
  rateLimitType?: RateLimitType
  validateInput?: boolean
  sanitizeInput?: boolean
  timeout?: number
  retries?: number
  retryDelay?: number
}

export interface ApiResponse<T = unknown> {
  data?: T
  error?: string
  status: number
  headers: Headers
  rateLimited?: boolean
  validationErrors?: string[]
}

/**
 * Secure API client with built-in protections
 */
export class SecureApiClient {
  private baseUrl: string
  private defaultHeaders: HeadersInit

  constructor(baseUrl: string = '', defaultHeaders: HeadersInit = {}) {
    this.baseUrl = baseUrl
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...defaultHeaders
    }
  }

  /**
   * Get CSRF token (client-side only)
   */
  private getCSRFToken(): string | null {
    if (typeof window === 'undefined') return null

    // Try to get from meta tag first
    const metaToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
    if (metaToken) return metaToken

    // Try to get from cookie
    const cookieToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('csrf-token='))
      ?.split('=')[1]

    return cookieToken || null
  }

  /**
   * Client-side rate limiting check
   */
  private checkClientRateLimit(type: RateLimitType): boolean {
    if (typeof window === 'undefined') return true

    const key = `client-rate-limit-${type}`
    const stored = localStorage.getItem(key)
    const now = Date.now()

    // Rate limit configurations
    const configs = {
      auth: { windowMs: 15 * 60 * 1000, maxRequests: 5 },
      api: { windowMs: 60 * 1000, maxRequests: 100 },
      forms: { windowMs: 60 * 1000, maxRequests: 10 },
      uploads: { windowMs: 60 * 1000, maxRequests: 5 },
      admin: { windowMs: 60 * 1000, maxRequests: 50 }
    }

    const config = configs[type]

    if (!stored) {
      localStorage.setItem(key, JSON.stringify({
        count: 1,
        windowStart: now
      }))
      return true
    }

    try {
      const data = JSON.parse(stored)

      // Check if window expired
      if (now - data.windowStart > config.windowMs) {
        localStorage.setItem(key, JSON.stringify({
          count: 1,
          windowStart: now
        }))
        return true
      }

      // Check limit
      if (data.count >= config.maxRequests) {
        return false
      }

      // Increment and store
      data.count++
      localStorage.setItem(key, JSON.stringify(data))
      return true

    } catch {
      return true // Fail open
    }
  }

  /**
   * Secure API call with comprehensive protections
   */
  async call<T = unknown>(
    endpoint: string,
    options: SecureApiOptions = {}
  ): Promise<ApiResponse<T>> {
    const {
      rateLimitType = 'api',
      validateInput = true,
      sanitizeInput = true,
      timeout = 30000,
      retries = 3,
      retryDelay = 1000,
      ...fetchOptions
    } = options

    // Client-side rate limiting check
    if (!this.checkClientRateLimit(rateLimitType)) {
      return {
        status: 429,
        headers: new Headers(),
        error: 'Rate limit exceeded',
        rateLimited: true
      }
    }

    // Input validation and sanitization
    let body = fetchOptions.body

    if (body && validateInput) {
      try {
        if (typeof body === 'string') {
          const parsedBody = JSON.parse(body)
          const validationResult = this.validateRequestBody(parsedBody)
          
          if (!validationResult.isValid) {
            return {
              status: 400,
              headers: new Headers(),
              error: 'Input validation failed',
              validationErrors: validationResult.errors
            }
          }

          if (sanitizeInput && validationResult.sanitizedData) {
            body = JSON.stringify(validationResult.sanitizedData)
          }
        }
      } catch (error) {
        return {
          status: 400,
          headers: new Headers(),
          error: 'Invalid JSON in request body'
        }
      }
    }

    // Prepare headers with security measures
    const csrfToken = this.getCSRFToken()
    const headers = {
      ...this.defaultHeaders,
      ...fetchOptions.headers,
      ...(csrfToken && { 'x-csrf-token': csrfToken }),
      'X-Requested-With': 'XMLHttpRequest',
      'X-Client-Version': '1.0.0',
      'X-Timestamp': Date.now().toString()
    }

    // Create AbortController for timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    const url = this.baseUrl + endpoint

    // Retry logic
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const response = await fetch(url, {
          ...fetchOptions,
          body,
          headers,
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        // Handle rate limiting
        if (response.status === 429) {
          const retryAfter = response.headers.get('Retry-After')
          const delay = retryAfter ? parseInt(retryAfter) * 1000 : retryDelay * attempt
          
          if (attempt < retries) {
            await this.delay(delay)
            continue
          }
          
          return {
            status: 429,
            headers: response.headers,
            error: 'Rate limit exceeded',
            rateLimited: true
          }
        }

        // Parse response
        let data: T | undefined
        const contentType = response.headers.get('content-type')
        
        if (contentType?.includes('application/json')) {
          try {
            const jsonData = await response.json()
            data = sanitizeInput ? this.sanitizeResponseData(jsonData) : jsonData
          } catch (error) {
            return {
              status: response.status,
              headers: response.headers,
              error: 'Invalid JSON in response'
            }
          }
        }

        if (!response.ok) {
          return {
            status: response.status,
            headers: response.headers,
            error: data ? (data as Record<string, unknown>).error as string || 'Request failed' : 'Request failed',
            data
          }
        }

        return {
          status: response.status,
          headers: response.headers,
          data
        }

      } catch (error) {
        clearTimeout(timeoutId)
        
        if (error instanceof Error) {
          if (error.name === 'AbortError') {
            return {
              status: 408,
              headers: new Headers(),
              error: 'Request timeout'
            }
          }
          
          // Network errors - retry if not last attempt
          if (attempt < retries) {
            await this.delay(retryDelay * attempt)
            continue
          }
          
          return {
            status: 0,
            headers: new Headers(),
            error: error.message || 'Network error'
          }
        }
      }
    }

    return {
      status: 0,
      headers: new Headers(),
      error: 'Max retries exceeded'
    }
  }

  /**
   * Validate request body
   */
  private validateRequestBody(data: unknown): {
    isValid: boolean
    errors: string[]
    sanitizedData?: Record<string, unknown>
  } {
    const errors: string[] = []
    const sanitizedData = { ...(data as Record<string, unknown>) }

    // Basic validation for common fields
    const dataObj = data as Record<string, unknown>
    if (dataObj.email && typeof dataObj.email === 'string') {
      const emailValidation = AdvancedValidator.validate(dataObj.email, {
        required: true,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        maxLength: 254
      })
      
      if (!emailValidation.isValid) {
        errors.push(...emailValidation.errors)
      } else {
        sanitizedData.email = emailValidation.sanitizedValue
      }
    }

    if (dataObj.password && typeof dataObj.password === 'string') {
      const passwordValidation = AdvancedValidator.validate(dataObj.password, {
        required: true,
        minLength: 8,
        maxLength: 128
      })
      
      if (!passwordValidation.isValid) {
        errors.push(...passwordValidation.errors)
      }
      // Don't sanitize passwords, just validate
    }

    // Validate text fields
    const textFields = ['title', 'content', 'description', 'name']
    textFields.forEach(field => {
      if (dataObj[field] && typeof dataObj[field] === 'string') {
        const validation = AdvancedValidator.validate(dataObj[field] as string, {
          maxLength: field === 'content' ? 50000 : 1000,
          sanitizer: (value: string) => AdvancedValidator.sanitizeHtml(value, { stripTags: true })
        })
        
        if (!validation.isValid) {
          errors.push(...validation.errors)
        } else {
          sanitizedData[field] = validation.sanitizedValue
        }
      }
    })

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData: errors.length === 0 ? sanitizedData : undefined
    }
  }

  /**
   * Sanitize response data
   */
  private sanitizeResponseData(data: unknown): unknown {
    if (typeof data !== 'object' || data === null) {
      return data
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeResponseData(item))
    }

    const sanitized: Record<string, unknown> = {}
    for (const [key, value] of Object.entries(data as Record<string, unknown>)) {
      if (typeof value === 'string') {
        // Sanitize string values that might contain HTML
        sanitized[key] = AdvancedValidator.sanitizeHtml(value, { stripTags: true })
      } else if (typeof value === 'object') {
        sanitized[key] = this.sanitizeResponseData(value)
      } else {
        sanitized[key] = value
      }
    }

    return sanitized
  }

  /**
   * Delay utility for retries
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // Convenience methods
  async get<T>(endpoint: string, options?: Omit<SecureApiOptions, 'method'>): Promise<ApiResponse<T>> {
    return this.call<T>(endpoint, { ...options, method: 'GET' })
  }

  async post<T>(endpoint: string, data?: unknown, options?: Omit<SecureApiOptions, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.call<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    })
  }

  async put<T>(endpoint: string, data?: unknown, options?: Omit<SecureApiOptions, 'method' | 'body'>): Promise<ApiResponse<T>> {
    return this.call<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    })
  }

  async delete<T>(endpoint: string, options?: Omit<SecureApiOptions, 'method'>): Promise<ApiResponse<T>> {
    return this.call<T>(endpoint, { ...options, method: 'DELETE' })
  }
}

// Default secure API client instance
export const secureApi = new SecureApiClient('/api')

// React hook for secure API calls
export function useSecureApi() {
  return secureApi
}
