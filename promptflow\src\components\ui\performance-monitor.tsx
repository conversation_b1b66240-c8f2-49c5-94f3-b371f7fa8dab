/**
 * Performance Monitor Component
 * Real-time performance monitoring and metrics display
 */

'use client'

import { useState, useEffect, memo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Activity, 
  Database, 
  Clock, 
  Zap, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { QueryPerformanceMonitor, DatabasePerformance } from '@/lib/database-optimization'
import { BrowserCache, CachePerformance } from '@/lib/cache-strategies'
import { getBundleInfo } from '@/lib/dynamic-imports'

interface PerformanceMetrics {
  queries: Array<{
    query: string
    count: number
    avgTime: number
    slowQueryRate: number
  }>
  cache: {
    localStorage: { size: number; items: number }
    sessionStorage: { size: number; items: number }
    memory: { items: number }
  }
  bundle: {
    cacheSize: number
    loadedComponents: string[]
    memoryUsage: any
  } | null
  vitals: {
    lcp?: number
    fid?: number
    cls?: number
  }
}

const PerformanceMonitor = memo(function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [isVisible, setIsVisible] = useState(false)
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Collect performance metrics
  const collectMetrics = () => {
    const dbPerformance = DatabasePerformance.getPerformanceReport()
    const cacheInfo = BrowserCache.getCacheInfo()
    const bundleInfo = getBundleInfo()
    
    // Web Vitals
    const vitals: PerformanceMetrics['vitals'] = {}
    
    if (typeof window !== 'undefined' && 'performance' in window) {
      try {
        // Get LCP (Largest Contentful Paint) - use PerformanceObserver instead
        if ('PerformanceObserver' in window) {
          // Use modern PerformanceObserver API instead of deprecated getEntriesByType
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            if (entries.length > 0) {
              vitals.lcp = entries[entries.length - 1].startTime
            }
          })
          try {
            observer.observe({ entryTypes: ['largest-contentful-paint'] })
            // Disconnect after first measurement
            setTimeout(() => observer.disconnect(), 1000)
          } catch (e) {
            // Fallback to legacy API if needed
            const lcpEntries = performance.getEntriesByType('largest-contentful-paint')
            if (lcpEntries.length > 0) {
              vitals.lcp = lcpEntries[lcpEntries.length - 1].startTime
            }
          }
        }

        // Get navigation timing safely
        if (performance.timing) {
          vitals.fid = performance.timing.loadEventEnd - performance.timing.loadEventStart
        }

        // CLS would need layout shift observer
        vitals.cls = 0 // Placeholder
      } catch (error) {
        // Silently handle performance API errors
        console.debug('Performance monitoring error:', error)
      }
    }

    setMetrics({
      queries: dbPerformance.queryMetrics,
      cache: cacheInfo,
      bundle: bundleInfo,
      vitals
    })
  }

  // Auto-refresh metrics
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(collectMetrics, 5000) // Every 5 seconds
    collectMetrics() // Initial collection

    return () => clearInterval(interval)
  }, [autoRefresh])

  // Performance status indicators
  const getPerformanceStatus = (avgTime: number) => {
    if (avgTime < 100) return { color: 'green', icon: CheckCircle, label: 'Excellent' }
    if (avgTime < 500) return { color: 'yellow', icon: AlertTriangle, label: 'Good' }
    return { color: 'red', icon: XCircle, label: 'Needs Improvement' }
  }

  const getVitalsStatus = (metric: string, value?: number) => {
    if (!value) return { color: 'gray', label: 'N/A' }
    
    switch (metric) {
      case 'lcp':
        if (value < 2500) return { color: 'green', label: 'Good' }
        if (value < 4000) return { color: 'yellow', label: 'Needs Improvement' }
        return { color: 'red', label: 'Poor' }
      case 'fid':
        if (value < 100) return { color: 'green', label: 'Good' }
        if (value < 300) return { color: 'yellow', label: 'Needs Improvement' }
        return { color: 'red', label: 'Poor' }
      case 'cls':
        if (value < 0.1) return { color: 'green', label: 'Good' }
        if (value < 0.25) return { color: 'yellow', label: 'Needs Improvement' }
        return { color: 'red', label: 'Poor' }
      default:
        return { color: 'gray', label: 'Unknown' }
    }
  }

  if (!isVisible) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50 bg-white shadow-lg"
      >
        <Activity className="h-4 w-4 mr-2" />
        Performance
      </Button>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-[80vh] overflow-y-auto">
      <Card className="bg-white shadow-xl border-2">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Performance Monitor
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAutoRefresh(!autoRefresh)}
              >
                {autoRefresh ? 'Pause' : 'Resume'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={collectMetrics}
              >
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsVisible(false)}
              >
                ×
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Web Vitals */}
          <div>
            <h4 className="font-semibold flex items-center gap-2 mb-2">
              <Zap className="h-4 w-4" />
              Core Web Vitals
            </h4>
            <div className="grid grid-cols-3 gap-2">
              {Object.entries(metrics?.vitals || {}).map(([key, value]) => {
                const status = getVitalsStatus(key, value)
                return (
                  <div key={key} className="text-center">
                    <div className="text-xs text-gray-500 uppercase">{key}</div>
                    <div className="font-mono text-sm">
                      {value ? `${Math.round(value)}ms` : 'N/A'}
                    </div>
                    <Badge variant={status.color as any} className="text-xs">
                      {status.label}
                    </Badge>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Query Performance */}
          <div>
            <h4 className="font-semibold flex items-center gap-2 mb-2">
              <Database className="h-4 w-4" />
              Query Performance
            </h4>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {metrics?.queries.slice(0, 5).map((query, index) => {
                const status = getPerformanceStatus(query.avgTime)
                const StatusIcon = status.icon
                return (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <StatusIcon className={`h-3 w-3 text-${status.color}-500`} />
                      <span className="truncate font-mono text-xs">
                        {query.query}
                      </span>
                    </div>
                    <div className="flex gap-2 text-xs">
                      <span className="text-gray-500">{query.count}x</span>
                      <span className="font-mono">{Math.round(query.avgTime)}ms</span>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Cache Status */}
          <div>
            <h4 className="font-semibold flex items-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4" />
              Cache Status
            </h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <div className="text-xs text-gray-500">LocalStorage</div>
                <div className="font-mono">
                  {metrics?.cache.localStorage.items || 0} items
                </div>
                <div className="text-xs text-gray-400">
                  {Math.round((metrics?.cache.localStorage.size || 0) / 1024)}KB
                </div>
              </div>
              <div>
                <div className="text-xs text-gray-500">Memory Cache</div>
                <div className="font-mono">
                  {metrics?.cache.memory.items || 0} items
                </div>
              </div>
            </div>
          </div>

          {/* Bundle Info */}
          {metrics?.bundle && (
            <div>
              <h4 className="font-semibold flex items-center gap-2 mb-2">
                <Clock className="h-4 w-4" />
                Bundle Status
              </h4>
              <div className="text-sm space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-500">Loaded Components:</span>
                  <span className="font-mono">{metrics.bundle.loadedComponents.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Cache Size:</span>
                  <span className="font-mono">{metrics.bundle.cacheSize}</span>
                </div>
                {metrics.bundle.memoryUsage && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Memory Used:</span>
                    <span className="font-mono">
                      {Math.round(metrics.bundle.memoryUsage.used / 1024 / 1024)}MB
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="pt-2 border-t">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  BrowserCache.clearAll()
                  collectMetrics()
                }}
                className="flex-1"
              >
                Clear Cache
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  console.log('Performance Report:', metrics)
                }}
                className="flex-1"
              >
                Log Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
})

export default PerformanceMonitor
