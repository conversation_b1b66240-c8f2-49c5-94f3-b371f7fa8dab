/**
 * Database Query Optimization
 * Supabase query optimization, indexing and connection pooling
 */

import { supabase } from '@/lib/supabase'
import { BrowserCache, CachePerformance } from '@/lib/cache-strategies'

// Optimized query configurations
export const QUERY_CONFIGS = {
  // Pagination settings
  pagination: {
    defaultLimit: 20,
    maxLimit: 100,
    prefetchPages: 2
  },
  
  // Field selection for different use cases
  fields: {
    // Minimal fields for lists
    projectList: 'id, name, description, created_at, updated_at',
    promptList: 'id, title, content, hashtags, created_at, updated_at, project_id',
    userBasic: 'id, email, created_at',
    
    // Detailed fields for single items
    projectDetail: `
      id, name, description, created_at, updated_at, user_id,
      prompts(count),
      user:users(email)
    `,
    promptDetail: `
      id, title, content, hashtags, created_at, updated_at, project_id,
      project:projects(name),
      user:projects(user:users(email))
    `,
    
    // Aggregated data
    userStats: `
      id,
      projects(count),
      prompts:projects(prompts(count))
    `
  },
  
  // Index hints for complex queries
  indexHints: {
    projectsByUser: 'user_id, updated_at',
    promptsByProject: 'project_id, created_at',
    searchPrompts: 'title, content, hashtags'
  }
} as const

// Query performance monitoring
class QueryPerformanceMonitor {
  private static metrics = new Map<string, {
    count: number
    totalTime: number
    avgTime: number
    slowQueries: number
  }>()

  static startQuery(queryKey: string): () => void {
    const startTime = Date.now()
    
    return () => {
      const endTime = Date.now()
      const duration = endTime - startTime
      
      this.recordQuery(queryKey, duration)
      
      // Log slow queries
      if (duration > 1000) {
        console.warn(`🐌 [SLOW_QUERY] ${queryKey} took ${duration}ms`)
      }
    }
  }

  private static recordQuery(queryKey: string, duration: number): void {
    const existing = this.metrics.get(queryKey) || {
      count: 0,
      totalTime: 0,
      avgTime: 0,
      slowQueries: 0
    }

    existing.count++
    existing.totalTime += duration
    existing.avgTime = existing.totalTime / existing.count
    
    if (duration > 1000) {
      existing.slowQueries++
    }

    this.metrics.set(queryKey, existing)
    
    // Record in cache performance system
    if (duration < 100) {
      CachePerformance.recordCacheHit(queryKey, duration)
    } else {
      CachePerformance.recordCacheMiss(queryKey, duration)
    }
  }

  static getMetrics() {
    return Array.from(this.metrics.entries())
      .map(([key, metric]) => ({
        query: key,
        ...metric,
        slowQueryRate: metric.slowQueries / metric.count
      }))
      .sort((a, b) => b.avgTime - a.avgTime)
  }
}

// Optimized query builders
export class OptimizedQueries {
  // Projects with optimized field selection
  static async getProjects(options: {
    userId?: string
    limit?: number
    offset?: number
    search?: string
    orderBy?: 'created_at' | 'updated_at' | 'name'
    ascending?: boolean
  } = {}) {
    const endTimer = QueryPerformanceMonitor.startQuery('getProjects')
    
    try {
      const {
        userId,
        limit = QUERY_CONFIGS.pagination.defaultLimit,
        offset = 0,
        search,
        orderBy = 'updated_at',
        ascending = false
      } = options

      let query = supabase
        .from('projects')
        .select(QUERY_CONFIGS.fields.projectList)

      // Apply filters
      if (userId) {
        query = query.eq('user_id', userId)
      }

      if (search) {
        query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
      }

      // Apply ordering and pagination
      query = query
        .order(orderBy, { ascending })
        .range(offset, offset + limit - 1)

      const { data, error, count } = await query

      if (error) throw error

      return { data, count, hasMore: (count || 0) > offset + limit }
    } finally {
      endTimer()
    }
  }

  // Prompts with optimized joins
  static async getPrompts(options: {
    projectId?: string
    userId?: string
    limit?: number
    offset?: number
    search?: string
    hashtags?: string[]
  } = {}) {
    const endTimer = QueryPerformanceMonitor.startQuery('getPrompts')
    
    try {
      const {
        projectId,
        userId,
        limit = QUERY_CONFIGS.pagination.defaultLimit,
        offset = 0,
        search,
        hashtags
      } = options

      let query = supabase
        .from('prompts')
        .select(QUERY_CONFIGS.fields.promptList)

      // Apply filters
      if (projectId) {
        query = query.eq('project_id', projectId)
      }

      if (userId) {
        query = query.eq('user_id', userId)
      }

      if (search) {
        query = query.or(`title.ilike.%${search}%,content.ilike.%${search}%`)
      }

      if (hashtags && hashtags.length > 0) {
        query = query.overlaps('hashtags', hashtags)
      }

      // Apply ordering and pagination
      query = query
        .order('updated_at', { ascending: false })
        .range(offset, offset + limit - 1)

      const { data, error, count } = await query

      if (error) throw error

      return { data, count, hasMore: (count || 0) > offset + limit }
    } finally {
      endTimer()
    }
  }

  // User statistics with aggregation
  static async getUserStats(userId: string) {
    const endTimer = QueryPerformanceMonitor.startQuery('getUserStats')
    
    try {
      // Check cache first
      const cacheKey = `user-stats-${userId}`
      const cached = BrowserCache.getMemory<any>(cacheKey)
      if (cached) {
        endTimer()
        return cached
      }

      const { data, error } = await supabase
        .from('users')
        .select(QUERY_CONFIGS.fields.userStats)
        .eq('id', userId)
        .single()

      if (error) throw error

      // Process aggregated data
      const stats = {
        projectCount: data.projects?.[0]?.count || 0,
        promptCount: data.prompts?.reduce((total: number, project: any) => 
          total + (project.prompts?.[0]?.count || 0), 0) || 0
      }

      // Cache for 5 minutes
      BrowserCache.setMemory(cacheKey, stats, 5 * 60 * 1000)

      return stats
    } finally {
      endTimer()
    }
  }

  // Batch operations for better performance
  static async batchCreatePrompts(prompts: Array<{
    title: string
    content: string
    project_id: string
    hashtags?: string[]
  }>) {
    const endTimer = QueryPerformanceMonitor.startQuery('batchCreatePrompts')
    
    try {
      const { data, error } = await supabase
        .from('prompts')
        .insert(prompts)
        .select(QUERY_CONFIGS.fields.promptList)

      if (error) throw error

      return data
    } finally {
      endTimer()
    }
  }

  // Optimized search with full-text search
  static async searchContent(options: {
    query: string
    type?: 'projects' | 'prompts' | 'all'
    userId?: string
    limit?: number
  }) {
    const endTimer = QueryPerformanceMonitor.startQuery('searchContent')
    
    try {
      const { query: searchQuery, type = 'all', userId, limit = 20 } = options

      const results: any = { projects: [], prompts: [] }

      if (type === 'projects' || type === 'all') {
        let projectQuery = supabase
          .from('projects')
          .select(QUERY_CONFIGS.fields.projectList)
          .or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`)
          .limit(limit)

        if (userId) {
          projectQuery = projectQuery.eq('user_id', userId)
        }

        const { data: projects } = await projectQuery
        results.projects = projects || []
      }

      if (type === 'prompts' || type === 'all') {
        let promptQuery = supabase
          .from('prompts')
          .select(QUERY_CONFIGS.fields.promptList)
          .or(`title.ilike.%${searchQuery}%,content.ilike.%${searchQuery}%`)
          .limit(limit)

        if (userId) {
          promptQuery = promptQuery.eq('user_id', userId)
        }

        const { data: prompts } = await promptQuery
        results.prompts = prompts || []
      }

      return results
    } finally {
      endTimer()
    }
  }
}

// Connection pooling and optimization
export class ConnectionOptimizer {
  private static connectionPool = new Map<string, any>()
  private static maxConnections = 10
  private static connectionTimeout = 30000 // 30 seconds

  static async getOptimizedConnection(key: string = 'default') {
    const existing = this.connectionPool.get(key)
    
    if (existing && Date.now() - existing.created < this.connectionTimeout) {
      return existing.client
    }

    // Create new optimized connection
    const client = supabase
    
    this.connectionPool.set(key, {
      client,
      created: Date.now()
    })

    // Cleanup old connections
    if (this.connectionPool.size > this.maxConnections) {
      const oldest = Array.from(this.connectionPool.entries())
        .sort(([,a], [,b]) => a.created - b.created)[0]
      
      this.connectionPool.delete(oldest[0])
    }

    return client
  }

  static clearConnections() {
    this.connectionPool.clear()
  }
}

// Database performance monitoring
export class DatabasePerformance {
  static getPerformanceReport() {
    return {
      queryMetrics: QueryPerformanceMonitor.getMetrics(),
      connectionPool: {
        active: ConnectionOptimizer['connectionPool'].size,
        max: ConnectionOptimizer['maxConnections']
      },
      cacheMetrics: CachePerformance.getCacheMetrics()
    }
  }

  static logSlowQueries() {
    const metrics = QueryPerformanceMonitor.getMetrics()
    const slowQueries = metrics.filter(m => m.avgTime > 500)
    
    if (slowQueries.length > 0) {
      console.warn('🐌 [SLOW_QUERIES] Found slow queries:', slowQueries)
    }
  }
}

// Export optimized query hooks
export { QueryPerformanceMonitor }
