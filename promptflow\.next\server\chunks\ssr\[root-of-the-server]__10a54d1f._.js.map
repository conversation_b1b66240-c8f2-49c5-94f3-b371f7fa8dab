{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      data-slot=\"scroll-area\"\n      className={cn(\"relative\", className)}\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        data-slot=\"scroll-area-viewport\"\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = \"vertical\",\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      data-slot=\"scroll-area-scrollbar\"\n      orientation={orientation}\n      className={cn(\n        \"flex touch-none p-px transition-colors select-none\",\n        orientation === \"vertical\" &&\n          \"h-full w-2.5 border-l border-l-transparent\",\n        orientation === \"horizontal\" &&\n          \"h-2.5 flex-col border-t border-t-transparent\",\n        className\n      )}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        data-slot=\"scroll-area-thumb\"\n        className=\"bg-border relative flex-1 rounded-full\"\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/store/app-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { persist } from 'zustand/middleware'\r\n\r\ninterface AppState {\r\n  activeProjectId: string | null\r\n  setActiveProjectId: (projectId: string | null) => void\r\n  isContextEnabled: boolean\r\n  setIsContextEnabled: (enabled: boolean) => void\r\n  // Sidebar collapse states\r\n  isProjectSidebarCollapsed: boolean\r\n  setIsProjectSidebarCollapsed: (collapsed: boolean) => void\r\n  isContextSidebarCollapsed: boolean\r\n  setIsContextSidebarCollapsed: (collapsed: boolean) => void\r\n}\r\n\r\nexport const useAppStore = create<AppState>()(\r\n  persist(\r\n    (set) => ({\r\n      activeProjectId: null,\r\n      setActiveProjectId: (projectId) => set({ activeProjectId: projectId }),\r\n      isContextEnabled: true,\r\n      setIsContextEnabled: (enabled) => set({ isContextEnabled: enabled }),\r\n      // Sidebar collapse states\r\n      isProjectSidebarCollapsed: false,\r\n      setIsProjectSidebarCollapsed: (collapsed) => set({ isProjectSidebarCollapsed: collapsed }),\r\n      isContextSidebarCollapsed: false,\r\n      setIsContextSidebarCollapsed: (collapsed) => set({ isContextSidebarCollapsed: collapsed }),\r\n    }),\r\n    {\r\n      name: 'promptflow-app-store',\r\n    }\r\n  )\r\n)"], "names": [], "mappings": ";;;AAAA;AACA;;;AAcO,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,iBAAiB;QACjB,oBAAoB,CAAC,YAAc,IAAI;gBAAE,iBAAiB;YAAU;QACpE,kBAAkB;QAClB,qBAAqB,CAAC,UAAY,IAAI;gBAAE,kBAAkB;YAAQ;QAClE,0BAA0B;QAC1B,2BAA2B;QAC3B,8BAA8B,CAAC,YAAc,IAAI;gBAAE,2BAA2B;YAAU;QACxF,2BAA2B;QAC3B,8BAA8B,CAAC,YAAc,IAAI;gBAAE,2BAA2B;YAAU;IAC1F,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/plan-limits.ts"], "sourcesContent": ["// Plan Limitleri Kontrol Middleware ve Utility Fonksiyonları\n\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\nexport interface PlanLimits {\n  current_projects: number\n  current_prompts: number\n  max_projects: number\n  max_prompts_per_project: number\n  can_create_project: boolean\n  can_create_prompt: boolean\n}\n\nexport interface UserPlan {\n  plan_id: string\n  plan_name: string\n  display_name: string\n  max_projects: number\n  max_prompts_per_project: number\n  features: Record<string, boolean | string | number>\n  status: string\n  expires_at: string | null\n}\n\n// Plan limitlerini kontrol et\nexport async function checkUserLimits(): Promise<PlanLimits | null> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { data, error } = await supabase.rpc('check_user_limits', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Plan limitleri kontrol edilemedi:', error)\n      throw new Error(error.message)\n    }\n\n    return data?.[0] || null\n  } catch (error) {\n    console.error('Plan limitleri kontrol hatası:', error)\n    return null\n  }\n}\n\n// Kullanıcının aktif planını getir\nexport async function getUserActivePlan(): Promise<UserPlan | null> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { data, error } = await supabase.rpc('get_user_active_plan', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Aktif plan getirilemedi:', error)\n      throw new Error(error.message)\n    }\n\n    return data?.[0] || null\n  } catch (error) {\n    console.error('Aktif plan getirme hatası:', error)\n    return null\n  }\n}\n\n// Proje oluşturma limiti kontrol et\nexport async function canCreateProject(): Promise<{ allowed: boolean; reason?: string }> {\n  const limits = await checkUserLimits()\n  \n  if (!limits) {\n    return { allowed: false, reason: 'Plan bilgileri alınamadı' }\n  }\n\n  if (!limits.can_create_project) {\n    return { \n      allowed: false, \n      reason: `Proje limiti aşıldı (${limits.current_projects}/${limits.max_projects})` \n    }\n  }\n\n  return { allowed: true }\n}\n\n// Prompt oluşturma limiti kontrol et\nexport async function canCreatePrompt(projectId?: string): Promise<{ allowed: boolean; reason?: string }> {\n  const limits = await checkUserLimits()\n  \n  if (!limits) {\n    return { allowed: false, reason: 'Plan bilgileri alınamadı' }\n  }\n\n  if (!limits.can_create_prompt) {\n    return { \n      allowed: false, \n      reason: `Prompt limiti aşıldı (${limits.current_prompts}/${limits.max_prompts_per_project})` \n    }\n  }\n\n  // Eğer belirli bir proje için kontrol ediliyorsa, o projenin prompt sayısını kontrol et\n  if (projectId) {\n    try {\n      const { count, error } = await supabase\n        .from('prompts')\n        .select('*', { count: 'exact', head: true })\n        .eq('project_id', projectId)\n\n      if (error) {\n        console.error('Proje prompt sayısı kontrol edilemedi:', error)\n        return { allowed: false, reason: 'Proje bilgileri alınamadı' }\n      }\n\n      const currentPrompts = count || 0\n      if (limits.max_prompts_per_project !== -1 && currentPrompts >= limits.max_prompts_per_project) {\n        return { \n          allowed: false, \n          reason: `Bu proje için prompt limiti aşıldı (${currentPrompts}/${limits.max_prompts_per_project})` \n        }\n      }\n    } catch (error) {\n      console.error('Proje prompt sayısı kontrol hatası:', error)\n      return { allowed: false, reason: 'Proje bilgileri kontrol edilemedi' }\n    }\n  }\n\n  return { allowed: true }\n}\n\n// Plan özelliği kontrol et\nexport async function hasPlanFeature(featureName: string): Promise<boolean> {\n  const plan = await getUserActivePlan()\n  return plan?.features?.[featureName] === true\n}\n\n// Kullanım istatistiklerini güncelle\nexport async function updateUsageStats(): Promise<void> {\n  try {\n    const { data: user } = await supabase.auth.getUser()\n    if (!user.user) {\n      throw new Error('Kullanıcı oturumu bulunamadı')\n    }\n\n    const { error } = await supabase.rpc('update_usage_stats', {\n      user_uuid: user.user.id\n    })\n\n    if (error) {\n      console.error('Kullanım istatistikleri güncellenemedi:', error)\n      throw new Error(error.message)\n    }\n  } catch (error) {\n    console.error('Kullanım istatistikleri güncelleme hatası:', error)\n    throw error\n  }\n}\n\n// Plan upgrade önerisi\nexport function getPlanUpgradeSuggestion(limits: PlanLimits): {\n  shouldUpgrade: boolean\n  reason: string\n  suggestedPlan: string\n} {\n  // Proje limiti %80'e ulaştıysa\n  if (limits.max_projects !== -1 && limits.current_projects / limits.max_projects >= 0.8) {\n    return {\n      shouldUpgrade: true,\n      reason: 'Proje limitinizin %80\\'ine ulaştınız',\n      suggestedPlan: 'professional'\n    }\n  }\n\n  // Prompt limiti %80'e ulaştıysa\n  if (limits.max_prompts_per_project !== -1 && limits.current_prompts / limits.max_prompts_per_project >= 0.8) {\n    return {\n      shouldUpgrade: true,\n      reason: 'Prompt limitinizin %80\\'ine ulaştınız',\n      suggestedPlan: 'professional'\n    }\n  }\n\n  return {\n    shouldUpgrade: false,\n    reason: '',\n    suggestedPlan: ''\n  }\n}\n\n// Plan limiti hata mesajları\nexport const PLAN_LIMIT_MESSAGES = {\n  PROJECT_LIMIT_REACHED: 'Proje oluşturma limitinize ulaştınız. Daha fazla proje oluşturmak için planınızı yükseltin.',\n  PROMPT_LIMIT_REACHED: 'Prompt oluşturma limitinize ulaştınız. Daha fazla prompt oluşturmak için planınızı yükseltin.',\n  FEATURE_NOT_AVAILABLE: 'Bu özellik mevcut planınızda bulunmamaktadır. Planınızı yükseltin.',\n  PLAN_EXPIRED: 'Planınızın süresi dolmuştur. Lütfen planınızı yenileyin.',\n  PLAN_SUSPENDED: 'Hesabınız askıya alınmıştır. Lütfen destek ekibi ile iletişime geçin.'\n}\n\n// Plan durumu kontrol et\nexport async function checkPlanStatus(): Promise<{\n  isActive: boolean\n  status: string\n  message?: string\n}> {\n  const plan = await getUserActivePlan()\n  \n  if (!plan) {\n    return {\n      isActive: false,\n      status: 'no_plan',\n      message: 'Aktif plan bulunamadı'\n    }\n  }\n\n  if (plan.status !== 'active') {\n    return {\n      isActive: false,\n      status: plan.status,\n      message: PLAN_LIMIT_MESSAGES.PLAN_SUSPENDED\n    }\n  }\n\n  if (plan.expires_at && new Date(plan.expires_at) < new Date()) {\n    return {\n      isActive: false,\n      status: 'expired',\n      message: PLAN_LIMIT_MESSAGES.PLAN_EXPIRED\n    }\n  }\n\n  return {\n    isActive: true,\n    status: 'active'\n  }\n}\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;AAE7D;;AAuBO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,qBAAqB;YAC9D,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,MAAM,CAAC,EAAE,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,wBAAwB;YACjE,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;QAEA,OAAO,MAAM,CAAC,EAAE,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,SAAS,MAAM;IAErB,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,QAAQ;QAA2B;IAC9D;IAEA,IAAI,CAAC,OAAO,kBAAkB,EAAE;QAC9B,OAAO;YACL,SAAS;YACT,QAAQ,CAAC,qBAAqB,EAAE,OAAO,gBAAgB,CAAC,CAAC,EAAE,OAAO,YAAY,CAAC,CAAC,CAAC;QACnF;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,gBAAgB,SAAkB;IACtD,MAAM,SAAS,MAAM;IAErB,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,QAAQ;QAA2B;IAC9D;IAEA,IAAI,CAAC,OAAO,iBAAiB,EAAE;QAC7B,OAAO;YACL,SAAS;YACT,QAAQ,CAAC,sBAAsB,EAAE,OAAO,eAAe,CAAC,CAAC,EAAE,OAAO,uBAAuB,CAAC,CAAC,CAAC;QAC9F;IACF;IAEA,wFAAwF;IACxF,IAAI,WAAW;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACpC,IAAI,CAAC,WACL,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK,GACzC,EAAE,CAAC,cAAc;YAEpB,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,OAAO;oBAAE,SAAS;oBAAO,QAAQ;gBAA4B;YAC/D;YAEA,MAAM,iBAAiB,SAAS;YAChC,IAAI,OAAO,uBAAuB,KAAK,CAAC,KAAK,kBAAkB,OAAO,uBAAuB,EAAE;gBAC7F,OAAO;oBACL,SAAS;oBACT,QAAQ,CAAC,oCAAoC,EAAE,eAAe,CAAC,EAAE,OAAO,uBAAuB,CAAC,CAAC,CAAC;gBACpG;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;gBAAE,SAAS;gBAAO,QAAQ;YAAoC;QACvE;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,eAAe,eAAe,WAAmB;IACtD,MAAM,OAAO,MAAM;IACnB,OAAO,MAAM,UAAU,CAAC,YAAY,KAAK;AAC3C;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,sBAAsB;YACzD,WAAW,KAAK,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,MAAM;IACR;AACF;AAGO,SAAS,yBAAyB,MAAkB;IAKzD,+BAA+B;IAC/B,IAAI,OAAO,YAAY,KAAK,CAAC,KAAK,OAAO,gBAAgB,GAAG,OAAO,YAAY,IAAI,KAAK;QACtF,OAAO;YACL,eAAe;YACf,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,gCAAgC;IAChC,IAAI,OAAO,uBAAuB,KAAK,CAAC,KAAK,OAAO,eAAe,GAAG,OAAO,uBAAuB,IAAI,KAAK;QAC3G,OAAO;YACL,eAAe;YACf,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,OAAO;QACL,eAAe;QACf,QAAQ;QACR,eAAe;IACjB;AACF;AAGO,MAAM,sBAAsB;IACjC,uBAAuB;IACvB,sBAAsB;IACtB,uBAAuB;IACvB,cAAc;IACd,gBAAgB;AAClB;AAGO,eAAe;IAKpB,MAAM,OAAO,MAAM;IAEnB,IAAI,CAAC,MAAM;QACT,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS;QACX;IACF;IAEA,IAAI,KAAK,MAAM,KAAK,UAAU;QAC5B,OAAO;YACL,UAAU;YACV,QAAQ,KAAK,MAAM;YACnB,SAAS,oBAAoB,cAAc;QAC7C;IACF;IAEA,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,QAAQ;QAC7D,OAAO;YACL,UAAU;YACV,QAAQ;YACR,SAAS,oBAAoB,YAAY;QAC3C;IACF;IAEA,OAAO;QACL,UAAU;QACV,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/project-validation.ts"], "sourcesContent": ["/**\n * Proje adı validation ve güvenlik utilities\n * Güvenlik odaklı input validation ve sanitization\n */\n\n// Proje adı limitleri ve kuralları\nexport const PROJECT_NAME_RULES = {\n  minLength: 3,\n  maxLength: 50,\n  allowedCharsRegex: /^[a-zA-Z0-9\\s\\-_.]+$/,\n  debounceMs: 300,\n  rateLimit: {\n    maxRequests: 10,\n    windowMinutes: 1\n  }\n} as const;\n\n// Validation sonuç tipi\nexport interface ValidationResult {\n  isValid: boolean;\n  error?: string;\n  sanitizedValue?: string;\n}\n\n// Rate limiting için local storage key\nconst RATE_LIMIT_KEY = 'project_name_update_rate_limit';\n\n/**\n * Proje adını sanitize eder\n */\nexport function sanitizeProjectName(name: string): string {\n  if (!name) return '';\n  \n  // Trim ve normalize\n  let sanitized = name.trim();\n  \n  // Çoklu boşlukları tek boşluğa çevir\n  sanitized = sanitized.replace(/\\s+/g, ' ');\n  \n  // Başlangıç ve bitiş boşluklarını kaldır\n  sanitized = sanitized.trim();\n  \n  return sanitized;\n}\n\n/**\n * Proje adını validate eder\n */\nexport function validateProjectName(name: string): ValidationResult {\n  const sanitized = sanitizeProjectName(name);\n  \n  // Boş kontrol\n  if (!sanitized) {\n    return {\n      isValid: false,\n      error: 'Proje adı boş olamaz'\n    };\n  }\n  \n  // Uzunluk kontrol\n  if (sanitized.length < PROJECT_NAME_RULES.minLength) {\n    return {\n      isValid: false,\n      error: `Proje adı en az ${PROJECT_NAME_RULES.minLength} karakter olmalıdır`\n    };\n  }\n  \n  if (sanitized.length > PROJECT_NAME_RULES.maxLength) {\n    return {\n      isValid: false,\n      error: `Proje adı en fazla ${PROJECT_NAME_RULES.maxLength} karakter olabilir`\n    };\n  }\n  \n  // Karakter kontrol\n  if (!PROJECT_NAME_RULES.allowedCharsRegex.test(sanitized)) {\n    return {\n      isValid: false,\n      error: 'Proje adı sadece harf, rakam, boşluk ve -_. karakterlerini içerebilir'\n    };\n  }\n  \n  // Sadece boşluk kontrolü\n  if (sanitized.replace(/\\s/g, '').length === 0) {\n    return {\n      isValid: false,\n      error: 'Proje adı sadece boşluk karakterlerinden oluşamaz'\n    };\n  }\n  \n  return {\n    isValid: true,\n    sanitizedValue: sanitized\n  };\n}\n\n/**\n * XSS koruması için HTML encode\n */\nexport function escapeHtml(text: string): string {\n  const div = document.createElement('div');\n  div.textContent = text;\n  return div.innerHTML;\n}\n\n/**\n * Client-side rate limiting kontrolü\n */\nexport function checkClientRateLimit(): boolean {\n  try {\n    const stored = localStorage.getItem(RATE_LIMIT_KEY);\n    const now = Date.now();\n    \n    if (!stored) {\n      // İlk istek\n      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n        count: 1,\n        windowStart: now\n      }));\n      return true;\n    }\n    \n    const data = JSON.parse(stored);\n    const windowDuration = PROJECT_NAME_RULES.rateLimit.windowMinutes * 60 * 1000; // ms\n    \n    // Pencere süresi dolmuş mu?\n    if (now - data.windowStart > windowDuration) {\n      // Yeni pencere başlat\n      localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n        count: 1,\n        windowStart: now\n      }));\n      return true;\n    }\n    \n    // Limit kontrolü\n    if (data.count >= PROJECT_NAME_RULES.rateLimit.maxRequests) {\n      return false;\n    }\n    \n    // Sayacı artır\n    localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify({\n      count: data.count + 1,\n      windowStart: data.windowStart\n    }));\n    \n    return true;\n  } catch (error) {\n    console.warn('Rate limit check failed:', error);\n    return true; // Hata durumunda izin ver\n  }\n}\n\n/**\n * Rate limit reset\n */\nexport function resetClientRateLimit(): void {\n  try {\n    localStorage.removeItem(RATE_LIMIT_KEY);\n  } catch (error) {\n    console.warn('Rate limit reset failed:', error);\n  }\n}\n\n/**\n * Debounced validation hook için utility\n */\nexport function createDebouncedValidator(\n  validator: (value: string) => ValidationResult,\n  delay: number = PROJECT_NAME_RULES.debounceMs\n) {\n  let timeoutId: NodeJS.Timeout;\n  \n  return (value: string, callback: (result: ValidationResult) => void) => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(() => {\n      const result = validator(value);\n      callback(result);\n    }, delay);\n  };\n}\n\n/**\n * Güvenli proje adı karşılaştırması\n */\nexport function isSameProjectName(name1: string, name2: string): boolean {\n  const sanitized1 = sanitizeProjectName(name1).toLowerCase();\n  const sanitized2 = sanitizeProjectName(name2).toLowerCase();\n  return sanitized1 === sanitized2;\n}\n\n/**\n * Duplicate name kontrolü için async validation\n */\nexport async function validateProjectNameUnique(\n  name: string,\n  currentProjectId: string,\n  projects: Array<{ id: string; name: string }>\n): Promise<ValidationResult> {\n  const sanitized = sanitizeProjectName(name);\n\n  // Önce temel validation\n  const basicValidation = validateProjectName(name);\n  if (!basicValidation.isValid) {\n    return basicValidation;\n  }\n\n  // Duplicate kontrolü (case-insensitive)\n  const isDuplicate = projects.some(project =>\n    project.id !== currentProjectId &&\n    isSameProjectName(project.name, sanitized)\n  );\n\n  if (isDuplicate) {\n    return {\n      isValid: false,\n      error: 'Bu isimde bir proje zaten mevcut'\n    };\n  }\n\n  return {\n    isValid: true,\n    sanitizedValue: sanitized\n  };\n}\n\n/**\n * Error mesajlarını kullanıcı dostu hale getir\n */\nexport function formatValidationError(error: string): string {\n  // Teknik hataları kullanıcı dostu mesajlara çevir\n  const errorMap: Record<string, string> = {\n    'unique_violation': 'Bu isimde bir proje zaten mevcut',\n    'check_violation': 'Proje adı geçersiz karakterler içeriyor',\n    'not_null_violation': 'Proje adı boş olamaz',\n    'foreign_key_violation': 'Geçersiz proje referansı',\n    'RateLimitExceeded': 'Çok fazla güncelleme isteği. Lütfen bekleyin.',\n    'InvalidInput': 'Geçersiz proje adı',\n    'DuplicateName': 'Bu isimde bir proje zaten mevcut',\n    'NotFound': 'Proje bulunamadı',\n    'Unauthorized': 'Bu işlem için yetkiniz yok'\n  };\n\n  return errorMap[error] || error;\n}\n\n/**\n * Advanced debounced validator with duplicate check\n */\nexport function createAdvancedDebouncedValidator(\n  projects: Array<{ id: string; name: string }>,\n  currentProjectId: string,\n  delay: number = PROJECT_NAME_RULES.debounceMs\n) {\n  let timeoutId: NodeJS.Timeout;\n\n  return (value: string, callback: (result: ValidationResult) => void) => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(async () => {\n      const result = await validateProjectNameUnique(value, currentProjectId, projects);\n      callback(result);\n    }, delay);\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,mCAAmC;;;;;;;;;;;;;;AAC5B,MAAM,qBAAqB;IAChC,WAAW;IACX,WAAW;IACX,mBAAmB;IACnB,YAAY;IACZ,WAAW;QACT,aAAa;QACb,eAAe;IACjB;AACF;AASA,uCAAuC;AACvC,MAAM,iBAAiB;AAKhB,SAAS,oBAAoB,IAAY;IAC9C,IAAI,CAAC,MAAM,OAAO;IAElB,oBAAoB;IACpB,IAAI,YAAY,KAAK,IAAI;IAEzB,qCAAqC;IACrC,YAAY,UAAU,OAAO,CAAC,QAAQ;IAEtC,yCAAyC;IACzC,YAAY,UAAU,IAAI;IAE1B,OAAO;AACT;AAKO,SAAS,oBAAoB,IAAY;IAC9C,MAAM,YAAY,oBAAoB;IAEtC,cAAc;IACd,IAAI,CAAC,WAAW;QACd,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,kBAAkB;IAClB,IAAI,UAAU,MAAM,GAAG,mBAAmB,SAAS,EAAE;QACnD,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gBAAgB,EAAE,mBAAmB,SAAS,CAAC,mBAAmB,CAAC;QAC7E;IACF;IAEA,IAAI,UAAU,MAAM,GAAG,mBAAmB,SAAS,EAAE;QACnD,OAAO;YACL,SAAS;YACT,OAAO,CAAC,mBAAmB,EAAE,mBAAmB,SAAS,CAAC,kBAAkB,CAAC;QAC/E;IACF;IAEA,mBAAmB;IACnB,IAAI,CAAC,mBAAmB,iBAAiB,CAAC,IAAI,CAAC,YAAY;QACzD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,IAAI,UAAU,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;QAC7C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,gBAAgB;IAClB;AACF;AAKO,SAAS,WAAW,IAAY;IACrC,MAAM,MAAM,SAAS,aAAa,CAAC;IACnC,IAAI,WAAW,GAAG;IAClB,OAAO,IAAI,SAAS;AACtB;AAKO,SAAS;IACd,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAI,CAAC,QAAQ;YACX,YAAY;YACZ,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBAClD,OAAO;gBACP,aAAa;YACf;YACA,OAAO;QACT;QAEA,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,MAAM,iBAAiB,mBAAmB,SAAS,CAAC,aAAa,GAAG,KAAK,MAAM,KAAK;QAEpF,4BAA4B;QAC5B,IAAI,MAAM,KAAK,WAAW,GAAG,gBAAgB;YAC3C,sBAAsB;YACtB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBAClD,OAAO;gBACP,aAAa;YACf;YACA,OAAO;QACT;QAEA,iBAAiB;QACjB,IAAI,KAAK,KAAK,IAAI,mBAAmB,SAAS,CAAC,WAAW,EAAE;YAC1D,OAAO;QACT;QAEA,eAAe;QACf,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;YAClD,OAAO,KAAK,KAAK,GAAG;YACpB,aAAa,KAAK,WAAW;QAC/B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;QACzC,OAAO,MAAM,0BAA0B;IACzC;AACF;AAKO,SAAS;IACd,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4BAA4B;IAC3C;AACF;AAKO,SAAS,yBACd,SAA8C,EAC9C,QAAgB,mBAAmB,UAAU;IAE7C,IAAI;IAEJ,OAAO,CAAC,OAAe;QACrB,aAAa;QACb,YAAY,WAAW;YACrB,MAAM,SAAS,UAAU;YACzB,SAAS;QACX,GAAG;IACL;AACF;AAKO,SAAS,kBAAkB,KAAa,EAAE,KAAa;IAC5D,MAAM,aAAa,oBAAoB,OAAO,WAAW;IACzD,MAAM,aAAa,oBAAoB,OAAO,WAAW;IACzD,OAAO,eAAe;AACxB;AAKO,eAAe,0BACpB,IAAY,EACZ,gBAAwB,EACxB,QAA6C;IAE7C,MAAM,YAAY,oBAAoB;IAEtC,wBAAwB;IACxB,MAAM,kBAAkB,oBAAoB;IAC5C,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;IACT;IAEA,wCAAwC;IACxC,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,UAChC,QAAQ,EAAE,KAAK,oBACf,kBAAkB,QAAQ,IAAI,EAAE;IAGlC,IAAI,aAAa;QACf,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,gBAAgB;IAClB;AACF;AAKO,SAAS,sBAAsB,KAAa;IACjD,kDAAkD;IAClD,MAAM,WAAmC;QACvC,oBAAoB;QACpB,mBAAmB;QACnB,sBAAsB;QACtB,yBAAyB;QACzB,qBAAqB;QACrB,gBAAgB;QAChB,iBAAiB;QACjB,YAAY;QACZ,gBAAgB;IAClB;IAEA,OAAO,QAAQ,CAAC,MAAM,IAAI;AAC5B;AAKO,SAAS,iCACd,QAA6C,EAC7C,gBAAwB,EACxB,QAAgB,mBAAmB,UAAU;IAE7C,IAAI;IAEJ,OAAO,CAAC,OAAe;QACrB,aAAa;QACb,YAAY,WAAW;YACrB,MAAM,SAAS,MAAM,0BAA0B,OAAO,kBAAkB;YACxE,SAAS;QACX,GAAG;IACL;AACF", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/rate-limiter.ts"], "sourcesContent": ["'use client'\n\n/**\n * Advanced Rate Limiting System for PromptFlow\n * Provides both client-side and server-side rate limiting\n */\n\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\n\n// Rate limiting configurations for different actions\nexport const RATE_LIMITS = {\n  // Authentication actions\n  login: { maxRequests: 5, windowMinutes: 15 },\n  signup: { maxRequests: 3, windowMinutes: 60 },\n  password_reset: { maxRequests: 3, windowMinutes: 60 },\n  \n  // Project actions\n  project_create: { maxRequests: 10, windowMinutes: 60 },\n  project_update: { maxRequests: 20, windowMinutes: 10 },\n  project_delete: { maxRequests: 5, windowMinutes: 60 },\n  \n  // Prompt actions\n  prompt_create: { maxRequests: 50, windowMinutes: 10 },\n  prompt_update: { maxRequests: 100, windowMinutes: 10 },\n  prompt_delete: { maxRequests: 20, windowMinutes: 10 },\n  \n  // Context actions\n  context_create: { maxRequests: 20, windowMinutes: 10 },\n  context_batch_add: { maxRequests: 5, windowMinutes: 10 },\n  \n  // Plan actions\n  plan_change: { maxRequests: 3, windowMinutes: 60 },\n  \n  // General API\n  api_general: { maxRequests: 200, windowMinutes: 10 },\n} as const\n\nexport type RateLimitAction = keyof typeof RATE_LIMITS\n\ninterface RateLimitData {\n  count: number\n  windowStart: number\n  lastRequest: number\n}\n\n/**\n * Client-side rate limiting with localStorage\n */\nexport class ClientRateLimiter {\n  private getStorageKey(action: RateLimitAction, userId?: string): string {\n    const userSuffix = userId ? `_${userId}` : ''\n    return `rate_limit_${action}${userSuffix}`\n  }\n\n  /**\n   * Check if action is allowed based on rate limits\n   */\n  async checkLimit(action: RateLimitAction, userId?: string): Promise<{\n    allowed: boolean\n    remainingRequests: number\n    resetTime: number\n    retryAfter?: number\n  }> {\n    try {\n      const config = RATE_LIMITS[action]\n      const storageKey = this.getStorageKey(action, userId)\n      const now = Date.now()\n      const windowMs = config.windowMinutes * 60 * 1000\n\n      // Get current data\n      const stored = localStorage.getItem(storageKey)\n      let data: RateLimitData\n\n      if (!stored) {\n        // First request\n        data = {\n          count: 1,\n          windowStart: now,\n          lastRequest: now\n        }\n        localStorage.setItem(storageKey, JSON.stringify(data))\n        \n        return {\n          allowed: true,\n          remainingRequests: config.maxRequests - 1,\n          resetTime: now + windowMs\n        }\n      }\n\n      data = JSON.parse(stored)\n\n      // Check if window has expired\n      if (now - data.windowStart > windowMs) {\n        // Reset window\n        data = {\n          count: 1,\n          windowStart: now,\n          lastRequest: now\n        }\n        localStorage.setItem(storageKey, JSON.stringify(data))\n        \n        return {\n          allowed: true,\n          remainingRequests: config.maxRequests - 1,\n          resetTime: now + windowMs\n        }\n      }\n\n      // Check if limit exceeded\n      if (data.count >= config.maxRequests) {\n        const resetTime = data.windowStart + windowMs\n        const retryAfter = Math.ceil((resetTime - now) / 1000)\n        \n        return {\n          allowed: false,\n          remainingRequests: 0,\n          resetTime,\n          retryAfter\n        }\n      }\n\n      // Increment counter\n      data.count++\n      data.lastRequest = now\n      localStorage.setItem(storageKey, JSON.stringify(data))\n\n      return {\n        allowed: true,\n        remainingRequests: config.maxRequests - data.count,\n        resetTime: data.windowStart + windowMs\n      }\n    } catch (error) {\n      console.warn(`Rate limit check failed for ${action}:`, error)\n      // On error, allow the request but log it\n      return {\n        allowed: true,\n        remainingRequests: 0,\n        resetTime: Date.now() + 60000 // 1 minute fallback\n      }\n    }\n  }\n\n  /**\n   * Reset rate limit for specific action\n   */\n  resetLimit(action: RateLimitAction, userId?: string): void {\n    try {\n      const storageKey = this.getStorageKey(action, userId)\n      localStorage.removeItem(storageKey)\n    } catch (error) {\n      console.warn(`Rate limit reset failed for ${action}:`, error)\n    }\n  }\n\n  /**\n   * Get current rate limit status\n   */\n  getStatus(action: RateLimitAction, userId?: string): {\n    count: number\n    remaining: number\n    resetTime: number\n  } | null {\n    try {\n      const config = RATE_LIMITS[action]\n      const storageKey = this.getStorageKey(action, userId)\n      const stored = localStorage.getItem(storageKey)\n      \n      if (!stored) {\n        return {\n          count: 0,\n          remaining: config.maxRequests,\n          resetTime: Date.now() + config.windowMinutes * 60 * 1000\n        }\n      }\n\n      const data: RateLimitData = JSON.parse(stored)\n      const windowMs = config.windowMinutes * 60 * 1000\n      const now = Date.now()\n\n      // Check if window expired\n      if (now - data.windowStart > windowMs) {\n        return {\n          count: 0,\n          remaining: config.maxRequests,\n          resetTime: now + windowMs\n        }\n      }\n\n      return {\n        count: data.count,\n        remaining: Math.max(0, config.maxRequests - data.count),\n        resetTime: data.windowStart + windowMs\n      }\n    } catch (error) {\n      console.warn(`Rate limit status check failed for ${action}:`, error)\n      return null\n    }\n  }\n}\n\n/**\n * Server-side rate limiting with Supabase\n */\nexport class ServerRateLimiter {\n  /**\n   * Check server-side rate limit using Supabase function\n   */\n  async checkLimit(\n    action: RateLimitAction,\n    userId?: string\n  ): Promise<{\n    allowed: boolean\n    remainingRequests: number\n    resetTime: number\n    retryAfter?: number\n  }> {\n    try {\n      const config = RATE_LIMITS[action]\n      \n      // Get current user if not provided\n      let targetUserId = userId\n      if (!targetUserId) {\n        const { data: { user } } = await supabase.auth.getUser()\n        if (!user) {\n          throw new Error('User not authenticated')\n        }\n        targetUserId = user.id\n      }\n\n      // Call Supabase rate limiting function\n      const { data, error } = await supabase.rpc('check_rate_limit', {\n        p_user_id: targetUserId,\n        p_action_type: action,\n        p_max_requests: config.maxRequests,\n        p_window_minutes: config.windowMinutes\n      })\n\n      if (error) {\n        console.error(`Server rate limit check failed for ${action}:`, error)\n        // On error, allow but log\n        return {\n          allowed: true,\n          remainingRequests: 0,\n          resetTime: Date.now() + config.windowMinutes * 60 * 1000\n        }\n      }\n\n      const allowed = data === true\n      const windowMs = config.windowMinutes * 60 * 1000\n      const resetTime = Date.now() + windowMs\n\n      if (!allowed) {\n        return {\n          allowed: false,\n          remainingRequests: 0,\n          resetTime,\n          retryAfter: Math.ceil(windowMs / 1000)\n        }\n      }\n\n      return {\n        allowed: true,\n        remainingRequests: config.maxRequests - 1, // Approximate\n        resetTime\n      }\n    } catch (error) {\n      console.error(`Server rate limit error for ${action}:`, error)\n      // On error, allow but log\n      return {\n        allowed: true,\n        remainingRequests: 0,\n        resetTime: Date.now() + 60000\n      }\n    }\n  }\n}\n\n// Global instances\nexport const clientRateLimiter = new ClientRateLimiter()\nexport const serverRateLimiter = new ServerRateLimiter()\n\n/**\n * Combined rate limiting check (client + server)\n */\nexport async function checkRateLimit(\n  action: RateLimitAction,\n  options: {\n    clientOnly?: boolean\n    serverOnly?: boolean\n    userId?: string\n  } = {}\n): Promise<{\n  allowed: boolean\n  remainingRequests: number\n  resetTime: number\n  retryAfter?: number\n  source: 'client' | 'server' | 'both'\n}> {\n  const { clientOnly = false, serverOnly = false, userId } = options\n\n  try {\n    // Client-side check\n    if (!serverOnly) {\n      const clientResult = await clientRateLimiter.checkLimit(action, userId)\n      if (!clientResult.allowed) {\n        return {\n          ...clientResult,\n          source: 'client'\n        }\n      }\n      \n      if (clientOnly) {\n        return {\n          ...clientResult,\n          source: 'client'\n        }\n      }\n    }\n\n    // Server-side check\n    if (!clientOnly) {\n      const serverResult = await serverRateLimiter.checkLimit(action, userId)\n      return {\n        ...serverResult,\n        source: serverOnly ? 'server' : 'both'\n      }\n    }\n\n    // Fallback (shouldn't reach here)\n    return {\n      allowed: true,\n      remainingRequests: 0,\n      resetTime: Date.now() + 60000,\n      source: 'client'\n    }\n  } catch (error) {\n    console.error(`Rate limit check failed for ${action}:`, error)\n    return {\n      allowed: true,\n      remainingRequests: 0,\n      resetTime: Date.now() + 60000,\n      source: 'client'\n    }\n  }\n}\n\n/**\n * Rate limit error class\n */\nexport class RateLimitError extends Error {\n  constructor(\n    public action: RateLimitAction,\n    public retryAfter: number,\n    public resetTime: number\n  ) {\n    super(`Rate limit exceeded for ${action}. Try again in ${retryAfter} seconds.`)\n    this.name = 'RateLimitError'\n  }\n}\n\n/**\n * Rate limiting hook for React components\n */\nexport function useRateLimit(action: RateLimitAction) {\n  return {\n    checkLimit: (options?: { clientOnly?: boolean; serverOnly?: boolean }) =>\n      checkRateLimit(action, options),\n    resetLimit: () => clientRateLimiter.resetLimit(action),\n    getStatus: () => clientRateLimiter.getStatus(action)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;CAGC,GAED;AAPA;;AAUO,MAAM,cAAc;IACzB,yBAAyB;IACzB,OAAO;QAAE,aAAa;QAAG,eAAe;IAAG;IAC3C,QAAQ;QAAE,aAAa;QAAG,eAAe;IAAG;IAC5C,gBAAgB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEpD,kBAAkB;IAClB,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,gBAAgB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEpD,iBAAiB;IACjB,eAAe;QAAE,aAAa;QAAI,eAAe;IAAG;IACpD,eAAe;QAAE,aAAa;QAAK,eAAe;IAAG;IACrD,eAAe;QAAE,aAAa;QAAI,eAAe;IAAG;IAEpD,kBAAkB;IAClB,gBAAgB;QAAE,aAAa;QAAI,eAAe;IAAG;IACrD,mBAAmB;QAAE,aAAa;QAAG,eAAe;IAAG;IAEvD,eAAe;IACf,aAAa;QAAE,aAAa;QAAG,eAAe;IAAG;IAEjD,cAAc;IACd,aAAa;QAAE,aAAa;QAAK,eAAe;IAAG;AACrD;AAaO,MAAM;IACH,cAAc,MAAuB,EAAE,MAAe,EAAU;QACtE,MAAM,aAAa,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG;QAC3C,OAAO,CAAC,WAAW,EAAE,SAAS,YAAY;IAC5C;IAEA;;GAEC,GACD,MAAM,WAAW,MAAuB,EAAE,MAAe,EAKtD;QACD,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAClC,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAE7C,mBAAmB;YACnB,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI;YAEJ,IAAI,CAAC,QAAQ;gBACX,gBAAgB;gBAChB,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,OAAO;oBACL,SAAS;oBACT,mBAAmB,OAAO,WAAW,GAAG;oBACxC,WAAW,MAAM;gBACnB;YACF;YAEA,OAAO,KAAK,KAAK,CAAC;YAElB,8BAA8B;YAC9B,IAAI,MAAM,KAAK,WAAW,GAAG,UAAU;gBACrC,eAAe;gBACf,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,OAAO;oBACL,SAAS;oBACT,mBAAmB,OAAO,WAAW,GAAG;oBACxC,WAAW,MAAM;gBACnB;YACF;YAEA,0BAA0B;YAC1B,IAAI,KAAK,KAAK,IAAI,OAAO,WAAW,EAAE;gBACpC,MAAM,YAAY,KAAK,WAAW,GAAG;gBACrC,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,YAAY,GAAG,IAAI;gBAEjD,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB;oBACA;gBACF;YACF;YAEA,oBAAoB;YACpB,KAAK,KAAK;YACV,KAAK,WAAW,GAAG;YACnB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAEhD,OAAO;gBACL,SAAS;gBACT,mBAAmB,OAAO,WAAW,GAAG,KAAK,KAAK;gBAClD,WAAW,KAAK,WAAW,GAAG;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE;YACvD,yCAAyC;YACzC,OAAO;gBACL,SAAS;gBACT,mBAAmB;gBACnB,WAAW,KAAK,GAAG,KAAK,MAAM,oBAAoB;YACpD;QACF;IACF;IAEA;;GAEC,GACD,WAAW,MAAuB,EAAE,MAAe,EAAQ;QACzD,IAAI;YACF,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE;QACzD;IACF;IAEA;;GAEC,GACD,UAAU,MAAuB,EAAE,MAAe,EAIzC;QACP,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAClC,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC9C,MAAM,SAAS,aAAa,OAAO,CAAC;YAEpC,IAAI,CAAC,QAAQ;gBACX,OAAO;oBACL,OAAO;oBACP,WAAW,OAAO,WAAW;oBAC7B,WAAW,KAAK,GAAG,KAAK,OAAO,aAAa,GAAG,KAAK;gBACtD;YACF;YAEA,MAAM,OAAsB,KAAK,KAAK,CAAC;YACvC,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAC7C,MAAM,MAAM,KAAK,GAAG;YAEpB,0BAA0B;YAC1B,IAAI,MAAM,KAAK,WAAW,GAAG,UAAU;gBACrC,OAAO;oBACL,OAAO;oBACP,WAAW,OAAO,WAAW;oBAC7B,WAAW,MAAM;gBACnB;YACF;YAEA,OAAO;gBACL,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,GAAG,CAAC,GAAG,OAAO,WAAW,GAAG,KAAK,KAAK;gBACtD,WAAW,KAAK,WAAW,GAAG;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAC9D,OAAO;QACT;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,MAAM,WACJ,MAAuB,EACvB,MAAe,EAMd;QACD,IAAI;YACF,MAAM,SAAS,WAAW,CAAC,OAAO;YAElC,mCAAmC;YACnC,IAAI,eAAe;YACnB,IAAI,CAAC,cAAc;gBACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,MAAM;gBAClB;gBACA,eAAe,KAAK,EAAE;YACxB;YAEA,uCAAuC;YACvC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,oBAAoB;gBAC7D,WAAW;gBACX,eAAe;gBACf,gBAAgB,OAAO,WAAW;gBAClC,kBAAkB,OAAO,aAAa;YACxC;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;gBAC/D,0BAA0B;gBAC1B,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,WAAW,KAAK,GAAG,KAAK,OAAO,aAAa,GAAG,KAAK;gBACtD;YACF;YAEA,MAAM,UAAU,SAAS;YACzB,MAAM,WAAW,OAAO,aAAa,GAAG,KAAK;YAC7C,MAAM,YAAY,KAAK,GAAG,KAAK;YAE/B,IAAI,CAAC,SAAS;gBACZ,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB;oBACA,YAAY,KAAK,IAAI,CAAC,WAAW;gBACnC;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,mBAAmB,OAAO,WAAW,GAAG;gBACxC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE;YACxD,0BAA0B;YAC1B,OAAO;gBACL,SAAS;gBACT,mBAAmB;gBACnB,WAAW,KAAK,GAAG,KAAK;YAC1B;QACF;IACF;AACF;AAGO,MAAM,oBAAoB,IAAI;AAC9B,MAAM,oBAAoB,IAAI;AAK9B,eAAe,eACpB,MAAuB,EACvB,UAII,CAAC,CAAC;IAQN,MAAM,EAAE,aAAa,KAAK,EAAE,aAAa,KAAK,EAAE,MAAM,EAAE,GAAG;IAE3D,IAAI;QACF,oBAAoB;QACpB,IAAI,CAAC,YAAY;YACf,MAAM,eAAe,MAAM,kBAAkB,UAAU,CAAC,QAAQ;YAChE,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;gBACV;YACF;YAEA,IAAI,YAAY;gBACd,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;gBACV;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,CAAC,YAAY;YACf,MAAM,eAAe,MAAM,kBAAkB,UAAU,CAAC,QAAQ;YAChE,OAAO;gBACL,GAAG,YAAY;gBACf,QAAQ,aAAa,WAAW;YAClC;QACF;QAEA,kCAAkC;QAClC,OAAO;YACL,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC,EAAE;QACxD,OAAO;YACL,SAAS;YACT,mBAAmB;YACnB,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;QACV;IACF;AACF;AAKO,MAAM,uBAAuB;;;;IAClC,YACE,AAAO,MAAuB,EAC9B,AAAO,UAAkB,EACzB,AAAO,SAAiB,CACxB;QACA,KAAK,CAAC,CAAC,wBAAwB,EAAE,OAAO,eAAe,EAAE,WAAW,SAAS,CAAC,QAJvE,SAAA,aACA,aAAA,iBACA,YAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAKO,SAAS,aAAa,MAAuB;IAClD,OAAO;QACL,YAAY,CAAC,UACX,eAAe,QAAQ;QACzB,YAAY,IAAM,kBAAkB,UAAU,CAAC;QAC/C,WAAW,IAAM,kBAAkB,SAAS,CAAC;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-projects.ts"], "sourcesContent": ["'use client'\r\n\r\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\r\nimport { Database } from '@/lib/supabase'\r\nimport { canCreateProject, updateUsageStats, PLAN_LIMIT_MESSAGES } from '@/lib/plan-limits'\r\nimport {\r\n  validateProjectName,\r\n  formatValidationError\r\n} from '@/lib/project-validation'\r\nimport { checkRateLimit, RateLimitError } from '@/lib/rate-limiter'\r\nimport { OptimizedQueries, QueryPerformanceMonitor } from '@/lib/database-optimization'\r\nimport { CACHE_CONFIGS } from '@/lib/cache-strategies'\r\n\r\ntype Project = Database['public']['Tables']['projects']['Row']\r\ntype ProjectInsert = Database['public']['Tables']['projects']['Insert']\r\ntype ProjectUpdate = Database['public']['Tables']['projects']['Update']\r\n\r\n// Projeleri getir\r\nexport function useProjects() {\r\n  return useQuery({\r\n    queryKey: ['projects'],\r\n    queryFn: async (): Promise<Project[]> => {\r\n      console.log(`📁 [USE_PROJECTS] Fetching projects...`)\r\n\r\n      try {\r\n        // Check if we have a session first\r\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession()\r\n        console.log(`📁 [USE_PROJECTS] Session check:`, {\r\n          hasSession: !!session,\r\n          sessionError: sessionError?.message,\r\n          userId: session?.user?.id\r\n        })\r\n\r\n        const { data, error } = await supabase\r\n          .from('projects')\r\n          .select('*')\r\n          .order('created_at', { ascending: false })\r\n\r\n        if (error) {\r\n          console.error(`❌ [USE_PROJECTS] Error fetching projects:`, error)\r\n          throw new Error(error.message)\r\n        }\r\n\r\n        console.log(`✅ [USE_PROJECTS] Projects fetched:`, data?.length || 0, 'projects')\r\n        return data || []\r\n      } catch (err) {\r\n        console.error(`💥 [USE_PROJECTS] Exception:`, err)\r\n        throw err\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Tek proje getir\r\nexport function useProject(projectId: string | null) {\r\n  return useQuery({\r\n    queryKey: ['project', projectId],\r\n    queryFn: async (): Promise<Project | null> => {\r\n      if (!projectId) return null\r\n\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .select('*')\r\n        .eq('id', projectId)\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    enabled: !!projectId,\r\n  })\r\n}\r\n\r\n// Proje oluştur\r\nexport function useCreateProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (project: Omit<ProjectInsert, 'user_id'>): Promise<Project> => {\r\n      // Plan limiti kontrol et\r\n      const limitCheck = await canCreateProject()\r\n      if (!limitCheck.allowed) {\r\n        throw new Error(limitCheck.reason || PLAN_LIMIT_MESSAGES.PROJECT_LIMIT_REACHED)\r\n      }\r\n\r\n      const { data: { user }, error: userError } = await supabase.auth.getUser()\r\n\r\n      if (userError || !user) {\r\n        throw new Error('Kullanıcı oturumu bulunamadı')\r\n      }\r\n\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .insert({\r\n          ...project,\r\n          user_id: user.id,\r\n        })\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n\r\n      return data\r\n    },\r\n    onSuccess: async () => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n}\r\n\r\n// Proje güncelle\r\nexport function useUpdateProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({ id, ...updates }: ProjectUpdate & { id: string }): Promise<Project> => {\r\n      console.log('🔄 [UPDATE_PROJECT] Starting update:', { id, updates })\r\n\r\n      // Önce mevcut kullanıcıyı kontrol et\r\n      const { data: user } = await supabase.auth.getUser()\r\n      if (!user.user) {\r\n        throw new Error('Kullanıcı girişi gerekli')\r\n      }\r\n\r\n      // Güncelleme işlemini yap - RLS politikaları otomatik olarak kontrol edecek\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .update({\r\n          ...updates,\r\n          updated_at: new Date().toISOString()\r\n        })\r\n        .eq('id', id)\r\n        .eq('user_id', user.user.id) // Ekstra güvenlik için user_id kontrolü\r\n        .select()\r\n        .single()\r\n\r\n      if (error) {\r\n        console.error('❌ [UPDATE_PROJECT] Database error:', error)\r\n        throw new Error(`Proje güncellenirken hata oluştu: ${error.message}`)\r\n      }\r\n\r\n      if (!data) {\r\n        throw new Error('Proje bulunamadı veya güncelleme yetkisi yok')\r\n      }\r\n\r\n      console.log('✅ [UPDATE_PROJECT] Success:', data)\r\n      return data\r\n    },\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      queryClient.invalidateQueries({ queryKey: ['project', data.id] })\r\n    },\r\n    onError: (error) => {\r\n      console.error('❌ [UPDATE_PROJECT] Mutation error:', error)\r\n    }\r\n  })\r\n}\r\n\r\n// Güvenli proje adı güncelleme\r\nexport function useUpdateProjectNameSecure() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      projectId,\r\n      newName\r\n    }: {\r\n      projectId: string;\r\n      newName: string\r\n    }): Promise<Project> => {\r\n      console.log(`🔒 [UPDATE_PROJECT_NAME] Starting secure update:`, { projectId, newName })\r\n\r\n      // Advanced rate limiting check\r\n      const rateLimitResult = await checkRateLimit('project_update')\r\n      if (!rateLimitResult.allowed) {\r\n        throw new RateLimitError(\r\n          'project_update',\r\n          rateLimitResult.retryAfter || 60,\r\n          rateLimitResult.resetTime\r\n        )\r\n      }\r\n\r\n      // Client-side validation\r\n      const validation = validateProjectName(newName)\r\n      if (!validation.isValid) {\r\n        throw new Error(validation.error || 'Geçersiz proje adı')\r\n      }\r\n\r\n      const sanitizedName = validation.sanitizedValue!\r\n\r\n      try {\r\n        // Güvenli database function'ını çağır\r\n        const { data, error } = await supabase.rpc('update_project_name_secure', {\r\n          p_project_id: projectId,\r\n          p_new_name: sanitizedName\r\n        })\r\n\r\n        if (error) {\r\n          console.error(`❌ [UPDATE_PROJECT_NAME] Database error:`, error)\r\n          throw new Error(formatValidationError(error.message))\r\n        }\r\n\r\n        // Function response kontrolü\r\n        if (!data?.success) {\r\n          console.error(`❌ [UPDATE_PROJECT_NAME] Function error:`, data)\r\n          throw new Error(data?.message || 'Proje adı güncellenemedi')\r\n        }\r\n\r\n        console.log(`✅ [UPDATE_PROJECT_NAME] Success:`, data.data)\r\n        return data.data as Project\r\n\r\n      } catch (err) {\r\n        console.error(`💥 [UPDATE_PROJECT_NAME] Exception:`, err)\r\n\r\n        // Error mesajını kullanıcı dostu hale getir\r\n        let errorMessage = err instanceof Error ? err.message : 'Bilinmeyen hata'\r\n\r\n        if (errorMessage.includes('unique_violation') || errorMessage.includes('DuplicateName')) {\r\n          errorMessage = 'Bu isimde bir proje zaten mevcut'\r\n        } else if (errorMessage.includes('check_violation') || errorMessage.includes('InvalidInput')) {\r\n          errorMessage = 'Proje adı geçersiz karakterler içeriyor'\r\n        } else if (errorMessage.includes('RateLimitExceeded')) {\r\n          errorMessage = 'Çok fazla güncelleme isteği. Lütfen bekleyin.'\r\n        }\r\n\r\n        throw new Error(errorMessage)\r\n      }\r\n    },\r\n    onMutate: async ({ projectId, newName }) => {\r\n      console.log(`🚀 [UPDATE_PROJECT_NAME] Starting optimistic update:`, { projectId, newName })\r\n\r\n      // Cancel outgoing refetches\r\n      await queryClient.cancelQueries({ queryKey: ['projects'] })\r\n      await queryClient.cancelQueries({ queryKey: ['project', projectId] })\r\n\r\n      // Snapshot previous values\r\n      const previousProjects = queryClient.getQueryData<Project[]>(['projects'])\r\n      const previousProject = queryClient.getQueryData<Project>(['project', projectId])\r\n\r\n      // Optimistically update projects list\r\n      if (previousProjects) {\r\n        queryClient.setQueryData<Project[]>(['projects'], (old) => {\r\n          if (!old) return old\r\n          return old.map(project =>\r\n            project.id === projectId\r\n              ? { ...project, name: newName.trim(), updated_at: new Date().toISOString() }\r\n              : project\r\n          )\r\n        })\r\n      }\r\n\r\n      // Optimistically update single project\r\n      if (previousProject) {\r\n        queryClient.setQueryData<Project>(['project', projectId], {\r\n          ...previousProject,\r\n          name: newName.trim(),\r\n          updated_at: new Date().toISOString()\r\n        })\r\n      }\r\n\r\n      return { previousProjects, previousProject }\r\n    },\r\n    onSuccess: (data) => {\r\n      console.log(`✅ [UPDATE_PROJECT_NAME] Success, updating cache with server data:`, data.id)\r\n\r\n      // Update with actual server data\r\n      queryClient.setQueryData<Project[]>(['projects'], (old) => {\r\n        if (!old) return old\r\n        return old.map(project =>\r\n          project.id === data.id ? { ...project, ...data } : project\r\n        )\r\n      })\r\n\r\n      queryClient.setQueryData<Project>(['project', data.id], data)\r\n    },\r\n    onError: (error, variables, context) => {\r\n      console.error(`💥 [UPDATE_PROJECT_NAME] Error, rolling back:`, error)\r\n\r\n      // Rollback optimistic updates\r\n      if (context?.previousProjects) {\r\n        queryClient.setQueryData(['projects'], context.previousProjects)\r\n      }\r\n      if (context?.previousProject) {\r\n        queryClient.setQueryData(['project', variables.projectId], context.previousProject)\r\n      }\r\n    },\r\n    onSettled: () => {\r\n      // Always refetch to ensure consistency\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n    }\r\n  })\r\n}\r\n\r\n// Proje sil\r\nexport function useDeleteProject() {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation({\r\n    mutationFn: async (projectId: string): Promise<void> => {\r\n      const { error } = await supabase\r\n        .from('projects')\r\n        .delete()\r\n        .eq('id', projectId)\r\n\r\n      if (error) {\r\n        throw new Error(error.message)\r\n      }\r\n    },\r\n    onSuccess: async () => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      // Kullanım istatistiklerini güncelle\r\n      try {\r\n        await updateUsageStats()\r\n        queryClient.invalidateQueries({ queryKey: ['user-limits'] })\r\n        queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\r\n      } catch (error) {\r\n        console.warn('Kullanım istatistikleri güncellenemedi:', error)\r\n      }\r\n    },\r\n  })\r\n} "], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AAIA;AAVA;;;;;;AAmBO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAW;QACtB,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC;YAEpD,IAAI;gBACF,mCAAmC;gBACnC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;gBACjF,QAAQ,GAAG,CAAC,CAAC,gCAAgC,CAAC,EAAE;oBAC9C,YAAY,CAAC,CAAC;oBACd,cAAc,cAAc;oBAC5B,QAAQ,SAAS,MAAM;gBACzB;gBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,CAAC,yCAAyC,CAAC,EAAE;oBAC3D,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAC,CAAC,kCAAkC,CAAC,EAAE,MAAM,UAAU,GAAG;gBACrE,OAAO,QAAQ,EAAE;YACnB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;gBAC9C,MAAM;YACR;QACF;IACF;AACF;AAGO,SAAS,WAAW,SAAwB;IACjD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAU;QAChC,SAAS;YACP,IAAI,CAAC,WAAW,OAAO;YAEvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,WACT,MAAM;YAET,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;QACA,SAAS,CAAC,CAAC;IACb;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,yBAAyB;YACzB,MAAM,aAAa,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;YACxC,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,IAAI,MAAM,WAAW,MAAM,IAAI,4HAAA,CAAA,sBAAmB,CAAC,qBAAqB;YAChF;YAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAExE,IAAI,aAAa,CAAC,MAAM;gBACtB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,GAAG,OAAO;gBACV,SAAS,KAAK,EAAE;YAClB,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,qCAAqC;YACrC,IAAI;gBACF,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;gBAC1D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,2CAA2C;YAC1D;QACF;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,EAAE,EAAE,GAAG,SAAyC;YACnE,QAAQ,GAAG,CAAC,wCAAwC;gBAAE;gBAAI;YAAQ;YAElE,qCAAqC;YACrC,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;YAClD,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,4EAA4E;YAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,GAAG,OAAO;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,EAAE,wCAAwC;aACpE,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,MAAM,OAAO,EAAE;YACtE;YAEA,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,OAAO;QACT;QACA,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW,KAAK,EAAE;iBAAC;YAAC;QACjE;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,SAAS,EACT,OAAO,EAIR;YACC,QAAQ,GAAG,CAAC,CAAC,gDAAgD,CAAC,EAAE;gBAAE;gBAAW;YAAQ;YAErF,+BAA+B;YAC/B,MAAM,kBAAkB,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;YAC7C,IAAI,CAAC,gBAAgB,OAAO,EAAE;gBAC5B,MAAM,IAAI,6HAAA,CAAA,iBAAc,CACtB,kBACA,gBAAgB,UAAU,IAAI,IAC9B,gBAAgB,SAAS;YAE7B;YAEA,yBAAyB;YACzB,MAAM,aAAa,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD,EAAE;YACvC,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,IAAI,MAAM,WAAW,KAAK,IAAI;YACtC;YAEA,MAAM,gBAAgB,WAAW,cAAc;YAE/C,IAAI;gBACF,sCAAsC;gBACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,8BAA8B;oBACvE,cAAc;oBACd,YAAY;gBACd;gBAEA,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;oBACzD,MAAM,IAAI,MAAM,CAAA,GAAA,mIAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,OAAO;gBACrD;gBAEA,6BAA6B;gBAC7B,IAAI,CAAC,MAAM,SAAS;oBAClB,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;oBACzD,MAAM,IAAI,MAAM,MAAM,WAAW;gBACnC;gBAEA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,CAAC,EAAE,KAAK,IAAI;gBACzD,OAAO,KAAK,IAAI;YAElB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,CAAC,mCAAmC,CAAC,EAAE;gBAErD,4CAA4C;gBAC5C,IAAI,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAExD,IAAI,aAAa,QAAQ,CAAC,uBAAuB,aAAa,QAAQ,CAAC,kBAAkB;oBACvF,eAAe;gBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB,aAAa,QAAQ,CAAC,iBAAiB;oBAC5F,eAAe;gBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB;oBACrD,eAAe;gBACjB;gBAEA,MAAM,IAAI,MAAM;YAClB;QACF;QACA,UAAU,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;YACrC,QAAQ,GAAG,CAAC,CAAC,oDAAoD,CAAC,EAAE;gBAAE;gBAAW;YAAQ;YAEzF,4BAA4B;YAC5B,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACzD,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU;oBAAC;oBAAW;iBAAU;YAAC;YAEnE,2BAA2B;YAC3B,MAAM,mBAAmB,YAAY,YAAY,CAAY;gBAAC;aAAW;YACzE,MAAM,kBAAkB,YAAY,YAAY,CAAU;gBAAC;gBAAW;aAAU;YAEhF,sCAAsC;YACtC,IAAI,kBAAkB;gBACpB,YAAY,YAAY,CAAY;oBAAC;iBAAW,EAAE,CAAC;oBACjD,IAAI,CAAC,KAAK,OAAO;oBACjB,OAAO,IAAI,GAAG,CAAC,CAAA,UACb,QAAQ,EAAE,KAAK,YACX;4BAAE,GAAG,OAAO;4BAAE,MAAM,QAAQ,IAAI;4BAAI,YAAY,IAAI,OAAO,WAAW;wBAAG,IACzE;gBAER;YACF;YAEA,uCAAuC;YACvC,IAAI,iBAAiB;gBACnB,YAAY,YAAY,CAAU;oBAAC;oBAAW;iBAAU,EAAE;oBACxD,GAAG,eAAe;oBAClB,MAAM,QAAQ,IAAI;oBAClB,YAAY,IAAI,OAAO,WAAW;gBACpC;YACF;YAEA,OAAO;gBAAE;gBAAkB;YAAgB;QAC7C;QACA,WAAW,CAAC;YACV,QAAQ,GAAG,CAAC,CAAC,iEAAiE,CAAC,EAAE,KAAK,EAAE;YAExF,iCAAiC;YACjC,YAAY,YAAY,CAAY;gBAAC;aAAW,EAAE,CAAC;gBACjD,IAAI,CAAC,KAAK,OAAO;gBACjB,OAAO,IAAI,GAAG,CAAC,CAAA,UACb,QAAQ,EAAE,KAAK,KAAK,EAAE,GAAG;wBAAE,GAAG,OAAO;wBAAE,GAAG,IAAI;oBAAC,IAAI;YAEvD;YAEA,YAAY,YAAY,CAAU;gBAAC;gBAAW,KAAK,EAAE;aAAC,EAAE;QAC1D;QACA,SAAS,CAAC,OAAO,WAAW;YAC1B,QAAQ,KAAK,CAAC,CAAC,6CAA6C,CAAC,EAAE;YAE/D,8BAA8B;YAC9B,IAAI,SAAS,kBAAkB;gBAC7B,YAAY,YAAY,CAAC;oBAAC;iBAAW,EAAE,QAAQ,gBAAgB;YACjE;YACA,IAAI,SAAS,iBAAiB;gBAC5B,YAAY,YAAY,CAAC;oBAAC;oBAAW,UAAU,SAAS;iBAAC,EAAE,QAAQ,eAAe;YACpF;QACF;QACA,WAAW;YACT,uCAAuC;YACvC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;QACF;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,qCAAqC;YACrC,IAAI;gBACF,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;gBACrB,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;gBAC1D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,2CAA2C;YAC1D;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-auth.ts"], "sourcesContent": ["'use client'\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\nimport { User } from '@supabase/supabase-js'\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { toast } from 'sonner'\n\n// Auth error handling\nfunction handleAuthError(error: Error | unknown, queryClient: ReturnType<typeof useQueryClient>) {\n  const errorMessage = error instanceof Error ? error.message : String(error)\n  \n  if (errorMessage.includes('Invalid Refresh Token') || \n      errorMessage.includes('Refresh Token Not Found') ||\n      errorMessage.includes('refresh_token_not_found')) {\n    // Refresh token hatası durumunda oturumu temizle\n    console.warn('Refresh token hatası, oturum temizleniyor:', errorMessage)\n    supabase.auth.signOut({ scope: 'local' })\n    queryClient.clear()\n    return true\n  }\n  return false\n}\n\n// Auth state listener with enhanced logout handling\nexport function useAuthStateListener() {\n  const queryClient = useQueryClient()\n  const router = useRouter()\n\n  useEffect(() => {\n    console.log(`🎧 [AUTH_LISTENER] Setting up auth state listener`)\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      (event, session) => {\n        console.log(`🔄 [AUTH_LISTENER] Auth state change: ${event}`, {\n          userId: session?.user?.id,\n          email: session?.user?.email,\n          hasSession: !!session\n        })\n\n        if (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {\n          console.log(`🔄 [AUTH_LISTENER] Invalidating queries for event: ${event}`)\n          queryClient.invalidateQueries({ queryKey: ['user'] })\n          queryClient.invalidateQueries({ queryKey: ['session'] })\n        }\n\n        if (event === 'SIGNED_OUT') {\n          console.log(`🚪 [AUTH_LISTENER] User signed out - clearing cache and redirecting`)\n          // Clear all cache and redirect to login\n          queryClient.clear()\n\n          // Clear any persisted state\n          localStorage.removeItem('promptflow-app-store')\n\n          // Force redirect to auth page\n          setTimeout(() => {\n            console.log(`🚪 [AUTH_LISTENER] Redirecting to /auth`)\n            router.push('/auth')\n            router.refresh()\n          }, 100)\n        }\n\n        if (event === 'SIGNED_IN') {\n          console.log(`🔑 [AUTH_LISTENER] User signed in - refreshing queries and redirecting`)\n          // Refresh queries when user signs in\n          queryClient.invalidateQueries({ queryKey: ['user'] })\n          queryClient.invalidateQueries({ queryKey: ['session'] })\n\n          // Redirect to dashboard after successful login\n          setTimeout(() => {\n            const currentPath = window.location.pathname\n            if (currentPath === '/auth' || currentPath === '/') {\n              console.log(`🚀 [AUTH_LISTENER] Redirecting from ${currentPath} to /dashboard`)\n              router.push('/dashboard')\n            }\n          }, 100)\n        }\n      }\n    )\n\n    return () => {\n      console.log(`🎧 [AUTH_LISTENER] Cleaning up auth state listener`)\n      subscription.unsubscribe()\n    }\n  }, [queryClient, router])\n}\n\n// Kullanıcı bilgilerini getir\nexport function useUser() {\n  const queryClient = useQueryClient()\n\n  return useQuery({\n    queryKey: ['user'],\n    queryFn: async (): Promise<User | null> => {\n      console.log(`🔐 [USE_USER] Getting user...`)\n      try {\n        // Önce session'ı kontrol et\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession()\n\n        if (sessionError) {\n          console.error(`❌ [USE_USER] Session error:`, sessionError)\n          return null\n        }\n\n        if (!session) {\n          console.log(`🔐 [USE_USER] No session found`)\n          return null\n        }\n\n        console.log(`🔐 [USE_USER] Session found, getting user...`)\n        const { data: { user }, error } = await supabase.auth.getUser()\n\n        if (error) {\n          console.error(`❌ [USE_USER] Error getting user:`, error)\n          // Auth session missing error'ını handle et\n          if (error.message.includes('Auth session missing')) {\n            console.log(`🔄 [USE_USER] Auth session missing, returning null`)\n            return null\n          }\n          // Refresh token hatası kontrolü\n          if (handleAuthError(error, queryClient)) {\n            console.log(`🔄 [USE_USER] Handled auth error, returning null`)\n            return null\n          }\n          throw new Error(error.message)\n        }\n\n        console.log(`✅ [USE_USER] User retrieved:`, user?.email || 'null')\n        return user\n      } catch (error: unknown) {\n        console.error(`💥 [USE_USER] Exception:`, error)\n        const errorMessage = error instanceof Error ? error.message : String(error)\n\n        // Auth session missing error'ını handle et\n        if (errorMessage.includes('Auth session missing')) {\n          console.log(`🔄 [USE_USER] Auth session missing exception, returning null`)\n          return null\n        }\n\n        // Refresh token hatası kontrolü\n        if (handleAuthError(error, queryClient)) {\n          console.log(`🔄 [USE_USER] Handled auth error exception, returning null`)\n          return null\n        }\n        throw error\n      }\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n    retry: (failureCount, error: unknown) => {\n      // Refresh token hatalarında ve session missing'de retry yapma\n      const errorMessage = error instanceof Error ? error.message : String(error)\n      console.log(`🔄 [USE_USER] Retry attempt ${failureCount}, error: ${errorMessage}`)\n      if (errorMessage.includes('Invalid Refresh Token') ||\n          errorMessage.includes('Refresh Token Not Found') ||\n          errorMessage.includes('Auth session missing')) {\n        console.log(`🚫 [USE_USER] Not retrying auth error: ${errorMessage}`)\n        return false\n      }\n      return failureCount < 3\n    },\n  })\n}\n\n// Oturum durumunu kontrol et\nexport function useSession() {\n  const queryClient = useQueryClient()\n  \n  return useQuery({\n    queryKey: ['session'],\n    queryFn: async () => {\n      try {\n        const { data: { session }, error } = await supabase.auth.getSession()\n        \n        if (error) {\n          // Refresh token hatası kontrolü\n          if (handleAuthError(error, queryClient)) {\n            return null\n          }\n          throw new Error(error.message)\n        }\n\n        return session\n      } catch (error: unknown) {\n        // Refresh token hatası kontrolü\n        if (handleAuthError(error, queryClient)) {\n          return null\n        }\n        throw error\n      }\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n    retry: (failureCount, error: unknown) => {\n      // Refresh token hatalarında retry yapma\n      const errorMessage = error instanceof Error ? error.message : String(error)\n      if (errorMessage.includes('Invalid Refresh Token') || \n          errorMessage.includes('Refresh Token Not Found')) {\n        return false\n      }\n      return failureCount < 3\n    },\n  })\n}\n\n// Email ile giriş yap\nexport function useSignInWithEmail() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ email, password }: { email: string; password: string }) => {\n      console.log(`🔑 [SIGN_IN] Attempting login for: ${email}`)\n\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) {\n        console.error(`❌ [SIGN_IN] Login failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [SIGN_IN] Login successful:`, {\n        userId: data.user?.id,\n        email: data.user?.email,\n        hasSession: !!data.session\n      })\n\n      return data\n    },\n    onSuccess: () => {\n      console.log(`🎉 [SIGN_IN] onSuccess triggered, invalidating queries`)\n      queryClient.invalidateQueries({ queryKey: ['user'] })\n      queryClient.invalidateQueries({ queryKey: ['session'] })\n    },\n    onError: (error) => {\n      console.error(`💥 [SIGN_IN] onError triggered:`, error)\n    }\n  })\n}\n\n// Email ile kayıt ol\nexport function useSignUpWithEmail() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ email, password }: { email: string; password: string }) => {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n      })\n\n      if (error) {\n        throw new Error(error.message)\n      }\n\n      return data\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['user'] })\n      queryClient.invalidateQueries({ queryKey: ['session'] })\n    },\n  })\n}\n\n// Enhanced logout with proper redirect\nexport function useSignOut() {\n  const queryClient = useQueryClient()\n  const router = useRouter()\n\n  return useMutation({\n    mutationFn: async () => {\n      console.log('Starting logout process...')\n\n      // Sign out from Supabase\n      const { error } = await supabase.auth.signOut({ scope: 'global' })\n\n      if (error) {\n        console.error('Logout error:', error)\n        throw new Error(error.message)\n      }\n\n      console.log('Logout successful')\n    },\n    onSuccess: () => {\n      console.log('Logout onSuccess triggered')\n\n      // Show success toast\n      toast.success('Başarıyla çıkış yapıldı', {\n        description: 'Giriş sayfasına yönlendiriliyorsunuz...'\n      })\n\n      // Clear all cache\n      queryClient.clear()\n\n      // Clear persisted state\n      localStorage.removeItem('promptflow-app-store')\n      sessionStorage.clear()\n\n      // Force redirect to auth page\n      setTimeout(() => {\n        console.log('Redirecting to auth page...')\n        router.push('/auth')\n        router.refresh()\n        window.location.href = '/auth' // Fallback for complete page reload\n      }, 1000) // Increased delay to show toast\n    },\n    onError: (error) => {\n      console.error('Logout failed:', error)\n\n      // Show error toast\n      toast.error('Çıkış yapılırken hata oluştu', {\n        description: 'Yine de oturum temizleniyor...'\n      })\n\n      // Even if logout fails, clear local state and redirect\n      queryClient.clear()\n      localStorage.removeItem('promptflow-app-store')\n      sessionStorage.clear()\n\n      setTimeout(() => {\n        router.push('/auth')\n        router.refresh()\n      }, 1000)\n    }\n  })\n}"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAPA;;;;;;AASA,sBAAsB;AACtB,SAAS,gBAAgB,KAAsB,EAAE,WAA8C;IAC7F,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;IAErE,IAAI,aAAa,QAAQ,CAAC,4BACtB,aAAa,QAAQ,CAAC,8BACtB,aAAa,QAAQ,CAAC,4BAA4B;QACpD,iDAAiD;QACjD,QAAQ,IAAI,CAAC,8CAA8C;QAC3D,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO;QAAQ;QACvC,YAAY,KAAK;QACjB,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,CAAC,iDAAiD,CAAC;QAE/D,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,CAAC,OAAO;YACN,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,OAAO,EAAE;gBAC5D,QAAQ,SAAS,MAAM;gBACvB,OAAO,SAAS,MAAM;gBACtB,YAAY,CAAC,CAAC;YAChB;YAEA,IAAI,UAAU,gBAAgB,UAAU,mBAAmB;gBACzD,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,OAAO;gBACzE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAO;gBAAC;gBACnD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAU;gBAAC;YACxD;YAEA,IAAI,UAAU,cAAc;gBAC1B,QAAQ,GAAG,CAAC,CAAC,mEAAmE,CAAC;gBACjF,wCAAwC;gBACxC,YAAY,KAAK;gBAEjB,4BAA4B;gBAC5B,aAAa,UAAU,CAAC;gBAExB,8BAA8B;gBAC9B,WAAW;oBACT,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;oBACrD,OAAO,IAAI,CAAC;oBACZ,OAAO,OAAO;gBAChB,GAAG;YACL;YAEA,IAAI,UAAU,aAAa;gBACzB,QAAQ,GAAG,CAAC,CAAC,sEAAsE,CAAC;gBACpF,qCAAqC;gBACrC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAO;gBAAC;gBACnD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAU;gBAAC;gBAEtD,+CAA+C;gBAC/C,WAAW;oBACT,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;oBAC5C,IAAI,gBAAgB,WAAW,gBAAgB,KAAK;wBAClD,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,YAAY,cAAc,CAAC;wBAC9E,OAAO,IAAI,CAAC;oBACd;gBACF,GAAG;YACL;QACF;QAGF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;YAChE,aAAa,WAAW;QAC1B;IACF,GAAG;QAAC;QAAa;KAAO;AAC1B;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAO;QAClB,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,6BAA6B,CAAC;YAC3C,IAAI;gBACF,4BAA4B;gBAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;gBAEjF,IAAI,cAAc;oBAChB,QAAQ,KAAK,CAAC,CAAC,2BAA2B,CAAC,EAAE;oBAC7C,OAAO;gBACT;gBAEA,IAAI,CAAC,SAAS;oBACZ,QAAQ,GAAG,CAAC,CAAC,8BAA8B,CAAC;oBAC5C,OAAO;gBACT;gBAEA,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;gBAC1D,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAE7D,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE;oBAClD,2CAA2C;oBAC3C,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,yBAAyB;wBAClD,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;wBAChE,OAAO;oBACT;oBACA,gCAAgC;oBAChC,IAAI,gBAAgB,OAAO,cAAc;wBACvC,QAAQ,GAAG,CAAC,CAAC,gDAAgD,CAAC;wBAC9D,OAAO;oBACT;oBACA,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAC,CAAC,4BAA4B,CAAC,EAAE,MAAM,SAAS;gBAC3D,OAAO;YACT,EAAE,OAAO,OAAgB;gBACvB,QAAQ,KAAK,CAAC,CAAC,wBAAwB,CAAC,EAAE;gBAC1C,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;gBAErE,2CAA2C;gBAC3C,IAAI,aAAa,QAAQ,CAAC,yBAAyB;oBACjD,QAAQ,GAAG,CAAC,CAAC,4DAA4D,CAAC;oBAC1E,OAAO;gBACT;gBAEA,gCAAgC;gBAChC,IAAI,gBAAgB,OAAO,cAAc;oBACvC,QAAQ,GAAG,CAAC,CAAC,0DAA0D,CAAC;oBACxE,OAAO;gBACT;gBACA,MAAM;YACR;QACF;QACA,WAAW,IAAI,KAAK;QACpB,OAAO,CAAC,cAAc;YACpB,8DAA8D;YAC9D,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;YACrE,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,aAAa,SAAS,EAAE,cAAc;YACjF,IAAI,aAAa,QAAQ,CAAC,4BACtB,aAAa,QAAQ,CAAC,8BACtB,aAAa,QAAQ,CAAC,yBAAyB;gBACjD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,cAAc;gBACpE,OAAO;YACT;YACA,OAAO,eAAe;QACxB;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAU;QACrB,SAAS;YACP,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;gBAEnE,IAAI,OAAO;oBACT,gCAAgC;oBAChC,IAAI,gBAAgB,OAAO,cAAc;wBACvC,OAAO;oBACT;oBACA,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT,EAAE,OAAO,OAAgB;gBACvB,gCAAgC;gBAChC,IAAI,gBAAgB,OAAO,cAAc;oBACvC,OAAO;gBACT;gBACA,MAAM;YACR;QACF;QACA,WAAW,IAAI,KAAK;QACpB,OAAO,CAAC,cAAc;YACpB,wCAAwC;YACxC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;YACrE,IAAI,aAAa,QAAQ,CAAC,4BACtB,aAAa,QAAQ,CAAC,4BAA4B;gBACpD,OAAO;YACT;YACA,OAAO,eAAe;QACxB;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAuC;YACzE,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,OAAO;YAEzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,yBAAyB,CAAC,EAAE;gBAC3C,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,CAAC,EAAE;gBAC3C,QAAQ,KAAK,IAAI,EAAE;gBACnB,OAAO,KAAK,IAAI,EAAE;gBAClB,YAAY,CAAC,CAAC,KAAK,OAAO;YAC5B;YAEA,OAAO;QACT;QACA,WAAW;YACT,QAAQ,GAAG,CAAC,CAAC,sDAAsD,CAAC;YACpE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAO;YAAC;YACnD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;QACxD;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAE;QACnD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAuC;YACzE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAO;YAAC;YACnD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;QACxD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY;YACV,QAAQ,GAAG,CAAC;YAEZ,yBAAyB;YACzB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;gBAAE,OAAO;YAAS;YAEhE,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC;QACd;QACA,WAAW;YACT,QAAQ,GAAG,CAAC;YAEZ,qBAAqB;YACrB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,2BAA2B;gBACvC,aAAa;YACf;YAEA,kBAAkB;YAClB,YAAY,KAAK;YAEjB,wBAAwB;YACxB,aAAa,UAAU,CAAC;YACxB,eAAe,KAAK;YAEpB,8BAA8B;YAC9B,WAAW;gBACT,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;gBACd,OAAO,QAAQ,CAAC,IAAI,GAAG,SAAQ,oCAAoC;YACrE,GAAG,OAAM,gCAAgC;QAC3C;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,kBAAkB;YAEhC,mBAAmB;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;gBAC1C,aAAa;YACf;YAEA,uDAAuD;YACvD,YAAY,KAAK;YACjB,aAAa,UAAU,CAAC;YACxB,eAAe,KAAK;YAEpB,WAAW;gBACT,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB,GAAG;QACL;IACF;AACF", "debugId": null}}, {"offset": {"line": 1544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-plans.ts"], "sourcesContent": ["'use client'\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\nimport { Database } from '@/lib/supabase'\n\ntype PlanType = Database['public']['Tables']['plan_types']['Row']\ntype UserPlan = Database['public']['Tables']['user_plans']['Row']\ntype UsageStats = Database['public']['Tables']['usage_stats']['Row']\n\n// Plan türlerini getir\nexport function usePlanTypes() {\n  return useQuery({\n    queryKey: ['plan-types'],\n    queryFn: async (): Promise<PlanType[]> => {\n      console.log(`📋 [USE_PLANS] Fetching plan types...`)\n      \n      const { data, error } = await supabase\n        .from('plan_types')\n        .select('*')\n        .eq('is_active', true)\n        .order('sort_order')\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Plan types fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Plan types fetched:`, data?.length)\n      return data || []\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n  })\n}\n\n// Kullanıcının aktif planını getir\nexport function useUserActivePlan() {\n  return useQuery({\n    queryKey: ['user-active-plan'],\n    queryFn: async () => {\n      console.log(`👤 [USE_PLANS] Fetching user active plan...`)\n      \n      const { data, error } = await supabase.rpc('get_user_active_plan', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] User active plan fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] User active plan fetched:`, data?.[0])\n      return data?.[0] || null\n    },\n    staleTime: 2 * 60 * 1000, // 2 dakika\n  })\n}\n\n// Kullanıcının limitlerini kontrol et\nexport function useUserLimits() {\n  return useQuery({\n    queryKey: ['user-limits'],\n    queryFn: async () => {\n      console.log(`🔍 [USE_PLANS] Checking user limits...`)\n      \n      const { data, error } = await supabase.rpc('check_user_limits', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] User limits check failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] User limits checked:`, data?.[0])\n      return data?.[0] || null\n    },\n    staleTime: 30 * 1000, // 30 saniye\n  })\n}\n\n// Kullanıcının plan geçmişini getir\nexport function useUserPlanHistory() {\n  return useQuery({\n    queryKey: ['user-plan-history'],\n    queryFn: async (): Promise<UserPlan[]> => {\n      console.log(`📜 [USE_PLANS] Fetching user plan history...`)\n      \n      const { data, error } = await supabase\n        .from('user_plans')\n        .select(`\n          *,\n          plan_types (\n            name,\n            display_name,\n            price_monthly,\n            price_yearly\n          )\n        `)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] User plan history fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] User plan history fetched:`, data?.length)\n      return data || []\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n  })\n}\n\n// Kullanıcının kullanım istatistiklerini getir\nexport function useUsageStats(days: number = 30) {\n  return useQuery({\n    queryKey: ['usage-stats', days],\n    queryFn: async (): Promise<UsageStats[]> => {\n      console.log(`📊 [USE_PLANS] Fetching usage stats for ${days} days...`)\n      \n      const startDate = new Date()\n      startDate.setDate(startDate.getDate() - days)\n      \n      const { data, error } = await supabase\n        .from('usage_stats')\n        .select('*')\n        .gte('stat_date', startDate.toISOString().split('T')[0])\n        .order('stat_date', { ascending: false })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Usage stats fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Usage stats fetched:`, data?.length)\n      return data || []\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n  })\n}\n\n// Plan değiştir\nexport function useChangePlan() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ \n      planName, \n      billingCycle = 'monthly',\n      paymentReference \n    }: { \n      planName: string\n      billingCycle?: string\n      paymentReference?: string \n    }) => {\n      console.log(`🔄 [USE_PLANS] Changing plan to: ${planName}`)\n      \n      const { data, error } = await supabase.rpc('change_user_plan', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id,\n        new_plan_name: planName,\n        billing_cycle_param: billingCycle,\n        payment_reference_param: paymentReference\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Plan change failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Plan changed successfully:`, data)\n      return data\n    },\n    onSuccess: () => {\n      console.log(`🎉 [USE_PLANS] Plan change successful, invalidating queries`)\n      queryClient.invalidateQueries({ queryKey: ['user-active-plan'] })\n      queryClient.invalidateQueries({ queryKey: ['user-limits'] })\n      queryClient.invalidateQueries({ queryKey: ['user-plan-history'] })\n      queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\n    },\n    onError: (error) => {\n      console.error(`💥 [USE_PLANS] Plan change error:`, error)\n    }\n  })\n}\n\n// Kullanım istatistiklerini güncelle\nexport function useUpdateUsageStats() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async () => {\n      console.log(`📈 [USE_PLANS] Updating usage stats...`)\n      \n      const { error } = await supabase.rpc('update_usage_stats', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Usage stats update failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Usage stats updated successfully`)\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\n      queryClient.invalidateQueries({ queryKey: ['user-limits'] })\n    }\n  })\n}\n\n// Plan limiti kontrol fonksiyonu\nexport async function checkPlanLimit(action: 'create_project' | 'create_prompt'): Promise<boolean> {\n  try {\n    const { data, error } = await supabase.rpc('check_user_limits', {\n      user_uuid: (await supabase.auth.getUser()).data.user?.id\n    })\n\n    if (error) {\n      console.error(`❌ [USE_PLANS] Limit check failed:`, error)\n      return false\n    }\n\n    const limits = data?.[0]\n    if (!limits) return false\n\n    switch (action) {\n      case 'create_project':\n        return limits.can_create_project\n      case 'create_prompt':\n        return limits.can_create_prompt\n      default:\n        return false\n    }\n  } catch (error) {\n    console.error(`❌ [USE_PLANS] Limit check error:`, error)\n    return false\n  }\n}\n\n// Plan özelliği kontrol fonksiyonu\nexport function usePlanFeature(featureName: string) {\n  const { data: activePlan } = useUserActivePlan()\n\n  return {\n    hasFeature: activePlan?.features?.[featureName] === true,\n    featureValue: activePlan?.features?.[featureName],\n    planName: activePlan?.plan_name,\n    displayName: activePlan?.display_name\n  }\n}\n\n// Kullanıcının deneme süresi bilgilerini getir\nexport function useUserTrialInfo() {\n  return useQuery({\n    queryKey: ['user-trial-info'],\n    queryFn: async () => {\n      console.log(`🔍 [USE_PLANS] Fetching user trial info...`)\n\n      const { data, error } = await supabase.rpc('get_user_trial_info', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] User trial info fetch failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] User trial info fetched:`, data?.[0])\n      return data?.[0] || null\n    },\n    staleTime: 30 * 1000, // 30 saniye\n  })\n}\n\n// İptal uygunluğunu kontrol et\nexport function useCancellationEligibility() {\n  return useQuery({\n    queryKey: ['cancellation-eligibility'],\n    queryFn: async () => {\n      console.log(`🔍 [USE_PLANS] Checking cancellation eligibility...`)\n\n      const { data, error } = await supabase.rpc('get_cancellation_eligibility', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Cancellation eligibility check failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Cancellation eligibility checked:`, data?.[0])\n      return data?.[0] || null\n    },\n    staleTime: 30 * 1000, // 30 saniye\n  })\n}\n\n// Planı iptal et\nexport function useCancelPlan() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({\n      cancellationReason,\n      requestRefund = false\n    }: {\n      cancellationReason?: string\n      requestRefund?: boolean\n    }) => {\n      console.log(`🚫 [USE_PLANS] Cancelling plan with reason: ${cancellationReason}`)\n\n      const { data, error } = await supabase.rpc('cancel_user_plan', {\n        user_uuid: (await supabase.auth.getUser()).data.user?.id,\n        cancellation_reason_param: cancellationReason,\n        request_refund: requestRefund\n      })\n\n      if (error) {\n        console.error(`❌ [USE_PLANS] Plan cancellation failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [USE_PLANS] Plan cancelled successfully:`, data?.[0])\n      return data?.[0]\n    },\n    onSuccess: (data) => {\n      console.log(`🎉 [USE_PLANS] Plan cancellation successful, invalidating queries`)\n      queryClient.invalidateQueries({ queryKey: ['user-active-plan'] })\n      queryClient.invalidateQueries({ queryKey: ['user-limits'] })\n      queryClient.invalidateQueries({ queryKey: ['user-plan-history'] })\n      queryClient.invalidateQueries({ queryKey: ['user-trial-info'] })\n      queryClient.invalidateQueries({ queryKey: ['cancellation-eligibility'] })\n      queryClient.invalidateQueries({ queryKey: ['usage-stats'] })\n    },\n    onError: (error) => {\n      console.error(`💥 [USE_PLANS] Plan cancellation error:`, error)\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AAAA;AAAA;AACA;AAHA;;;AAWO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAa;QACxB,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,qCAAqC,CAAC;YAEnD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,sCAAsC,CAAC,EAAE;gBACxD,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,iCAAiC,CAAC,EAAE,MAAM;YACvD,OAAO,QAAQ,EAAE;QACnB;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAmB;QAC9B,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,2CAA2C,CAAC;YAEzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,wBAAwB;gBACjE,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;YACxD;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,4CAA4C,CAAC,EAAE;gBAC9D,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC,EAAE,MAAM,CAAC,EAAE;YAChE,OAAO,MAAM,CAAC,EAAE,IAAI;QACtB;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAc;QACzB,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC;YAEpD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,qBAAqB;gBAC9D,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;YACxD;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;gBACzD,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,kCAAkC,CAAC,EAAE,MAAM,CAAC,EAAE;YAC3D,OAAO,MAAM,CAAC,EAAE,IAAI;QACtB;QACA,WAAW,KAAK;IAClB;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAoB;QAC/B,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;YAE1D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;;;;;;QAQT,CAAC,EACA,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,6CAA6C,CAAC,EAAE;gBAC/D,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC,EAAE,MAAM;YAC9D,OAAO,QAAQ,EAAE;QACnB;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS,cAAc,OAAe,EAAE;IAC7C,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAe;SAAK;QAC/B,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,KAAK,QAAQ,CAAC;YAErE,MAAM,YAAY,IAAI;YACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;YAExC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,GAAG,CAAC,aAAa,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EACtD,KAAK,CAAC,aAAa;gBAAE,WAAW;YAAM;YAEzC,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;gBACzD,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,kCAAkC,CAAC,EAAE,MAAM;YACxD,OAAO,QAAQ,EAAE;QACnB;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,QAAQ,EACR,eAAe,SAAS,EACxB,gBAAgB,EAKjB;YACC,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,UAAU;YAE1D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,oBAAoB;gBAC7D,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;gBACtD,eAAe;gBACf,qBAAqB;gBACrB,yBAAyB;YAC3B;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAE;gBACnD,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC,EAAE;YACxD,OAAO;QACT;QACA,WAAW;YACT,QAAQ,GAAG,CAAC,CAAC,2DAA2D,CAAC;YACzE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAmB;YAAC;YAC/D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;YAC1D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAoB;YAAC;YAChE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;QAC5D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAE;QACrD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY;YACV,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC;YAEpD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,sBAAsB;gBACzD,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;YACxD;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,wCAAwC,CAAC,EAAE;gBAC1D,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,8CAA8C,CAAC;QAC9D;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;YAC1D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;QAC5D;IACF;AACF;AAGO,eAAe,eAAe,MAA0C;IAC7E,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,qBAAqB;YAC9D,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;QACxD;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAE;YACnD,OAAO;QACT;QAEA,MAAM,SAAS,MAAM,CAAC,EAAE;QACxB,IAAI,CAAC,QAAQ,OAAO;QAEpB,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO,kBAAkB;YAClC,KAAK;gBACH,OAAO,OAAO,iBAAiB;YACjC;gBACE,OAAO;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE;QAClD,OAAO;IACT;AACF;AAGO,SAAS,eAAe,WAAmB;IAChD,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG;IAE7B,OAAO;QACL,YAAY,YAAY,UAAU,CAAC,YAAY,KAAK;QACpD,cAAc,YAAY,UAAU,CAAC,YAAY;QACjD,UAAU,YAAY;QACtB,aAAa,YAAY;IAC3B;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAkB;QAC7B,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;YAExD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,uBAAuB;gBAChE,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;YACxD;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,2CAA2C,CAAC,EAAE;gBAC7D,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC,EAAE,MAAM,CAAC,EAAE;YAC/D,OAAO,MAAM,CAAC,EAAE,IAAI;QACtB;QACA,WAAW,KAAK;IAClB;AACF;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAA2B;QACtC,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,mDAAmD,CAAC;YAEjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,gCAAgC;gBACzE,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;YACxD;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,oDAAoD,CAAC,EAAE;gBACtE,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,CAAC,EAAE,MAAM,CAAC,EAAE;YACxE,OAAO,MAAM,CAAC,EAAE,IAAI;QACtB;QACA,WAAW,KAAK;IAClB;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EACjB,kBAAkB,EAClB,gBAAgB,KAAK,EAItB;YACC,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,oBAAoB;YAE/E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,GAAG,CAAC,oBAAoB;gBAC7D,WAAW,CAAC,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;gBACtD,2BAA2B;gBAC3B,gBAAgB;YAClB;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;gBACzD,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC,EAAE,MAAM,CAAC,EAAE;YACnE,OAAO,MAAM,CAAC,EAAE;QAClB;QACA,WAAW,CAAC;YACV,QAAQ,GAAG,CAAC,CAAC,iEAAiE,CAAC;YAC/E,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAmB;YAAC;YAC/D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;YAC1D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAoB;YAAC;YAChE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAkB;YAAC;YAC9D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAA2B;YAAC;YACvE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;QAC5D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAE;QAC3D;IACF;AACF", "debugId": null}}, {"offset": {"line": 1880, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/limit-warning.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON><PERSON><PERSON>riangle, Crown, TrendingUp, X } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { useState } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface LimitWarningProps {\n  type: 'project' | 'prompt'\n  current: number\n  max: number\n  planName: string\n  onUpgrade?: () => void\n  className?: string\n}\n\nexport function LimitWarning({ \n  type, \n  current, \n  max, \n  planName, \n  onUpgrade,\n  className \n}: LimitWarningProps) {\n  const [dismissed, setDismissed] = useState(false)\n\n  if (dismissed || max === -1) return null\n\n  const percentage = (current / max) * 100\n  const isNearLimit = percentage >= 80\n  const isAtLimit = current >= max\n\n  if (!isNearLimit) return null\n\n  const getWarningLevel = () => {\n    if (isAtLimit) return 'error'\n    if (percentage >= 90) return 'warning'\n    return 'info'\n  }\n\n  const getWarningColors = () => {\n    const level = getWarningLevel()\n    switch (level) {\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800'\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800'\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800'\n    }\n  }\n\n  const getIconColors = () => {\n    const level = getWarningLevel()\n    switch (level) {\n      case 'error':\n        return 'text-red-500'\n      case 'warning':\n        return 'text-yellow-500'\n      case 'info':\n        return 'text-blue-500'\n      default:\n        return 'text-gray-500'\n    }\n  }\n\n  const getTitle = () => {\n    if (isAtLimit) {\n      return type === 'project' ? 'Proje limiti doldu!' : 'Prompt limiti doldu!'\n    }\n    return type === 'project' ? 'Proje limitine yaklaşıyorsunuz' : 'Prompt limitine yaklaşıyorsunuz'\n  }\n\n  const getDescription = () => {\n    if (isAtLimit) {\n      return type === 'project' \n        ? 'Yeni proje oluşturmak için planınızı yükseltin.'\n        : 'Bu projede yeni prompt oluşturmak için planınızı yükseltin.'\n    }\n    \n    const remaining = max - current\n    return type === 'project'\n      ? `${remaining} proje hakkınız kaldı. Daha fazla proje için planınızı yükseltin.`\n      : `Bu projede ${remaining} prompt hakkınız kaldı. Daha fazla prompt için planınızı yükseltin.`\n  }\n\n  const getSuggestedPlan = () => {\n    if (planName === 'free') return 'Profesyonel'\n    if (planName === 'professional') return 'Kurumsal'\n    return null\n  }\n\n  return (\n    <Card className={cn('border-l-4', getWarningColors(), className)}>\n      <CardContent className=\"p-4\">\n        <div className=\"flex items-start gap-3\">\n          <AlertTriangle className={cn('h-5 w-5 mt-0.5 flex-shrink-0', getIconColors())} />\n          \n          <div className=\"flex-1 space-y-2\">\n            <div className=\"flex items-center justify-between\">\n              <h4 className=\"font-medium text-sm\">\n                {getTitle()}\n              </h4>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-6 w-6 p-0 hover:bg-transparent\"\n                onClick={() => setDismissed(true)}\n              >\n                <X className=\"h-4 w-4\" />\n              </Button>\n            </div>\n            \n            <p className=\"text-sm opacity-90\">\n              {getDescription()}\n            </p>\n\n            <div className=\"flex items-center justify-between pt-2\">\n              <div className=\"flex items-center gap-2\">\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  {current}/{max} kullanıldı\n                </Badge>\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  %{Math.round(percentage)} dolu\n                </Badge>\n              </div>\n\n              {onUpgrade && getSuggestedPlan() && (\n                <Button\n                  size=\"sm\"\n                  onClick={onUpgrade}\n                  className=\"h-7 text-xs\"\n                >\n                  <Crown className=\"h-3 w-3 mr-1\" />\n                  {getSuggestedPlan()} Plan\n                </Button>\n              )}\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Hızlı kullanım için özel bileşenler\nexport function ProjectLimitWarning({ \n  current, \n  max, \n  planName, \n  onUpgrade,\n  className \n}: Omit<LimitWarningProps, 'type'>) {\n  return (\n    <LimitWarning\n      type=\"project\"\n      current={current}\n      max={max}\n      planName={planName}\n      onUpgrade={onUpgrade}\n      className={className}\n    />\n  )\n}\n\nexport function PromptLimitWarning({ \n  current, \n  max, \n  planName, \n  onUpgrade,\n  className \n}: Omit<LimitWarningProps, 'type'>) {\n  return (\n    <LimitWarning\n      type=\"prompt\"\n      current={current}\n      max={max}\n      planName={planName}\n      onUpgrade={onUpgrade}\n      className={className}\n    />\n  )\n}\n\n// Inline uyarı bileşeni (daha kompakt)\nexport function InlineLimitWarning({ \n  type, \n  current, \n  max, \n  onUpgrade \n}: Pick<LimitWarningProps, 'type' | 'current' | 'max' | 'onUpgrade'>) {\n  if (max === -1 || current < max) return null\n\n  return (\n    <div className=\"flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded text-red-800 text-sm\">\n      <AlertTriangle className=\"h-4 w-4 text-red-500\" />\n      <span className=\"flex-1\">\n        {type === 'project' ? 'Proje limiti doldu' : 'Prompt limiti doldu'}\n      </span>\n      {onUpgrade && (\n        <Button size=\"sm\" variant=\"outline\" onClick={onUpgrade} className=\"h-6 text-xs\">\n          <TrendingUp className=\"h-3 w-3 mr-1\" />\n          Yükselt\n        </Button>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAkBO,SAAS,aAAa,EAC3B,IAAI,EACJ,OAAO,EACP,GAAG,EACH,QAAQ,EACR,SAAS,EACT,SAAS,EACS;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,aAAa,QAAQ,CAAC,GAAG,OAAO;IAEpC,MAAM,aAAa,AAAC,UAAU,MAAO;IACrC,MAAM,cAAc,cAAc;IAClC,MAAM,YAAY,WAAW;IAE7B,IAAI,CAAC,aAAa,OAAO;IAEzB,MAAM,kBAAkB;QACtB,IAAI,WAAW,OAAO;QACtB,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,MAAM,QAAQ;QACd,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,QAAQ;QACd,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,WAAW;QACf,IAAI,WAAW;YACb,OAAO,SAAS,YAAY,wBAAwB;QACtD;QACA,OAAO,SAAS,YAAY,mCAAmC;IACjE;IAEA,MAAM,iBAAiB;QACrB,IAAI,WAAW;YACb,OAAO,SAAS,YACZ,oDACA;QACN;QAEA,MAAM,YAAY,MAAM;QACxB,OAAO,SAAS,YACZ,GAAG,UAAU,iEAAiE,CAAC,GAC/E,CAAC,WAAW,EAAE,UAAU,mEAAmE,CAAC;IAClG;IAEA,MAAM,mBAAmB;QACvB,IAAI,aAAa,QAAQ,OAAO;QAChC,IAAI,aAAa,gBAAgB,OAAO;QACxC,OAAO;IACT;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc,oBAAoB;kBACpD,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;;;;;;kCAE7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX;;;;;;kDAEH,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,aAAa;kDAE5B,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIjB,8OAAC;gCAAE,WAAU;0CACV;;;;;;0CAGH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAChC;oDAAQ;oDAAE;oDAAI;;;;;;;0DAEjB,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAAU;oDACzC,KAAK,KAAK,CAAC;oDAAY;;;;;;;;;;;;;oCAI5B,aAAa,oCACZ,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB;4CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;AAGO,SAAS,oBAAoB,EAClC,OAAO,EACP,GAAG,EACH,QAAQ,EACR,SAAS,EACT,SAAS,EACuB;IAChC,qBACE,8OAAC;QACC,MAAK;QACL,SAAS;QACT,KAAK;QACL,UAAU;QACV,WAAW;QACX,WAAW;;;;;;AAGjB;AAEO,SAAS,mBAAmB,EACjC,OAAO,EACP,GAAG,EACH,QAAQ,EACR,SAAS,EACT,SAAS,EACuB;IAChC,qBACE,8OAAC;QACC,MAAK;QACL,SAAS;QACT,KAAK;QACL,UAAU;QACV,WAAW;QACX,WAAW;;;;;;AAGjB;AAGO,SAAS,mBAAmB,EACjC,IAAI,EACJ,OAAO,EACP,GAAG,EACH,SAAS,EACyD;IAClE,IAAI,QAAQ,CAAC,KAAK,UAAU,KAAK,OAAO;IAExC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,8OAAC;gBAAK,WAAU;0BACb,SAAS,YAAY,uBAAuB;;;;;;YAE9C,2BACC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAK;gBAAK,SAAQ;gBAAU,SAAS;gBAAW,WAAU;;kCAChE,8OAAC,kNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 2186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> & {\n    indicatorClassName?: string\n  }\n>(({ className, value, indicatorClassName, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-2 w-full overflow-hidden rounded-full bg-gray-100\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className={cn(\n        \"h-full w-full flex-1 bg-gray-900 transition-all\",\n        indicatorClassName\n      )}\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAK/B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE,GAAG,OAAO,EAAE,oBACrD,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mDACA;YAEF,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Kapat</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n} "], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,qMAAA,CAAA,aAAgB,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\nimport { Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst RadioGroup = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Root\n      className={cn(\"grid gap-2\", className)}\n      {...props}\n      ref={ref}\n    />\n  )\n})\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\n\nconst RadioGroupItem = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  )\n})\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\n\nexport { RadioGroup, RadioGroupItem }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;AACA,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,qMAAA,CAAA,aAAgB,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4OACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;AACA,eAAe,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/plan-upgrade-modal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Separator } from '@/components/ui/separator'\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'\nimport { Label } from '@/components/ui/label'\nimport { Crown, Zap, Shield, Check, X } from 'lucide-react'\nimport { useChangePlan } from '@/hooks/use-plans'\nimport { Database } from '@/lib/supabase'\nimport { cn } from '@/lib/utils'\nimport { toast } from 'sonner'\n\ntype PlanType = Database['public']['Tables']['plan_types']['Row']\ntype UserPlan = {\n  plan_id: string\n  plan_name: string\n  display_name: string\n  max_projects: number\n  max_prompts_per_project: number\n  features: Record<string, boolean | string | number>\n  status: string\n  expires_at: string | null\n}\n\ninterface PlanUpgradeModalProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  currentPlan: UserPlan | null\n  planTypes: PlanType[]\n}\n\nexport function PlanUpgradeModal({ \n  open, \n  onOpenChange, \n  currentPlan, \n  planTypes \n}: PlanUpgradeModalProps) {\n  const [selectedPlan, setSelectedPlan] = useState<string>('')\n  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly')\n  const changePlanMutation = useChangePlan()\n\n  const getPlanIcon = (planName: string) => {\n    switch (planName) {\n      case 'free':\n        return <Zap className=\"h-6 w-6 text-blue-500\" />\n      case 'professional':\n        return <Crown className=\"h-6 w-6 text-yellow-500\" />\n      case 'enterprise':\n        return <Shield className=\"h-6 w-6 text-purple-500\" />\n      default:\n        return <Zap className=\"h-6 w-6 text-gray-500\" />\n    }\n  }\n\n  const getPlanColor = (planName: string) => {\n    switch (planName) {\n      case 'free':\n        return 'border-blue-200 bg-blue-50'\n      case 'professional':\n        return 'border-yellow-200 bg-yellow-50'\n      case 'enterprise':\n        return 'border-purple-200 bg-purple-50'\n      default:\n        return 'border-gray-200 bg-gray-50'\n    }\n  }\n\n  const getPrice = (plan: PlanType) => {\n    if (plan.name === 'enterprise') return 'Özel Fiyat'\n    if (plan.name === 'free') return 'Ücretsiz'\n    \n    const price = billingCycle === 'monthly' ? plan.price_monthly : plan.price_yearly\n    const period = billingCycle === 'monthly' ? 'ay' : 'yıl'\n    \n    return `₺${price}/${period}`\n  }\n\n  const getYearlySavings = (plan: PlanType) => {\n    if (plan.name === 'free' || plan.name === 'enterprise') return null\n    \n    const monthlyTotal = plan.price_monthly * 12\n    const yearlySavings = monthlyTotal - plan.price_yearly\n    const savingsPercentage = Math.round((yearlySavings / monthlyTotal) * 100)\n    \n    return { amount: yearlySavings, percentage: savingsPercentage }\n  }\n\n  const handleUpgrade = async () => {\n    if (!selectedPlan) {\n      toast.error('Lütfen bir plan seçin')\n      return\n    }\n\n    try {\n      await changePlanMutation.mutateAsync({\n        planName: selectedPlan,\n        billingCycle: billingCycle\n      })\n      \n      toast.success('Planınız başarıyla güncellendi!')\n      onOpenChange(false)\n    } catch (error) {\n      toast.error(error instanceof Error ? error.message : 'Plan güncellenirken hata oluştu')\n    }\n  }\n\n  const isCurrentPlan = (planName: string) => {\n    return currentPlan?.plan_name === planName\n  }\n\n  const canSelectPlan = (planName: string) => {\n    if (planName === 'enterprise') return false // Kurumsal plan manuel atanır\n    return !isCurrentPlan(planName)\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"text-2xl\">Planınızı Yükseltin</DialogTitle>\n          <DialogDescription>\n            İhtiyaçlarınıza en uygun planı seçin ve daha fazla özellikten yararlanın\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* Billing Cycle Seçimi */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-base font-medium\">Faturalama Döngüsü</Label>\n            <RadioGroup \n              value={billingCycle} \n              onValueChange={(value) => setBillingCycle(value as 'monthly' | 'yearly')}\n              className=\"flex gap-4\"\n            >\n              <div className=\"flex items-center space-x-2\">\n                <RadioGroupItem value=\"monthly\" id=\"monthly\" />\n                <Label htmlFor=\"monthly\">Aylık</Label>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <RadioGroupItem value=\"yearly\" id=\"yearly\" />\n                <Label htmlFor=\"yearly\" className=\"flex items-center gap-2\">\n                  Yıllık\n                  <Badge variant=\"secondary\" className=\"text-xs\">%17 İndirim</Badge>\n                </Label>\n              </div>\n            </RadioGroup>\n          </div>\n\n          {/* Plan Kartları */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            {planTypes.map((plan) => {\n              const savings = getYearlySavings(plan)\n              const isCurrent = isCurrentPlan(plan.name)\n              const canSelect = canSelectPlan(plan.name)\n              \n              return (\n                <Card \n                  key={plan.id}\n                  className={cn(\n                    'relative cursor-pointer transition-all duration-200',\n                    getPlanColor(plan.name),\n                    selectedPlan === plan.name && 'ring-2 ring-blue-500',\n                    isCurrent && 'ring-2 ring-green-500',\n                    !canSelect && 'opacity-60 cursor-not-allowed'\n                  )}\n                  onClick={() => canSelect && setSelectedPlan(plan.name)}\n                >\n                  {isCurrent && (\n                    <Badge className=\"absolute -top-2 left-1/2 transform -translate-x-1/2 bg-green-500\">\n                      Mevcut Plan\n                    </Badge>\n                  )}\n                  \n                  {plan.name === 'professional' && (\n                    <Badge className=\"absolute -top-2 right-4 bg-blue-500\">\n                      Popüler\n                    </Badge>\n                  )}\n\n                  <CardHeader className=\"text-center pb-2\">\n                    <div className=\"flex justify-center mb-2\">\n                      {getPlanIcon(plan.name)}\n                    </div>\n                    <CardTitle className=\"text-xl\">{plan.display_name}</CardTitle>\n                    <CardDescription className=\"text-sm\">\n                      {plan.description}\n                    </CardDescription>\n                    \n                    <div className=\"pt-2\">\n                      <div className=\"text-3xl font-bold text-gray-900\">\n                        {getPrice(plan)}\n                      </div>\n                      {billingCycle === 'yearly' && savings && (\n                        <div className=\"text-sm text-green-600 font-medium\">\n                          Yıllık ₺{savings.amount} tasarruf\n                        </div>\n                      )}\n                    </div>\n                  </CardHeader>\n\n                  <CardContent className=\"space-y-4\">\n                    <Separator />\n                    \n                    {/* Limitler */}\n                    <div className=\"space-y-2\">\n                      <div className=\"flex items-center justify-between text-sm\">\n                        <span>Proje Limiti</span>\n                        <span className=\"font-medium\">\n                          {plan.max_projects === -1 ? 'Sınırsız' : plan.max_projects}\n                        </span>\n                      </div>\n                      <div className=\"flex items-center justify-between text-sm\">\n                        <span>Prompt Limiti</span>\n                        <span className=\"font-medium\">\n                          {plan.max_prompts_per_project === -1 ? 'Sınırsız' : `${plan.max_prompts_per_project}/proje`}\n                        </span>\n                      </div>\n                    </div>\n\n                    <Separator />\n\n                    {/* Özellikler */}\n                    <div className=\"space-y-2\">\n                      <h4 className=\"font-medium text-sm\">Özellikler</h4>\n                      <div className=\"space-y-1\">\n                        {Object.entries(plan.features).map(([key, value]) => (\n                          <div key={key} className=\"flex items-center gap-2 text-xs\">\n                            {value ? (\n                              <Check className=\"h-3 w-3 text-green-500\" />\n                            ) : (\n                              <X className=\"h-3 w-3 text-gray-400\" />\n                            )}\n                            <span className={value ? 'text-gray-900' : 'text-gray-500'}>\n                              {key === 'context_gallery' && 'Context Gallery'}\n                              {key === 'api_access' && 'API Erişimi'}\n                              {key === 'support' && `${value} Destek`}\n                              {key === 'team_features' && 'Takım Özellikleri'}\n                              {key === 'advanced_analytics' && 'Gelişmiş Analitik'}\n                              {key === 'sso' && 'SSO Entegrasyonu'}\n                              {key === 'custom_deployment' && 'Özel Deployment'}\n                            </span>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n\n                    {plan.name === 'enterprise' && (\n                      <div className=\"text-xs text-gray-600 bg-gray-50 p-2 rounded\">\n                        Kurumsal plan için satış ekibimizle iletişime geçin\n                      </div>\n                    )}\n                  </CardContent>\n                </Card>\n              )\n            })}\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex justify-end gap-3 pt-4\">\n            <Button variant=\"outline\" onClick={() => onOpenChange(false)}>\n              İptal\n            </Button>\n            <Button \n              onClick={handleUpgrade}\n              disabled={!selectedPlan || changePlanMutation.isPending}\n            >\n              {changePlanMutation.isPending ? 'Güncelleniyor...' : 'Planı Güncelle'}\n            </Button>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AApBA;;;;;;;;;;;;;;AAyCO,SAAS,iBAAiB,EAC/B,IAAI,EACJ,YAAY,EACZ,WAAW,EACX,SAAS,EACa;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,qBAAqB,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD;IAEvC,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B;gBACE,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;QAC1B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,IAAI,KAAK,IAAI,KAAK,cAAc,OAAO;QACvC,IAAI,KAAK,IAAI,KAAK,QAAQ,OAAO;QAEjC,MAAM,QAAQ,iBAAiB,YAAY,KAAK,aAAa,GAAG,KAAK,YAAY;QACjF,MAAM,SAAS,iBAAiB,YAAY,OAAO;QAEnD,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,QAAQ;IAC9B;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,KAAK,cAAc,OAAO;QAE/D,MAAM,eAAe,KAAK,aAAa,GAAG;QAC1C,MAAM,gBAAgB,eAAe,KAAK,YAAY;QACtD,MAAM,oBAAoB,KAAK,KAAK,CAAC,AAAC,gBAAgB,eAAgB;QAEtE,OAAO;YAAE,QAAQ;YAAe,YAAY;QAAkB;IAChE;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,cAAc;YACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,mBAAmB,WAAW,CAAC;gBACnC,UAAU;gBACV,cAAc;YAChB;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,aAAa;QACf,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACvD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,aAAa,cAAc;IACpC;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,aAAa,cAAc,OAAO,MAAM,8BAA8B;;QAC1E,OAAO,CAAC,cAAc;IACxB;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAW;;;;;;sCAClC,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAwB;;;;;;8CACzC,8OAAC,0IAAA,CAAA,aAAU;oCACT,OAAO;oCACP,eAAe,CAAC,QAAU,gBAAgB;oCAC1C,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0IAAA,CAAA,iBAAc;oDAAC,OAAM;oDAAU,IAAG;;;;;;8DACnC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0IAAA,CAAA,iBAAc;oDAAC,OAAM;oDAAS,IAAG;;;;;;8DAClC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAS,WAAU;;wDAA0B;sEAE1D,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOvD,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC;gCACd,MAAM,UAAU,iBAAiB;gCACjC,MAAM,YAAY,cAAc,KAAK,IAAI;gCACzC,MAAM,YAAY,cAAc,KAAK,IAAI;gCAEzC,qBACE,8OAAC,gIAAA,CAAA,OAAI;oCAEH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uDACA,aAAa,KAAK,IAAI,GACtB,iBAAiB,KAAK,IAAI,IAAI,wBAC9B,aAAa,yBACb,CAAC,aAAa;oCAEhB,SAAS,IAAM,aAAa,gBAAgB,KAAK,IAAI;;wCAEpD,2BACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAmE;;;;;;wCAKrF,KAAK,IAAI,KAAK,gCACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsC;;;;;;sDAKzD,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;8DACZ,YAAY,KAAK,IAAI;;;;;;8DAExB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,KAAK,YAAY;;;;;;8DACjD,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,KAAK,WAAW;;;;;;8DAGnB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,SAAS;;;;;;wDAEX,iBAAiB,YAAY,yBAC5B,8OAAC;4DAAI,WAAU;;gEAAqC;gEACzC,QAAQ,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;sDAMhC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC,qIAAA,CAAA,YAAS;;;;;8DAGV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEAAK,WAAU;8EACb,KAAK,YAAY,KAAK,CAAC,IAAI,aAAa,KAAK,YAAY;;;;;;;;;;;;sEAG9D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEAAK,WAAU;8EACb,KAAK,uBAAuB,KAAK,CAAC,IAAI,aAAa,GAAG,KAAK,uBAAuB,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;;;8DAKjG,8OAAC,qIAAA,CAAA,YAAS;;;;;8DAGV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAsB;;;;;;sEACpC,8OAAC;4DAAI,WAAU;sEACZ,OAAO,OAAO,CAAC,KAAK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC9C,8OAAC;oEAAc,WAAU;;wEACtB,sBACC,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;iGAEjB,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;sFAEf,8OAAC;4EAAK,WAAW,QAAQ,kBAAkB;;gFACxC,QAAQ,qBAAqB;gFAC7B,QAAQ,gBAAgB;gFACxB,QAAQ,aAAa,GAAG,MAAM,OAAO,CAAC;gFACtC,QAAQ,mBAAmB;gFAC3B,QAAQ,wBAAwB;gFAChC,QAAQ,SAAS;gFACjB,QAAQ,uBAAuB;;;;;;;;mEAb1B;;;;;;;;;;;;;;;;gDAoBf,KAAK,IAAI,KAAK,8BACb,8OAAC;oDAAI,WAAU;8DAA+C;;;;;;;;;;;;;mCA1F7D,KAAK,EAAE;;;;;4BAiGlB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,aAAa;8CAAQ;;;;;;8CAG9D,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,CAAC,gBAAgB,mBAAmB,SAAS;8CAEtD,mBAAmB,SAAS,GAAG,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnE", "debugId": null}}, {"offset": {"line": 3009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/plan-display.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { Progress } from '@/components/ui/progress'\nimport { Separator } from '@/components/ui/separator'\nimport { Crown, Zap, Shield, TrendingUp, AlertTriangle } from 'lucide-react'\nimport { useUserActivePlan, useUserLimits, usePlanTypes } from '@/hooks/use-plans'\nimport { PlanUpgradeModal } from './plan-upgrade-modal'\nimport { cn } from '@/lib/utils'\n\nexport function PlanDisplay() {\n  const [showUpgradeModal, setShowUpgradeModal] = useState(false)\n  const { data: activePlan, isLoading: planLoading } = useUserActivePlan()\n  const { data: limits, isLoading: limitsLoading } = useUserLimits()\n  const { data: planTypes } = usePlanTypes()\n\n  if (planLoading || limitsLoading) {\n    return (\n      <Card>\n        <CardHeader>\n          <div className=\"h-6 bg-gray-200 rounded animate-pulse\" />\n          <div className=\"h-4 bg-gray-200 rounded animate-pulse w-2/3\" />\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-3\">\n            <div className=\"h-4 bg-gray-200 rounded animate-pulse\" />\n            <div className=\"h-4 bg-gray-200 rounded animate-pulse w-3/4\" />\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  const getPlanIcon = (planName: string) => {\n    switch (planName) {\n      case 'free':\n        return <Zap className=\"h-5 w-5 text-blue-500\" />\n      case 'professional':\n        return <Crown className=\"h-5 w-5 text-yellow-500\" />\n      case 'enterprise':\n        return <Shield className=\"h-5 w-5 text-purple-500\" />\n      default:\n        return <Zap className=\"h-5 w-5 text-gray-500\" />\n    }\n  }\n\n  const getPlanColor = (planName: string) => {\n    switch (planName) {\n      case 'free':\n        return 'bg-blue-50 text-blue-700 border-blue-200'\n      case 'professional':\n        return 'bg-yellow-50 text-yellow-700 border-yellow-200'\n      case 'enterprise':\n        return 'bg-purple-50 text-purple-700 border-purple-200'\n      default:\n        return 'bg-gray-50 text-gray-700 border-gray-200'\n    }\n  }\n\n  const getUsageColor = (current: number, max: number) => {\n    if (max === -1) return 'text-green-600' // Sınırsız\n    const percentage = (current / max) * 100\n    if (percentage >= 90) return 'text-red-600'\n    if (percentage >= 70) return 'text-yellow-600'\n    return 'text-green-600'\n  }\n\n  const getProgressColor = (current: number, max: number) => {\n    if (max === -1) return 'bg-green-500' // Sınırsız\n    const percentage = (current / max) * 100\n    if (percentage >= 90) return 'bg-red-500'\n    if (percentage >= 70) return 'bg-yellow-500'\n    return 'bg-green-500'\n  }\n\n  const shouldShowUpgradeWarning = () => {\n    if (!limits || !activePlan) return false\n    if (activePlan.plan_name === 'enterprise') return false\n    \n    const projectUsage = limits.max_projects === -1 ? 0 : (limits.current_projects / limits.max_projects) * 100\n    const promptUsage = limits.max_prompts_per_project === -1 ? 0 : (limits.current_prompts / limits.max_prompts_per_project) * 100\n    \n    return projectUsage >= 80 || promptUsage >= 80\n  }\n\n  return (\n    <>\n      <Card className=\"w-full\">\n        <CardHeader className=\"pb-3\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              {activePlan && getPlanIcon(activePlan.plan_name)}\n              <div>\n                <CardTitle className=\"text-lg\">\n                  {activePlan?.display_name || 'Plan Yükleniyor...'}\n                </CardTitle>\n                <CardDescription>\n                  Mevcut planınız ve kullanım durumunuz\n                </CardDescription>\n              </div>\n            </div>\n            {activePlan && (\n              <Badge \n                variant=\"outline\" \n                className={cn('font-medium', getPlanColor(activePlan.plan_name))}\n              >\n                {activePlan.status === 'active' ? 'Aktif' : activePlan.status}\n              </Badge>\n            )}\n          </div>\n        </CardHeader>\n\n        <CardContent className=\"space-y-4\">\n          {/* Kullanım İstatistikleri */}\n          {limits && (\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"font-medium\">Projeler</span>\n                  <span className={getUsageColor(limits.current_projects, limits.max_projects)}>\n                    {limits.current_projects} / {limits.max_projects === -1 ? '∞' : limits.max_projects}\n                  </span>\n                </div>\n                <Progress \n                  value={limits.max_projects === -1 ? 0 : (limits.current_projects / limits.max_projects) * 100}\n                  className=\"h-2\"\n                  style={{\n                    '--progress-background': getProgressColor(limits.current_projects, limits.max_projects)\n                  } as React.CSSProperties}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"font-medium\">Prompt&apos;lar (Proje başına max)</span>\n                  <span className={getUsageColor(limits.current_prompts, limits.max_prompts_per_project)}>\n                    {limits.current_prompts} / {limits.max_prompts_per_project === -1 ? '∞' : limits.max_prompts_per_project}\n                  </span>\n                </div>\n                <Progress \n                  value={limits.max_prompts_per_project === -1 ? 0 : (limits.current_prompts / limits.max_prompts_per_project) * 100}\n                  className=\"h-2\"\n                  style={{\n                    '--progress-background': getProgressColor(limits.current_prompts, limits.max_prompts_per_project)\n                  } as React.CSSProperties}\n                />\n              </div>\n            </div>\n          )}\n\n          {/* Upgrade Uyarısı */}\n          {shouldShowUpgradeWarning() && (\n            <div className=\"flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n              <AlertTriangle className=\"h-4 w-4 text-yellow-600\" />\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-yellow-800\">\n                  Limitinize yaklaşıyorsunuz\n                </p>\n                <p className=\"text-xs text-yellow-700\">\n                  Daha fazla özellik için planınızı yükseltin\n                </p>\n              </div>\n            </div>\n          )}\n\n          <Separator />\n\n          {/* Plan Özellikleri */}\n          {activePlan?.features && (\n            <div className=\"space-y-2\">\n              <h4 className=\"text-sm font-medium text-gray-900\">Plan Özellikleri</h4>\n              <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                {activePlan.features.context_gallery && (\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full\" />\n                    <span>Context Gallery</span>\n                  </div>\n                )}\n                {activePlan.features.api_access && (\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full\" />\n                    <span>API Erişimi</span>\n                  </div>\n                )}\n                {activePlan.features.team_features && (\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full\" />\n                    <span>Takım Özellikleri</span>\n                  </div>\n                )}\n                {activePlan.features.advanced_analytics && (\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full\" />\n                    <span>Gelişmiş Analitik</span>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {/* Upgrade Butonu */}\n          {activePlan?.plan_name !== 'enterprise' && (\n            <Button \n              onClick={() => setShowUpgradeModal(true)}\n              className=\"w-full\"\n              variant={shouldShowUpgradeWarning() ? \"default\" : \"outline\"}\n            >\n              <TrendingUp className=\"h-4 w-4 mr-2\" />\n              Planı Yükselt\n            </Button>\n          )}\n        </CardContent>\n      </Card>\n\n      <PlanUpgradeModal \n        open={showUpgradeModal}\n        onOpenChange={setShowUpgradeModal}\n        currentPlan={activePlan}\n        planTypes={planTypes || []}\n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAaO,SAAS;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,MAAM,UAAU,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD;IACrE,MAAM,EAAE,MAAM,MAAM,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD;IAC/D,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEvC,IAAI,eAAe,eAAe;QAChC,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;;sCACT,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B;gBACE,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;QAC1B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC,SAAiB;QACtC,IAAI,QAAQ,CAAC,GAAG,OAAO,iBAAiB,WAAW;;QACnD,MAAM,aAAa,AAAC,UAAU,MAAO;QACrC,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC,SAAiB;QACzC,IAAI,QAAQ,CAAC,GAAG,OAAO,eAAe,WAAW;;QACjD,MAAM,aAAa,AAAC,UAAU,MAAO;QACrC,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,2BAA2B;QAC/B,IAAI,CAAC,UAAU,CAAC,YAAY,OAAO;QACnC,IAAI,WAAW,SAAS,KAAK,cAAc,OAAO;QAElD,MAAM,eAAe,OAAO,YAAY,KAAK,CAAC,IAAI,IAAI,AAAC,OAAO,gBAAgB,GAAG,OAAO,YAAY,GAAI;QACxG,MAAM,cAAc,OAAO,uBAAuB,KAAK,CAAC,IAAI,IAAI,AAAC,OAAO,eAAe,GAAG,OAAO,uBAAuB,GAAI;QAE5H,OAAO,gBAAgB,MAAM,eAAe;IAC9C;IAEA,qBACE;;0BACE,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,cAAc,YAAY,WAAW,SAAS;sDAC/C,8OAAC;;8DACC,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAClB,YAAY,gBAAgB;;;;;;8DAE/B,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;;;;;;;gCAKpB,4BACC,8OAAC,iIAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe,aAAa,WAAW,SAAS;8CAE7D,WAAW,MAAM,KAAK,WAAW,UAAU,WAAW,MAAM;;;;;;;;;;;;;;;;;kCAMrE,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BAEpB,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,8OAAC;wDAAK,WAAW,cAAc,OAAO,gBAAgB,EAAE,OAAO,YAAY;;4DACxE,OAAO,gBAAgB;4DAAC;4DAAI,OAAO,YAAY,KAAK,CAAC,IAAI,MAAM,OAAO,YAAY;;;;;;;;;;;;;0DAGvF,8OAAC,oIAAA,CAAA,WAAQ;gDACP,OAAO,OAAO,YAAY,KAAK,CAAC,IAAI,IAAI,AAAC,OAAO,gBAAgB,GAAG,OAAO,YAAY,GAAI;gDAC1F,WAAU;gDACV,OAAO;oDACL,yBAAyB,iBAAiB,OAAO,gBAAgB,EAAE,OAAO,YAAY;gDACxF;;;;;;;;;;;;kDAIJ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,8OAAC;wDAAK,WAAW,cAAc,OAAO,eAAe,EAAE,OAAO,uBAAuB;;4DAClF,OAAO,eAAe;4DAAC;4DAAI,OAAO,uBAAuB,KAAK,CAAC,IAAI,MAAM,OAAO,uBAAuB;;;;;;;;;;;;;0DAG5G,8OAAC,oIAAA,CAAA,WAAQ;gDACP,OAAO,OAAO,uBAAuB,KAAK,CAAC,IAAI,IAAI,AAAC,OAAO,eAAe,GAAG,OAAO,uBAAuB,GAAI;gDAC/G,WAAU;gDACV,OAAO;oDACL,yBAAyB,iBAAiB,OAAO,eAAe,EAAE,OAAO,uBAAuB;gDAClG;;;;;;;;;;;;;;;;;;4BAOP,4CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAsC;;;;;;0DAGnD,8OAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;0CAO7C,8OAAC,qIAAA,CAAA,YAAS;;;;;4BAGT,YAAY,0BACX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,8OAAC;wCAAI,WAAU;;4CACZ,WAAW,QAAQ,CAAC,eAAe,kBAClC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;;;;;;;4CAGT,WAAW,QAAQ,CAAC,UAAU,kBAC7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;;;;;;;4CAGT,WAAW,QAAQ,CAAC,aAAa,kBAChC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;;;;;;;4CAGT,WAAW,QAAQ,CAAC,kBAAkB,kBACrC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;4BAQf,YAAY,cAAc,8BACzB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,oBAAoB;gCACnC,WAAU;gCACV,SAAS,6BAA6B,YAAY;;kDAElD,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC,8IAAA,CAAA,mBAAgB;gBACf,MAAM;gBACN,cAAc;gBACd,aAAa;gBACb,WAAW,aAAa,EAAE;;;;;;;;AAIlC", "debugId": null}}, {"offset": {"line": 3561, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/project-name-editor.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef, useCallback, memo } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Edit2, Check, X, Loader2, CheckCircle } from 'lucide-react'\nimport { useUpdateProjectNameSecure, useProjects } from '@/hooks/use-projects'\nimport {\n  createAdvancedDebouncedValidator,\n  isSameProjectName\n} from '@/lib/project-validation'\nimport { toast } from 'sonner'\nimport { cn } from '@/lib/utils'\n\ninterface ProjectNameEditorProps {\n  projectId: string\n  currentName: string\n  onNameUpdated?: (newName: string) => void\n  className?: string\n  disabled?: boolean\n}\n\nconst ProjectNameEditor = memo(function ProjectNameEditor({\n  projectId,\n  currentName,\n  onNameUpdated,\n  className,\n  disabled = false\n}: ProjectNameEditorProps) {\n  const [isEditing, setIsEditing] = useState(false)\n  const [editValue, setEditValue] = useState(currentName)\n  const [validationError, setValidationError] = useState<string>('')\n  const [isValidating, setIsValidating] = useState(false)\n  const [validationSuccess, setValidationSuccess] = useState(false)\n  \n  const inputRef = useRef<HTMLInputElement>(null)\n  const updateMutation = useUpdateProjectNameSecure()\n  const { data: projects = [] } = useProjects()\n\n  // Advanced debounced validator with duplicate check\n  const debouncedValidator = useCallback(() => {\n    return createAdvancedDebouncedValidator(projects, projectId, 300)\n  }, [projects, projectId])();\n\n  // Edit mode'a geçiş\n  const startEditing = useCallback(() => {\n    if (disabled || updateMutation.isPending) return\n    \n    setIsEditing(true)\n    setEditValue(currentName)\n    setValidationError('')\n    setIsValidating(false)\n    setValidationSuccess(false)\n  }, [disabled, updateMutation.isPending, currentName])\n\n  // Edit mode'dan çıkış\n  const cancelEditing = useCallback(() => {\n    setIsEditing(false)\n    setEditValue(currentName)\n    setValidationError('')\n    setIsValidating(false)\n    setValidationSuccess(false)\n  }, [currentName])\n\n  // Kaydetme işlemi\n  const saveChanges = useCallback(async () => {\n    if (!editValue.trim() || validationError || isValidating) {\n      return\n    }\n\n    // Değişiklik var mı kontrol et\n    if (isSameProjectName(editValue, currentName)) {\n      cancelEditing()\n      return\n    }\n\n    try {\n      const result = await updateMutation.mutateAsync({\n        projectId,\n        newName: editValue.trim()\n      })\n\n      toast.success('Proje adı başarıyla güncellendi!')\n      setIsEditing(false)\n      onNameUpdated?.(result.name)\n      \n    } catch (error) {\n      console.error('Project name update error:', error)\n      const errorMessage = error instanceof Error ? error.message : 'Proje adı güncellenirken bir hata oluştu'\n      toast.error(errorMessage)\n      \n      // Input'a focus ver ki kullanıcı düzeltebilsin\n      setTimeout(() => {\n        inputRef.current?.focus()\n      }, 100)\n    }\n  }, [editValue, validationError, isValidating, currentName, projectId, updateMutation, onNameUpdated, cancelEditing])\n\n  // Input değişikliği\n  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value\n    setEditValue(value)\n    \n    if (value.trim()) {\n      setIsValidating(true)\n      setValidationSuccess(false)\n      debouncedValidator(value, (result) => {\n        setValidationError(result.isValid ? '' : result.error || '')\n        setValidationSuccess(result.isValid && !isSameProjectName(value, currentName))\n        setIsValidating(false)\n      })\n    } else {\n      setValidationError('Proje adı boş olamaz')\n      setValidationSuccess(false)\n      setIsValidating(false)\n    }\n  }, [debouncedValidator, currentName])\n\n  // Keyboard event handlers\n  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault()\n      saveChanges()\n    } else if (e.key === 'Escape') {\n      e.preventDefault()\n      cancelEditing()\n    }\n  }, [saveChanges, cancelEditing])\n\n  // Auto-focus when editing starts\n  useEffect(() => {\n    if (isEditing && inputRef.current) {\n      inputRef.current.focus()\n      inputRef.current.select()\n    }\n  }, [isEditing])\n\n  // Update editValue when currentName changes\n  useEffect(() => {\n    if (!isEditing) {\n      setEditValue(currentName)\n    }\n  }, [currentName, isEditing])\n\n  // Editing mode UI\n  if (isEditing) {\n    return (\n      <div className={cn(\"flex items-center gap-2 w-full\", className)}>\n        <div className=\"flex-1 relative\">\n          <Input\n            ref={inputRef}\n            value={editValue}\n            onChange={handleInputChange}\n            onKeyDown={handleKeyDown}\n            className={cn(\n              \"text-sm font-medium\",\n              validationError && \"border-red-500 focus:border-red-500\",\n              validationSuccess && \"border-green-500 focus:border-green-500\",\n              \"focus:ring-2 focus:ring-blue-500/20\"\n            )}\n            placeholder=\"Proje adı...\"\n            disabled={updateMutation.isPending}\n            maxLength={50}\n            aria-label=\"Proje adını düzenle\"\n            aria-describedby={`project-name-feedback-${projectId}`}\n            aria-invalid={!!validationError}\n          />\n\n          {/* Character counter */}\n          <div className=\"absolute top-full right-0 mt-1 text-xs text-gray-400 z-10\">\n            <span className=\"sr-only\">Karakter sayısı: </span>\n            {editValue.length}/50\n          </div>\n\n          {/* Validation feedback */}\n          {(validationError || isValidating || validationSuccess) && (\n            <div\n              id={`project-name-feedback-${projectId}`}\n              className=\"absolute top-full left-0 mt-1 text-xs z-10\"\n              role=\"status\"\n              aria-live=\"polite\"\n            >\n              {isValidating ? (\n                <span className=\"text-gray-500 flex items-center gap-1\">\n                  <Loader2 className=\"h-3 w-3 animate-spin\" />\n                  Kontrol ediliyor...\n                </span>\n              ) : validationError ? (\n                <span className=\"text-red-500 flex items-center gap-1\">\n                  <X className=\"h-3 w-3\" />\n                  {validationError}\n                </span>\n              ) : validationSuccess ? (\n                <span className=\"text-green-600 flex items-center gap-1\">\n                  <CheckCircle className=\"h-3 w-3\" />\n                  Proje adı kullanılabilir\n                </span>\n              ) : null}\n            </div>\n          )}\n        </div>\n\n        {/* Action buttons */}\n        <div className=\"flex items-center gap-1\">\n          <Button\n            size=\"sm\"\n            variant=\"ghost\"\n            onClick={saveChanges}\n            disabled={\n              updateMutation.isPending ||\n              !!validationError ||\n              isValidating ||\n              !editValue.trim() ||\n              isSameProjectName(editValue, currentName)\n            }\n            className=\"h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50 touch-target\"\n            title=\"Kaydet (Enter)\"\n            aria-label=\"Proje adını kaydet\"\n          >\n            {updateMutation.isPending ? (\n              <Loader2 className=\"h-4 w-4 animate-spin\" />\n            ) : (\n              <Check className=\"h-4 w-4\" />\n            )}\n          </Button>\n          \n          <Button\n            size=\"sm\"\n            variant=\"ghost\"\n            onClick={cancelEditing}\n            disabled={updateMutation.isPending}\n            className=\"h-8 w-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-50 touch-target\"\n            title=\"İptal (Esc)\"\n            aria-label=\"Düzenlemeyi iptal et\"\n          >\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n    )\n  }\n\n  // Display mode UI\n  return (\n    <div className={cn(\"flex items-center gap-2 group w-full\", className)}>\n      <span\n        className=\"flex-1 text-sm font-medium text-gray-900 truncate\"\n        title={currentName}\n      >\n        {currentName}\n      </span>\n\n      <Button\n        size=\"sm\"\n        variant=\"ghost\"\n        onClick={(e) => {\n          e.stopPropagation(); // Prevent triggering parent click handlers\n          startEditing();\n        }}\n        disabled={disabled || updateMutation.isPending}\n        className={cn(\n          \"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity touch-target\",\n          \"text-gray-400 hover:text-gray-600 hover:bg-gray-50\",\n          \"focus:opacity-100 focus:ring-2 focus:ring-blue-500/20\",\n          \"lg:opacity-0 opacity-100\" // Mobile'da her zaman görünür, desktop'ta sadece hover'da\n        )}\n        title=\"Proje adını düzenle\"\n        aria-label=\"Proje adını düzenle\"\n      >\n        <Edit2 className=\"h-3 w-3\" />\n      </Button>\n    </div>\n  )\n})\n\nexport { ProjectNameEditor }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAIA;AACA;AAZA;;;;;;;;;;AAsBA,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,SAAS,kBAAkB,EACxD,SAAS,EACT,WAAW,EACX,aAAa,EACb,SAAS,EACT,WAAW,KAAK,EACO;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,iBAAiB,CAAA,GAAA,+HAAA,CAAA,6BAA0B,AAAD;IAChD,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAE1C,oDAAoD;IACpD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,OAAO,CAAA,GAAA,mIAAA,CAAA,mCAAgC,AAAD,EAAE,UAAU,WAAW;IAC/D,GAAG;QAAC;QAAU;KAAU;IAExB,oBAAoB;IACpB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,YAAY,eAAe,SAAS,EAAE;QAE1C,aAAa;QACb,aAAa;QACb,mBAAmB;QACnB,gBAAgB;QAChB,qBAAqB;IACvB,GAAG;QAAC;QAAU,eAAe,SAAS;QAAE;KAAY;IAEpD,sBAAsB;IACtB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,aAAa;QACb,aAAa;QACb,mBAAmB;QACnB,gBAAgB;QAChB,qBAAqB;IACvB,GAAG;QAAC;KAAY;IAEhB,kBAAkB;IAClB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,CAAC,UAAU,IAAI,MAAM,mBAAmB,cAAc;YACxD;QACF;QAEA,+BAA+B;QAC/B,IAAI,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,cAAc;YAC7C;YACA;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,eAAe,WAAW,CAAC;gBAC9C;gBACA,SAAS,UAAU,IAAI;YACzB;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,aAAa;YACb,gBAAgB,OAAO,IAAI;QAE7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAEZ,+CAA+C;YAC/C,WAAW;gBACT,SAAS,OAAO,EAAE;YACpB,GAAG;QACL;IACF,GAAG;QAAC;QAAW;QAAiB;QAAc;QAAa;QAAW;QAAgB;QAAe;KAAc;IAEnH,oBAAoB;IACpB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,aAAa;QAEb,IAAI,MAAM,IAAI,IAAI;YAChB,gBAAgB;YAChB,qBAAqB;YACrB,mBAAmB,OAAO,CAAC;gBACzB,mBAAmB,OAAO,OAAO,GAAG,KAAK,OAAO,KAAK,IAAI;gBACzD,qBAAqB,OAAO,OAAO,IAAI,CAAC,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;gBACjE,gBAAgB;YAClB;QACF,OAAO;YACL,mBAAmB;YACnB,qBAAqB;YACrB,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAoB;KAAY;IAEpC,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,EAAE,cAAc;YAChB;QACF;IACF,GAAG;QAAC;QAAa;KAAc;IAE/B,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,SAAS,OAAO,EAAE;YACjC,SAAS,OAAO,CAAC,KAAK;YACtB,SAAS,OAAO,CAAC,MAAM;QACzB;IACF,GAAG;QAAC;KAAU;IAEd,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,aAAa;QACf;IACF,GAAG;QAAC;QAAa;KAAU;IAE3B,kBAAkB;IAClB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;;8BACnD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,KAAK;4BACL,OAAO;4BACP,UAAU;4BACV,WAAW;4BACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uBACA,mBAAmB,uCACnB,qBAAqB,2CACrB;4BAEF,aAAY;4BACZ,UAAU,eAAe,SAAS;4BAClC,WAAW;4BACX,cAAW;4BACX,oBAAkB,CAAC,sBAAsB,EAAE,WAAW;4BACtD,gBAAc,CAAC,CAAC;;;;;;sCAIlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAU;;;;;;gCACzB,UAAU,MAAM;gCAAC;;;;;;;wBAInB,CAAC,mBAAmB,gBAAgB,iBAAiB,mBACpD,8OAAC;4BACC,IAAI,CAAC,sBAAsB,EAAE,WAAW;4BACxC,WAAU;4BACV,MAAK;4BACL,aAAU;sCAET,6BACC,8OAAC;gCAAK,WAAU;;kDACd,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAyB;;;;;;uCAG5C,gCACF,8OAAC;gCAAK,WAAU;;kDACd,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCACZ;;;;;;uCAED,kCACF,8OAAC;gCAAK,WAAU;;kDACd,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;uCAGnC;;;;;;;;;;;;8BAMV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,UACE,eAAe,SAAS,IACxB,CAAC,CAAC,mBACF,gBACA,CAAC,UAAU,IAAI,MACf,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;4BAE/B,WAAU;4BACV,OAAM;4BACN,cAAW;sCAEV,eAAe,SAAS,iBACvB,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAEnB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAIrB,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,eAAe,SAAS;4BAClC,WAAU;4BACV,OAAM;4BACN,cAAW;sCAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAKvB;IAEA,kBAAkB;IAClB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;;0BACzD,8OAAC;gBACC,WAAU;gBACV,OAAO;0BAEN;;;;;;0BAGH,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,SAAS,CAAC;oBACR,EAAE,eAAe,IAAI,2CAA2C;oBAChE;gBACF;gBACA,UAAU,YAAY,eAAe,SAAS;gBAC9C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iFACA,sDACA,yDACA,2BAA2B,0DAA0D;;gBAEvF,OAAM;gBACN,cAAW;0BAEX,cAAA,8OAAC,kMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIzB", "debugId": null}}, {"offset": {"line": 3935, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/project-sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardHeader } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { Plus, Search, Folder, LogOut, User, X, ChevronLeft, ChevronRight } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useAppStore } from \"@/store/app-store\";\nimport { useProjects, useCreateProject } from \"@/hooks/use-projects\";\nimport { useUser, useSignOut } from \"@/hooks/use-auth\";\nimport { useUserLimits } from \"@/hooks/use-plans\";\nimport { InlineLimitWarning } from \"@/components/limit-warning\";\nimport { PlanDisplay } from \"@/components/plan-display\";\nimport { ProjectNameEditor } from \"@/components/project-name-editor\";\nimport { toast } from \"sonner\";\n\ninterface ProjectSidebarProps {\n  onClose?: () => void;\n  isCollapsed?: boolean;\n  onToggleCollapse?: () => void;\n}\n\nexport function ProjectSidebar({ onClose, isCollapsed = false, onToggleCollapse }: ProjectSidebarProps) {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [isCreatingProject, setIsCreatingProject] = useState(false);\n  const [newProjectName, setNewProjectName] = useState(\"\");\n  const { activeProjectId, setActiveProjectId } = useAppStore();\n  \n  const { data: projects = [], isLoading: projectsLoading, error: projectsError } = useProjects();\n  const { data: user } = useUser();\n  const { data: limits } = useUserLimits();\n  const createProjectMutation = useCreateProject();\n  const signOutMutation = useSignOut();\n\n  console.log(`📁 [PROJECT_SIDEBAR] Projects:`, {\n    count: projects.length,\n    loading: projectsLoading,\n    error: projectsError,\n    projects: projects.map(p => ({ id: p.id, name: p.name }))\n  });\n\n  const filteredProjects = projects.filter(project =>\n    project.name.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const handleCreateProject = async () => {\n    if (newProjectName.trim()) {\n      // Plan limiti kontrol et\n      if (limits && !limits.can_create_project) {\n        toast.error(`Proje oluşturma limitinize ulaştınız (${limits.current_projects}/${limits.max_projects}). Planınızı yükseltin.`);\n        return;\n      }\n\n      try {\n        const newProject = await createProjectMutation.mutateAsync({\n          name: newProjectName.trim(),\n          context_text: \"\",\n        });\n        setActiveProjectId(newProject.id);\n        setNewProjectName(\"\");\n        setIsCreatingProject(false);\n      } catch (error) {\n        console.error(\"Proje oluşturma hatası:\", error);\n      }\n    }\n  };\n\n  const handleSignOut = async () => {\n    try {\n      console.log('Logout button clicked')\n      await signOutMutation.mutateAsync();\n    } catch (error) {\n      console.error('Logout error in component:', error)\n      // Even if there's an error, the mutation's onError will handle cleanup\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Header */}\n      <div className={`border-b border-gray-200 ${isCollapsed ? 'p-2' : 'p-4 lg:p-6'}`}>\n        <div className={`flex items-center ${isCollapsed ? 'justify-center mb-2' : 'justify-between mb-4'}`}>\n          {!isCollapsed && (\n            <div className=\"flex items-center gap-2\">\n              <img\n                src=\"/logo.png\"\n                alt=\"Promptbir Logo\"\n                className=\"h-6 w-auto\"\n              />\n              <h1 className=\"text-xl font-semibold text-gray-900\">Promptbir</h1>\n            </div>\n          )}\n          {isCollapsed && (\n            <img\n              src=\"/logo.png\"\n              alt=\"Promptbir Logo\"\n              className=\"h-6 w-auto\"\n              title=\"Promptbir\"\n            />\n          )}\n          <div className={`flex items-center gap-2 ${isCollapsed ? 'flex-col' : ''}`}>\n            {/* Desktop Toggle Button */}\n            {onToggleCollapse && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={onToggleCollapse}\n                className=\"hidden lg:flex\"\n                title={isCollapsed ? 'Proje panelini genişlet' : 'Proje panelini daralt'}\n              >\n                {isCollapsed ? <ChevronRight className=\"h-4 w-4\" /> : <ChevronLeft className=\"h-4 w-4\" />}\n              </Button>\n            )}\n            {/* Mobile Close Button */}\n            {onClose && (\n              <Button variant=\"ghost\" size=\"sm\" onClick={onClose} className=\"lg:hidden\">\n                <X className=\"h-4 w-4\" />\n              </Button>\n            )}\n            <Link href=\"/profile\">\n              <Button variant=\"ghost\" size=\"sm\" title={user?.email || 'Kullanıcı'}>\n                <User className=\"h-4 w-4\" />\n              </Button>\n            </Link>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleSignOut}\n              disabled={signOutMutation.isPending}\n              title=\"Çıkış Yap\"\n            >\n              <LogOut className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Search - Hidden when collapsed */}\n        {!isCollapsed && (\n          <div className=\"relative mb-4\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <Input\n              placeholder=\"Projelerde ara...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n        )}\n\n        {/* Plan Limit Warning */}\n        {!isCollapsed && limits && (\n          <InlineLimitWarning\n            type=\"project\"\n            current={limits.current_projects}\n            max={limits.max_projects}\n            onUpgrade={() => {/* Plan upgrade modal açılacak */}}\n          />\n        )}\n\n        {/* New Project Button */}\n        <Button\n          onClick={() => setIsCreatingProject(true)}\n          className={`${isCollapsed ? 'w-8 h-8 p-0' : 'w-full'}`}\n          size=\"sm\"\n          title={isCollapsed ? 'Yeni Proje' : undefined}\n          disabled={limits && !limits.can_create_project}\n        >\n          <Plus className=\"h-4 w-4\" />\n          {!isCollapsed && <span className=\"ml-2\">Yeni Proje</span>}\n        </Button>\n      </div>\n\n      {/* Projects List */}\n      <ScrollArea className={`flex-1 ${isCollapsed ? 'p-2' : 'p-4'}`}>\n        <div className=\"space-y-2\">\n          {/* New Project Input - Hidden when collapsed */}\n          {isCreatingProject && !isCollapsed && (\n            <Card className=\"border-blue-200 bg-blue-50\">\n              <CardContent className=\"p-3\">\n                <Input\n                  placeholder=\"Proje adı...\"\n                  value={newProjectName}\n                  onChange={(e) => setNewProjectName(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && handleCreateProject()}\n                  className=\"mb-2\"\n                  autoFocus\n                />\n                <div className=\"flex gap-2\">\n                  <Button size=\"sm\" onClick={handleCreateProject}>\n                    Oluştur\n                  </Button>\n                  <Button \n                    size=\"sm\" \n                    variant=\"outline\" \n                    onClick={() => {\n                      setIsCreatingProject(false);\n                      setNewProjectName(\"\");\n                    }}\n                  >\n                    İptal\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Project Items */}\n          {filteredProjects.map((project) => (\n            <Card\n              key={project.id}\n              className={`transition-all hover:shadow-md cursor-pointer ${\n                activeProjectId === project.id\n                  ? 'border-blue-500 bg-blue-50'\n                  : 'border-gray-200 hover:border-gray-300'\n              } ${isCollapsed ? 'p-2' : ''}`}\n              onClick={isCollapsed ? () => {\n                setActiveProjectId(project.id);\n                onClose?.(); // Mobilde seçim sonrası sidebar'ı kapat\n              } : () => {\n                setActiveProjectId(project.id);\n                onClose?.(); // Mobilde seçim sonrası sidebar'ı kapat\n              }}\n              title={isCollapsed ? project.name : undefined}\n            >\n              {isCollapsed ? (\n                <div className=\"flex items-center justify-center\">\n                  <Folder className={`h-4 w-4 ${\n                    activeProjectId === project.id ? 'text-blue-600' : 'text-gray-600'\n                  }`} />\n                </div>\n              ) : (\n                <>\n                  <CardHeader className=\"pb-2\">\n                    <div className=\"flex items-center gap-2\">\n                      <Folder className=\"h-4 w-4 text-blue-600 flex-shrink-0\" />\n                      <ProjectNameEditor\n                        projectId={project.id}\n                        currentName={project.name}\n                        onNameUpdated={(newName) => {\n                          console.log(`📝 [PROJECT_SIDEBAR] Project name updated:`, {\n                            projectId: project.id,\n                            oldName: project.name,\n                            newName\n                          })\n                          // Cache otomatik olarak güncellenecek\n                        }}\n                        className=\"flex-1 min-w-0\"\n                      />\n                    </div>\n                  </CardHeader>\n                  <CardContent className=\"pt-0\">\n                    <p className=\"text-xs text-gray-500\">\n                      {new Date(project.created_at).toLocaleDateString('tr-TR')}\n                    </p>\n                  </CardContent>\n                </>\n              )}\n            </Card>\n          ))}\n        </div>\n      </ScrollArea>\n\n      {/* Plan Display - Hidden when collapsed */}\n      {!isCollapsed && (\n        <div className=\"p-4 border-t border-gray-200\">\n          <PlanDisplay />\n        </div>\n      )}\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;;;AAwBO,SAAS,eAAe,EAAE,OAAO,EAAE,cAAc,KAAK,EAAE,gBAAgB,EAAuB;IACpG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAE1D,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,WAAW,eAAe,EAAE,OAAO,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAC5F,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC7B,MAAM,EAAE,MAAM,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD;IACrC,MAAM,wBAAwB,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IAC7C,MAAM,kBAAkB,CAAA,GAAA,2HAAA,CAAA,aAAU,AAAD;IAEjC,QAAQ,GAAG,CAAC,CAAC,8BAA8B,CAAC,EAAE;QAC5C,OAAO,SAAS,MAAM;QACtB,SAAS;QACT,OAAO;QACP,UAAU,SAAS,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,IAAI,EAAE,EAAE;gBAAE,MAAM,EAAE,IAAI;YAAC,CAAC;IACzD;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UACvC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG5D,MAAM,sBAAsB;QAC1B,IAAI,eAAe,IAAI,IAAI;YACzB,yBAAyB;YACzB,IAAI,UAAU,CAAC,OAAO,kBAAkB,EAAE;gBACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,sCAAsC,EAAE,OAAO,gBAAgB,CAAC,CAAC,EAAE,OAAO,YAAY,CAAC,uBAAuB,CAAC;gBAC5H;YACF;YAEA,IAAI;gBACF,MAAM,aAAa,MAAM,sBAAsB,WAAW,CAAC;oBACzD,MAAM,eAAe,IAAI;oBACzB,cAAc;gBAChB;gBACA,mBAAmB,WAAW,EAAE;gBAChC,kBAAkB;gBAClB,qBAAqB;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,gBAAgB,WAAW;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,uEAAuE;QACzE;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,yBAAyB,EAAE,cAAc,QAAQ,cAAc;;kCAC9E,8OAAC;wBAAI,WAAW,CAAC,kBAAkB,EAAE,cAAc,wBAAwB,wBAAwB;;4BAChG,CAAC,6BACA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;kDAEZ,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;4BAGvD,6BACC,8OAAC;gCACC,KAAI;gCACJ,KAAI;gCACJ,WAAU;gCACV,OAAM;;;;;;0CAGV,8OAAC;gCAAI,WAAW,CAAC,wBAAwB,EAAE,cAAc,aAAa,IAAI;;oCAEvE,kCACC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,OAAO,cAAc,4BAA4B;kDAEhD,4BAAc,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;iEAAe,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;oCAIhF,yBACC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,SAAS;wCAAS,WAAU;kDAC5D,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;kDAGjB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,OAAO,MAAM,SAAS;sDACtD,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGpB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,gBAAgB,SAAS;wCACnC,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAMvB,CAAC,6BACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;oBAMf,CAAC,eAAe,wBACf,8OAAC,sIAAA,CAAA,qBAAkB;wBACjB,MAAK;wBACL,SAAS,OAAO,gBAAgB;wBAChC,KAAK,OAAO,YAAY;wBACxB,WAAW,KAAwC;;;;;;kCAKvD,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,qBAAqB;wBACpC,WAAW,GAAG,cAAc,gBAAgB,UAAU;wBACtD,MAAK;wBACL,OAAO,cAAc,eAAe;wBACpC,UAAU,UAAU,CAAC,OAAO,kBAAkB;;0CAE9C,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,CAAC,6BAAe,8OAAC;gCAAK,WAAU;0CAAO;;;;;;;;;;;;;;;;;;0BAK5C,8OAAC,0IAAA,CAAA,aAAU;gBAAC,WAAW,CAAC,OAAO,EAAE,cAAc,QAAQ,OAAO;0BAC5D,cAAA,8OAAC;oBAAI,WAAU;;wBAEZ,qBAAqB,CAAC,6BACrB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;wCACxC,WAAU;wCACV,SAAS;;;;;;kDAEX,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAS;0DAAqB;;;;;;0DAGhD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;oDACP,qBAAqB;oDACrB,kBAAkB;gDACpB;0DACD;;;;;;;;;;;;;;;;;;;;;;;wBASR,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,gIAAA,CAAA,OAAI;gCAEH,WAAW,CAAC,8CAA8C,EACxD,oBAAoB,QAAQ,EAAE,GAC1B,+BACA,wCACL,CAAC,EAAE,cAAc,QAAQ,IAAI;gCAC9B,SAAS,cAAc;oCACrB,mBAAmB,QAAQ,EAAE;oCAC7B,aAAa,wCAAwC;gCACvD,IAAI;oCACF,mBAAmB,QAAQ,EAAE;oCAC7B,aAAa,wCAAwC;gCACvD;gCACA,OAAO,cAAc,QAAQ,IAAI,GAAG;0CAEnC,4BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAW,CAAC,QAAQ,EAC1B,oBAAoB,QAAQ,EAAE,GAAG,kBAAkB,iBACnD;;;;;;;;;;yDAGJ;;sDACE,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC,+IAAA,CAAA,oBAAiB;wDAChB,WAAW,QAAQ,EAAE;wDACrB,aAAa,QAAQ,IAAI;wDACzB,eAAe,CAAC;4DACd,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC,EAAE;gEACxD,WAAW,QAAQ,EAAE;gEACrB,SAAS,QAAQ,IAAI;gEACrB;4DACF;wDACA,sCAAsC;wDACxC;wDACA,WAAU;;;;;;;;;;;;;;;;;sDAIhB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;+BA3CpD,QAAQ,EAAE;;;;;;;;;;;;;;;;YAsDtB,CAAC,6BACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;AAKtB", "debugId": null}}, {"offset": {"line": 4424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/auth-guard.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter, usePathname } from 'next/navigation'\nimport { useUser, useAuthStateListener } from '@/hooks/use-auth'\n\ninterface AuthGuardProps {\n  children: React.ReactNode\n}\n\nexport function AuthGuard({ children }: AuthGuardProps) {\n  const { data: user, isLoading, error } = useUser()\n  const router = useRouter()\n  const pathname = usePathname()\n\n  console.log(`🛡️ [AUTH_GUARD] Status: loading=${isLoading}, user=${user?.email || 'null'}, error=${error ? 'YES' : 'NO'}, pathname=${pathname}`)\n\n  // Auth state değişikliklerini dinle\n  useAuthStateListener()\n\n  // Authenticated user'ları dashboard'a yönlendir\n  useEffect(() => {\n    if (!isLoading && user && pathname === '/auth') {\n      console.log(`🚀 [AUTH_GUARD] Authenticated user on /auth, redirecting to dashboard`)\n      router.replace('/dashboard')\n    }\n  }, [user, isLoading, pathname, router])\n\n  // Loading durumu - middleware handles redirects, so we just show loading\n  if (isLoading) {\n    console.log(`⏳ [AUTH_GUARD] Showing loading state`)\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">PromptFlow yükleniyor...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Auth error durumu (refresh token hataları vs.)\n  if (error) {\n    const errorMessage = error instanceof Error ? error.message : String(error)\n    console.error(`❌ [AUTH_GUARD] Auth error:`, errorMessage)\n\n    if (errorMessage.includes('Invalid Refresh Token') ||\n        errorMessage.includes('Refresh Token Not Found')) {\n      console.log(`🔄 [AUTH_GUARD] Refresh token error - showing loading`)\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Oturum yenileniyor...</p>\n          </div>\n        </div>\n      )\n    }\n  }\n\n  // If no user, let middleware handle redirect - just show loading\n  if (!user) {\n    console.log(`🚫 [AUTH_GUARD] No user - showing loading (middleware will handle redirect)`)\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Kimlik doğrulanıyor...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Kullanıcı giriş yapmışsa ana uygulamayı göster\n  console.log(`✅ [AUTH_GUARD] User authenticated - showing app`)\n  return <>{children}</>\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,UAAU,OAAO,EAAE,MAAM,SAAS,OAAO,QAAQ,EAAE,QAAQ,QAAQ,KAAK,WAAW,EAAE,UAAU;IAE/I,oCAAoC;IACpC,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD;IAEnB,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,QAAQ,aAAa,SAAS;YAC9C,QAAQ,GAAG,CAAC,CAAC,qEAAqE,CAAC;YACnF,OAAO,OAAO,CAAC;QACjB;IACF,GAAG;QAAC;QAAM;QAAW;QAAU;KAAO;IAEtC,yEAAyE;IACzE,IAAI,WAAW;QACb,QAAQ,GAAG,CAAC,CAAC,oCAAoC,CAAC;QAClD,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,iDAAiD;IACjD,IAAI,OAAO;QACT,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,QAAQ,KAAK,CAAC,CAAC,0BAA0B,CAAC,EAAE;QAE5C,IAAI,aAAa,QAAQ,CAAC,4BACtB,aAAa,QAAQ,CAAC,4BAA4B;YACpD,QAAQ,GAAG,CAAC,CAAC,qDAAqD,CAAC;YACnE,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;QAIrC;IACF;IAEA,iEAAiE;IACjE,IAAI,CAAC,MAAM;QACT,QAAQ,GAAG,CAAC,CAAC,2EAA2E,CAAC;QACzF,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,iDAAiD;IACjD,QAAQ,GAAG,CAAC,CAAC,+CAA+C,CAAC;IAC7D,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 4574, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-responsive.ts"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect, useCallback, useMemo } from 'react'\n\n// Enhanced breakpoint system\nexport const BREAKPOINTS = {\n  xs: 0,      // Extra small devices (phones)\n  sm: 640,    // Small devices (large phones)\n  md: 768,    // Medium devices (tablets)\n  lg: 1024,   // Large devices (laptops)\n  xl: 1280,   // Extra large devices (desktops)\n  '2xl': 1536 // 2X large devices (large desktops)\n} as const\n\nexport type Breakpoint = keyof typeof BREAKPOINTS\nexport type BreakpointValue = typeof BREAKPOINTS[Breakpoint]\n\n// Device type detection\nexport type DeviceType = 'mobile' | 'tablet' | 'desktop'\nexport type Orientation = 'portrait' | 'landscape'\n\ninterface ResponsiveState {\n  width: number\n  height: number\n  breakpoint: Breakpoint\n  deviceType: DeviceType\n  orientation: Orientation\n  isMobile: boolean\n  isTablet: boolean\n  isDesktop: boolean\n  isTouch: boolean\n  pixelRatio: number\n}\n\n// Enhanced responsive hook with device detection\nexport function useResponsive(): ResponsiveState {\n  const [state, setState] = useState<ResponsiveState>(() => {\n    if (typeof window === 'undefined') {\n      return {\n        width: 1024,\n        height: 768,\n        breakpoint: 'lg' as Breakpoint,\n        deviceType: 'desktop' as DeviceType,\n        orientation: 'landscape' as Orientation,\n        isMobile: false,\n        isTablet: false,\n        isDesktop: true,\n        isTouch: false,\n        pixelRatio: 1\n      }\n    }\n\n    const width = window.innerWidth\n    const height = window.innerHeight\n    const breakpoint = getBreakpoint(width)\n    const deviceType = getDeviceType(width)\n    const orientation = width > height ? 'landscape' : 'portrait'\n    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0\n\n    return {\n      width,\n      height,\n      breakpoint,\n      deviceType,\n      orientation,\n      isMobile: deviceType === 'mobile',\n      isTablet: deviceType === 'tablet',\n      isDesktop: deviceType === 'desktop',\n      isTouch,\n      pixelRatio: window.devicePixelRatio || 1\n    }\n  })\n\n  const updateState = useCallback(() => {\n    if (typeof window === 'undefined') return\n\n    const width = window.innerWidth\n    const height = window.innerHeight\n    const breakpoint = getBreakpoint(width)\n    const deviceType = getDeviceType(width)\n    const orientation = width > height ? 'landscape' : 'portrait'\n    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0\n\n    setState({\n      width,\n      height,\n      breakpoint,\n      deviceType,\n      orientation,\n      isMobile: deviceType === 'mobile',\n      isTablet: deviceType === 'tablet',\n      isDesktop: deviceType === 'desktop',\n      isTouch,\n      pixelRatio: window.devicePixelRatio || 1\n    })\n  }, [])\n\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    // Debounced resize handler\n    let timeoutId: NodeJS.Timeout\n    const debouncedUpdate = () => {\n      clearTimeout(timeoutId)\n      timeoutId = setTimeout(updateState, 150)\n    }\n\n    window.addEventListener('resize', debouncedUpdate)\n    window.addEventListener('orientationchange', updateState)\n\n    return () => {\n      window.removeEventListener('resize', debouncedUpdate)\n      window.removeEventListener('orientationchange', updateState)\n      clearTimeout(timeoutId)\n    }\n  }, [updateState])\n\n  return state\n}\n\n// Utility functions\nfunction getBreakpoint(width: number): Breakpoint {\n  if (width >= BREAKPOINTS['2xl']) return '2xl'\n  if (width >= BREAKPOINTS.xl) return 'xl'\n  if (width >= BREAKPOINTS.lg) return 'lg'\n  if (width >= BREAKPOINTS.md) return 'md'\n  if (width >= BREAKPOINTS.sm) return 'sm'\n  return 'xs'\n}\n\nfunction getDeviceType(width: number): DeviceType {\n  if (width < BREAKPOINTS.md) return 'mobile'\n  if (width < BREAKPOINTS.lg) return 'tablet'\n  return 'desktop'\n}\n\n// Media query hook\nexport function useMediaQuery(query: string): boolean {\n  const [matches, setMatches] = useState(false)\n\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    const mediaQuery = window.matchMedia(query)\n    setMatches(mediaQuery.matches)\n\n    const handler = (event: MediaQueryListEvent) => {\n      setMatches(event.matches)\n    }\n\n    mediaQuery.addEventListener('change', handler)\n    return () => mediaQuery.removeEventListener('change', handler)\n  }, [query])\n\n  return matches\n}\n\n// Breakpoint-specific hooks\nexport function useBreakpoint(breakpoint: Breakpoint): boolean {\n  return useMediaQuery(`(min-width: ${BREAKPOINTS[breakpoint]}px)`)\n}\n\nexport function useBreakpointValue<T>(values: Partial<Record<Breakpoint, T>>): T | undefined {\n  const { breakpoint } = useResponsive()\n  \n  return useMemo(() => {\n    // Find the best matching value for current breakpoint\n    const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs']\n    const currentIndex = breakpointOrder.indexOf(breakpoint)\n    \n    // Look for exact match first\n    if (values[breakpoint] !== undefined) {\n      return values[breakpoint]\n    }\n    \n    // Look for smaller breakpoints\n    for (let i = currentIndex + 1; i < breakpointOrder.length; i++) {\n      const bp = breakpointOrder[i]\n      if (values[bp] !== undefined) {\n        return values[bp]\n      }\n    }\n    \n    return undefined\n  }, [breakpoint, values])\n}\n\n// Container query hook (for component-level responsive design)\nexport function useContainerQuery(containerRef: React.RefObject<HTMLElement>) {\n  const [containerWidth, setContainerWidth] = useState(0)\n\n  useEffect(() => {\n    if (!containerRef.current) return\n\n    const resizeObserver = new ResizeObserver((entries) => {\n      for (const entry of entries) {\n        setContainerWidth(entry.contentRect.width)\n      }\n    })\n\n    resizeObserver.observe(containerRef.current)\n\n    return () => {\n      resizeObserver.disconnect()\n    }\n  }, [containerRef])\n\n  return {\n    containerWidth,\n    isContainerSmall: containerWidth < 400,\n    isContainerMedium: containerWidth >= 400 && containerWidth < 768,\n    isContainerLarge: containerWidth >= 768\n  }\n}\n\n// Responsive grid hook\nexport function useResponsiveGrid(options: {\n  minItemWidth: number\n  gap?: number\n  maxColumns?: number\n}) {\n  const { width } = useResponsive()\n  const { minItemWidth, gap = 16, maxColumns = Infinity } = options\n\n  return useMemo(() => {\n    const availableWidth = width - gap\n    const itemsPerRow = Math.floor(availableWidth / (minItemWidth + gap))\n    const columns = Math.min(Math.max(itemsPerRow, 1), maxColumns)\n    const itemWidth = (availableWidth - (columns - 1) * gap) / columns\n\n    return {\n      columns,\n      itemWidth,\n      gridTemplateColumns: `repeat(${columns}, 1fr)`,\n      gap: `${gap}px`\n    }\n  }, [width, minItemWidth, gap, maxColumns])\n}\n\n// Safe area hook for mobile devices\nexport function useSafeArea() {\n  const [safeArea, setSafeArea] = useState({\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  })\n\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    const updateSafeArea = () => {\n      const style = getComputedStyle(document.documentElement)\n      \n      setSafeArea({\n        top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),\n        right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0'),\n        bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),\n        left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0')\n      })\n    }\n\n    updateSafeArea()\n    window.addEventListener('resize', updateSafeArea)\n    window.addEventListener('orientationchange', updateSafeArea)\n\n    return () => {\n      window.removeEventListener('resize', updateSafeArea)\n      window.removeEventListener('orientationchange', updateSafeArea)\n    }\n  }, [])\n\n  return safeArea\n}\n\n// Responsive text scaling\nexport function useResponsiveText() {\n  const { deviceType, width } = useResponsive()\n\n  return useMemo(() => {\n    const baseSize = 16 // Base font size in px\n    \n    if (deviceType === 'mobile') {\n      // Ensure minimum 16px to prevent zoom on iOS\n      return Math.max(baseSize, 16)\n    }\n    \n    if (deviceType === 'tablet') {\n      return baseSize * 1.1\n    }\n    \n    // Desktop scaling based on width\n    if (width > 1920) {\n      return baseSize * 1.2\n    }\n    \n    return baseSize\n  }, [deviceType, width])\n}\n\n// Performance-optimized responsive component wrapper\nexport function withResponsive<P extends object>(\n  Component: React.ComponentType<P & { responsive: ResponsiveState }>\n) {\n  return function ResponsiveComponent(props: P) {\n    const responsive = useResponsive()\n    const combinedProps = { ...props, responsive } as P & { responsive: ResponsiveState }\n\n    return React.createElement(Component, combinedProps)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAEA;AAFA;;AAKO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO,KAAK,oCAAoC;AAClD;AAuBO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,wCAAmC;YACjC,OAAO;gBACL,OAAO;gBACP,QAAQ;gBACR,YAAY;gBACZ,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,SAAS;gBACT,YAAY;YACd;QACF;;;QAEA,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IAcR;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,wCAAmC;;;QAEnC,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IAcR,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;;QAEnC,2BAA2B;QAC3B,IAAI;QACJ,MAAM;IAaR,GAAG;QAAC;KAAY;IAEhB,OAAO;AACT;AAEA,oBAAoB;AACpB,SAAS,cAAc,KAAa;IAClC,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,OAAO;IACxC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;IACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;IACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;IACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;IACpC,OAAO;AACT;AAEA,SAAS,cAAc,KAAa;IAClC,IAAI,QAAQ,YAAY,EAAE,EAAE,OAAO;IACnC,IAAI,QAAQ,YAAY,EAAE,EAAE,OAAO;IACnC,OAAO;AACT;AAGO,SAAS,cAAc,KAAa;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;;QAEnC,MAAM;QAGN,MAAM;IAMR,GAAG;QAAC;KAAM;IAEV,OAAO;AACT;AAGO,SAAS,cAAc,UAAsB;IAClD,OAAO,cAAc,CAAC,YAAY,EAAE,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC;AAClE;AAEO,SAAS,mBAAsB,MAAsC;IAC1E,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,sDAAsD;QACtD,MAAM,kBAAgC;YAAC;YAAO;YAAM;YAAM;YAAM;YAAM;SAAK;QAC3E,MAAM,eAAe,gBAAgB,OAAO,CAAC;QAE7C,6BAA6B;QAC7B,IAAI,MAAM,CAAC,WAAW,KAAK,WAAW;YACpC,OAAO,MAAM,CAAC,WAAW;QAC3B;QAEA,+BAA+B;QAC/B,IAAK,IAAI,IAAI,eAAe,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC9D,MAAM,KAAK,eAAe,CAAC,EAAE;YAC7B,IAAI,MAAM,CAAC,GAAG,KAAK,WAAW;gBAC5B,OAAO,MAAM,CAAC,GAAG;YACnB;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAY;KAAO;AACzB;AAGO,SAAS,kBAAkB,YAA0C;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,MAAM,iBAAiB,IAAI,eAAe,CAAC;YACzC,KAAK,MAAM,SAAS,QAAS;gBAC3B,kBAAkB,MAAM,WAAW,CAAC,KAAK;YAC3C;QACF;QAEA,eAAe,OAAO,CAAC,aAAa,OAAO;QAE3C,OAAO;YACL,eAAe,UAAU;QAC3B;IACF,GAAG;QAAC;KAAa;IAEjB,OAAO;QACL;QACA,kBAAkB,iBAAiB;QACnC,mBAAmB,kBAAkB,OAAO,iBAAiB;QAC7D,kBAAkB,kBAAkB;IACtC;AACF;AAGO,SAAS,kBAAkB,OAIjC;IACC,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,EAAE,aAAa,QAAQ,EAAE,GAAG;IAE1D,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,MAAM,iBAAiB,QAAQ;QAC/B,MAAM,cAAc,KAAK,KAAK,CAAC,iBAAiB,CAAC,eAAe,GAAG;QACnE,MAAM,UAAU,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,aAAa,IAAI;QACnD,MAAM,YAAY,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI;QAE3D,OAAO;YACL;YACA;YACA,qBAAqB,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC;YAC9C,KAAK,GAAG,IAAI,EAAE,CAAC;QACjB;IACF,GAAG;QAAC;QAAO;QAAc;QAAK;KAAW;AAC3C;AAGO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,KAAK;QACL,OAAO;QACP,QAAQ;QACR,MAAM;IACR;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;;QAEnC,MAAM;IAmBR,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG;IAE9B,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,MAAM,WAAW,GAAG,uBAAuB;;QAE3C,IAAI,eAAe,UAAU;YAC3B,6CAA6C;YAC7C,OAAO,KAAK,GAAG,CAAC,UAAU;QAC5B;QAEA,IAAI,eAAe,UAAU;YAC3B,OAAO,WAAW;QACpB;QAEA,iCAAiC;QACjC,IAAI,QAAQ,MAAM;YAChB,OAAO,WAAW;QACpB;QAEA,OAAO;IACT,GAAG;QAAC;QAAY;KAAM;AACxB;AAGO,SAAS,eACd,SAAmE;IAEnE,OAAO,SAAS,oBAAoB,KAAQ;QAC1C,MAAM,aAAa;QACnB,MAAM,gBAAgB;YAAE,GAAG,KAAK;YAAE;QAAW;QAE7C,qBAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,WAAW;IACxC;AACF", "debugId": null}}, {"offset": {"line": 4800, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, Suspense, useEffect, memo } from \"react\";\nimport { ProjectSidebar } from \"@/components/project-sidebar\";\nimport { AuthGuard } from \"@/components/auth-guard\";\nimport { ErrorBoundary } from \"@/components/error-boundary\";\nimport { useResponsive } from \"@/hooks/use-responsive\";\nimport { Button } from \"@/components/ui/button\";\nimport { <PERSON>u, Settings } from \"lucide-react\";\nimport { useAppStore } from \"@/store/app-store\";\n\n// Optimized lazy loading with caching\nimport {\n  LazyPromptWorkspace,\n  LazyContextSidebar,\n  preloadPageImports\n} from \"@/lib/dynamic-imports\";\n\nconst Dashboard = memo(function Dashboard() {\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\n  const [isMobileContextOpen, setIsMobileContextOpen] = useState(false);\n  const [isContextGalleryOpen, setIsContextGalleryOpen] = useState(false);\n\n  // Responsive state\n  const { isMobile, orientation } = useResponsive();\n\n  // Zustand store for sidebar collapse states\n  const {\n    isProjectSidebarCollapsed,\n    setIsProjectSidebarCollapsed,\n    isContextSidebarCollapsed,\n    setIsContextSidebarCollapsed\n  } = useAppStore();\n\n  // Preload critical imports on component mount\n  useEffect(() => {\n    try {\n      preloadPageImports('dashboard')\n    } catch (error) {\n      console.warn('Failed to preload dashboard imports:', error)\n    }\n  }, [])\n\n  // Auto-close mobile sidebars on orientation change\n  useEffect(() => {\n    if (isMobile) {\n      setIsMobileSidebarOpen(false);\n      setIsMobileContextOpen(false);\n    }\n  }, [orientation, isMobile]);\n\n  return (\n    <AuthGuard>\n      <ErrorBoundary level=\"page\" showDetails={process.env.NODE_ENV === 'development'}>\n      <div className={`\n        flex full-height-mobile bg-gray-50 relative\n        ${isMobile ? 'flex-col' : 'flex-row'}\n      `}>\n        {/* Mobile Overlay */}\n        {(isMobileSidebarOpen || isMobileContextOpen) && (\n          <div\n            className=\"fixed inset-0 bg-black/50 z-40 lg:hidden mobile-transition\"\n            onClick={() => {\n              setIsMobileSidebarOpen(false);\n              setIsMobileContextOpen(false);\n            }}\n          />\n        )}\n\n        {/* Sol Sütun - Proje Listesi */}\n        <div className={`\n          fixed lg:relative inset-y-0 left-0 z-50\n          w-[85vw] sm:w-80\n          ${isProjectSidebarCollapsed ? 'lg:w-16' : 'lg:w-80'}\n          transform transition-all duration-300 ease-in-out\n          lg:transform-none lg:translate-x-0\n          border-r border-gray-200 bg-white\n          ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\n          ${isMobileSidebarOpen ? 'block' : 'hidden lg:block'}\n        `}>\n          <ProjectSidebar\n            onClose={() => setIsMobileSidebarOpen(false)}\n            isCollapsed={isProjectSidebarCollapsed}\n            onToggleCollapse={() => setIsProjectSidebarCollapsed(!isProjectSidebarCollapsed)}\n          />\n        </div>\n\n        {/* Orta Sütun - Prompt Workspace */}\n        <div className=\"flex-1 flex flex-col min-w-0\">\n          {/* Mobile Header */}\n          <div className=\"lg:hidden flex items-center justify-between p-4 bg-white border-b border-gray-200 safe-area-top\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setIsMobileSidebarOpen(true)}\n              className=\"flex items-center gap-2 touch-target focus-visible-enhanced\"\n            >\n              <Menu className=\"h-5 w-5\" />\n              <span className=\"font-medium mobile-text-base\">Projeler</span>\n            </Button>\n            \n            <h1 className=\"text-lg font-semibold text-gray-900 mobile-text-base\">Promptbir</h1>\n            \n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setIsMobileContextOpen(true)}\n              className=\"flex items-center gap-2 touch-target focus-visible-enhanced\"\n            >\n              <Settings className=\"h-5 w-5\" />\n              <span className=\"font-medium mobile-text-base\">Ayarlar</span>\n            </Button>\n          </div>\n\n          <Suspense fallback={\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            </div>\n          }>\n            <LazyPromptWorkspace\n              {...{\n                isContextGalleryOpen,\n                onToggleContextGallery: () => setIsContextGalleryOpen(!isContextGalleryOpen)\n              } as any}\n            />\n          </Suspense>\n        </div>\n\n        {/* Sağ Sütun - Context Sidebar */}\n        <div className={`\n          fixed xl:relative inset-y-0 right-0 z-50\n          w-[90vw] sm:w-96\n          ${isContextSidebarCollapsed ? 'xl:w-16' : 'xl:w-96'}\n          transform transition-all duration-300 ease-in-out\n          xl:transform-none xl:translate-x-0\n          border-l border-gray-200 bg-white\n          ${isMobileContextOpen ? 'translate-x-0' : 'translate-x-full xl:translate-x-0'}\n          ${isMobileContextOpen ? 'block' : 'hidden xl:block'}\n        `}>\n          <Suspense fallback={\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            </div>\n          }>\n            <LazyContextSidebar\n              {...{\n                onClose: () => setIsMobileContextOpen(false),\n                isCollapsed: isContextSidebarCollapsed,\n                onToggleCollapse: () => setIsContextSidebarCollapsed(!isContextSidebarCollapsed),\n                isContextGalleryOpen,\n                onToggleContextGallery: () => setIsContextGalleryOpen(!isContextGalleryOpen)\n              } as any}\n            />\n          </Suspense>\n        </div>\n      </div>\n      </ErrorBoundary>\n    </AuthGuard>\n  );\n})\n\nexport default Dashboard\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA,sCAAsC;AACtC;AAZA;;;;;;;;;;;AAkBA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,SAAS;IAC9B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,mBAAmB;IACnB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD;IAE9C,4CAA4C;IAC5C,MAAM,EACJ,yBAAyB,EACzB,4BAA4B,EAC5B,yBAAyB,EACzB,4BAA4B,EAC7B,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEd,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD,EAAE;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,wCAAwC;QACvD;IACF,GAAG,EAAE;IAEL,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,uBAAuB;YACvB,uBAAuB;QACzB;IACF,GAAG;QAAC;QAAa;KAAS;IAE1B,qBACE,8OAAC,mIAAA,CAAA,YAAS;kBACR,cAAA,8OAAC,uIAAA,CAAA,gBAAa;YAAC,OAAM;YAAO,aAAa,oDAAyB;sBAClE,cAAA,8OAAC;gBAAI,WAAW,CAAC;;QAEf,EAAE,WAAW,aAAa,WAAW;MACvC,CAAC;;oBAEE,CAAC,uBAAuB,mBAAmB,mBAC1C,8OAAC;wBACC,WAAU;wBACV,SAAS;4BACP,uBAAuB;4BACvB,uBAAuB;wBACzB;;;;;;kCAKJ,8OAAC;wBAAI,WAAW,CAAC;;;UAGf,EAAE,4BAA4B,YAAY,UAAU;;;;UAIpD,EAAE,sBAAsB,kBAAkB,qCAAqC;UAC/E,EAAE,sBAAsB,UAAU,kBAAkB;QACtD,CAAC;kCACC,cAAA,8OAAC,wIAAA,CAAA,iBAAc;4BACb,SAAS,IAAM,uBAAuB;4BACtC,aAAa;4BACb,kBAAkB,IAAM,6BAA6B,CAAC;;;;;;;;;;;kCAK1D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,uBAAuB;wCACtC,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;kDAGjD,8OAAC;wCAAG,WAAU;kDAAuD;;;;;;kDAErE,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,uBAAuB;wCACtC,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;0CAInD,8OAAC,qMAAA,CAAA,WAAQ;gCAAC,wBACR,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;0CAGjB,cAAA,8OAAC,gIAAA,CAAA,sBAAmB;oCAEhB;oCACA,wBAAwB,IAAM,wBAAwB,CAAC;;;;;;;;;;;;;;;;;kCAO/D,8OAAC;wBAAI,WAAW,CAAC;;;UAGf,EAAE,4BAA4B,YAAY,UAAU;;;;UAIpD,EAAE,sBAAsB,kBAAkB,oCAAoC;UAC9E,EAAE,sBAAsB,UAAU,kBAAkB;QACtD,CAAC;kCACC,cAAA,8OAAC,qMAAA,CAAA,WAAQ;4BAAC,wBACR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;sCAGjB,cAAA,8OAAC,gIAAA,CAAA,qBAAkB;gCAEf,SAAS,IAAM,uBAAuB;gCACtC,aAAa;gCACb,kBAAkB,IAAM,6BAA6B,CAAC;gCACtD;gCACA,wBAAwB,IAAM,wBAAwB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvE;uCAEe", "debugId": null}}]}