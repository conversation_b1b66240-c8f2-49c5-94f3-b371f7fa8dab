'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Copy, 
  Eye, 
  Calendar, 
  User, 
  Tag, 
  Lock, 
  AlertTriangle,
  CheckCircle,
  Share2,
  ExternalLink
} from 'lucide-react'
import { useSharedPrompt } from '@/hooks/use-shared-prompts'
import { toast } from 'sonner'

export default function SharedPromptPage() {
  const params = useParams()
  const token = params.token as string
  const [password, setPassword] = useState('')
  const [showPasswordInput, setShowPasswordInput] = useState(false)
  const [copied, setCopied] = useState(false)

  const { 
    data: sharedPrompt, 
    error, 
    isLoading, 
    refetch 
  } = useSharedPrompt(token, password)

  useEffect(() => {
    if (error?.message === '<PERSON><PERSON><PERSON> gerek<PERSON>') {
      setShowPasswordInput(true)
    }
  }, [error])

  const handlePasswordSubmit = () => {
    if (password.trim()) {
      refetch()
    }
  }

  const handleCopyPrompt = async () => {
    if (!sharedPrompt) return

    try {
      const taskCode = sharedPrompt.prompt.task_code || `task-${sharedPrompt.prompt.id.slice(-4)}`
      const fullText = `${taskCode}\n${sharedPrompt.prompt.prompt_text}`
      
      await navigator.clipboard.writeText(fullText)
      setCopied(true)
      toast.success('Prompt panoya kopyalandı!')
      
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast.error('Kopyalama başarısız')
    }
  }

  const handleShareAgain = async () => {
    const currentUrl = window.location.href
    try {
      await navigator.clipboard.writeText(currentUrl)
      toast.success('Link panoya kopyalandı!')
    } catch (error) {
      toast.error('Link kopyalanamadı')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardHeader>
            <div className="h-6 bg-gray-200 rounded animate-pulse mb-2" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
              <div className="h-20 bg-gray-200 rounded animate-pulse" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error && !showPasswordInput) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Hata
            </CardTitle>
            <CardDescription>
              {error.message}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => window.location.href = '/'}
              className="w-full"
            >
              Ana Sayfaya Dön
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (showPasswordInput && !sharedPrompt) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-yellow-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Şifre Gerekli
            </CardTitle>
            <CardDescription>
              Bu paylaşım şifre ile korunmaktadır
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Input
                type="password"
                placeholder="Şifre girin"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handlePasswordSubmit()
                  }
                }}
              />
            </div>
            <div className="flex gap-2">
              <Button 
                onClick={handlePasswordSubmit}
                disabled={!password.trim()}
                className="flex-1"
              >
                Görüntüle
              </Button>
              <Button 
                variant="outline"
                onClick={() => window.location.href = '/'}
              >
                İptal
              </Button>
            </div>
            {error?.message === 'Şifre yanlış' && (
              <p className="text-sm text-red-600 text-center">
                Şifre yanlış, lütfen tekrar deneyin
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!sharedPrompt) return null

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Share2 className="h-4 w-4 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  PromptBir
                </h1>
                <p className="text-sm text-gray-500">
                  Paylaşılan Prompt
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.href = '/'}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              PromptBir'e Git
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto p-4 py-8">
        <Card className="shadow-lg">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <CardTitle className="text-xl mb-2">
                  {sharedPrompt.title || sharedPrompt.prompt.task_code || 'Paylaşılan Prompt'}
                </CardTitle>
                {sharedPrompt.description && (
                  <CardDescription className="text-base">
                    {sharedPrompt.description}
                  </CardDescription>
                )}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleShareAgain}
                >
                  <Share2 className="h-4 w-4" />
                </Button>
                <Button
                  onClick={handleCopyPrompt}
                  disabled={copied}
                  className={copied ? 'bg-green-600 hover:bg-green-700' : ''}
                >
                  {copied ? (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Kopyalandı
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4 mr-2" />
                      Kopyala
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 mt-4">
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                {sharedPrompt.author_email}
              </div>
              <div className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                {sharedPrompt.view_count} görüntülenme
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {new Date(sharedPrompt.created_at).toLocaleDateString('tr-TR')}
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            <Separator />

            {/* Prompt Content */}
            <div>
              <h3 className="text-lg font-medium mb-3">Prompt İçeriği</h3>
              <div className="bg-gray-50 rounded-lg p-4 border">
                <div className="font-mono text-sm whitespace-pre-wrap">
                  {sharedPrompt.prompt.task_code && (
                    <div className="text-blue-600 font-semibold mb-2">
                      {sharedPrompt.prompt.task_code}
                    </div>
                  )}
                  <div className="text-gray-800">
                    {sharedPrompt.prompt.prompt_text}
                  </div>
                </div>
              </div>
            </div>

            {/* Tags */}
            {sharedPrompt.prompt.tags && sharedPrompt.prompt.tags.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  Etiketler
                </h4>
                <div className="flex flex-wrap gap-2">
                  {sharedPrompt.prompt.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Project Info */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-1">
                Proje: {sharedPrompt.project_name}
              </h4>
              <p className="text-xs text-blue-700">
                Bu prompt "{sharedPrompt.project_name}" projesinden paylaşılmıştır
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>
            Bu prompt{' '}
            <a 
              href="/" 
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              PromptBir
            </a>
            {' '}ile paylaşılmıştır
          </p>
        </div>
      </div>
    </div>
  )
}
