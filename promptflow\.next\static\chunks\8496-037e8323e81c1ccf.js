"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8496],{9454:(e,t,r)=>{r.d(t,{q:()=>s});var a=r(95155),n=r(12115),i=r(35695),o=r(22100);function s(e){let{children:t}=e,{data:r,isLoading:s,error:l}=(0,o.Jd)(),u=(0,i.useRouter)(),c=(0,i.usePathname)();if(console.log("\uD83D\uDEE1️ [AUTH_GUARD] Status: loading=".concat(s,", user=").concat((null==r?void 0:r.email)||"null",", error=").concat(l?"YES":"NO",", pathname=").concat(c)),(0,o.rU)(),(0,n.useEffect)(()=>{!s&&r&&"/auth"===c&&(console.log("\uD83D\uDE80 [AUTH_GUARD] Authenticated user on /auth, redirecting to dashboard"),u.replace("/dashboard"))},[r,s,c,u]),s)return console.log("⏳ [AUTH_GUARD] Showing loading state"),(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"PromptFlow y\xfckleniyor..."})]})});if(l){let e=l instanceof Error?l.message:String(l);if(console.error("❌ [AUTH_GUARD] Auth error:",e),e.includes("Invalid Refresh Token")||e.includes("Refresh Token Not Found"))return console.log("\uD83D\uDD04 [AUTH_GUARD] Refresh token error - showing loading"),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Oturum yenileniyor..."})]})})}return r?(console.log("✅ [AUTH_GUARD] User authenticated - showing app"),(0,a.jsx)(a.Fragment,{children:t})):(console.log("\uD83D\uDEAB [AUTH_GUARD] No user - showing loading (middleware will handle redirect)"),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Kimlik doğrulanıyor..."})]})}))}},22346:(e,t,r)=>{r.d(t,{w:()=>o});var a=r(95155);r(12115);var n=r(87489),i=r(59434);function o(e){let{className:t,orientation:r="horizontal",decorative:o=!0,...s}=e;return(0,a.jsx)(n.b,{"data-slot":"separator",decorative:o,orientation:r,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...s})}},26126:(e,t,r)=>{r.d(t,{E:()=>l});var a=r(95155);r(12115);var n=r(99708),i=r(74466),o=r(59434);let s=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,asChild:i=!1,...l}=e,u=i?n.DX:"span";return(0,a.jsx)(u,{"data-slot":"badge",className:(0,o.cn)(s({variant:r}),t),...l})}},54165:(e,t,r)=>{r.d(t,{Cf:()=>m,Es:()=>p,L3:()=>f,c7:()=>g,lG:()=>l,rr:()=>w,zM:()=>u});var a=r(95155),n=r(12115),i=r(15452),o=r(54416),s=r(59434);let l=i.bL,u=i.l9,c=i.ZL;i.bm;let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(i.hJ,{ref:t,className:(0,s.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...n})});d.displayName=i.hJ.displayName;let m=n.forwardRef((e,t)=>{let{className:r,children:n,...l}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(d,{}),(0,a.jsxs)(i.UC,{ref:t,className:(0,s.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...l,children:[n,(0,a.jsxs)(i.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Kapat"})]})]})]})});m.displayName=i.UC.displayName;let g=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...r})};g.displayName="DialogHeader";let p=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...r})};p.displayName="DialogFooter";let f=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(i.hE,{ref:t,className:(0,s.cn)("text-lg font-semibold leading-none tracking-tight",r),...n})});f.displayName=i.hE.displayName;let w=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(i.VY,{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...n})});w.displayName=i.VY.displayName},67238:(e,t,r)=>{r.d(t,{bL:()=>y,eW:()=>E,By:()=>h,YK:()=>w,sS:()=>_,eo:()=>v});var a=r(32960),n=r(26715),i=r(5041),o=r(70478),s=r(86489),l=r(86730);let u={login:{maxRequests:5,windowMinutes:15},signup:{maxRequests:3,windowMinutes:60},password_reset:{maxRequests:3,windowMinutes:60},project_create:{maxRequests:10,windowMinutes:60},project_update:{maxRequests:20,windowMinutes:10},project_delete:{maxRequests:5,windowMinutes:60},prompt_create:{maxRequests:50,windowMinutes:10},prompt_update:{maxRequests:100,windowMinutes:10},prompt_delete:{maxRequests:20,windowMinutes:10},context_create:{maxRequests:20,windowMinutes:10},context_batch_add:{maxRequests:5,windowMinutes:10},plan_change:{maxRequests:3,windowMinutes:60},api_general:{maxRequests:200,windowMinutes:10}};class c{getStorageKey(e,t){return"rate_limit_".concat(e).concat(t?"_".concat(t):"")}async checkLimit(e,t){try{let r,a=u[e],n=this.getStorageKey(e,t),i=Date.now(),o=60*a.windowMinutes*1e3,s=localStorage.getItem(n);if(!s||(r=JSON.parse(s),i-r.windowStart>o))return r={count:1,windowStart:i,lastRequest:i},localStorage.setItem(n,JSON.stringify(r)),{allowed:!0,remainingRequests:a.maxRequests-1,resetTime:i+o};if(r.count>=a.maxRequests){let e=r.windowStart+o,t=Math.ceil((e-i)/1e3);return{allowed:!1,remainingRequests:0,resetTime:e,retryAfter:t}}return r.count++,r.lastRequest=i,localStorage.setItem(n,JSON.stringify(r)),{allowed:!0,remainingRequests:a.maxRequests-r.count,resetTime:r.windowStart+o}}catch(t){return console.warn("Rate limit check failed for ".concat(e,":"),t),{allowed:!0,remainingRequests:0,resetTime:Date.now()+6e4}}}resetLimit(e,t){try{let r=this.getStorageKey(e,t);localStorage.removeItem(r)}catch(t){console.warn("Rate limit reset failed for ".concat(e,":"),t)}}getStatus(e,t){try{let r=u[e],a=this.getStorageKey(e,t),n=localStorage.getItem(a);if(!n)return{count:0,remaining:r.maxRequests,resetTime:Date.now()+60*r.windowMinutes*1e3};let i=JSON.parse(n),o=60*r.windowMinutes*1e3,s=Date.now();if(s-i.windowStart>o)return{count:0,remaining:r.maxRequests,resetTime:s+o};return{count:i.count,remaining:Math.max(0,r.maxRequests-i.count),resetTime:i.windowStart+o}}catch(t){return console.warn("Rate limit status check failed for ".concat(e,":"),t),null}}}class d{async checkLimit(e,t){try{let r=u[e],a=t;if(!a){let{data:{user:e}}=await o.L.auth.getUser();if(!e)throw Error("User not authenticated");a=e.id}let{data:n,error:i}=await o.L.rpc("check_rate_limit",{p_user_id:a,p_action_type:e,p_max_requests:r.maxRequests,p_window_minutes:r.windowMinutes});if(i)return console.error("Server rate limit check failed for ".concat(e,":"),i),{allowed:!0,remainingRequests:0,resetTime:Date.now()+60*r.windowMinutes*1e3};let s=60*r.windowMinutes*1e3,l=Date.now()+s;if(!0!==n)return{allowed:!1,remainingRequests:0,resetTime:l,retryAfter:Math.ceil(s/1e3)};return{allowed:!0,remainingRequests:r.maxRequests-1,resetTime:l}}catch(t){return console.error("Server rate limit error for ".concat(e,":"),t),{allowed:!0,remainingRequests:0,resetTime:Date.now()+6e4}}}}let m=new c,g=new d;async function p(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{clientOnly:r=!1,serverOnly:a=!1,userId:n}=t;try{if(!a){let t=await m.checkLimit(e,n);if(!t.allowed||r)return{...t,source:"client"}}if(!r)return{...await g.checkLimit(e,n),source:a?"server":"both"};return{allowed:!0,remainingRequests:0,resetTime:Date.now()+6e4,source:"client"}}catch(t){return console.error("Rate limit check failed for ".concat(e,":"),t),{allowed:!0,remainingRequests:0,resetTime:Date.now()+6e4,source:"client"}}}class f extends Error{constructor(e,t,r){super("Rate limit exceeded for ".concat(e,". Try again in ").concat(t," seconds.")),this.action=e,this.retryAfter=t,this.resetTime=r,this.name="RateLimitError"}}function w(){return(0,a.I)({queryKey:["projects"],queryFn:async()=>{console.log("\uD83D\uDCC1 [USE_PROJECTS] Fetching projects...");try{var e;let{data:{session:t},error:r}=await o.L.auth.getSession();console.log("\uD83D\uDCC1 [USE_PROJECTS] Session check:",{hasSession:!!t,sessionError:null==r?void 0:r.message,userId:null==t||null==(e=t.user)?void 0:e.id});let{data:a,error:n}=await o.L.from("projects").select("*").order("created_at",{ascending:!1});if(n)throw console.error("❌ [USE_PROJECTS] Error fetching projects:",n),Error(n.message);return console.log("✅ [USE_PROJECTS] Projects fetched:",(null==a?void 0:a.length)||0,"projects"),a||[]}catch(e){throw console.error("\uD83D\uDCA5 [USE_PROJECTS] Exception:",e),e}}})}function h(e){return(0,a.I)({queryKey:["project",e],queryFn:async()=>{if(!e)return null;let{data:t,error:r}=await o.L.from("projects").select("*").eq("id",e).single();if(r)throw Error(r.message);return t},enabled:!!e})}function y(){let e=(0,n.jE)();return(0,i.n)({mutationFn:async e=>{let t=await (0,s.UE)();if(!t.allowed)throw Error(t.reason||s.x5.PROJECT_LIMIT_REACHED);let{data:{user:r},error:a}=await o.L.auth.getUser();if(a||!r)throw Error("Kullanıcı oturumu bulunamadı");let{data:n,error:i}=await o.L.from("projects").insert({...e,user_id:r.id}).select().single();if(i)throw Error(i.message);return n},onSuccess:async()=>{e.invalidateQueries({queryKey:["projects"]});try{await (0,s.O3)(),e.invalidateQueries({queryKey:["user-limits"]}),e.invalidateQueries({queryKey:["usage-stats"]})}catch(e){console.warn("Kullanım istatistikleri g\xfcncellenemedi:",e)}}})}function _(){let e=(0,n.jE)();return(0,i.n)({mutationFn:async e=>{let{id:t,...r}=e;console.log("\uD83D\uDD04 [UPDATE_PROJECT] Starting update:",{id:t,updates:r});let{data:a}=await o.L.auth.getUser();if(!a.user)throw Error("Kullanıcı girişi gerekli");let{data:n,error:i}=await o.L.from("projects").update({...r,updated_at:new Date().toISOString()}).eq("id",t).eq("user_id",a.user.id).select().single();if(i)throw console.error("❌ [UPDATE_PROJECT] Database error:",i),Error("Proje g\xfcncellenirken hata oluştu: ".concat(i.message));if(!n)throw Error("Proje bulunamadı veya g\xfcncelleme yetkisi yok");return console.log("✅ [UPDATE_PROJECT] Success:",n),n},onSuccess:t=>{e.invalidateQueries({queryKey:["projects"]}),e.invalidateQueries({queryKey:["project",t.id]})},onError:e=>{console.error("❌ [UPDATE_PROJECT] Mutation error:",e)}})}function v(){let e=(0,n.jE)();return(0,i.n)({mutationFn:async e=>{let{projectId:t,newName:r}=e;console.log("\uD83D\uDD12 [UPDATE_PROJECT_NAME] Starting secure update:",{projectId:t,newName:r});let a=await p("project_update");if(!a.allowed)throw new f("project_update",a.retryAfter||60,a.resetTime);let n=(0,l.$A)(r);if(!n.isValid)throw Error(n.error||"Ge\xe7ersiz proje adı");let i=n.sanitizedValue;try{let{data:e,error:r}=await o.L.rpc("update_project_name_secure",{p_project_id:t,p_new_name:i});if(r)throw console.error("❌ [UPDATE_PROJECT_NAME] Database error:",r),Error((0,l.$3)(r.message));if(!(null==e?void 0:e.success))throw console.error("❌ [UPDATE_PROJECT_NAME] Function error:",e),Error((null==e?void 0:e.message)||"Proje adı g\xfcncellenemedi");return console.log("✅ [UPDATE_PROJECT_NAME] Success:",e.data),e.data}catch(t){console.error("\uD83D\uDCA5 [UPDATE_PROJECT_NAME] Exception:",t);let e=t instanceof Error?t.message:"Bilinmeyen hata";throw e.includes("unique_violation")||e.includes("DuplicateName")?e="Bu isimde bir proje zaten mevcut":e.includes("check_violation")||e.includes("InvalidInput")?e="Proje adı ge\xe7ersiz karakterler i\xe7eriyor":e.includes("RateLimitExceeded")&&(e="\xc7ok fazla g\xfcncelleme isteği. L\xfctfen bekleyin."),Error(e)}},onMutate:async t=>{let{projectId:r,newName:a}=t;console.log("\uD83D\uDE80 [UPDATE_PROJECT_NAME] Starting optimistic update:",{projectId:r,newName:a}),await e.cancelQueries({queryKey:["projects"]}),await e.cancelQueries({queryKey:["project",r]});let n=e.getQueryData(["projects"]),i=e.getQueryData(["project",r]);return n&&e.setQueryData(["projects"],e=>e?e.map(e=>e.id===r?{...e,name:a.trim(),updated_at:new Date().toISOString()}:e):e),i&&e.setQueryData(["project",r],{...i,name:a.trim(),updated_at:new Date().toISOString()}),{previousProjects:n,previousProject:i}},onSuccess:t=>{console.log("✅ [UPDATE_PROJECT_NAME] Success, updating cache with server data:",t.id),e.setQueryData(["projects"],e=>e?e.map(e=>e.id===t.id?{...e,...t}:e):e),e.setQueryData(["project",t.id],t)},onError:(t,r,a)=>{console.error("\uD83D\uDCA5 [UPDATE_PROJECT_NAME] Error, rolling back:",t),(null==a?void 0:a.previousProjects)&&e.setQueryData(["projects"],a.previousProjects),(null==a?void 0:a.previousProject)&&e.setQueryData(["project",r.projectId],a.previousProject)},onSettled:()=>{e.invalidateQueries({queryKey:["projects"]})}})}function E(){let e=(0,n.jE)();return(0,i.n)({mutationFn:async e=>{let{error:t}=await o.L.from("projects").delete().eq("id",e);if(t)throw Error(t.message)},onSuccess:async()=>{e.invalidateQueries({queryKey:["projects"]});try{await (0,s.O3)(),e.invalidateQueries({queryKey:["user-limits"]}),e.invalidateQueries({queryKey:["usage-stats"]})}catch(e){console.warn("Kullanım istatistikleri g\xfcncellenemedi:",e)}}})}},74178:(e,t,r)=>{r.d(t,{C:()=>s,EU:()=>l,Jd:()=>c,P5:()=>d,ov:()=>m,p$:()=>u,pS:()=>g});var a=r(32960),n=r(26715),i=r(5041),o=r(70478);function s(){return(0,a.I)({queryKey:["plan-types"],queryFn:async()=>{console.log("\uD83D\uDCCB [USE_PLANS] Fetching plan types...");let{data:e,error:t}=await o.L.from("plan_types").select("*").eq("is_active",!0).order("sort_order");if(t)throw console.error("❌ [USE_PLANS] Plan types fetch failed:",t),Error(t.message);return console.log("✅ [USE_PLANS] Plan types fetched:",null==e?void 0:e.length),e||[]},staleTime:3e5})}function l(){return(0,a.I)({queryKey:["user-active-plan"],queryFn:async()=>{var e;console.log("\uD83D\uDC64 [USE_PLANS] Fetching user active plan...");let{data:t,error:r}=await o.L.rpc("get_user_active_plan",{user_uuid:null==(e=(await o.L.auth.getUser()).data.user)?void 0:e.id});if(r)throw console.error("❌ [USE_PLANS] User active plan fetch failed:",r),Error(r.message);return console.log("✅ [USE_PLANS] User active plan fetched:",null==t?void 0:t[0]),(null==t?void 0:t[0])||null},staleTime:12e4})}function u(){return(0,a.I)({queryKey:["user-limits"],queryFn:async()=>{var e;console.log("\uD83D\uDD0D [USE_PLANS] Checking user limits...");let{data:t,error:r}=await o.L.rpc("check_user_limits",{user_uuid:null==(e=(await o.L.auth.getUser()).data.user)?void 0:e.id});if(r)throw console.error("❌ [USE_PLANS] User limits check failed:",r),Error(r.message);return console.log("✅ [USE_PLANS] User limits checked:",null==t?void 0:t[0]),(null==t?void 0:t[0])||null},staleTime:3e4})}function c(){let e=(0,n.jE)();return(0,i.n)({mutationFn:async e=>{var t;let{planName:r,billingCycle:a="monthly",paymentReference:n}=e;console.log("\uD83D\uDD04 [USE_PLANS] Changing plan to: ".concat(r));let{data:i,error:s}=await o.L.rpc("change_user_plan",{user_uuid:null==(t=(await o.L.auth.getUser()).data.user)?void 0:t.id,new_plan_name:r,billing_cycle_param:a,payment_reference_param:n});if(s)throw console.error("❌ [USE_PLANS] Plan change failed:",s),Error(s.message);return console.log("✅ [USE_PLANS] Plan changed successfully:",i),i},onSuccess:()=>{console.log("\uD83C\uDF89 [USE_PLANS] Plan change successful, invalidating queries"),e.invalidateQueries({queryKey:["user-active-plan"]}),e.invalidateQueries({queryKey:["user-limits"]}),e.invalidateQueries({queryKey:["user-plan-history"]}),e.invalidateQueries({queryKey:["usage-stats"]})},onError:e=>{console.error("\uD83D\uDCA5 [USE_PLANS] Plan change error:",e)}})}function d(){return(0,a.I)({queryKey:["user-trial-info"],queryFn:async()=>{var e;console.log("\uD83D\uDD0D [USE_PLANS] Fetching user trial info...");let{data:t,error:r}=await o.L.rpc("get_user_trial_info",{user_uuid:null==(e=(await o.L.auth.getUser()).data.user)?void 0:e.id});if(r)throw console.error("❌ [USE_PLANS] User trial info fetch failed:",r),Error(r.message);return console.log("✅ [USE_PLANS] User trial info fetched:",null==t?void 0:t[0]),(null==t?void 0:t[0])||null},staleTime:3e4})}function m(){return(0,a.I)({queryKey:["cancellation-eligibility"],queryFn:async()=>{var e;console.log("\uD83D\uDD0D [USE_PLANS] Checking cancellation eligibility...");let{data:t,error:r}=await o.L.rpc("get_cancellation_eligibility",{user_uuid:null==(e=(await o.L.auth.getUser()).data.user)?void 0:e.id});if(r)throw console.error("❌ [USE_PLANS] Cancellation eligibility check failed:",r),Error(r.message);return console.log("✅ [USE_PLANS] Cancellation eligibility checked:",null==t?void 0:t[0]),(null==t?void 0:t[0])||null},staleTime:3e4})}function g(){let e=(0,n.jE)();return(0,i.n)({mutationFn:async e=>{var t;let{cancellationReason:r,requestRefund:a=!1}=e;console.log("\uD83D\uDEAB [USE_PLANS] Cancelling plan with reason: ".concat(r));let{data:n,error:i}=await o.L.rpc("cancel_user_plan",{user_uuid:null==(t=(await o.L.auth.getUser()).data.user)?void 0:t.id,cancellation_reason_param:r,request_refund:a});if(i)throw console.error("❌ [USE_PLANS] Plan cancellation failed:",i),Error(i.message);return console.log("✅ [USE_PLANS] Plan cancelled successfully:",null==n?void 0:n[0]),null==n?void 0:n[0]},onSuccess:t=>{console.log("\uD83C\uDF89 [USE_PLANS] Plan cancellation successful, invalidating queries"),e.invalidateQueries({queryKey:["user-active-plan"]}),e.invalidateQueries({queryKey:["user-limits"]}),e.invalidateQueries({queryKey:["user-plan-history"]}),e.invalidateQueries({queryKey:["user-trial-info"]}),e.invalidateQueries({queryKey:["cancellation-eligibility"]}),e.invalidateQueries({queryKey:["usage-stats"]})},onError:e=>{console.error("\uD83D\uDCA5 [USE_PLANS] Plan cancellation error:",e)}})}},86489:(e,t,r)=>{r.d(t,{O3:()=>s,UE:()=>i,vw:()=>o,x5:()=>l});var a=r(70478);async function n(){try{let{data:e}=await a.L.auth.getUser();if(!e.user)throw Error("Kullanıcı oturumu bulunamadı");let{data:t,error:r}=await a.L.rpc("check_user_limits",{user_uuid:e.user.id});if(r)throw console.error("Plan limitleri kontrol edilemedi:",r),Error(r.message);return(null==t?void 0:t[0])||null}catch(e){return console.error("Plan limitleri kontrol hatası:",e),null}}async function i(){let e=await n();return e?e.can_create_project?{allowed:!0}:{allowed:!1,reason:"Proje limiti aşıldı (".concat(e.current_projects,"/").concat(e.max_projects,")")}:{allowed:!1,reason:"Plan bilgileri alınamadı"}}async function o(e){let t=await n();if(!t)return{allowed:!1,reason:"Plan bilgileri alınamadı"};if(!t.can_create_prompt)return{allowed:!1,reason:"Prompt limiti aşıldı (".concat(t.current_prompts,"/").concat(t.max_prompts_per_project,")")};if(e)try{let{count:r,error:n}=await a.L.from("prompts").select("*",{count:"exact",head:!0}).eq("project_id",e);if(n)return console.error("Proje prompt sayısı kontrol edilemedi:",n),{allowed:!1,reason:"Proje bilgileri alınamadı"};let i=r||0;if(-1!==t.max_prompts_per_project&&i>=t.max_prompts_per_project)return{allowed:!1,reason:"Bu proje i\xe7in prompt limiti aşıldı (".concat(i,"/").concat(t.max_prompts_per_project,")")}}catch(e){return console.error("Proje prompt sayısı kontrol hatası:",e),{allowed:!1,reason:"Proje bilgileri kontrol edilemedi"}}return{allowed:!0}}async function s(){try{let{data:e}=await a.L.auth.getUser();if(!e.user)throw Error("Kullanıcı oturumu bulunamadı");let{error:t}=await a.L.rpc("update_usage_stats",{user_uuid:e.user.id});if(t)throw console.error("Kullanım istatistikleri g\xfcncellenemedi:",t),Error(t.message)}catch(e){throw console.error("Kullanım istatistikleri g\xfcncelleme hatası:",e),e}}let l={PROJECT_LIMIT_REACHED:"Proje oluşturma limitinize ulaştınız. Daha fazla proje oluşturmak i\xe7in planınızı y\xfckseltin.",PROMPT_LIMIT_REACHED:"Prompt oluşturma limitinize ulaştınız. Daha fazla prompt oluşturmak i\xe7in planınızı y\xfckseltin.",FEATURE_NOT_AVAILABLE:"Bu \xf6zellik mevcut planınızda bulunmamaktadır. Planınızı y\xfckseltin.",PLAN_EXPIRED:"Planınızın s\xfcresi dolmuştur. L\xfctfen planınızı yenileyin.",PLAN_SUSPENDED:"Hesabınız askıya alınmıştır. L\xfctfen destek ekibi ile iletişime ge\xe7in."}},86730:(e,t,r)=>{r.d(t,{$3:()=>l,$A:()=>i,Br:()=>o,qj:()=>u});let a={minLength:3,maxLength:50,allowedCharsRegex:/^[a-zA-Z0-9\s\-_.]+$/,debounceMs:300};function n(e){if(!e)return"";let t=e.trim();return(t=t.replace(/\s+/g," ")).trim()}function i(e){let t=n(e);return t?t.length<a.minLength?{isValid:!1,error:"Proje adı en az ".concat(a.minLength," karakter olmalıdır")}:t.length>a.maxLength?{isValid:!1,error:"Proje adı en fazla ".concat(a.maxLength," karakter olabilir")}:a.allowedCharsRegex.test(t)?0===t.replace(/\s/g,"").length?{isValid:!1,error:"Proje adı sadece boşluk karakterlerinden oluşamaz"}:{isValid:!0,sanitizedValue:t}:{isValid:!1,error:"Proje adı sadece harf, rakam, boşluk ve -_. karakterlerini i\xe7erebilir"}:{isValid:!1,error:"Proje adı boş olamaz"}}function o(e,t){return n(e).toLowerCase()===n(t).toLowerCase()}async function s(e,t,r){let a=n(e),s=i(e);return s.isValid?r.some(e=>e.id!==t&&o(e.name,a))?{isValid:!1,error:"Bu isimde bir proje zaten mevcut"}:{isValid:!0,sanitizedValue:a}:s}function l(e){return({unique_violation:"Bu isimde bir proje zaten mevcut",check_violation:"Proje adı ge\xe7ersiz karakterler i\xe7eriyor",not_null_violation:"Proje adı boş olamaz",foreign_key_violation:"Ge\xe7ersiz proje referansı",RateLimitExceeded:"\xc7ok fazla g\xfcncelleme isteği. L\xfctfen bekleyin.",InvalidInput:"Ge\xe7ersiz proje adı",DuplicateName:"Bu isimde bir proje zaten mevcut",NotFound:"Proje bulunamadı",Unauthorized:"Bu işlem i\xe7in yetkiniz yok"})[e]||e}function u(e,t){let r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a.debounceMs;return(a,i)=>{clearTimeout(r),r=setTimeout(async()=>{i(await s(a,t,e))},n)}}}}]);