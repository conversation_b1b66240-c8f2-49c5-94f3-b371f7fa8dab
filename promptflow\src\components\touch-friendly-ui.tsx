'use client'

import { useState, useRef, useEffect, ReactNode } from 'react'
import { cn } from '@/lib/utils'
import { useResponsive } from '@/hooks/use-responsive'

// Touch-friendly button component
interface TouchButtonProps {
  children: ReactNode
  onClick?: () => void
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  className?: string
  hapticFeedback?: boolean
}

export function TouchButton({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className,
  hapticFeedback = true
}: TouchButtonProps) {
  const { isTouch } = useResponsive()
  const [isPressed, setIsPressed] = useState(false)

  const handleTouchStart = () => {
    setIsPressed(true)
    if (hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(10) // Light haptic feedback
    }
  }

  const handleTouchEnd = () => {
    setIsPressed(false)
  }

  const handleClick = () => {
    if (!disabled && onClick) {
      onClick()
    }
  }

  const baseClasses = cn(
    'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    isTouch && 'touch-target', // Minimum 44px touch target
    isPressed && 'scale-95 transform', // Press animation
    className
  )

  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 active:bg-gray-400',
    ghost: 'bg-transparent text-gray-700 hover:bg-gray-100 active:bg-gray-200',
    danger: 'bg-red-600 text-white hover:bg-red-700 active:bg-red-800'
  }

  const sizeClasses = {
    sm: isTouch ? 'px-3 py-2 text-sm min-h-[44px]' : 'px-3 py-1.5 text-sm',
    md: isTouch ? 'px-4 py-3 text-base min-h-[48px]' : 'px-4 py-2 text-base',
    lg: isTouch ? 'px-6 py-4 text-lg min-h-[52px]' : 'px-6 py-3 text-lg'
  }

  return (
    <button
      className={cn(baseClasses, variantClasses[variant], sizeClasses[size])}
      onClick={handleClick}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
      disabled={disabled}
    >
      {children}
    </button>
  )
}

// Touch-friendly card component
interface TouchCardProps {
  children: ReactNode
  onClick?: () => void
  className?: string
  hoverable?: boolean
  pressable?: boolean
}

export function TouchCard({
  children,
  onClick,
  className,
  hoverable = true,
  pressable = true
}: TouchCardProps) {
  const { isTouch } = useResponsive()
  const [isPressed, setIsPressed] = useState(false)

  const handleTouchStart = () => {
    if (pressable) setIsPressed(true)
  }

  const handleTouchEnd = () => {
    setIsPressed(false)
  }

  return (
    <div
      className={cn(
        'bg-white rounded-lg border border-gray-200 p-4 transition-all duration-200',
        hoverable && 'hover:shadow-md',
        pressable && isPressed && 'scale-98 transform shadow-sm',
        onClick && 'cursor-pointer',
        isTouch && onClick && 'touch-target',
        className
      )}
      onClick={onClick}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onMouseDown={() => pressable && setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
    >
      {children}
    </div>
  )
}

// Swipe gesture component
interface SwipeGestureProps {
  children: ReactNode
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  threshold?: number
  className?: string
}

export function SwipeGesture({
  children,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  threshold = 50,
  className
}: SwipeGestureProps) {
  const startPos = useRef({ x: 0, y: 0 })
  const [isSwiping, setIsSwiping] = useState(false)

  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0]
    startPos.current = { x: touch.clientX, y: touch.clientY }
    setIsSwiping(true)
  }

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!isSwiping) return

    const touch = e.changedTouches[0]
    const deltaX = touch.clientX - startPos.current.x
    const deltaY = touch.clientY - startPos.current.y

    const absDeltaX = Math.abs(deltaX)
    const absDeltaY = Math.abs(deltaY)

    if (Math.max(absDeltaX, absDeltaY) < threshold) {
      setIsSwiping(false)
      return
    }

    if (absDeltaX > absDeltaY) {
      // Horizontal swipe
      if (deltaX > 0 && onSwipeRight) {
        onSwipeRight()
      } else if (deltaX < 0 && onSwipeLeft) {
        onSwipeLeft()
      }
    } else {
      // Vertical swipe
      if (deltaY > 0 && onSwipeDown) {
        onSwipeDown()
      } else if (deltaY < 0 && onSwipeUp) {
        onSwipeUp()
      }
    }

    setIsSwiping(false)
  }

  return (
    <div
      className={cn('touch-none', className)}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
    >
      {children}
    </div>
  )
}

// Pull-to-refresh component
interface PullToRefreshProps {
  children: ReactNode
  onRefresh: () => Promise<void>
  threshold?: number
  className?: string
}

export function PullToRefresh({
  children,
  onRefresh,
  threshold = 80,
  className
}: PullToRefreshProps) {
  const [pullDistance, setPullDistance] = useState(0)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [canRefresh, setCanRefresh] = useState(false)
  const startY = useRef(0)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleTouchStart = (e: React.TouchEvent) => {
    if (containerRef.current?.scrollTop === 0) {
      startY.current = e.touches[0].clientY
    }
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (isRefreshing || containerRef.current?.scrollTop !== 0) return

    const currentY = e.touches[0].clientY
    const distance = Math.max(0, currentY - startY.current)
    
    if (distance > 0) {
      e.preventDefault()
      setPullDistance(Math.min(distance, threshold * 1.5))
      setCanRefresh(distance >= threshold)
    }
  }

  const handleTouchEnd = async () => {
    if (canRefresh && !isRefreshing) {
      setIsRefreshing(true)
      try {
        await onRefresh()
      } finally {
        setIsRefreshing(false)
        setPullDistance(0)
        setCanRefresh(false)
      }
    } else {
      setPullDistance(0)
      setCanRefresh(false)
    }
  }

  const pullProgress = Math.min(pullDistance / threshold, 1)

  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-auto', className)}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Pull indicator */}
      {pullDistance > 0 && (
        <div
          className="absolute top-0 left-0 right-0 flex items-center justify-center bg-blue-50 transition-all duration-200"
          style={{ height: `${Math.min(pullDistance, threshold)}px` }}
        >
          <div className="flex items-center space-x-2 text-blue-600">
            <div
              className={cn(
                'w-6 h-6 border-2 border-blue-600 rounded-full transition-transform duration-200',
                isRefreshing ? 'animate-spin border-t-transparent' : '',
                canRefresh ? 'rotate-180' : ''
              )}
              style={{
                transform: `rotate(${pullProgress * 180}deg)`
              }}
            >
              {!isRefreshing && (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="w-0 h-0 border-l-2 border-r-2 border-b-2 border-transparent border-b-blue-600" />
                </div>
              )}
            </div>
            <span className="text-sm font-medium">
              {isRefreshing ? 'Yenileniyor...' : canRefresh ? 'Bırakın' : 'Çekin'}
            </span>
          </div>
        </div>
      )}

      <div
        className="transition-transform duration-200"
        style={{
          transform: `translateY(${pullDistance}px)`
        }}
      >
        {children}
      </div>
    </div>
  )
}

// Long press gesture component
interface LongPressProps {
  children: ReactNode
  onLongPress: () => void
  delay?: number
  className?: string
}

export function LongPress({
  children,
  onLongPress,
  delay = 500,
  className
}: LongPressProps) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [isPressed, setIsPressed] = useState(false)

  const startLongPress = () => {
    setIsPressed(true)
    timeoutRef.current = setTimeout(() => {
      onLongPress()
      if ('vibrate' in navigator) {
        navigator.vibrate(50) // Stronger haptic feedback for long press
      }
    }, delay)
  }

  const cancelLongPress = () => {
    setIsPressed(false)
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return (
    <div
      className={cn(
        'transition-transform duration-200',
        isPressed && 'scale-95 transform',
        className
      )}
      onTouchStart={startLongPress}
      onTouchEnd={cancelLongPress}
      onTouchCancel={cancelLongPress}
      onMouseDown={startLongPress}
      onMouseUp={cancelLongPress}
      onMouseLeave={cancelLongPress}
    >
      {children}
    </div>
  )
}
