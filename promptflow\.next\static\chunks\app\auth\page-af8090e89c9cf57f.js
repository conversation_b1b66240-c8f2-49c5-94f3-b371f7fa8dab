(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1579,8365],{31579:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>f});var r=a(95155),t=a(12115),i=a(35695),l=a(30285),n=a(66695),d=a(62523),o=a(85057),c=a(22100),u=a(35169),m=a(78749),x=a(92657),h=a(85339),p=a(51154),g=a(6874),b=a.n(g);function f(){let[e,s]=(0,t.useState)("signin"),[a,g]=(0,t.useState)(""),[f,j]=(0,t.useState)(""),[N,v]=(0,t.useState)(""),[y,w]=(0,t.useState)(!1),[k,z]=(0,t.useState)(""),[C,A]=(0,t.useState)(""),S=(0,i.useRouter)(),{data:E,isLoading:P}=(0,c.Jd)(),H=(0,c.a7)(),_=(0,c.wF)(),G=H.isPending||_.isPending;(0,t.useEffect)(()=>{!P&&E&&(console.log("\uD83D\uDE80 [AUTH_PAGE] User already authenticated, redirecting to dashboard"),S.replace("/dashboard"))},[E,P,S]);let D=async s=>{if(s.preventDefault(),z(""),A(""),!a||!f)return void z("L\xfctfen t\xfcm alanları doldurun");if("signup"===e&&f!==N)return void z("Şifreler eşleşmiyor");if("signup"===e&&f.length<6)return void z("Şifre en az 6 karakter olmalıdır");try{"signup"===e?(await _.mutateAsync({email:a,password:f}),A("Hesabınız oluşturuldu! E-posta adresinizi kontrol edin.")):"signin"===e?(await H.mutateAsync({email:a,password:f}),console.log("✅ [AUTH_PAGE] Sign in successful, redirect will be handled by auth state listener")):"forgot"===e&&A("Şifre sıfırlama e-postası g\xf6nderildi!")}catch(e){z(e instanceof Error?e.message:"Bir hata oluştu")}},F=e=>{s(e),g(""),j(""),v(""),z(""),A(""),w(!1)};return P?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Y\xfckleniyor..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex flex-col",children:[(0,r.jsx)("header",{className:"p-4 sm:p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,r.jsxs)(b(),{href:"/",className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(u.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Ana Sayfaya D\xf6n"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("img",{src:"/logo.png",alt:"Promptbir Logo",className:"h-8 w-auto"}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Promptbir"})]})]})}),(0,r.jsx)("main",{className:"flex-1 flex items-center justify-center p-4",children:(0,r.jsx)("div",{className:"w-full max-w-md",children:(0,r.jsxs)(n.Zp,{className:"shadow-xl border-0 bg-white/80 backdrop-blur-sm",children:[(0,r.jsxs)(n.aR,{className:"space-y-1 text-center",children:[(0,r.jsxs)(n.ZB,{className:"text-2xl font-bold",children:["signin"===e&&"Giriş Yap","signup"===e&&"Hesap Oluştur","forgot"===e&&"Şifre Sıfırla"]}),(0,r.jsxs)(n.BT,{children:["signin"===e&&"Hesabınıza giriş yapın","signup"===e&&"Promptbir'a katılın ve prompt'larınızı y\xf6netin","forgot"===e&&"E-posta adresinizi girin, size şifre sıfırlama bağlantısı g\xf6nderelim"]})]}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"email",children:"E-posta"}),(0,r.jsx)(d.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:a,onChange:e=>g(e.target.value),disabled:G,className:"h-11",autoComplete:"email",required:!0})]}),"forgot"!==e&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"password",children:"Şifre"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.p,{id:"password",type:y?"text":"password",placeholder:"••••••••",value:f,onChange:e=>j(e.target.value),disabled:G,className:"h-11 pr-10",autoComplete:"signup"===e?"new-password":"current-password",required:!0}),(0,r.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>w(!y),disabled:G,children:y?(0,r.jsx)(m.A,{className:"h-4 w-4 text-gray-400"}):(0,r.jsx)(x.A,{className:"h-4 w-4 text-gray-400"})})]})]}),"signup"===e&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"confirmPassword",children:"Şifre Tekrar"}),(0,r.jsx)(d.p,{id:"confirmPassword",type:"password",placeholder:"••••••••",value:N,onChange:e=>v(e.target.value),disabled:G,className:"h-11",autoComplete:"new-password",required:!0})]}),k&&(0,r.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 text-red-600 flex-shrink-0"}),(0,r.jsx)("p",{className:"text-sm text-red-700",children:k})]}),C&&(0,r.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md",children:[(0,r.jsx)("div",{className:"h-4 w-4 bg-green-600 rounded-full flex-shrink-0"}),(0,r.jsx)("p",{className:"text-sm text-green-700",children:C})]}),(0,r.jsxs)(l.$,{type:"submit",className:"w-full h-11 bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700",disabled:G,children:[G&&(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"signin"===e&&"Giriş Yap","signup"===e&&"Hesap Oluştur","forgot"===e&&"Şifre Sıfırlama Bağlantısı G\xf6nder"]})]}),(0,r.jsxs)("div",{className:"space-y-2 text-center text-sm",children:["signin"===e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{type:"button",onClick:()=>F("forgot"),className:"text-blue-600 hover:text-blue-700 underline",disabled:G,children:"Şifrenizi mi unuttunuz?"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Hesabınız yok mu? "}),(0,r.jsx)("button",{type:"button",onClick:()=>F("signup"),className:"text-blue-600 hover:text-blue-700 underline font-medium",disabled:G,children:"Hesap oluşturun"})]})]}),"signup"===e&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Zaten hesabınız var mı? "}),(0,r.jsx)("button",{type:"button",onClick:()=>F("signin"),className:"text-blue-600 hover:text-blue-700 underline font-medium",disabled:G,children:"Giriş yapın"})]}),"forgot"===e&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Şifrenizi hatırladınız mı? "}),(0,r.jsx)("button",{type:"button",onClick:()=>F("signin"),className:"text-blue-600 hover:text-blue-700 underline font-medium",disabled:G,children:"Giriş yapın"})]})]})]})]})})})]})}},36864:(e,s,a)=>{Promise.resolve().then(a.bind(a,31579))}},e=>{e.O(0,[2123,8611,1024,8779,8650,8523,8405,9420,4939,1486,2662,3240,8669,3189,4696,1277,7979,1899,7098,4450,9744,4495,9592,433,2652,5030,465,2903,2663,9173,408,558,1356,6475,2130,4207,4191,6489,5230,7339,957,5677,6691,1151,7114,5803,3976,3492,2608,5644,2789,9824,4075,9473,7530,6759,0,6325,6077,4409,9184,7650,1595,4532,1911,1664,7358],()=>e(e.s=36864)),_N_E=e.O()}]);