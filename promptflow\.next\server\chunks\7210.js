"use strict";exports.id=7210,exports.ids=[7210],exports.modules={1359:(a,b,c)=>{c.d(b,{Oh:()=>f});var d=c(43210),e=0;function f(){d.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??g()),document.body.insertAdjacentElement("beforeend",a[1]??g()),e++,()=>{1===e&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),e--}},[])}function g(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}},5336:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},11273:(a,b,c)=>{c.d(b,{A:()=>g,q:()=>f});var d=c(43210),e=c(60687);function f(a,b){let c=d.createContext(b),f=a=>{let{children:b,...f}=a,g=d.useMemo(()=>f,Object.values(f));return(0,e.jsx)(c.Provider,{value:g,children:b})};return f.displayName=a+"Provider",[f,function(e){let f=d.useContext(c);if(f)return f;if(void 0!==b)return b;throw Error(`\`${e}\` must be used within \`${a}\``)}]}function g(a,b=[]){let c=[],f=()=>{let b=c.map(a=>d.createContext(a));return function(c){let e=c?.[a]||b;return d.useMemo(()=>({[`__scope${a}`]:{...c,[a]:e}}),[c,e])}};return f.scopeName=a,[function(b,f){let g=d.createContext(f),h=c.length;c=[...c,f];let i=b=>{let{scope:c,children:f,...i}=b,j=c?.[a]?.[h]||g,k=d.useMemo(()=>i,Object.values(i));return(0,e.jsx)(j.Provider,{value:k,children:f})};return i.displayName=b+"Provider",[i,function(c,e){let i=e?.[a]?.[h]||g,j=d.useContext(i);if(j)return j;if(void 0!==f)return f;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return d.useMemo(()=>({[`__scope${b.scopeName}`]:e}),[e])}};return c.scopeName=b.scopeName,c}(f,...b)]}},13495:(a,b,c)=>{c.d(b,{c:()=>e});var d=c(43210);function e(a){let b=d.useRef(a);return d.useEffect(()=>{b.current=a}),d.useMemo(()=>(...a)=>b.current?.(...a),[])}},13964:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14163:(a,b,c)=>{c.d(b,{hO:()=>i,sG:()=>h});var d=c(43210),e=c(51215),f=c(8730),g=c(60687),h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,f.TL)(`Primitive.${b}`),e=d.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return e.displayName=`Primitive.${b}`,{...a,[b]:e}},{});function i(a,b){a&&e.flushSync(()=>a.dispatchEvent(b))}},18853:(a,b,c)=>{c.d(b,{X:()=>f});var d=c(43210),e=c(66156);function f(a){let[b,c]=d.useState(void 0);return(0,e.N)(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}},25028:(a,b,c)=>{c.d(b,{Z:()=>i});var d=c(43210),e=c(51215),f=c(14163),g=c(66156),h=c(60687),i=d.forwardRef((a,b)=>{let{container:c,...i}=a,[j,k]=d.useState(!1);(0,g.N)(()=>k(!0),[]);let l=c||j&&globalThis?.document?.body;return l?e.createPortal((0,h.jsx)(f.sG.div,{...i,ref:b}),l):null});i.displayName="Portal"},31355:(a,b,c)=>{c.d(b,{qW:()=>m});var d,e=c(43210),f=c(70569),g=c(14163),h=c(98599),i=c(13495),j=c(60687),k="dismissableLayer.update",l=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),m=e.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:m,onPointerDownOutside:p,onFocusOutside:q,onInteractOutside:r,onDismiss:s,...t}=a,u=e.useContext(l),[v,w]=e.useState(null),x=v?.ownerDocument??globalThis?.document,[,y]=e.useState({}),z=(0,h.s)(b,a=>w(a)),A=Array.from(u.layers),[B]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(B),D=v?A.indexOf(v):-1,E=u.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let c=(0,i.c)(a),d=e.useRef(!1),f=e.useRef(()=>{});return e.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){o("dismissableLayer.pointerDownOutside",c,e,{discrete:!0})},e={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",f.current),f.current=d,b.addEventListener("click",f.current,{once:!0})):d()}else b.removeEventListener("click",f.current);d.current=!1},e=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(e),b.removeEventListener("pointerdown",a),b.removeEventListener("click",f.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...u.branches].some(a=>a.contains(b));F&&!c&&(p?.(a),r?.(a),a.defaultPrevented||s?.())},x),H=function(a,b=globalThis?.document){let c=(0,i.c)(a),d=e.useRef(!1);return e.useEffect(()=>{let a=a=>{a.target&&!d.current&&o("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...u.branches].some(a=>a.contains(b))&&(q?.(a),r?.(a),a.defaultPrevented||s?.())},x);return!function(a,b=globalThis?.document){let c=(0,i.c)(a);e.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{D===u.layers.size-1&&(m?.(a),!a.defaultPrevented&&s&&(a.preventDefault(),s()))},x),e.useEffect(()=>{if(v)return c&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(d=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(v)),u.layers.add(v),n(),()=>{c&&1===u.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=d)}},[v,x,c,u]),e.useEffect(()=>()=>{v&&(u.layers.delete(v),u.layersWithOutsidePointerEventsDisabled.delete(v),n())},[v,u]),e.useEffect(()=>{let a=()=>y({});return document.addEventListener(k,a),()=>document.removeEventListener(k,a)},[]),(0,j.jsx)(g.sG.div,{...t,ref:z,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:(0,f.m)(a.onFocusCapture,H.onFocusCapture),onBlurCapture:(0,f.m)(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:(0,f.m)(a.onPointerDownCapture,G.onPointerDownCapture)})});function n(){let a=new CustomEvent(k);document.dispatchEvent(a)}function o(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,g.hO)(e,f):e.dispatchEvent(f)}m.displayName="DismissableLayer",e.forwardRef((a,b)=>{let c=e.useContext(l),d=e.useRef(null),f=(0,h.s)(b,d);return e.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,j.jsx)(g.sG.div,{...a,ref:f})}).displayName="DismissableLayerBranch"},32547:(a,b,c)=>{c.d(b,{n:()=>l});var d=c(43210),e=c(98599),f=c(14163),g=c(13495),h=c(60687),i="focusScope.autoFocusOnMount",j="focusScope.autoFocusOnUnmount",k={bubbles:!1,cancelable:!0},l=d.forwardRef((a,b)=>{let{loop:c=!1,trapped:l=!1,onMountAutoFocus:q,onUnmountAutoFocus:r,...s}=a,[t,u]=d.useState(null),v=(0,g.c)(q),w=(0,g.c)(r),x=d.useRef(null),y=(0,e.s)(b,a=>u(a)),z=d.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;d.useEffect(()=>{if(l){let a=function(a){if(z.paused||!t)return;let b=a.target;t.contains(b)?x.current=b:o(x.current,{select:!0})},b=function(a){if(z.paused||!t)return;let b=a.relatedTarget;null!==b&&(t.contains(b)||o(x.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&o(t)});return t&&c.observe(t,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[l,t,z.paused]),d.useEffect(()=>{if(t){p.add(z);let a=document.activeElement;if(!t.contains(a)){let b=new CustomEvent(i,k);t.addEventListener(i,v),t.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(o(d,{select:b}),document.activeElement!==c)return}(m(t).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&o(t))}return()=>{t.removeEventListener(i,v),setTimeout(()=>{let b=new CustomEvent(j,k);t.addEventListener(j,w),t.dispatchEvent(b),b.defaultPrevented||o(a??document.body,{select:!0}),t.removeEventListener(j,w),p.remove(z)},0)}}},[t,v,w,z]);let A=d.useCallback(a=>{if(!c&&!l||z.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,d=document.activeElement;if(b&&d){let b=a.currentTarget,[e,f]=function(a){let b=m(a);return[n(b,a),n(b.reverse(),a)]}(b);e&&f?a.shiftKey||d!==f?a.shiftKey&&d===e&&(a.preventDefault(),c&&o(f,{select:!0})):(a.preventDefault(),c&&o(e,{select:!0})):d===b&&a.preventDefault()}},[c,l,z.paused]);return(0,h.jsx)(f.sG.div,{tabIndex:-1,...s,ref:y,onKeyDown:A})});function m(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function n(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function o(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}l.displayName="FocusScope";var p=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=q(a,b)).unshift(b)},remove(b){a=q(a,b),a[0]?.resume()}}}();function q(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}},42247:(a,b,c)=>{c.d(b,{A:()=>U});var d,e,f=function(){return(f=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function g(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var h=("function"==typeof SuppressedError&&SuppressedError,c(43210)),i="right-scroll-bar-position",j="width-before-scroll-bar";function k(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var l="undefined"!=typeof window?h.useLayoutEffect:h.useEffect,m=new WeakMap;function n(a){return a}var o=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=n),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=f({async:!0,ssr:!1},a),e}(),p=function(){},q=h.forwardRef(function(a,b){var c,d,e,i,j=h.useRef(null),n=h.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),q=n[0],r=n[1],s=a.forwardProps,t=a.children,u=a.className,v=a.removeScrollBar,w=a.enabled,x=a.shards,y=a.sideCar,z=a.noRelative,A=a.noIsolation,B=a.inert,C=a.allowPinchZoom,D=a.as,E=a.gapMode,F=g(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),G=(c=[j,b],d=function(a){return c.forEach(function(b){return k(b,a)})},(e=(0,h.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,i=e.facade,l(function(){var a=m.get(i);if(a){var b=new Set(a),d=new Set(c),e=i.current;b.forEach(function(a){d.has(a)||k(a,null)}),d.forEach(function(a){b.has(a)||k(a,e)})}m.set(i,c)},[c]),i),H=f(f({},F),q);return h.createElement(h.Fragment,null,w&&h.createElement(y,{sideCar:o,removeScrollBar:v,shards:x,noRelative:z,noIsolation:A,inert:B,setCallbacks:r,allowPinchZoom:!!C,lockRef:j,gapMode:E}),s?h.cloneElement(h.Children.only(t),f(f({},H),{ref:G})):h.createElement(void 0===D?"div":D,f({},H,{className:u,ref:G}),t))});q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},q.classNames={fullWidth:j,zeroRight:i};var r=function(a){var b=a.sideCar,c=g(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return h.createElement(d,f({},c))};r.isSideCarExport=!0;var s=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=e||c.nc;return b&&a.setAttribute("nonce",b),a}())){var f,g;(f=b).styleSheet?f.styleSheet.cssText=d:f.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},t=function(){var a=s();return function(b,c){h.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},u=function(){var a=t();return function(b){return a(b.styles,b.dynamic),null}},v={left:0,top:0,right:0,gap:0},w=function(a){return parseInt(a||"",10)||0},x=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[w(c),w(d),w(e)]},y=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return v;var b=x(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},z=u(),A="data-scroll-locked",B=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(i," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(j," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(i," .").concat(i," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(j," .").concat(j," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},C=function(){var a=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(a)?a:0},D=function(){h.useEffect(function(){return document.body.setAttribute(A,(C()+1).toString()),function(){var a=C()-1;a<=0?document.body.removeAttribute(A):document.body.setAttribute(A,a.toString())}},[])},E=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;D();var f=h.useMemo(function(){return y(e)},[e]);return h.createElement(z,{styles:B(f,!b,e,c?"":"!important")})},F=!1;if("undefined"!=typeof window)try{var G=Object.defineProperty({},"passive",{get:function(){return F=!0,!0}});window.addEventListener("test",G,G),window.removeEventListener("test",G,G)}catch(a){F=!1}var H=!!F&&{passive:!1},I=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},J=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),K(a,d)){var e=L(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},K=function(a,b){return"v"===a?I(b,"overflowY"):I(b,"overflowX")},L=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},M=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=L(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&K(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},N=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},O=function(a){return[a.deltaX,a.deltaY]},P=function(a){return a&&"current"in a?a.current:a},Q=0,R=[];let S=(d=function(a){var b=h.useRef([]),c=h.useRef([0,0]),d=h.useRef(),e=h.useState(Q++)[0],f=h.useState(u)[0],g=h.useRef(a);h.useEffect(function(){g.current=a},[a]),h.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(P),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=h.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!g.current.allowPinchZoom;var e,f=N(a),h=c.current,i="deltaX"in a?a.deltaX:h[0]-f[0],j="deltaY"in a?a.deltaY:h[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=J(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=J(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return M(n,b,a,"h"===n?i:j,!0)},[]),j=h.useCallback(function(a){if(R.length&&R[R.length-1]===f){var c="deltaY"in a?O(a):N(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(g.current.shards||[]).map(P).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!g.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=h.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=h.useCallback(function(a){c.current=N(a),d.current=void 0},[]),m=h.useCallback(function(b){k(b.type,O(b),b.target,i(b,a.lockRef.current))},[]),n=h.useCallback(function(b){k(b.type,N(b),b.target,i(b,a.lockRef.current))},[]);h.useEffect(function(){return R.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,H),document.addEventListener("touchmove",j,H),document.addEventListener("touchstart",l,H),function(){R=R.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,H),document.removeEventListener("touchmove",j,H),document.removeEventListener("touchstart",l,H)}},[]);var o=a.removeScrollBar,p=a.inert;return h.createElement(h.Fragment,null,p?h.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?h.createElement(E,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},o.useMedium(d),r);var T=h.forwardRef(function(a,b){return h.createElement(q,f({},a,{ref:b,sideCar:S}))});T.classNames=q.classNames;let U=T},63376:(a,b,c)=>{c.d(b,{Eq:()=>j});var d=new WeakMap,e=new WeakMap,f={},g=0,h=function(a){return a&&(a.host||h(a.parentNode))},i=function(a,b,c,i){var j=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=h(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});f[c]||(f[c]=new WeakMap);var k=f[c],l=[],m=new Set,n=new Set(j),o=function(a){!a||m.has(a)||(m.add(a),o(a.parentNode))};j.forEach(o);var p=function(a){!a||n.has(a)||Array.prototype.forEach.call(a.children,function(a){if(m.has(a))p(a);else try{var b=a.getAttribute(i),f=null!==b&&"false"!==b,g=(d.get(a)||0)+1,h=(k.get(a)||0)+1;d.set(a,g),k.set(a,h),l.push(a),1===g&&f&&e.set(a,!0),1===h&&a.setAttribute(c,"true"),f||a.setAttribute(i,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return p(b),m.clear(),g++,function(){l.forEach(function(a){var b=d.get(a)-1,f=k.get(a)-1;d.set(a,b),k.set(a,f),b||(e.has(a)||a.removeAttribute(i),e.delete(a)),f||a.removeAttribute(c)}),--g||(d=new WeakMap,d=new WeakMap,e=new WeakMap,f={})}},j=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),i(d,e,c,"aria-hidden")):function(){return null}}},65551:(a,b,c)=>{c.d(b,{i:()=>h});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useInsertionEffect ".trim().toString()]||f.N;function h({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[f,h,i]=function({defaultProp:a,onChange:b}){let[c,d]=e.useState(a),f=e.useRef(c),h=e.useRef(b);return g(()=>{h.current=b},[b]),e.useEffect(()=>{f.current!==c&&(h.current?.(c),f.current=c)},[c,f]),[c,d,h]}({defaultProp:b,onChange:c}),j=void 0!==a,k=j?a:f;{let b=e.useRef(void 0!==a);e.useEffect(()=>{let a=b.current;if(a!==j){let b=j?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=j},[j,d])}return[k,e.useCallback(b=>{if(j){let c="function"==typeof b?b(a):b;c!==a&&i.current?.(c)}else h(b)},[j,a,h,i])]}Symbol("RADIX:SYNC_STATE")},66156:(a,b,c)=>{c.d(b,{N:()=>e});var d=c(43210),e=globalThis?.document?d.useLayoutEffect:()=>{}},70569:(a,b,c)=>{c.d(b,{m:()=>d});function d(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}},78148:(a,b,c)=>{c.d(b,{b:()=>h});var d=c(43210),e=c(14163),f=c(60687),g=d.forwardRef((a,b)=>(0,f.jsx)(e.sG.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));g.displayName="Label";var h=g},83721:(a,b,c)=>{c.d(b,{Z:()=>e});var d=c(43210);function e(a){let b=d.useRef({value:a,previous:a});return d.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}},96963:(a,b,c)=>{c.d(b,{B:()=>i});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useId ".trim().toString()]||(()=>void 0),h=0;function i(a){let[b,c]=e.useState(g());return(0,f.N)(()=>{a||c(a=>a??String(h++))},[a]),a||(b?`radix-${b}`:"")}}};