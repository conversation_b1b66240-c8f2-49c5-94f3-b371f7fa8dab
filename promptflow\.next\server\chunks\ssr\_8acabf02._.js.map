{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/context-sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, Suspense, lazy } from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { ContextGallerySkeleton } from \"@/components/progressive-loader\";\n\nimport { FileText, Save, Trash2, Settings, AlertTriangle, ToggleLeft, ToggleRight, X, ChevronLeft, ChevronRight, Grid3X3 } from \"lucide-react\";\nimport { useAppStore } from \"@/store/app-store\";\nimport { useProject, useUpdateProject, useDeleteProject } from \"@/hooks/use-projects\";\n\n\n\ninterface ContextSidebarProps {\n  onClose?: () => void;\n  isCollapsed?: boolean;\n  onToggleCollapse?: () => void;\n  isContextGalleryOpen?: boolean;\n  onToggleContextGallery?: () => void;\n}\n\nexport function ContextSidebar({\n  onClose,\n  isCollapsed = false,\n  onToggleCollapse,\n  isContextGalleryOpen = false,\n  onToggleContextGallery\n}: ContextSidebarProps) {\n  const [contextText, setContextText] = useState(\"\");\n  const [isSaving, setIsSaving] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | null>(null);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [deleteConfirmText, setDeleteConfirmText] = useState(\"\");\n  const { activeProjectId, setActiveProjectId, isContextEnabled, setIsContextEnabled } = useAppStore();\n  \n  const { data: project } = useProject(activeProjectId);\n  const updateProjectMutation = useUpdateProject();\n  const deleteProjectMutation = useDeleteProject();\n\n\n  // Aktif proje değiştiğinde context'i yükle\n  useEffect(() => {\n    if (project) {\n      setContextText(project.context_text || \"\");\n    } else {\n      setContextText(\"\");\n    }\n  }, [project]);\n\n  // Otomatik kayıt devre dışı bırakıldı - artık sadece manuel kayıt\n  // useEffect(() => {\n  //   if (!activeProjectId || !contextText.trim()) return;\n\n  //   const timer = setTimeout(() => {\n  //     handleSaveContext();\n  //   }, 2000);\n\n  //   return () => clearTimeout(timer);\n  // // eslint-disable-next-line react-hooks/exhaustive-deps\n  // }, [contextText, activeProjectId]);\n\n  const handleSaveContext = async () => {\n    if (!activeProjectId) return;\n\n    setIsSaving(true);\n    try {\n      await updateProjectMutation.mutateAsync({\n        id: activeProjectId,\n        context_text: contextText,\n      });\n      \n      setLastSaved(new Date());\n    } catch (error) {\n      console.error('Context kaydetme hatası:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleClearContext = () => {\n    setContextText(\"\");\n    setLastSaved(null);\n  };\n\n  const handleManualSave = () => {\n    handleSaveContext();\n  };\n\n  const handleDeleteProject = async () => {\n    if (!activeProjectId || !project) return;\n\n    // Proje adı kontrolü\n    if (deleteConfirmText !== project.name) {\n      alert('Proje adını doğru yazın!');\n      return;\n    }\n\n    try {\n      await deleteProjectMutation.mutateAsync(activeProjectId);\n      setActiveProjectId(null);\n      setShowDeleteConfirm(false);\n      setDeleteConfirmText(\"\");\n    } catch (error) {\n      console.error('Proje silme hatası:', error);\n      alert('Proje silinirken bir hata oluştu!');\n    }\n  };\n\n  const handleCancelDelete = () => {\n    setShowDeleteConfirm(false);\n    setDeleteConfirmText(\"\");\n  };\n\n\n\n  if (!activeProjectId) {\n    return (\n      <div className=\"flex items-center justify-center h-full p-6\">\n        <div className=\"text-center\">\n          <FileText className={`${isCollapsed ? 'h-6 w-6' : 'h-12 w-12'} text-gray-400 mx-auto mb-4`} />\n          {!isCollapsed && (\n            <>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Context Alanı</h3>\n              <p className=\"text-gray-500 text-sm\">Proje seçtikten sonra context metninizi buraya yazabilirsiniz</p>\n            </>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Header */}\n      <div className={`border-b border-gray-200 ${isCollapsed ? 'p-2' : 'p-4 lg:p-6'}`}>\n        <div className={`flex items-center ${isCollapsed ? 'justify-center mb-2' : 'justify-between mb-4'}`}>\n          <div className={`flex items-center gap-2 ${isCollapsed ? 'flex-col' : ''}`}>\n            {/* Desktop Toggle Button */}\n            {onToggleCollapse && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={onToggleCollapse}\n                className=\"hidden xl:flex\"\n                title={isCollapsed ? 'Ayarlar panelini genişlet' : 'Ayarlar panelini daralt'}\n              >\n                {isCollapsed ? <ChevronLeft className=\"h-4 w-4\" /> : <ChevronRight className=\"h-4 w-4\" />}\n              </Button>\n            )}\n            {!isCollapsed && <h3 className=\"text-lg font-semibold text-gray-900\">Context Ayarları</h3>}\n          </div>\n          <div className={`flex items-center gap-2 ${isCollapsed ? 'flex-col' : ''}`}>\n            {/* Mobile Close Button */}\n            {onClose && (\n              <Button variant=\"ghost\" size=\"sm\" onClick={onClose} className=\"xl:hidden\">\n                <X className=\"h-4 w-4\" />\n              </Button>\n            )}\n          </div>\n        </div>\n\n\n      </div>\n\n      {/* Context Editor */}\n      <div className={`flex-1 ${isCollapsed ? 'p-2' : 'p-6'}`}>\n        {isCollapsed ? (\n          <div className=\"flex flex-col items-center space-y-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setIsContextEnabled(!isContextEnabled)}\n              className={`w-8 h-8 p-0 rounded-md transition-all ${\n                isContextEnabled\n                  ? 'bg-green-100 text-green-700 hover:bg-green-200'\n                  : 'bg-gray-100 text-gray-500 hover:bg-gray-200'\n              }`}\n              title={`Context ${isContextEnabled ? 'Aktif' : 'Pasif'}`}\n            >\n              {isContextEnabled ? (\n                <ToggleRight className=\"h-4 w-4\" />\n              ) : (\n                <ToggleLeft className=\"h-4 w-4\" />\n              )}\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleSaveContext}\n              disabled={isSaving}\n              className=\"w-8 h-8 p-0\"\n              title=\"Context Kaydet\"\n            >\n              <Save className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n          {/* Context Gallery Button */}\n          <div className=\"mb-4\">\n            <Button\n              variant={isContextGalleryOpen ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={onToggleContextGallery}\n              className={`w-full justify-center transition-all duration-200 ${\n                isContextGalleryOpen\n                  ? \"bg-purple-600 hover:bg-purple-700 text-white shadow-md\"\n                  : \"border-purple-200 text-purple-600 hover:text-purple-700 hover:border-purple-300 hover:bg-purple-50\"\n              }`}\n            >\n              <Grid3X3 className=\"h-4 w-4 mr-2\" />\n              Context Gallery\n            </Button>\n          </div>\n\n          <div className=\"flex items-center justify-between mb-3\">\n            <div className=\"flex items-center gap-2\">\n              <label className=\"text-sm font-medium text-gray-700\">\n                Ön Tanımlı Metin\n              </label>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setIsContextEnabled(!isContextEnabled)}\n                className={`flex items-center gap-2 px-3 py-1 rounded-md transition-all ${\n                  isContextEnabled \n                    ? 'bg-green-100 text-green-700 hover:bg-green-200' \n                    : 'bg-gray-100 text-gray-500 hover:bg-gray-200'\n                }`}\n              >\n                {isContextEnabled ? (\n                  <ToggleRight className=\"h-4 w-4\" />\n                ) : (\n                  <ToggleLeft className=\"h-4 w-4\" />\n                )}\n                <span className=\"text-xs font-medium\">\n                  {isContextEnabled ? 'Aktif' : 'Pasif'}\n                </span>\n              </Button>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              {isSaving && (\n                <Badge variant=\"secondary\" className=\"text-xs\">\n                  Kaydediliyor...\n                </Badge>\n              )}\n              {lastSaved && (\n                <span className=\"text-xs text-gray-500\">\n                  Son kaydedilme: {lastSaved.toLocaleTimeString('tr-TR')}\n                </span>\n              )}\n            </div>\n          </div>\n\n\n\n          <Textarea\n            placeholder=\"Tüm promptların başına eklenecek context metninizi buraya yazın...\"\n            value={contextText}\n            onChange={(e) => setContextText(e.target.value)}\n            className=\"min-h-[200px] resize-none\"\n            disabled={isSaving}\n          />\n\n          <div className=\"flex gap-2\">\n            <Button\n              variant=\"default\"\n              size=\"sm\"\n              onClick={handleManualSave}\n              disabled={isSaving || !contextText.trim()}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n            >\n              <Save className=\"h-4 w-4 mr-2\" />\n              {isSaving ? 'Kaydediliyor...' : 'Kaydet'}\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleClearContext}\n              disabled={isSaving}\n              className=\"text-red-600 hover:text-red-700\"\n            >\n              <Trash2 className=\"h-4 w-4 mr-2\" />\n              Temizle\n            </Button>\n          </div>\n\n          <Separator className=\"my-6\" />\n\n          {/* Project Settings */}\n          <Card>\n          <CardHeader>\n            <CardTitle className=\"text-base\">Proje Ayarları</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n\n            {/* Character Count Display */}\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Karakter Sayısı</span>\n              <Badge variant=\"outline\">\n                {contextText.length.toLocaleString()}\n              </Badge>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Proje Adı</span>\n              <Badge variant=\"outline\">{project?.name}</Badge>\n            </div>\n            <Separator />\n            \n            {/* Proje Silme Bölümü */}\n            {!showDeleteConfirm ? (\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"w-full text-red-600 hover:text-red-700 hover:bg-red-50\"\n                onClick={() => setShowDeleteConfirm(true)}\n              >\n                <Trash2 className=\"h-4 w-4 mr-2\" />\n                Projeyi Sil\n              </Button>\n            ) : (\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md\">\n                  <AlertTriangle className=\"h-4 w-4 text-red-600 flex-shrink-0\" />\n                  <div>\n                    <p className=\"text-sm text-red-800 font-medium\">Dikkat!</p>\n                    <p className=\"text-xs text-red-700\">\n                      Bu işlem geri alınamaz. Proje ve tüm prompt&apos;lar silinecek.\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium text-gray-700\">\n                    Onaylamak için proje adını yazın: <span className=\"text-red-600 font-semibold\">{project?.name}</span>\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={deleteConfirmText}\n                    onChange={(e) => setDeleteConfirmText(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500\"\n                    placeholder=\"Proje adını yazın...\"\n                  />\n                </div>\n                \n                <div className=\"flex gap-2\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={handleDeleteProject}\n                    disabled={deleteConfirmText !== project?.name || deleteProjectMutation.isPending}\n                    className=\"flex-1 text-red-600 hover:text-red-700 hover:bg-red-50\"\n                  >\n                    <Trash2 className=\"h-4 w-4 mr-2\" />\n                    {deleteProjectMutation.isPending ? 'Siliniyor...' : 'Sil'}\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={handleCancelDelete}\n                    className=\"flex-1\"\n                  >\n                    İptal\n                  </Button>\n                </div>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default ContextSidebar;"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAZA;;;;;;;;;;;AAwBO,SAAS,eAAe,EAC7B,OAAO,EACP,cAAc,KAAK,EACnB,gBAAgB,EAChB,uBAAuB,KAAK,EAC5B,sBAAsB,EACF;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEjG,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,aAAU,AAAD,EAAE;IACrC,MAAM,wBAAwB,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IAC7C,MAAM,wBAAwB,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IAG7C,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,eAAe,QAAQ,YAAY,IAAI;QACzC,OAAO;YACL,eAAe;QACjB;IACF,GAAG;QAAC;KAAQ;IAEZ,kEAAkE;IAClE,oBAAoB;IACpB,yDAAyD;IAEzD,qCAAqC;IACrC,2BAA2B;IAC3B,cAAc;IAEd,sCAAsC;IACtC,0DAA0D;IAC1D,sCAAsC;IAEtC,MAAM,oBAAoB;QACxB,IAAI,CAAC,iBAAiB;QAEtB,YAAY;QACZ,IAAI;YACF,MAAM,sBAAsB,WAAW,CAAC;gBACtC,IAAI;gBACJ,cAAc;YAChB;YAEA,aAAa,IAAI;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,qBAAqB;QACzB,eAAe;QACf,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,mBAAmB,CAAC,SAAS;QAElC,qBAAqB;QACrB,IAAI,sBAAsB,QAAQ,IAAI,EAAE;YACtC,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,sBAAsB,WAAW,CAAC;YACxC,mBAAmB;YACnB,qBAAqB;YACrB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB;QACzB,qBAAqB;QACrB,qBAAqB;IACvB;IAIA,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,WAAW,GAAG,cAAc,YAAY,YAAY,2BAA2B,CAAC;;;;;;oBACzF,CAAC,6BACA;;0CACE,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;IAMjD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,yBAAyB,EAAE,cAAc,QAAQ,cAAc;0BAC9E,cAAA,8OAAC;oBAAI,WAAW,CAAC,kBAAkB,EAAE,cAAc,wBAAwB,wBAAwB;;sCACjG,8OAAC;4BAAI,WAAW,CAAC,wBAAwB,EAAE,cAAc,aAAa,IAAI;;gCAEvE,kCACC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,OAAO,cAAc,8BAA8B;8CAElD,4BAAc,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;6DAAe,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;gCAGhF,CAAC,6BAAe,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;;sCAEvE,8OAAC;4BAAI,WAAW,CAAC,wBAAwB,EAAE,cAAc,aAAa,IAAI;sCAEvE,yBACC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,SAAS;gCAAS,WAAU;0CAC5D,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,8OAAC;gBAAI,WAAW,CAAC,OAAO,EAAE,cAAc,QAAQ,OAAO;0BACpD,4BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAW,CAAC,sCAAsC,EAChD,mBACI,mDACA,+CACJ;4BACF,OAAO,CAAC,QAAQ,EAAE,mBAAmB,UAAU,SAAS;sCAEvD,iCACC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;sCAG1B,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;yCAIpB,8OAAC;oBAAI,WAAU;;sCAEf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,uBAAuB,YAAY;gCAC5C,MAAK;gCACL,SAAS;gCACT,WAAW,CAAC,kDAAkD,EAC5D,uBACI,2DACA,sGACJ;;kDAEF,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAKxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,oBAAoB,CAAC;4CACpC,WAAW,CAAC,4DAA4D,EACtE,mBACI,mDACA,+CACJ;;gDAED,iCACC,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DAExB,8OAAC;oDAAK,WAAU;8DACb,mBAAmB,UAAU;;;;;;;;;;;;;;;;;;8CAIpC,8OAAC;oCAAI,WAAU;;wCACZ,0BACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAAU;;;;;;wCAIhD,2BACC,8OAAC;4CAAK,WAAU;;gDAAwB;gDACrB,UAAU,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;sCAQtD,8OAAC,oIAAA,CAAA,WAAQ;4BACP,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;4BACV,UAAU;;;;;;sCAGZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU,YAAY,CAAC,YAAY,IAAI;oCACvC,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,WAAW,oBAAoB;;;;;;;8CAElC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAKvC,8OAAC,qIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCAGrB,8OAAC,gIAAA,CAAA,OAAI;;8CACL,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAY;;;;;;;;;;;8CAEnC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAGrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DACZ,YAAY,MAAM,CAAC,cAAc;;;;;;;;;;;;sDAGtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW,SAAS;;;;;;;;;;;;sDAErC,8OAAC,qIAAA,CAAA,YAAS;;;;;wCAGT,CAAC,kCACA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,qBAAqB;;8DAEpC,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;iEAIrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;sEACzB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAmC;;;;;;8EAChD,8OAAC;oEAAE,WAAU;8EAAuB;;;;;;;;;;;;;;;;;;8DAMxC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;;gEAAoC;8EACjB,8OAAC;oEAAK,WAAU;8EAA8B,SAAS;;;;;;;;;;;;sEAE3F,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;4DACpD,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,UAAU,sBAAsB,SAAS,QAAQ,sBAAsB,SAAS;4DAChF,WAAU;;8EAEV,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,sBAAsB,SAAS,GAAG,iBAAiB;;;;;;;sEAEtD,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAanB;uCAEe", "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/file-text.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/lucide-react/src/icons/file-text.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z', key: '1rqfz7' }],\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n  ['path', { d: 'M10 9H8', key: 'b1mrlr' }],\n  ['path', { d: 'M16 13H8', key: 't4e002' }],\n  ['path', { d: 'M16 17H8', key: 'z1uh3a' }],\n];\n\n/**\n * @component @name FileText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjRhMiAyIDAgMCAwIDIgMmg0IiAvPgogIDxwYXRoIGQ9Ik0xMCA5SDgiIC8+CiAgPHBhdGggZD0iTTE2IDEzSDgiIC8+CiAgPHBhdGggZD0iTTE2IDE3SDgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileText = createLucideIcon('file-text', __iconNode);\n\nexport default FileText;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3F;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/save.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/lucide-react/src/icons/save.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z',\n      key: '1c8476',\n    },\n  ],\n  ['path', { d: 'M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7', key: '1ydtos' }],\n  ['path', { d: 'M7 3v4a1 1 0 0 0 1 1h7', key: 't51u73' }],\n];\n\n/**\n * @component @name Save\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUuMiAzYTIgMiAwIDAgMSAxLjQuNmwzLjggMy44YTIgMiAwIDAgMSAuNiAxLjRWMTlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yeiIgLz4KICA8cGF0aCBkPSJNMTcgMjF2LTdhMSAxIDAgMCAwLTEtMUg4YTEgMSAwIDAgMC0xIDF2NyIgLz4KICA8cGF0aCBkPSJNNyAzdjRhMSAxIDAgMCAwIDEgMWg3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/save\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Save = createLucideIcon('save', __iconNode);\n\nexport default Save;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 871, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAS,CAAA;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/toggle-left.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/lucide-react/src/icons/toggle-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '9', cy: '12', r: '3', key: 'u3jwor' }],\n  ['rect', { width: '20', height: '14', x: '2', y: '5', rx: '7', key: 'g7kal2' }],\n];\n\n/**\n * @component @name ToggleLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iMTIiIHI9IjMiIC8+CiAgPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjE0IiB4PSIyIiB5PSI1IiByeD0iNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/toggle-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ToggleLeft = createLucideIcon('toggle-left', __iconNode);\n\nexport default ToggleLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,EAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK;YAAA,EAAG,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/toggle-right.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/lucide-react/src/icons/toggle-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '15', cy: '12', r: '3', key: '1afu0r' }],\n  ['rect', { width: '20', height: '14', x: '2', y: '5', rx: '7', key: 'g7kal2' }],\n];\n\n/**\n * @component @name ToggleRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxNSIgY3k9IjEyIiByPSIzIiAvPgogIDxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxNCIgeD0iMiIgeT0iNSIgcng9IjciIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/toggle-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ToggleRight = createLucideIcon('toggle-right', __iconNode);\n\nexport default ToggleRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,EAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK;YAAA,EAAG,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/grid-3x3.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/node_modules/lucide-react/src/icons/grid-3x3.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' }],\n  ['path', { d: 'M3 9h18', key: '1pudct' }],\n  ['path', { d: 'M3 15h18', key: '5xshup' }],\n  ['path', { d: 'M9 3v18', key: 'fh3hqa' }],\n  ['path', { d: 'M15 3v18', key: '14nvp0' }],\n];\n\n/**\n * @component @name Grid3x3\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDloMTgiIC8+CiAgPHBhdGggZD0iTTMgMTVoMTgiIC8+CiAgPHBhdGggZD0iTTkgM3YxOCIgLz4KICA8cGF0aCBkPSJNMTUgM3YxOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/grid-3x3\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Grid3x3 = createLucideIcon('grid-3x3', __iconNode);\n\nexport default Grid3x3;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}