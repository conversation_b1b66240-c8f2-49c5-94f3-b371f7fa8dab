'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { useState, useEffect } from 'react'
import { createOptimizedQueryClient, BrowserCache, CachePerformance } from '@/lib/cache-strategies'

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => createOptimizedQueryClient())

  // Initialize cache monitoring
  useEffect(() => {
    // Clear old cache on app start
    const lastClearTime = BrowserCache.getLocal<number>('cache-clear-time')
    const now = Date.now()
    const oneDay = 24 * 60 * 60 * 1000

    if (!lastClearTime || now - lastClearTime > oneDay) {
      BrowserCache.clearAll()
      BrowserCache.setLocal('cache-clear-time', now)
      console.log('🧹 [CACHE] Daily cache cleanup completed')
    }

    // Performance monitoring in development
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(() => {
        const metrics = CachePerformance.getCacheMetrics()
        const cacheInfo = BrowserCache.getCacheInfo()

        console.log('📊 [CACHE_METRICS]', {
          performance: metrics.slice(0, 5), // Top 5 queries
          storage: cacheInfo
        })
      }, 30000) // Every 30 seconds

      return () => clearInterval(interval)
    }
  }, [])

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools
          initialIsOpen={false}
          position={"bottom-right" as any}
        />
      )}
    </QueryClientProvider>
  )
}