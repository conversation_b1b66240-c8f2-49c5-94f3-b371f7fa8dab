'use client'

import { useState } from 'react'
import { Share2, <PERSON><PERSON>, Eye, EyeOff, Calendar, Lock, Globe, Link2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useCreateSharedPrompt, useUserSharedPrompts } from '@/hooks/use-shared-prompts'
import { toast } from 'sonner'

interface SharePromptButtonProps {
  promptId: string
  promptTitle?: string
  promptText: string
  taskCode?: string
  className?: string
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
}

export function SharePromptButton({
  promptId,
  promptTitle,
  promptText,
  taskCode,
  className,
  variant = 'ghost',
  size = 'sm'
}: SharePromptButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [shareTitle, setShareTitle] = useState(promptTitle || taskCode || '')
  const [shareDescription, setShareDescription] = useState('')
  const [isPublic, setIsPublic] = useState(true)
  const [password, setPassword] = useState('')
  const [expiresAt, setExpiresAt] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  const createSharedPromptMutation = useCreateSharedPrompt()
  const { data: userSharedPrompts } = useUserSharedPrompts()

  // Bu prompt zaten paylaşılmış mı kontrol et
  const existingShare = userSharedPrompts?.find(sp => sp.prompt_id === promptId)

  const handleShare = async () => {
    try {
      await createSharedPromptMutation.mutateAsync({
        prompt_id: promptId,
        title: shareTitle.trim() || undefined,
        description: shareDescription.trim() || undefined,
        is_public: isPublic,
        password: password.trim() || undefined,
        expires_at: expiresAt || undefined
      })
      
      setIsOpen(false)
      resetForm()
    } catch (error) {
      console.error('Share error:', error)
    }
  }

  const resetForm = () => {
    setShareTitle(promptTitle || taskCode || '')
    setShareDescription('')
    setIsPublic(true)
    setPassword('')
    setExpiresAt('')
    setShowPassword(false)
  }

  const copyExistingLink = async () => {
    if (existingShare) {
      const shareUrl = `${window.location.origin}/share/${existingShare.share_token}`
      try {
        await navigator.clipboard.writeText(shareUrl)
        toast.success('Link panoya kopyalandı!')
      } catch (error) {
        toast.error('Link kopyalanamadı')
      }
    }
  }

  const formatPromptPreview = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={className}
          onClick={() => setIsOpen(true)}
        >
          <Share2 className="h-4 w-4" />
          {size !== 'icon' && (
            <span className="ml-1">
              {existingShare ? 'Paylaşıldı' : 'Paylaş'}
            </span>
          )}
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Prompt Paylaş
          </DialogTitle>
          <DialogDescription>
            Bu prompt'u başkalarıyla paylaşmak için bir link oluşturun
          </DialogDescription>
        </DialogHeader>

        {existingShare ? (
          // Zaten paylaşılmış prompt için mevcut link göster
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Link2 className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">
                  Bu prompt zaten paylaşılmış
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Input
                  value={`${window.location.origin}/share/${existingShare.share_token}`}
                  readOnly
                  className="text-xs bg-white"
                />
                <Button
                  size="sm"
                  onClick={copyExistingLink}
                  className="shrink-0"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <div className="mt-2 flex items-center gap-4 text-xs text-green-700">
                <span className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  {existingShare.view_count} görüntülenme
                </span>
                <span>
                  {new Date(existingShare.created_at).toLocaleDateString('tr-TR')}
                </span>
              </div>
            </div>

            <div className="p-3 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium mb-2">Prompt Önizleme</h4>
              <p className="text-xs text-gray-600 font-mono">
                {formatPromptPreview(promptText)}
              </p>
            </div>
          </div>
        ) : (
          // Yeni paylaşım oluşturma formu
          <div className="space-y-4">
            {/* Prompt Önizleme */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium mb-2">Prompt Önizleme</h4>
              <p className="text-xs text-gray-600 font-mono">
                {formatPromptPreview(promptText)}
              </p>
            </div>

            <Separator />

            {/* Paylaşım Ayarları */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="share-title">Başlık (Opsiyonel)</Label>
                <Input
                  id="share-title"
                  value={shareTitle}
                  onChange={(e) => setShareTitle(e.target.value)}
                  placeholder="Paylaşım için özel başlık"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="share-description">Açıklama (Opsiyonel)</Label>
                <Textarea
                  id="share-description"
                  value={shareDescription}
                  onChange={(e) => setShareDescription(e.target.value)}
                  placeholder="Bu prompt hakkında kısa açıklama"
                  className="mt-1 resize-none"
                  rows={2}
                />
              </div>

              {/* Görünürlük Ayarları */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {isPublic ? (
                    <Globe className="h-4 w-4 text-green-600" />
                  ) : (
                    <Lock className="h-4 w-4 text-orange-600" />
                  )}
                  <Label htmlFor="is-public">Herkese Açık</Label>
                </div>
                <Switch
                  id="is-public"
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                />
              </div>

              {/* Şifre Koruması */}
              <div>
                <Label htmlFor="password">Şifre Koruması (Opsiyonel)</Label>
                <div className="flex gap-2 mt-1">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Şifre belirleyin"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Son Kullanma Tarihi */}
              <div>
                <Label htmlFor="expires-at">Son Kullanma Tarihi (Opsiyonel)</Label>
                <Input
                  id="expires-at"
                  type="datetime-local"
                  value={expiresAt}
                  onChange={(e) => setExpiresAt(e.target.value)}
                  className="mt-1"
                  min={new Date().toISOString().slice(0, 16)}
                />
              </div>
            </div>

            <Separator />

            {/* Paylaş Butonu */}
            <div className="flex gap-2">
              <Button
                onClick={handleShare}
                disabled={createSharedPromptMutation.isPending}
                className="flex-1"
              >
                {createSharedPromptMutation.isPending ? (
                  'Oluşturuluyor...'
                ) : (
                  <>
                    <Share2 className="h-4 w-4 mr-2" />
                    Paylaşım Linki Oluştur
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsOpen(false)}
              >
                İptal
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
