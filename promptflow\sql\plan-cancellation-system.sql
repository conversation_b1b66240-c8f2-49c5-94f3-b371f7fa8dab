-- Plan Cancellation System with 14-day Trial Logic
-- This migration adds cancellation functionality with trial period and refund logic

-- 1. Add trial_ends_at column to user_plans if not exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_plans' AND column_name = 'trial_ends_at'
    ) THEN
        ALTER TABLE user_plans ADD COLUMN trial_ends_at timestamp with time zone;
        
        -- Update existing plans to have trial period (14 days from started_at)
        UPDATE user_plans 
        SET trial_ends_at = started_at + interval '14 days'
        WHERE trial_ends_at IS NULL AND status = 'active';
        
        RAISE NOTICE 'Added trial_ends_at column to user_plans';
    END IF;
END $$;

-- 2. Add cancellation_reason column for tracking why users cancel
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_plans' AND column_name = 'cancellation_reason'
    ) THEN
        ALTER TABLE user_plans ADD COLUMN cancellation_reason text;
        RAISE NOTICE 'Added cancellation_reason column to user_plans';
    END IF;
END $$;

-- 3. Add refund_status column for tracking refund processing
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_plans' AND column_name = 'refund_status'
    ) THEN
        ALTER TABLE user_plans ADD COLUMN refund_status text DEFAULT 'none' CHECK (refund_status IN ('none', 'pending', 'processed', 'failed'));
        RAISE NOTICE 'Added refund_status column to user_plans';
    END IF;
END $$;

-- 4. Add refund_amount column
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_plans' AND column_name = 'refund_amount'
    ) THEN
        ALTER TABLE user_plans ADD COLUMN refund_amount numeric(10,2) DEFAULT 0;
        RAISE NOTICE 'Added refund_amount column to user_plans';
    END IF;
END $$;

-- 5. Function to check if user is in trial period
CREATE OR REPLACE FUNCTION is_user_in_trial_period(user_uuid uuid)
RETURNS boolean AS $$
DECLARE
    trial_end_date timestamp with time zone;
BEGIN
    SELECT trial_ends_at INTO trial_end_date
    FROM user_plans
    WHERE user_id = user_uuid 
    AND status = 'active'
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- If no trial end date or trial has ended, return false
    IF trial_end_date IS NULL OR trial_end_date < NOW() THEN
        RETURN false;
    END IF;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Function to get user's trial information
CREATE OR REPLACE FUNCTION get_user_trial_info(user_uuid uuid)
RETURNS TABLE (
    is_in_trial boolean,
    trial_ends_at timestamp with time zone,
    days_remaining integer,
    plan_name text,
    plan_display_name text,
    can_cancel_with_refund boolean
) AS $$
DECLARE
    user_plan_record record;
    trial_active boolean;
    days_left integer;
BEGIN
    -- Get current active plan
    SELECT up.*, pt.name as plan_name, pt.display_name as plan_display_name
    INTO user_plan_record
    FROM user_plans up
    JOIN plan_types pt ON up.plan_type_id = pt.id
    WHERE up.user_id = user_uuid 
    AND up.status = 'active'
    ORDER BY up.created_at DESC
    LIMIT 1;
    
    -- If no active plan found, return defaults
    IF user_plan_record IS NULL THEN
        RETURN QUERY SELECT false, NULL::timestamp with time zone, 0, 'free'::text, 'Ücretsiz'::text, false;
        RETURN;
    END IF;
    
    -- Check if in trial period
    trial_active := user_plan_record.trial_ends_at IS NOT NULL AND user_plan_record.trial_ends_at > NOW();
    
    -- Calculate days remaining
    IF trial_active THEN
        days_left := EXTRACT(days FROM (user_plan_record.trial_ends_at - NOW()))::integer;
    ELSE
        days_left := 0;
    END IF;
    
    -- Can cancel with refund if in trial period and not free plan
    RETURN QUERY SELECT 
        trial_active,
        user_plan_record.trial_ends_at,
        days_left,
        user_plan_record.plan_name,
        user_plan_record.plan_display_name,
        trial_active AND user_plan_record.plan_name != 'free';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Function to get cancellation eligibility
CREATE OR REPLACE FUNCTION get_cancellation_eligibility(user_uuid uuid)
RETURNS TABLE (
    can_cancel boolean,
    reason text,
    is_in_trial boolean,
    refund_eligible boolean,
    estimated_refund_amount numeric,
    plan_name text,
    plan_display_name text
) AS $$
DECLARE
    current_plan_record record;
    trial_active boolean;
    refund_amount numeric := 0;
BEGIN
    -- Get current active plan
    SELECT up.*, pt.name as plan_name, pt.display_name as plan_display_name, pt.price_monthly, pt.price_yearly
    INTO current_plan_record
    FROM user_plans up
    JOIN plan_types pt ON up.plan_type_id = pt.id
    WHERE up.user_id = user_uuid 
    AND up.status = 'active'
    ORDER BY up.created_at DESC
    LIMIT 1;
    
    -- If no active plan
    IF current_plan_record IS NULL THEN
        RETURN QUERY SELECT false, 'Aktif plan bulunamadı'::text, false, false, 0::numeric, ''::text, ''::text;
        RETURN;
    END IF;
    
    -- If free plan
    IF current_plan_record.plan_name = 'free' THEN
        RETURN QUERY SELECT false, 'Ücretsiz plan iptal edilemez'::text, false, false, 0::numeric, current_plan_record.plan_name, current_plan_record.plan_display_name;
        RETURN;
    END IF;
    
    -- Check trial status
    trial_active := current_plan_record.trial_ends_at IS NOT NULL AND current_plan_record.trial_ends_at > NOW();
    
    -- Calculate potential refund
    IF trial_active THEN
        IF current_plan_record.billing_cycle = 'yearly' THEN
            refund_amount := current_plan_record.price_yearly;
        ELSE
            refund_amount := current_plan_record.price_monthly;
        END IF;
    END IF;
    
    -- Return eligibility info
    RETURN QUERY SELECT 
        true, -- Can cancel paid plans
        'Plan iptal edilebilir'::text,
        trial_active,
        trial_active, -- Refund eligible if in trial
        refund_amount,
        current_plan_record.plan_name,
        current_plan_record.plan_display_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Function to cancel user plan with refund logic
CREATE OR REPLACE FUNCTION cancel_user_plan(
    user_uuid uuid,
    cancellation_reason_param text DEFAULT NULL,
    request_refund boolean DEFAULT false
)
RETURNS TABLE (
    success boolean,
    message text,
    refund_eligible boolean,
    refund_amount numeric,
    cancelled_plan_id uuid
) AS $$
DECLARE
    current_plan_record record;
    is_trial_active boolean;
    calculated_refund_amount numeric := 0;
    plan_price numeric := 0;
BEGIN
    -- Get current active plan
    SELECT up.*, pt.name as plan_name, pt.display_name as plan_display_name, pt.price_monthly, pt.price_yearly
    INTO current_plan_record
    FROM user_plans up
    JOIN plan_types pt ON up.plan_type_id = pt.id
    WHERE up.user_id = user_uuid
    AND up.status = 'active'
    ORDER BY up.created_at DESC
    LIMIT 1;

    -- Check if user has an active plan
    IF current_plan_record IS NULL THEN
        RETURN QUERY SELECT false, 'Aktif plan bulunamadı'::text, false, 0::numeric, NULL::uuid;
        RETURN;
    END IF;

    -- Check if it's free plan (cannot be cancelled)
    IF current_plan_record.plan_name = 'free' THEN
        RETURN QUERY SELECT false, 'Ücretsiz plan iptal edilemez'::text, false, 0::numeric, NULL::uuid;
        RETURN;
    END IF;

    -- Check if already cancelled
    IF current_plan_record.status = 'cancelled' THEN
        RETURN QUERY SELECT false, 'Plan zaten iptal edilmiş'::text, false, 0::numeric, NULL::uuid;
        RETURN;
    END IF;

    -- Check if in trial period
    is_trial_active := current_plan_record.trial_ends_at IS NOT NULL AND current_plan_record.trial_ends_at > NOW();

    -- Calculate refund amount if in trial and refund requested
    IF is_trial_active AND request_refund THEN
        -- Get the price based on billing cycle
        IF current_plan_record.billing_cycle = 'yearly' THEN
            plan_price := current_plan_record.price_yearly;
        ELSE
            plan_price := current_plan_record.price_monthly;
        END IF;

        calculated_refund_amount := plan_price;
    END IF;

    -- Update the plan to cancelled status
    UPDATE user_plans
    SET
        status = 'cancelled',
        cancelled_at = NOW(),
        cancellation_reason = cancellation_reason_param,
        refund_status = CASE
            WHEN calculated_refund_amount > 0 THEN 'pending'
            ELSE 'none'
        END,
        refund_amount = calculated_refund_amount,
        updated_at = NOW()
    WHERE id = current_plan_record.id;

    -- Create a transaction record
    INSERT INTO plan_transactions (
        user_id,
        from_plan_id,
        to_plan_id,
        transaction_type,
        amount,
        payment_status,
        notes,
        processed_at
    ) VALUES (
        user_uuid,
        current_plan_record.plan_type_id,
        (SELECT id FROM plan_types WHERE name = 'free' LIMIT 1), -- Downgrade to free
        'cancellation',
        -calculated_refund_amount, -- Negative amount for refund
        CASE
            WHEN calculated_refund_amount > 0 THEN 'pending'
            ELSE 'completed'
        END,
        COALESCE(cancellation_reason_param, 'Plan cancelled by user'),
        NOW()
    );

    -- Create new free plan for user
    INSERT INTO user_plans (user_id, plan_type_id, status, billing_cycle, started_at)
    VALUES (
        user_uuid,
        (SELECT id FROM plan_types WHERE name = 'free' LIMIT 1),
        'active',
        'lifetime',
        NOW()
    );

    -- Return success result
    RETURN QUERY SELECT
        true,
        CASE
            WHEN calculated_refund_amount > 0 THEN 'Plan başarıyla iptal edildi. İade işlemi başlatıldı.'
            ELSE 'Plan başarıyla iptal edildi.'
        END::text,
        calculated_refund_amount > 0,
        calculated_refund_amount,
        current_plan_record.id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Update existing user plans to have trial period if they don't have one
UPDATE user_plans
SET trial_ends_at = started_at + interval '14 days'
WHERE trial_ends_at IS NULL
AND status = 'active'
AND plan_type_id IN (
    SELECT id FROM plan_types WHERE name != 'free'
);

-- 10. Grant execute permissions
GRANT EXECUTE ON FUNCTION is_user_in_trial_period(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_trial_info(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION get_cancellation_eligibility(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION cancel_user_plan(uuid, text, boolean) TO authenticated;
