#!/usr/bin/env node

/**
 * Seed Context Categories Script
 * Adds default categories to context_categories table
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import path from 'path';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Default categories to seed
const defaultCategories = [
  {
    name: 'Geliştirme',
    description: 'Kod parçacıkları, API dokümantasyonu, debugging promptları',
    icon: '💻',
    color: '#3B82F6',
    sort_order: 1,
    is_active: true
  },
  {
    name: 'Yazım',
    description: 'İçerik oluşturma, metin yazımı, blog yazıları',
    icon: '✍️',
    color: '#10B981',
    sort_order: 2,
    is_active: true
  },
  {
    name: 'Analiz',
    description: 'Veri analizi, araştırma promptları, rapor oluşturma',
    icon: '📊',
    color: '#8B5CF6',
    sort_order: 3,
    is_active: true
  },
  {
    name: 'Yaratıcılık',
    description: 'Tasarım brifleri, yaratıcı yazım, beyin fırtınası',
    icon: '🎨',
    color: '#F59E0B',
    sort_order: 4,
    is_active: true
  },
  {
    name: 'İş',
    description: 'Toplantı notları, proje planlama, strateji',
    icon: '💼',
    color: '#EF4444',
    sort_order: 5,
    is_active: true
  },
  {
    name: 'Eğitim',
    description: 'Öğrenme materyalleri, açıklamalar, eğitim içerikleri',
    icon: '📚',
    color: '#06B6D4',
    sort_order: 6,
    is_active: true
  }
];

async function seedCategories() {
  console.log('🌱 Starting context categories seeding...\n');

  try {
    // Check if categories already exist
    const { data: existingCategories, error: checkError } = await supabase
      .from('context_categories')
      .select('name')
      .eq('is_active', true);

    if (checkError) {
      console.error('❌ Error checking existing categories:', checkError);
      return;
    }

    if (existingCategories && existingCategories.length > 0) {
      console.log('ℹ️  Categories already exist:');
      existingCategories.forEach(cat => console.log(`   - ${cat.name}`));
      console.log('\n✅ Seeding skipped - categories already present');
      return;
    }

    // Insert default categories
    const { data, error } = await supabase
      .from('context_categories')
      .insert(defaultCategories)
      .select();

    if (error) {
      console.error('❌ Error seeding categories:', error);
      return;
    }

    console.log('✅ Successfully seeded context categories:');
    data.forEach(category => {
      console.log(`   ${category.icon} ${category.name} - ${category.description}`);
    });

    console.log(`\n🎉 Total ${data.length} categories added successfully!`);

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the seeding
seedCategories();
