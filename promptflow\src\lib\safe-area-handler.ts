/**
 * Safe Area Handler
 * iOS safe area, Android navigation bar, and viewport meta tag optimizations
 */

import { useState, useEffect } from 'react'

// Safe area configuration
export const SAFE_AREA_CONFIG = {
  // CSS custom properties for safe areas
  cssVariables: {
    top: '--safe-area-inset-top',
    right: '--safe-area-inset-right', 
    bottom: '--safe-area-inset-bottom',
    left: '--safe-area-inset-left'
  },
  
  // Fallback values for browsers that don't support safe areas
  fallbacks: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  },
  
  // Device-specific adjustments
  deviceAdjustments: {
    ios: {
      statusBar: 44,
      homeIndicator: 34,
      notch: 44
    },
    android: {
      statusBar: 24,
      navigationBar: 48,
      gestureBar: 16
    }
  }
} as const

// Safe area detection and handling
export class SafeAreaHandler {
  private static isInitialized = false
  private static currentInsets = { top: 0, right: 0, bottom: 0, left: 0 }
  
  // Initialize safe area handling
  static initialize() {
    if (this.isInitialized || typeof window === 'undefined') return
    
    this.detectDevice()
    this.setupViewportMeta()
    this.applySafeAreaCSS()
    this.setupEventListeners()
    this.updateSafeAreaInsets()
    
    this.isInitialized = true
  }

  // Detect device type and capabilities
  private static detectDevice() {
    const userAgent = navigator.userAgent.toLowerCase()
    const isIOS = /iphone|ipad|ipod/.test(userAgent)
    const isAndroid = /android/.test(userAgent)
    const isSafari = /safari/.test(userAgent) && !/chrome/.test(userAgent)
    
    // Add device classes to body
    document.body.classList.toggle('is-ios', isIOS)
    document.body.classList.toggle('is-android', isAndroid)
    document.body.classList.toggle('is-safari', isSafari)
    
    // Detect specific iOS devices
    if (isIOS) {
      this.detectIOSDevice()
    }
    
    // Detect Android navigation type
    if (isAndroid) {
      this.detectAndroidNavigation()
    }
  }

  // Detect specific iOS device features
  private static detectIOSDevice() {
    const screen = window.screen
    const width = screen.width
    const height = screen.height
    
    // iPhone X series detection (devices with notch/dynamic island)
    const hasNotch = (
      (width === 375 && height === 812) || // iPhone X, XS, 11 Pro
      (width === 414 && height === 896) || // iPhone XR, 11, XS Max, 11 Pro Max
      (width === 390 && height === 844) || // iPhone 12, 12 Pro
      (width === 428 && height === 926) || // iPhone 12 Pro Max
      (width === 393 && height === 852) || // iPhone 14, 14 Pro
      (width === 430 && height === 932)    // iPhone 14 Pro Max
    )
    
    document.body.classList.toggle('has-notch', hasNotch)
    document.body.classList.toggle('has-home-indicator', true) // All modern iOS devices
  }

  // Detect Android navigation type
  private static detectAndroidNavigation() {
    // Try to detect gesture navigation vs button navigation
    const hasGestureNavigation = window.innerHeight > screen.height * 0.95
    
    document.body.classList.toggle('has-gesture-navigation', hasGestureNavigation)
    document.body.classList.toggle('has-button-navigation', !hasGestureNavigation)
  }

  // Setup viewport meta tag
  private static setupViewportMeta() {
    let viewportMeta = document.querySelector('meta[name="viewport"]') as HTMLMetaElement
    
    if (!viewportMeta) {
      viewportMeta = document.createElement('meta')
      viewportMeta.name = 'viewport'
      document.head.appendChild(viewportMeta)
    }
    
    // Enhanced viewport configuration
    const viewportContent = [
      'width=device-width',
      'initial-scale=1',
      'viewport-fit=cover', // Important for safe area support
      'user-scalable=no',   // Prevent zoom on iOS
      'shrink-to-fit=no'    // Prevent shrinking on iOS
    ].join(', ')
    
    viewportMeta.content = viewportContent
    
    // Add iOS-specific meta tags
    if (document.body.classList.contains('is-ios')) {
      this.addIOSMetaTags()
    }
  }

  // Add iOS-specific meta tags
  private static addIOSMetaTags() {
    const metaTags = [
      { name: 'apple-mobile-web-app-capable', content: 'yes' },
      { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
      { name: 'apple-touch-fullscreen', content: 'yes' },
      { name: 'format-detection', content: 'telephone=no' }
    ]
    
    metaTags.forEach(tag => {
      let meta = document.querySelector(`meta[name="${tag.name}"]`) as HTMLMetaElement
      if (!meta) {
        meta = document.createElement('meta')
        meta.name = tag.name
        document.head.appendChild(meta)
      }
      meta.content = tag.content
    })
  }

  // Apply safe area CSS
  private static applySafeAreaCSS() {
    const css = `
      :root {
        /* Safe area insets with fallbacks */
        ${SAFE_AREA_CONFIG.cssVariables.top}: env(safe-area-inset-top, ${SAFE_AREA_CONFIG.fallbacks.top}px);
        ${SAFE_AREA_CONFIG.cssVariables.right}: env(safe-area-inset-right, ${SAFE_AREA_CONFIG.fallbacks.right}px);
        ${SAFE_AREA_CONFIG.cssVariables.bottom}: env(safe-area-inset-bottom, ${SAFE_AREA_CONFIG.fallbacks.bottom}px);
        ${SAFE_AREA_CONFIG.cssVariables.left}: env(safe-area-inset-left, ${SAFE_AREA_CONFIG.fallbacks.left}px);
        
        /* Convenience variables */
        --safe-area-top: var(${SAFE_AREA_CONFIG.cssVariables.top});
        --safe-area-right: var(${SAFE_AREA_CONFIG.cssVariables.right});
        --safe-area-bottom: var(${SAFE_AREA_CONFIG.cssVariables.bottom});
        --safe-area-left: var(${SAFE_AREA_CONFIG.cssVariables.left});
      }
      
      /* Base safe area styles */
      .safe-area-inset {
        padding-top: var(--safe-area-top);
        padding-right: var(--safe-area-right);
        padding-bottom: var(--safe-area-bottom);
        padding-left: var(--safe-area-left);
      }
      
      .safe-area-inset-top {
        padding-top: var(--safe-area-top);
      }
      
      .safe-area-inset-bottom {
        padding-bottom: var(--safe-area-bottom);
      }
      
      .safe-area-inset-left {
        padding-left: var(--safe-area-left);
      }
      
      .safe-area-inset-right {
        padding-right: var(--safe-area-right);
      }
      
      /* Full viewport height accounting for safe areas */
      .full-height-safe {
        height: calc(100vh - var(--safe-area-top) - var(--safe-area-bottom));
        min-height: calc(100vh - var(--safe-area-top) - var(--safe-area-bottom));
      }
      
      /* Fixed positioning with safe areas */
      .fixed-top-safe {
        position: fixed;
        top: var(--safe-area-top);
        left: var(--safe-area-left);
        right: var(--safe-area-right);
      }
      
      .fixed-bottom-safe {
        position: fixed;
        bottom: var(--safe-area-bottom);
        left: var(--safe-area-left);
        right: var(--safe-area-right);
      }
      
      /* iOS-specific adjustments */
      .is-ios.has-notch {
        --status-bar-height: 44px;
      }
      
      .is-ios:not(.has-notch) {
        --status-bar-height: 20px;
      }
      
      .is-ios.has-home-indicator {
        --home-indicator-height: 34px;
      }
      
      /* Android-specific adjustments */
      .is-android {
        --status-bar-height: 24px;
      }
      
      .is-android.has-button-navigation {
        --navigation-bar-height: 48px;
      }
      
      .is-android.has-gesture-navigation {
        --navigation-bar-height: 16px;
      }
      
      /* Landscape orientation adjustments */
      @media (orientation: landscape) {
        .is-ios.has-notch {
          --safe-area-left: max(var(--safe-area-left), 44px);
          --safe-area-right: max(var(--safe-area-right), 44px);
        }
      }
      
      /* Utility classes for common layouts */
      .app-container {
        min-height: 100vh;
        padding: var(--safe-area-top) var(--safe-area-right) var(--safe-area-bottom) var(--safe-area-left);
      }
      
      .header-safe {
        padding-top: calc(1rem + var(--safe-area-top));
      }
      
      .footer-safe {
        padding-bottom: calc(1rem + var(--safe-area-bottom));
      }
      
      /* Modal and overlay adjustments */
      .modal-safe {
        top: var(--safe-area-top);
        bottom: var(--safe-area-bottom);
        left: var(--safe-area-left);
        right: var(--safe-area-right);
      }
      
      /* Sticky elements */
      .sticky-top-safe {
        position: sticky;
        top: var(--safe-area-top);
      }
      
      .sticky-bottom-safe {
        position: sticky;
        bottom: var(--safe-area-bottom);
      }
    `
    
    const styleId = 'safe-area-styles'
    let style = document.getElementById(styleId) as HTMLStyleElement
    
    if (!style) {
      style = document.createElement('style')
      style.id = styleId
      document.head.appendChild(style)
    }
    
    style.textContent = css
  }

  // Setup event listeners
  private static setupEventListeners() {
    // Listen for orientation changes
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        this.updateSafeAreaInsets()
      }, 100)
    })
    
    // Listen for resize events
    window.addEventListener('resize', () => {
      this.updateSafeAreaInsets()
    })
    
    // Listen for visual viewport changes (iOS keyboard)
    if ('visualViewport' in window) {
      window.visualViewport?.addEventListener('resize', () => {
        this.handleVisualViewportChange()
      })
    }
  }

  // Update safe area insets
  private static updateSafeAreaInsets() {
    const computedStyle = getComputedStyle(document.documentElement)
    
    this.currentInsets = {
      top: parseInt(computedStyle.getPropertyValue(SAFE_AREA_CONFIG.cssVariables.top)) || 0,
      right: parseInt(computedStyle.getPropertyValue(SAFE_AREA_CONFIG.cssVariables.right)) || 0,
      bottom: parseInt(computedStyle.getPropertyValue(SAFE_AREA_CONFIG.cssVariables.bottom)) || 0,
      left: parseInt(computedStyle.getPropertyValue(SAFE_AREA_CONFIG.cssVariables.left)) || 0
    }
    
    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('safeAreaChange', {
      detail: this.currentInsets
    }))
  }

  // Handle visual viewport changes (mainly for iOS keyboard)
  private static handleVisualViewportChange() {
    if (!window.visualViewport) return
    
    const viewport = window.visualViewport
    const heightDifference = window.innerHeight - viewport.height
    
    // Adjust for keyboard
    if (heightDifference > 150) { // Keyboard is likely open
      document.body.classList.add('keyboard-open')
      document.documentElement.style.setProperty('--keyboard-height', `${heightDifference}px`)
    } else {
      document.body.classList.remove('keyboard-open')
      document.documentElement.style.setProperty('--keyboard-height', '0px')
    }
  }

  // Public getters
  static get insets() {
    return { ...this.currentInsets }
  }

  static get isSupported() {
    return CSS.supports('padding: env(safe-area-inset-top)')
  }
}

// React hook for safe area
export function useSafeArea() {
  const [insets, setInsets] = useState({ top: 0, right: 0, bottom: 0, left: 0 })
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false)

  useEffect(() => {
    SafeAreaHandler.initialize()
    setInsets(SafeAreaHandler.insets)

    const handleSafeAreaChange = (event: CustomEvent) => {
      setInsets(event.detail)
    }

    const handleKeyboardChange = () => {
      setIsKeyboardOpen(document.body.classList.contains('keyboard-open'))
    }

    window.addEventListener('safeAreaChange', handleSafeAreaChange as EventListener)
    
    // Watch for keyboard class changes
    const observer = new MutationObserver(handleKeyboardChange)
    observer.observe(document.body, { attributes: true, attributeFilter: ['class'] })

    return () => {
      window.removeEventListener('safeAreaChange', handleSafeAreaChange as EventListener)
      observer.disconnect()
    }
  }, [])

  return {
    insets,
    isKeyboardOpen,
    isSupported: SafeAreaHandler.isSupported,
    hasNotch: document.body.classList.contains('has-notch'),
    isIOS: document.body.classList.contains('is-ios'),
    isAndroid: document.body.classList.contains('is-android')
  }
}

// Auto-initialize on module load
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      SafeAreaHandler.initialize()
    })
  } else {
    SafeAreaHandler.initialize()
  }
}
