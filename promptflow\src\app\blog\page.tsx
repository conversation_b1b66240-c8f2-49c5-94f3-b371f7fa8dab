import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Sparkles, 
  Calendar, 
  Clock, 
  User,
  ArrowRight,
  BookOpen,
  TrendingUp,
  Zap,
  Shield,
  Users
} from 'lucide-react'

export const metadata: Metadata = {
  title: 'Blog - PromptBir | AI ve Prompt Yönetimi Hakkında Yazılar',
  description: 'AI teknolojileri, prompt engineering, verimlilik artırma ve PromptBir platformu hakkında güncel blog yazıları.',
  keywords: [
    'AI blog',
    'prompt engineering',
    'yapay zeka',
    'verimlilik',
    'PromptBir blog',
    'AI araçları'
  ],
  openGraph: {
    title: 'Blog - PromptBir',
    description: 'AI teknolojileri ve prompt yönetimi hakkında güncel yazılar',
    type: 'website',
    url: 'https://promptbir.com/blog'
  }
}

const blogPosts = [
  {
    id: 1,
    title: 'PromptBir ile AI Verimliliğinizi 10 Kat Artırın',
    slug: 'promptbir-ile-ai-verimlilik',
    excerpt: 'AI prompt\'larınızı organize ederek nasıl daha verimli çalışabileceğinizi öğrenin. PromptBir\'in sunduğu özellikler ile iş akışınızı optimize edin.',
    content: 'AI teknolojilerinin hızla geliştiği günümüzde, doğru prompt yönetimi kritik önem taşıyor...',
    author: 'PromptBir Ekibi',
    publishDate: '2024-01-15',
    readTime: '8 dakika',
    category: 'Verimlilik',
    tags: ['AI', 'Verimlilik', 'Prompt Yönetimi'],
    featured: true
  },
  {
    id: 2,
    title: 'Prompt Engineering: Başlangıçtan İleri Seviyeye Rehber',
    slug: 'prompt-engineering-rehber',
    excerpt: 'Etkili prompt yazma sanatını öğrenin. Temel prensiplerden ileri tekniklere kadar kapsamlı rehber.',
    content: 'Prompt engineering, AI ile etkileşimde bulunmanın en önemli becerilerinden biri...',
    author: 'Dr. Ahmet Yılmaz',
    publishDate: '2024-01-10',
    readTime: '12 dakika',
    category: 'Eğitim',
    tags: ['Prompt Engineering', 'AI', 'Eğitim'],
    featured: true
  },
  {
    id: 3,
    title: 'Takım Çalışmasında AI Prompt Paylaşımının Önemi',
    slug: 'takim-calismasi-prompt-paylasimi',
    excerpt: 'Ekip halinde çalışırken prompt paylaşımının nasıl verimliliği artırdığını ve en iyi uygulamaları keşfedin.',
    content: 'Modern iş dünyasında takım çalışması ve bilgi paylaşımı kritik önem taşıyor...',
    author: 'Elif Kaya',
    publishDate: '2024-01-05',
    readTime: '6 dakika',
    category: 'Takım Çalışması',
    tags: ['Takım Çalışması', 'Paylaşım', 'Verimlilik'],
    featured: false
  },
  {
    id: 4,
    title: 'AI Güvenliği ve Prompt Injection Saldırıları',
    slug: 'ai-guvenlik-prompt-injection',
    excerpt: 'AI sistemlerinin güvenlik açıkları ve prompt injection saldırılarından nasıl korunabileceğinizi öğrenin.',
    content: 'AI sistemlerinin yaygınlaşmasıyla birlikte güvenlik konuları da önem kazanıyor...',
    author: 'Mehmet Demir',
    publishDate: '2023-12-28',
    readTime: '10 dakika',
    category: 'Güvenlik',
    tags: ['AI Güvenliği', 'Prompt Injection', 'Siber Güvenlik'],
    featured: false
  },
  {
    id: 5,
    title: 'PromptBir API: Geliştiriciler için Kapsamlı Rehber',
    slug: 'promptbir-api-rehber',
    excerpt: 'PromptBir API\'sini kullanarak kendi uygulamalarınızda prompt yönetimi nasıl entegre edebileceğinizi öğrenin.',
    content: 'PromptBir API, geliştiricilerin kendi uygulamalarında prompt yönetimi yapabilmelerini sağlar...',
    author: 'PromptBir Geliştirici Ekibi',
    publishDate: '2023-12-20',
    readTime: '15 dakika',
    category: 'Geliştirici',
    tags: ['API', 'Geliştirici', 'Entegrasyon'],
    featured: false
  }
]

const categories = ['Tümü', 'Verimlilik', 'Eğitim', 'Takım Çalışması', 'Güvenlik', 'Geliştirici']

export default function BlogPage() {
  const featuredPosts = blogPosts.filter(post => post.featured)
  const regularPosts = blogPosts.filter(post => !post.featured)

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link 
              href="/" 
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Ana Sayfaya Dön</span>
            </Link>
            <div className="flex items-center space-x-2">
              <Sparkles className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">PromptBir</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <BookOpen className="h-8 w-8 text-blue-600" />
          </div>
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
            PromptBir Blog
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            AI teknolojileri, prompt engineering ve verimlilik artırma konularında 
            güncel yazılar ve rehberler.
          </p>
        </div>

        {/* Featured Posts */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 flex items-center gap-3">
            <TrendingUp className="h-8 w-8 text-orange-600" />
            Öne Çıkan Yazılar
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            {featuredPosts.map((post) => (
              <Card key={post.id} className="shadow-lg hover:shadow-xl transition-shadow group">
                <CardHeader>
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="secondary" className="bg-orange-100 text-orange-700">
                      Öne Çıkan
                    </Badge>
                    <Badge variant="outline">
                      {post.category}
                    </Badge>
                  </div>
                  <CardTitle className="text-xl group-hover:text-blue-600 transition-colors">
                    <Link href={`/blog/${post.slug}`}>
                      {post.title}
                    </Link>
                  </CardTitle>
                  <CardDescription className="text-base leading-relaxed">
                    {post.excerpt}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        <span>{post.author}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(post.publishDate).toLocaleDateString('tr-TR')}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{post.readTime}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <Link 
                    href={`/blog/${post.slug}`}
                    className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium group-hover:gap-3 transition-all"
                  >
                    Devamını Oku
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Category Filter */}
        <section className="mb-8">
          <div className="flex flex-wrap gap-3 justify-center">
            {categories.map((category) => (
              <Badge 
                key={category}
                variant={category === 'Tümü' ? 'default' : 'outline'}
                className="cursor-pointer hover:bg-blue-100 hover:text-blue-700 transition-colors px-4 py-2"
              >
                {category}
              </Badge>
            ))}
          </div>
        </section>

        {/* Regular Posts */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 flex items-center gap-3">
            <BookOpen className="h-8 w-8 text-blue-600" />
            Tüm Yazılar
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {regularPosts.map((post) => (
              <Card key={post.id} className="shadow-lg hover:shadow-xl transition-shadow group">
                <CardHeader>
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="outline">
                      {post.category}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
                    <Link href={`/blog/${post.slug}`}>
                      {post.title}
                    </Link>
                  </CardTitle>
                  <CardDescription className="leading-relaxed">
                    {post.excerpt}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(post.publishDate).toLocaleDateString('tr-TR')}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{post.readTime}</span>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-1 mb-4">
                    {post.tags.slice(0, 2).map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {post.tags.length > 2 && (
                      <Badge variant="secondary" className="text-xs">
                        +{post.tags.length - 2}
                      </Badge>
                    )}
                  </div>
                  <Link 
                    href={`/blog/${post.slug}`}
                    className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium group-hover:gap-3 transition-all"
                  >
                    Oku
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Newsletter Signup */}
        <section className="mb-16">
          <Card className="shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="p-12 text-center">
              <Zap className="h-16 w-16 mx-auto mb-6 text-blue-100" />
              <h2 className="text-3xl font-bold mb-4">
                Yeni Yazılardan Haberdar Olun
              </h2>
              <p className="text-blue-100 mb-8 max-w-2xl mx-auto">
                AI teknolojileri ve prompt engineering konularındaki en güncel yazılarımızı 
                e-posta ile almak için abone olun.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="E-posta adresiniz"
                  className="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500"
                />
                <button className="px-6 py-3 bg-white text-blue-600 rounded-lg font-medium hover:bg-blue-50 transition-colors">
                  Abone Ol
                </button>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Popular Topics */}
        <section>
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Popüler Konular</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="shadow-lg text-center">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">AI Verimliliği</h3>
                <p className="text-gray-600 text-sm">
                  AI araçları ile iş süreçlerinizi optimize edin
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-lg text-center">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Prompt Engineering</h3>
                <p className="text-gray-600 text-sm">
                  Etkili prompt yazma teknikleri
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-lg text-center">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Takım Çalışması</h3>
                <p className="text-gray-600 text-sm">
                  Ekip halinde AI kullanımı
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-lg text-center">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">AI Güvenliği</h3>
                <p className="text-gray-600 text-sm">
                  Güvenli AI kullanımı rehberleri
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-400">
            © 2024 PromptBir. Tüm hakları saklıdır.
          </p>
        </div>
      </footer>
    </div>
  )
}
