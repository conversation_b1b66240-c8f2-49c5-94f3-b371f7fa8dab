/**
 * Semantic HTML Optimizer
 * Proper HTML5 semantic elements, heading hierarchy, and landmark roles optimization
 */

// Semantic HTML structure definitions
export const SEMANTIC_STRUCTURE = {
  landmarks: {
    banner: 'header[role="banner"], header:not([role])',
    navigation: 'nav[role="navigation"], nav:not([role])',
    main: 'main[role="main"], main:not([role])',
    complementary: 'aside[role="complementary"], aside:not([role])',
    contentinfo: 'footer[role="contentinfo"], footer:not([role])',
    search: '[role="search"]',
    form: 'form[role="form"], form:not([role])',
    region: '[role="region"]'
  },
  
  headingLevels: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
  
  semanticElements: [
    'article', 'section', 'nav', 'aside', 'header', 'footer', 'main',
    'figure', 'figcaption', 'time', 'mark', 'details', 'summary'
  ],
  
  interactiveElements: [
    'button', 'a', 'input', 'select', 'textarea', 'details', 'summary'
  ]
} as const

// Semantic HTML audit results
interface SemanticAuditResult {
  score: number
  issues: Array<{
    type: 'critical' | 'warning' | 'info'
    category: 'structure' | 'headings' | 'landmarks' | 'accessibility' | 'seo'
    element: string
    description: string
    recommendation: string
    line?: number
  }>
  structure: {
    hasMain: boolean
    hasHeader: boolean
    hasFooter: boolean
    hasNavigation: boolean
    headingStructure: Array<{ level: number; text: string; element: HTMLElement }>
    landmarks: string[]
  }
}

// Semantic HTML Auditor
export class SemanticHTMLAuditor {
  private issues: SemanticAuditResult['issues'] = []
  private structure: SemanticAuditResult['structure'] = {
    hasMain: false,
    hasHeader: false,
    hasFooter: false,
    hasNavigation: false,
    headingStructure: [],
    landmarks: []
  }

  // Perform comprehensive semantic HTML audit
  static performAudit(): SemanticAuditResult {
    const auditor = new SemanticHTMLAuditor()
    
    auditor.auditDocumentStructure()
    auditor.auditHeadingHierarchy()
    auditor.auditLandmarkRoles()
    auditor.auditSemanticElements()
    auditor.auditAccessibilityFeatures()
    auditor.auditSEOElements()
    
    return auditor.generateReport()
  }

  // Audit document structure
  private auditDocumentStructure() {
    // Check for main landmark
    const main = document.querySelector('main')
    this.structure.hasMain = !!main
    
    if (!main) {
      this.addIssue({
        type: 'critical',
        category: 'structure',
        element: 'body',
        description: 'Missing main landmark',
        recommendation: 'Add <main> element to contain primary content'
      })
    } else if (document.querySelectorAll('main').length > 1) {
      this.addIssue({
        type: 'critical',
        category: 'structure',
        element: 'main',
        description: 'Multiple main landmarks found',
        recommendation: 'Use only one <main> element per page'
      })
    }

    // Check for header
    const header = document.querySelector('header')
    this.structure.hasHeader = !!header
    
    if (!header) {
      this.addIssue({
        type: 'warning',
        category: 'structure',
        element: 'body',
        description: 'Missing header landmark',
        recommendation: 'Add <header> element for page header'
      })
    }

    // Check for footer
    const footer = document.querySelector('footer')
    this.structure.hasFooter = !!footer
    
    if (!footer) {
      this.addIssue({
        type: 'info',
        category: 'structure',
        element: 'body',
        description: 'Missing footer landmark',
        recommendation: 'Add <footer> element for page footer'
      })
    }

    // Check for navigation
    const nav = document.querySelector('nav')
    this.structure.hasNavigation = !!nav
    
    if (!nav) {
      this.addIssue({
        type: 'warning',
        category: 'structure',
        element: 'body',
        description: 'Missing navigation landmark',
        recommendation: 'Add <nav> element for navigation'
      })
    }

    // Check for proper nesting
    this.auditElementNesting()
  }

  // Audit heading hierarchy
  private auditHeadingHierarchy() {
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
    let previousLevel = 0
    let hasH1 = false

    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1))
      const text = heading.textContent?.trim() || ''
      
      this.structure.headingStructure.push({
        level,
        text,
        element: heading as HTMLElement
      })

      // Check for H1
      if (level === 1) {
        if (hasH1) {
          this.addIssue({
            type: 'warning',
            category: 'headings',
            element: this.getElementSelector(heading as HTMLElement),
            description: 'Multiple H1 elements found',
            recommendation: 'Use only one H1 per page for main heading'
          })
        }
        hasH1 = true
      }

      // Check heading hierarchy
      if (index === 0 && level !== 1) {
        this.addIssue({
          type: 'warning',
          category: 'headings',
          element: this.getElementSelector(heading as HTMLElement),
          description: 'First heading is not H1',
          recommendation: 'Start with H1 for main page heading'
        })
      }

      if (level > previousLevel + 1) {
        this.addIssue({
          type: 'warning',
          category: 'headings',
          element: this.getElementSelector(heading as HTMLElement),
          description: `Heading level skipped: H${previousLevel} to H${level}`,
          recommendation: 'Use sequential heading levels (H1, H2, H3, etc.)'
        })
      }

      // Check for empty headings
      if (!text) {
        this.addIssue({
          type: 'critical',
          category: 'headings',
          element: this.getElementSelector(heading as HTMLElement),
          description: 'Empty heading element',
          recommendation: 'Provide meaningful heading text'
        })
      }

      previousLevel = level
    })

    if (!hasH1) {
      this.addIssue({
        type: 'critical',
        category: 'headings',
        element: 'body',
        description: 'No H1 heading found',
        recommendation: 'Add H1 element for main page heading'
      })
    }
  }

  // Audit landmark roles
  private auditLandmarkRoles() {
    Object.entries(SEMANTIC_STRUCTURE.landmarks).forEach(([role, selector]) => {
      const elements = document.querySelectorAll(selector)
      
      if (elements.length > 0) {
        this.structure.landmarks.push(role)
        
        // Check for proper labeling of landmarks
        elements.forEach(element => {
          if (elements.length > 1 && !element.hasAttribute('aria-label') && !element.hasAttribute('aria-labelledby')) {
            this.addIssue({
              type: 'warning',
              category: 'landmarks',
              element: this.getElementSelector(element as HTMLElement),
              description: `Multiple ${role} landmarks without labels`,
              recommendation: 'Add aria-label or aria-labelledby to distinguish landmarks'
            })
          }
        })
      }
    })

    // Check for skip links
    const skipLink = document.querySelector('a[href="#main"], a[href="#content"]')
    if (!skipLink) {
      this.addIssue({
        type: 'warning',
        category: 'landmarks',
        element: 'body',
        description: 'Missing skip link',
        recommendation: 'Add skip link for keyboard navigation'
      })
    }
  }

  // Audit semantic elements usage
  private auditSemanticElements() {
    // Check for proper use of article elements
    const articles = document.querySelectorAll('article')
    articles.forEach(article => {
      const heading = article.querySelector('h1, h2, h3, h4, h5, h6')
      if (!heading) {
        this.addIssue({
          type: 'warning',
          category: 'structure',
          element: this.getElementSelector(article),
          description: 'Article without heading',
          recommendation: 'Add heading to article element'
        })
      }
    })

    // Check for proper use of section elements
    const sections = document.querySelectorAll('section')
    sections.forEach(section => {
      const heading = section.querySelector('h1, h2, h3, h4, h5, h6')
      if (!heading && !section.hasAttribute('aria-label') && !section.hasAttribute('aria-labelledby')) {
        this.addIssue({
          type: 'warning',
          category: 'structure',
          element: this.getElementSelector(section),
          description: 'Section without heading or label',
          recommendation: 'Add heading or aria-label to section element'
        })
      }
    })

    // Check for figure and figcaption
    const figures = document.querySelectorAll('figure')
    figures.forEach(figure => {
      const figcaption = figure.querySelector('figcaption')
      if (!figcaption) {
        this.addIssue({
          type: 'info',
          category: 'structure',
          element: this.getElementSelector(figure),
          description: 'Figure without caption',
          recommendation: 'Add figcaption for better accessibility'
        })
      }
    })

    // Check for time elements
    const timeElements = document.querySelectorAll('time')
    timeElements.forEach(time => {
      if (!time.hasAttribute('datetime')) {
        this.addIssue({
          type: 'info',
          category: 'structure',
          element: this.getElementSelector(time),
          description: 'Time element without datetime attribute',
          recommendation: 'Add datetime attribute for machine-readable time'
        })
      }
    })
  }

  // Audit accessibility features
  private auditAccessibilityFeatures() {
    // Check for proper form labels
    const inputs = document.querySelectorAll('input, select, textarea')
    inputs.forEach(input => {
      const hasLabel = input.hasAttribute('aria-label') ||
                      input.hasAttribute('aria-labelledby') ||
                      document.querySelector(`label[for="${input.id}"]`)
      
      if (!hasLabel) {
        this.addIssue({
          type: 'critical',
          category: 'accessibility',
          element: this.getElementSelector(input as HTMLElement),
          description: 'Form control without label',
          recommendation: 'Add label element or aria-label attribute'
        })
      }
    })

    // Check for alt text on images
    const images = document.querySelectorAll('img')
    images.forEach(img => {
      if (!img.hasAttribute('alt')) {
        this.addIssue({
          type: 'critical',
          category: 'accessibility',
          element: this.getElementSelector(img),
          description: 'Image without alt text',
          recommendation: 'Add alt attribute describing the image'
        })
      }
    })

    // Check for proper button text
    const buttons = document.querySelectorAll('button')
    buttons.forEach(button => {
      const text = button.textContent?.trim()
      const hasAriaLabel = button.hasAttribute('aria-label')
      
      if (!text && !hasAriaLabel) {
        this.addIssue({
          type: 'critical',
          category: 'accessibility',
          element: this.getElementSelector(button),
          description: 'Button without accessible text',
          recommendation: 'Add text content or aria-label to button'
        })
      }
    })
  }

  // Audit SEO elements
  private auditSEOElements() {
    // Check for meta description
    const metaDescription = document.querySelector('meta[name="description"]')
    if (!metaDescription) {
      this.addIssue({
        type: 'warning',
        category: 'seo',
        element: 'head',
        description: 'Missing meta description',
        recommendation: 'Add meta description for better SEO'
      })
    }

    // Check for title element
    const title = document.querySelector('title')
    if (!title || !title.textContent?.trim()) {
      this.addIssue({
        type: 'critical',
        category: 'seo',
        element: 'head',
        description: 'Missing or empty title element',
        recommendation: 'Add descriptive title element'
      })
    }

    // Check for lang attribute
    const html = document.documentElement
    if (!html.hasAttribute('lang')) {
      this.addIssue({
        type: 'warning',
        category: 'seo',
        element: 'html',
        description: 'Missing lang attribute',
        recommendation: 'Add lang attribute to html element'
      })
    }
  }

  // Audit element nesting
  private auditElementNesting() {
    // Check for interactive elements inside interactive elements
    const interactiveElements = document.querySelectorAll(
      SEMANTIC_STRUCTURE.interactiveElements.join(', ')
    )
    
    interactiveElements.forEach(element => {
      const nestedInteractive = element.querySelector(
        SEMANTIC_STRUCTURE.interactiveElements.join(', ')
      )
      
      if (nestedInteractive) {
        this.addIssue({
          type: 'critical',
          category: 'structure',
          element: this.getElementSelector(element as HTMLElement),
          description: 'Interactive element nested inside another interactive element',
          recommendation: 'Avoid nesting interactive elements'
        })
      }
    })

    // Check for proper heading nesting in sections
    const sections = document.querySelectorAll('section, article, aside')
    sections.forEach(section => {
      const headings = section.querySelectorAll('h1, h2, h3, h4, h5, h6')
      if (headings.length > 0) {
        const firstHeading = headings[0]
        const level = parseInt(firstHeading.tagName.charAt(1))
        
        // Check if section heading is appropriate for its context
        const parentSection = section.closest('section, article, aside')
        if (parentSection) {
          const parentHeading = parentSection.querySelector('h1, h2, h3, h4, h5, h6')
          if (parentHeading) {
            const parentLevel = parseInt(parentHeading.tagName.charAt(1))
            if (level <= parentLevel) {
              this.addIssue({
                type: 'warning',
                category: 'headings',
                element: this.getElementSelector(firstHeading as HTMLElement),
                description: 'Section heading level not properly nested',
                recommendation: 'Use heading levels that reflect content hierarchy'
              })
            }
          }
        }
      }
    })
  }

  // Helper methods
  private addIssue(issue: SemanticAuditResult['issues'][0]) {
    this.issues.push(issue)
  }

  private getElementSelector(element: HTMLElement): string {
    if (element.id) return `#${element.id}`
    if (element.className) return `.${element.className.split(' ')[0]}`
    return element.tagName.toLowerCase()
  }

  private generateReport(): SemanticAuditResult {
    const criticalIssues = this.issues.filter(i => i.type === 'critical').length
    const warningIssues = this.issues.filter(i => i.type === 'warning').length
    const infoIssues = this.issues.filter(i => i.type === 'info').length
    
    // Calculate score (100 - penalties)
    const score = Math.max(0, 100 - (criticalIssues * 15) - (warningIssues * 5) - (infoIssues * 1))
    
    return {
      score,
      issues: this.issues,
      structure: this.structure
    }
  }
}

// Semantic HTML optimizer
export class SemanticHTMLOptimizer {
  // Auto-fix common semantic issues
  static autoFix(): void {
    this.addMissingLandmarks()
    this.addMissingLabels()
    this.fixHeadingStructure()
    this.addSkipLinks()
  }

  // Add missing landmark elements
  private static addMissingLandmarks() {
    // Add main if missing
    if (!document.querySelector('main')) {
      const content = document.querySelector('#content, .content, .main-content')
      if (content && content.tagName !== 'MAIN') {
        const main = document.createElement('main')
        content.parentNode?.insertBefore(main, content)
        main.appendChild(content)
      }
    }

    // Add header if missing
    if (!document.querySelector('header')) {
      const topElement = document.querySelector('.header, .top-bar, .navbar')
      if (topElement && topElement.tagName !== 'HEADER') {
        const header = document.createElement('header')
        topElement.parentNode?.insertBefore(header, topElement)
        header.appendChild(topElement)
      }
    }
  }

  // Add missing labels
  private static addMissingLabels() {
    const unlabeledInputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])')
    unlabeledInputs.forEach(input => {
      const placeholder = input.getAttribute('placeholder')
      if (placeholder) {
        input.setAttribute('aria-label', placeholder)
      }
    })
  }

  // Fix heading structure
  private static fixHeadingStructure() {
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
    let expectedLevel = 1

    headings.forEach(heading => {
      const currentLevel = parseInt(heading.tagName.charAt(1))
      
      if (currentLevel > expectedLevel) {
        // Adjust heading level
        const newHeading = document.createElement(`h${expectedLevel}`)
        newHeading.innerHTML = heading.innerHTML
        newHeading.className = heading.className
        heading.parentNode?.replaceChild(newHeading, heading)
      }
      
      expectedLevel = Math.min(6, expectedLevel + 1)
    })
  }

  // Add skip links
  private static addSkipLinks() {
    if (!document.querySelector('a[href="#main"], a[href="#content"]')) {
      const main = document.querySelector('main')
      if (main) {
        main.id = main.id || 'main'
        
        const skipLink = document.createElement('a')
        skipLink.href = '#main'
        skipLink.textContent = 'Skip to main content'
        skipLink.className = 'sr-only focus:not-sr-only'
        
        document.body.insertBefore(skipLink, document.body.firstChild)
      }
    }
  }
}

// Auto-audit on page load
export function enableSemanticAudit() {
  if (typeof window === 'undefined') return

  window.addEventListener('load', () => {
    const result = SemanticHTMLAuditor.performAudit()
    
    if (process.env.NODE_ENV === 'development') {
      console.group('🏗️ Semantic HTML Audit')
      console.log(`Score: ${result.score}/100`)
      console.log(`Issues: ${result.issues.length}`)
      
      if (result.issues.length > 0) {
        console.table(result.issues)
      }
      
      console.log('Structure:', result.structure)
      console.groupEnd()
    }
  })
}
