'use client'

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Plus, Search, Folder, LogOut, User, X, ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";
import { useAppStore } from "@/store/app-store";
import { useProjects, useCreateProject } from "@/hooks/use-projects";
import { useUser, useSignOut } from "@/hooks/use-auth";
import { useUserLimits } from "@/hooks/use-plans";
import { InlineLimitWarning } from "@/components/limit-warning";
import { PlanDisplay } from "@/components/plan-display";
import { ProjectNameEditor } from "@/components/project-name-editor";
import { toast } from "sonner";

interface ProjectSidebarProps {
  onClose?: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export function ProjectSidebar({ onClose, isCollapsed = false, onToggleCollapse }: ProjectSidebarProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreatingProject, setIsCreatingProject] = useState(false);
  const [newProjectName, setNewProjectName] = useState("");
  const { activeProjectId, setActiveProjectId } = useAppStore();
  
  const { data: projects = [], isLoading: projectsLoading, error: projectsError } = useProjects();
  const { data: user } = useUser();
  const { data: limits } = useUserLimits();
  const createProjectMutation = useCreateProject();
  const signOutMutation = useSignOut();

  console.log(`📁 [PROJECT_SIDEBAR] Projects:`, {
    count: projects.length,
    loading: projectsLoading,
    error: projectsError,
    projects: projects.map(p => ({ id: p.id, name: p.name }))
  });

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateProject = async () => {
    if (newProjectName.trim()) {
      // Plan limiti kontrol et
      if (limits && !limits.can_create_project) {
        toast.error(`Proje oluşturma limitinize ulaştınız (${limits.current_projects}/${limits.max_projects}). Planınızı yükseltin.`);
        return;
      }

      try {
        const newProject = await createProjectMutation.mutateAsync({
          name: newProjectName.trim(),
          context_text: "",
        });
        setActiveProjectId(newProject.id);
        setNewProjectName("");
        setIsCreatingProject(false);
      } catch (error) {
        console.error("Proje oluşturma hatası:", error);
      }
    }
  };

  const handleSignOut = async () => {
    try {
      console.log('Logout button clicked')
      await signOutMutation.mutateAsync();
    } catch (error) {
      console.error('Logout error in component:', error)
      // Even if there's an error, the mutation's onError will handle cleanup
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className={`border-b border-gray-200 ${isCollapsed ? 'p-2' : 'p-4 lg:p-6'}`}>
        <div className={`flex items-center ${isCollapsed ? 'justify-center mb-2' : 'justify-between mb-4'}`}>
          {!isCollapsed && (
            <div className="flex items-center gap-2">
              <img
                src="/logo.png"
                alt="Promptbir Logo"
                className="h-6 w-auto"
              />
              <h1 className="text-xl font-semibold text-gray-900">Promptbir</h1>
            </div>
          )}
          {isCollapsed && (
            <img
              src="/logo.png"
              alt="Promptbir Logo"
              className="h-6 w-auto"
              title="Promptbir"
            />
          )}
          <div className={`flex items-center gap-2 ${isCollapsed ? 'flex-col' : ''}`}>
            {/* Desktop Toggle Button */}
            {onToggleCollapse && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleCollapse}
                className="hidden lg:flex"
                title={isCollapsed ? 'Proje panelini genişlet' : 'Proje panelini daralt'}
              >
                {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
              </Button>
            )}
            {/* Mobile Close Button */}
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose} className="lg:hidden">
                <X className="h-4 w-4" />
              </Button>
            )}
            <Link href="/profile">
              <Button variant="ghost" size="sm" title={user?.email || 'Kullanıcı'}>
                <User className="h-4 w-4" />
              </Button>
            </Link>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSignOut}
              disabled={signOutMutation.isPending}
              title="Çıkış Yap"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Search - Hidden when collapsed */}
        {!isCollapsed && (
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Projelerde ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        )}

        {/* Plan Limit Warning */}
        {!isCollapsed && limits && (
          <InlineLimitWarning
            type="project"
            current={limits.current_projects}
            max={limits.max_projects}
            onUpgrade={() => {/* Plan upgrade modal açılacak */}}
          />
        )}

        {/* New Project Button */}
        <Button
          onClick={() => setIsCreatingProject(true)}
          className={`${isCollapsed ? 'w-8 h-8 p-0' : 'w-full'}`}
          size="sm"
          title={isCollapsed ? 'Yeni Proje' : undefined}
          disabled={limits && !limits.can_create_project}
        >
          <Plus className="h-4 w-4" />
          {!isCollapsed && <span className="ml-2">Yeni Proje</span>}
        </Button>
      </div>

      {/* Projects List */}
      <ScrollArea className={`flex-1 ${isCollapsed ? 'p-2' : 'p-4'}`}>
        <div className="space-y-2">
          {/* New Project Input - Hidden when collapsed */}
          {isCreatingProject && !isCollapsed && (
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-3">
                <Input
                  placeholder="Proje adı..."
                  value={newProjectName}
                  onChange={(e) => setNewProjectName(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleCreateProject()}
                  className="mb-2"
                  autoFocus
                />
                <div className="flex gap-2">
                  <Button size="sm" onClick={handleCreateProject}>
                    Oluştur
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => {
                      setIsCreatingProject(false);
                      setNewProjectName("");
                    }}
                  >
                    İptal
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Project Items */}
          {filteredProjects.map((project) => (
            <Card
              key={project.id}
              className={`transition-all hover:shadow-md cursor-pointer ${
                activeProjectId === project.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              } ${isCollapsed ? 'p-2' : ''}`}
              onClick={isCollapsed ? () => {
                setActiveProjectId(project.id);
                onClose?.(); // Mobilde seçim sonrası sidebar'ı kapat
              } : () => {
                setActiveProjectId(project.id);
                onClose?.(); // Mobilde seçim sonrası sidebar'ı kapat
              }}
              title={isCollapsed ? project.name : undefined}
            >
              {isCollapsed ? (
                <div className="flex items-center justify-center">
                  <Folder className={`h-4 w-4 ${
                    activeProjectId === project.id ? 'text-blue-600' : 'text-gray-600'
                  }`} />
                </div>
              ) : (
                <>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <Folder className="h-4 w-4 text-blue-600 flex-shrink-0" />
                      <ProjectNameEditor
                        projectId={project.id}
                        currentName={project.name}
                        onNameUpdated={(newName) => {
                          console.log(`📝 [PROJECT_SIDEBAR] Project name updated:`, {
                            projectId: project.id,
                            oldName: project.name,
                            newName
                          })
                          // Cache otomatik olarak güncellenecek
                        }}
                        className="flex-1 min-w-0"
                      />
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-xs text-gray-500">
                      {new Date(project.created_at).toLocaleDateString('tr-TR')}
                    </p>
                  </CardContent>
                </>
              )}
            </Card>
          ))}
        </div>
      </ScrollArea>

      {/* Plan Display - Hidden when collapsed */}
      {!isCollapsed && (
        <div className="p-4 border-t border-gray-200">
          <PlanDisplay />
        </div>
      )}
    </div>
  );
}