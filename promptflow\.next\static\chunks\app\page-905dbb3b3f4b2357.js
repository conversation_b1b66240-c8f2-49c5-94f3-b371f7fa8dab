(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{10223:(e,s,r)=>{"use strict";r.d(s,{LandingPage:()=>v});var t=r(95155),l=r(12115),a=r(6874),i=r.n(a),n=r(30285),o=r(92138),c=r(54416),d=r(74783),m=r(53311),x=r(71539),h=r(75525),u=r(17580),p=r(38564),g=r(40646),b=r(33109),j=r(22100);function v(){let[e,s]=(0,l.useState)(!1),{data:r,isLoading:a}=(0,j.Jd)(),v=(0,l.useCallback)(()=>{s(!1)},[]);(0,l.useEffect)(()=>{let r=e=>{"Escape"===e.key&&s(!1)};return e?(document.addEventListener("keydown",r),document.body.style.overflow="hidden"):document.body.style.overflow="unset",()=>{document.removeEventListener("keydown",r),document.body.style.overflow="unset"}},[e]);let f={"@context":"https://schema.org","@type":"SoftwareApplication",name:"PromptFlow",alternateName:"Promptbir",description:"AI destekli prompt y\xf6netim platformu. Yapay zeka prompt&apos;larınızı organize edin, takımınızla paylaşın ve verimliliğinizi 10x artırın.",url:"https://promptbir.com",applicationCategory:"DeveloperApplication",operatingSystem:"Web Browser",browserRequirements:"Requires JavaScript. Requires HTML5.",softwareVersion:"1.0",datePublished:"2024-01-01",dateModified:new Date().toISOString(),offers:[{"@type":"Offer",name:"\xdccretsiz Plan",price:"0",priceCurrency:"TRY",description:"5 proje, 100 prompt/proje, temel \xf6zellikler",category:"Free"},{"@type":"Offer",name:"Profesyonel Plan",price:"99",priceCurrency:"TRY",description:"50 proje, 5000 prompt/proje, API erişimi, gelişmiş analitik",category:"Professional"}],aggregateRating:{"@type":"AggregateRating",ratingValue:"4.9",ratingCount:"1250",bestRating:"5",worstRating:"1"},author:{"@type":"Organization",name:"PromptFlow Team",url:"https://promptbir.com"},featureList:["AI destekli prompt y\xf6netimi","Drag & drop organizasyon","Takım \xe7alışması","Context Gallery","API erişimi","Ger\xe7ek zamanlı senkronizasyon","Enterprise g\xfcvenlik"]};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(f)}}),(0,t.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Organization",name:"PromptFlow",alternateName:"Promptbir",url:"https://promptbir.com",logo:"https://promptbir.com/logo.png",description:"AI destekli geliştirme s\xfcre\xe7lerini optimize eden minimalist prompt y\xf6netim platformu",foundingDate:"2024",contactPoint:{"@type":"ContactPoint",contactType:"customer service",availableLanguage:["Turkish","English"],url:"https://promptbir.com"},sameAs:["https://twitter.com/promptbir","https://github.com/promptbir","https://linkedin.com/company/promptbir"]})}}),(0,t.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:[{"@type":"Question",name:"PromptFlow nedir?",acceptedAnswer:{"@type":"Answer",text:"PromptFlow, AI destekli geliştirme s\xfcre\xe7lerini optimize eden minimalist prompt y\xf6netim platformudur. Prompt'larınızı organize edebilir, takımınızla paylaşabilir ve verimliliğinizi artırabilirsiniz."}},{"@type":"Question",name:"\xdccretsiz plan neler i\xe7erir?",acceptedAnswer:{"@type":"Answer",text:"\xdccretsiz plan 5 proje, proje başına 100 prompt, temel \xf6zellikler ve email destek i\xe7erir."}},{"@type":"Question",name:"Profesyonel plan neler i\xe7erir?",acceptedAnswer:{"@type":"Answer",text:"Profesyonel plan 50 proje, proje başına 5000 prompt, API erişimi, takım \xf6zellikleri, Context Gallery, gelişmiş analitik ve \xf6ncelikli destek i\xe7erir."}}]})}}),(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,t.jsxs)("nav",{className:"border-b border-gray-200 bg-white/80 backdrop-blur-sm sticky top-0 z-50",role:"navigation","aria-label":"Ana navigasyon",children:[(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)("div",{className:"flex-shrink-0 flex items-center",children:[(0,t.jsx)("img",{src:"/logo.png",alt:"Promptbir Logo",className:"h-8 w-auto mr-2"}),(0,t.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Promptbir"})]})}),(0,t.jsx)("div",{className:"hidden md:block",children:(0,t.jsxs)("div",{className:"ml-10 flex items-baseline space-x-4",children:[(0,t.jsx)(i(),{href:"#features",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"\xd6zellikler"}),(0,t.jsx)(i(),{href:"#pricing",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Fiyatlandırma"}),(0,t.jsx)(i(),{href:"/blog",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Blog"}),(0,t.jsx)(i(),{href:"/about",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Hakkımızda"}),(0,t.jsx)(i(),{href:"/contact",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"İletişim"})]})}),(0,t.jsx)("div",{className:"hidden md:flex items-center space-x-4",children:a?(0,t.jsx)("div",{className:"w-20 h-8 bg-gray-200 animate-pulse rounded"}):r?(0,t.jsx)(i(),{href:"/dashboard",children:(0,t.jsxs)(n.$,{size:"sm",className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700",children:["Dashboard",(0,t.jsx)(o.A,{className:"ml-2 h-4 w-4"})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i(),{href:"/auth",children:(0,t.jsx)(n.$,{variant:"ghost",size:"sm",children:"Giriş Yap"})}),(0,t.jsx)(i(),{href:"/auth",children:(0,t.jsxs)(n.$,{size:"sm",className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700",children:["\xdcye Ol",(0,t.jsx)(o.A,{className:"ml-2 h-4 w-4"})]})})]})}),(0,t.jsx)("div",{className:"md:hidden",children:(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>s(!e),className:"touch-target",children:e?(0,t.jsx)(c.A,{className:"h-6 w-6"}):(0,t.jsx)(d.A,{className:"h-6 w-6"})})})]})}),e&&(0,t.jsxs)("div",{className:"md:hidden border-t border-gray-200 bg-white shadow-lg",children:[(0,t.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:[(0,t.jsx)(i(),{href:"#features",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target",onClick:v,children:"\xd6zellikler"}),(0,t.jsx)(i(),{href:"#pricing",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target",onClick:v,children:"Fiyatlandırma"}),(0,t.jsx)(i(),{href:"/blog",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target",onClick:v,children:"Blog"}),(0,t.jsx)(i(),{href:"/about",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target",onClick:v,children:"Hakkımızda"}),(0,t.jsx)(i(),{href:"/contact",className:"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target",onClick:v,children:"İletişim"})]}),(0,t.jsx)("div",{className:"px-4 py-3 border-t border-gray-200 space-y-2 safe-area-bottom",children:a?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"w-full h-10 bg-gray-200 animate-pulse rounded"}),(0,t.jsx)("div",{className:"w-full h-10 bg-gray-200 animate-pulse rounded"})]}):r?(0,t.jsx)(i(),{href:"/dashboard",className:"block",onClick:v,children:(0,t.jsxs)(n.$,{size:"sm",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 justify-center mobile-btn",children:["Dashboard",(0,t.jsx)(o.A,{className:"ml-2 h-4 w-4"})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i(),{href:"/auth",className:"block",onClick:v,children:(0,t.jsx)(n.$,{variant:"ghost",size:"sm",className:"w-full justify-center mobile-btn",children:"Giriş Yap"})}),(0,t.jsx)(i(),{href:"/auth",className:"block",onClick:v,children:(0,t.jsxs)(n.$,{size:"sm",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 justify-center mobile-btn",children:["\xdcye Ol",(0,t.jsx)(o.A,{className:"ml-2 h-4 w-4"})]})})]})})]})]}),(0,t.jsxs)("section",{className:"relative overflow-hidden min-h-screen flex items-center","aria-labelledby":"hero-heading",children:[(0,t.jsxs)("div",{className:"absolute inset-0 -z-10",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50"}),(0,t.jsx)("div",{className:"absolute top-0 left-1/4 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"}),(0,t.jsx)("div",{className:"absolute top-0 right-1/4 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"}),(0,t.jsx)("div",{className:"absolute bottom-0 left-1/3 w-72 h-72 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"})]}),(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center",children:(0,t.jsxs)("div",{className:"mx-auto max-w-4xl",children:[(0,t.jsx)("div",{className:"promptbir-fade-in-up mb-8",children:(0,t.jsxs)("span",{className:"inline-flex items-center rounded-full bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:[(0,t.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"AI Destekli Prompt Y\xf6netimi"]})}),(0,t.jsxs)("h1",{id:"hero-heading",className:"promptbir-fade-in-up text-3xl sm:text-5xl lg:text-7xl font-bold tracking-tight text-gray-900 mb-6 leading-tight",children:[(0,t.jsx)("span",{className:"block",children:"AI Prompt'larınızı"}),(0,t.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent block mt-2",children:"Profesyonelce Y\xf6netin"})]}),(0,t.jsxs)("p",{className:"promptbir-fade-in-up-delay mt-6 text-lg sm:text-xl leading-7 sm:leading-8 text-gray-600 max-w-3xl mx-auto px-4 sm:px-0",children:["Yapay zeka destekli geliştirme s\xfcrecinizi hızlandırın. Prompt'larınızı organize edin, takımınızla paylaşın ve verimliliğinizi ",(0,t.jsx)("span",{className:"font-semibold text-blue-600",children:"10x artırın"}),"."]}),(0,t.jsx)("div",{className:"promptbir-fade-in-up-delay mt-10 flex flex-col sm:flex-row items-center justify-center gap-4 px-4 sm:px-0",children:a?(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 w-full sm:w-auto",children:[(0,t.jsx)("div",{className:"w-full sm:w-48 h-14 bg-gray-200 animate-pulse rounded-xl"}),(0,t.jsx)("div",{className:"w-full sm:w-40 h-14 bg-gray-200 animate-pulse rounded-xl"})]}):r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i(),{href:"/dashboard",className:"w-full sm:w-auto",children:(0,t.jsxs)(n.$,{size:"lg",className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-xl mobile-btn",children:[(0,t.jsx)(x.A,{className:"mr-2 h-5 w-5"}),"Dashboard'a Git",(0,t.jsx)(o.A,{className:"ml-2 h-5 w-5"})]})}),(0,t.jsxs)(n.$,{variant:"outline",size:"lg",className:"w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-xl border-2 hover:bg-gray-50 mobile-btn",children:[(0,t.jsx)("span",{className:"mr-2",children:"\uD83C\uDFA5"}),"Demo İzle"]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i(),{href:"/auth",className:"w-full sm:w-auto",children:(0,t.jsxs)(n.$,{size:"lg",className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-xl mobile-btn",children:[(0,t.jsx)(x.A,{className:"mr-2 h-5 w-5"}),"\xdccretsiz Başla",(0,t.jsx)(o.A,{className:"ml-2 h-5 w-5"})]})}),(0,t.jsxs)(n.$,{variant:"outline",size:"lg",className:"w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-xl border-2 hover:bg-gray-50 mobile-btn",children:[(0,t.jsx)("span",{className:"mr-2",children:"\uD83C\uDFA5"}),"Demo İzle"]})]})}),(0,t.jsxs)("div",{className:"promptbir-fade-in-up-delay mt-16 grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 max-w-2xl mx-auto px-4 sm:px-0",children:[(0,t.jsxs)("div",{className:"text-center p-4 sm:p-0",children:[(0,t.jsx)("div",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"10,000+"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Aktif Kullanıcı"})]}),(0,t.jsxs)("div",{className:"text-center p-4 sm:p-0",children:[(0,t.jsx)("div",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"1M+"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Y\xf6netilen Prompt"})]}),(0,t.jsxs)("div",{className:"text-center p-4 sm:p-0",children:[(0,t.jsx)("div",{className:"text-2xl sm:text-3xl font-bold text-gray-900",children:"99.9%"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Uptime"})]})]})]})}),(0,t.jsx)("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce",children:(0,t.jsx)("div",{className:"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center",children:(0,t.jsx)("div",{className:"w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse"})})})]}),(0,t.jsx)("section",{id:"features",className:"py-24 bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-20",children:[(0,t.jsxs)("h2",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-4",children:["G\xfc\xe7l\xfc ",(0,t.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"\xd6zellikler"})]}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"AI destekli geliştirme s\xfcrecinizi optimize eden modern ara\xe7lar"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3",children:[(0,t.jsxs)("div",{className:"group relative",children:[(0,t.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"}),(0,t.jsxs)("div",{className:"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,t.jsx)(x.A,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Hızlı Erişim"}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Prompt'larınıza tek tıkla erişin. Drag & drop ile organize edin. \xc7oklu se\xe7im ile toplu işlemler yapın."})]})]}),(0,t.jsxs)("div",{className:"group relative",children:[(0,t.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"}),(0,t.jsxs)("div",{className:"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,t.jsx)(h.A,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"G\xfcvenli Saklama"}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Enterprise d\xfczeyinde g\xfcvenlik. Row Level Security ile verileriniz tamamen korunur ve sadece size aittir."})]})]}),(0,t.jsxs)("div",{className:"group relative",children:[(0,t.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"}),(0,t.jsxs)("div",{className:"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,t.jsx)(u.A,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Takım \xc7alışması"}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Takımınızla prompt'ları paylaşın. Context galeri ile ortak şablonlar oluşturun ve verimliliği artırın."})]})]}),(0,t.jsxs)("div",{className:"group relative",children:[(0,t.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"}),(0,t.jsxs)("div",{className:"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,t.jsx)(m.A,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"AI Optimizasyonu"}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Prompt'larınızı AI ile optimize edin. Akıllı kategorileme ve otomatik etiketleme ile d\xfczen sağlayın."})]})]}),(0,t.jsxs)("div",{className:"group relative",children:[(0,t.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"}),(0,t.jsxs)("div",{className:"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,t.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Ger\xe7ek Zamanlı"}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"T\xfcm değişiklikler anında senkronize olur. Takım \xfcyeleri aynı anda \xe7alışabilir, \xe7akışma olmaz."})]})]}),(0,t.jsxs)("div",{className:"group relative",children:[(0,t.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200"}),(0,t.jsxs)("div",{className:"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,t.jsx)("span",{className:"text-white font-bold text-lg",children:"∞"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Sınırsız Kullanım"}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Sınırsız proje, prompt ve kullanıcı. B\xfcy\xfcd\xfck\xe7e \xf6l\xe7eklenen altyapı ile hi\xe7 endişelenmeyin."})]})]})]})]})}),(0,t.jsx)("section",{id:"testimonials",className:"py-24 bg-white",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-20",children:[(0,t.jsxs)("h2",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-4",children:["Kullanıcılarımız ",(0,t.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Ne Diyor?"})]}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Binlerce geliştirici Promptbir ile verimliliğini artırdı"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-all duration-300",children:[(0,t.jsx)("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsx)(p.A,{className:"w-5 h-5 text-yellow-400 fill-current"},s))}),(0,t.jsx)("p",{className:"text-gray-700 mb-6 leading-relaxed",children:'"Promptbir sayesinde prompt y\xf6netimi \xe7ok kolay oldu. Takımımızla paylaştığımız şablonlar sayesinde geliştirme hızımız 10x arttı."'}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4",children:(0,t.jsx)("span",{className:"text-blue-600 font-semibold",children:"AK"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-gray-900",children:"Ahmet Ocak"}),(0,t.jsx)("div",{className:"text-gray-600 text-sm",children:"Senior Developer, TechCorp"})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-all duration-300",children:[(0,t.jsx)("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsx)(p.A,{className:"w-5 h-5 text-yellow-400 fill-current"},s))}),(0,t.jsx)("p",{className:"text-gray-700 mb-6 leading-relaxed",children:'"AI projelerinde prompt versiyonlama \xe7ok kritikti. Promptbir ile hem organize hem de g\xfcvenli bir şekilde y\xf6netebiliyoruz."'}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4",children:(0,t.jsx)("span",{className:"text-purple-600 font-semibold",children:"EY"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-gray-900",children:"Elif Yılmaz"}),(0,t.jsx)("div",{className:"text-gray-600 text-sm",children:"AI Engineer, StartupX"})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-all duration-300",children:[(0,t.jsx)("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsx)(p.A,{className:"w-5 h-5 text-yellow-400 fill-current"},s))}),(0,t.jsx)("p",{className:"text-gray-700 mb-6 leading-relaxed",children:'"Drag & drop \xf6zelliği harika! Prompt\'ları organize etmek hi\xe7 bu kadar kolay olmamıştı. Kesinlikle tavsiye ederim."'}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4",children:(0,t.jsx)("span",{className:"text-green-600 font-semibold",children:"M\xd6"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-gray-900",children:"Mehmet \xd6zkan"}),(0,t.jsx)("div",{className:"text-gray-600 text-sm",children:"Product Manager, InnovateLab"})]})]})]})]})]})}),(0,t.jsx)("section",{id:"pricing",className:"py-24 bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-20",children:[(0,t.jsxs)("h2",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-4",children:["Basit ve ",(0,t.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Şeffaf Fiyatlandırma"})]}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"İhtiyacınıza g\xf6re se\xe7in, istediğiniz zaman değiştirin"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto",children:[(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-lg p-8 border-2 border-gray-200 hover:border-blue-300 transition-all duration-300",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Başlangı\xe7"}),(0,t.jsx)("div",{className:"text-4xl font-bold text-gray-900 mb-1",children:"\xdccretsiz"}),(0,t.jsx)("p",{className:"text-gray-600 mb-8",children:"Bireysel kullanım i\xe7in"}),(0,t.jsxs)("ul",{className:"space-y-4 mb-8 text-left",children:[(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"5 proje"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"100 prompt/proje"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"Sınırlı Context Gallery"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"Email destek"})]})]}),(0,t.jsx)(i(),{href:"/dashboard",children:(0,t.jsx)(n.$,{className:"w-full",variant:"outline",children:"Hemen Başla"})})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 border-2 border-blue-500 relative transform scale-105 hover:scale-110 transition-all duration-300",children:[(0,t.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,t.jsx)("span",{className:"bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold",children:"En Pop\xfcler"})}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Profesyonel"}),(0,t.jsx)("div",{className:"text-4xl font-bold text-gray-900 mb-1",children:"₺99"}),(0,t.jsx)("p",{className:"text-gray-600 mb-2",children:"aylık"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mb-6",children:"veya ₺990/yıl (%17 indirim)"}),(0,t.jsxs)("ul",{className:"space-y-4 mb-8 text-left",children:[(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"50 proje"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"5.000 prompt/proje"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"API erişimi"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"Takım \xf6zellikleri"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"Context Gallery"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"Gelişmiş analitik"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"\xd6ncelikli destek"})]})]}),(0,t.jsx)(i(),{href:"/dashboard",children:(0,t.jsx)(n.$,{className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700",children:"Pro'ya Ge\xe7"})})]})]}),(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-lg p-8 border-2 border-gray-200 hover:border-purple-300 transition-all duration-300",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Kurumsal"}),(0,t.jsx)("div",{className:"text-4xl font-bold text-gray-900 mb-1",children:"\xd6zel"}),(0,t.jsx)("p",{className:"text-gray-600 mb-8",children:"B\xfcy\xfck takımlar i\xe7in"}),(0,t.jsxs)("ul",{className:"space-y-4 mb-8 text-left",children:[(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"T\xfcm Pro \xf6zellikler"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"SSO entegrasyonu"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"\xd6zel deployment"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("span",{children:"24/7 destek"})]})]}),(0,t.jsx)(n.$,{className:"w-full",variant:"outline",children:"İletişime Ge\xe7"})]})})]}),(0,t.jsx)("div",{className:"mt-16 text-center",children:(0,t.jsx)("p",{className:"text-gray-600",children:"T\xfcm planlar 14 g\xfcn \xfccretsiz deneme i\xe7erir. İstediğiniz zaman iptal edebilirsiniz."})})]})}),(0,t.jsxs)("section",{className:"relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600"}),(0,t.jsx)("div",{className:"relative max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:py-24 lg:px-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("h2",{className:"text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl",children:[(0,t.jsx)("span",{className:"block",children:"Hazır mısınız?"}),(0,t.jsx)("span",{className:"block mt-2 text-blue-100",children:"Hemen başlayın."})]}),(0,t.jsx)("p",{className:"mt-6 text-xl text-blue-100 max-w-2xl mx-auto",children:"Binlerce geliştirici gibi siz de AI prompt'larınızı profesyonelce y\xf6netin"}),(0,t.jsxs)("div",{className:"mt-10 flex flex-col sm:flex-row items-center justify-center gap-4",children:[(0,t.jsx)(i(),{href:"/dashboard",children:(0,t.jsxs)(n.$,{size:"lg",variant:"secondary",className:"bg-white text-blue-600 hover:bg-gray-50 px-8 py-4 text-lg font-semibold rounded-xl",children:[(0,t.jsx)(g.A,{className:"mr-2 h-5 w-5"}),"\xdccretsiz Başla",(0,t.jsx)(o.A,{className:"ml-2 h-5 w-5"})]})}),(0,t.jsxs)("div",{className:"flex items-center text-blue-100",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 mr-2"}),(0,t.jsx)("span",{children:"Kredi kartı gerektirmez"})]})]}),(0,t.jsxs)("div",{className:"mt-12 flex flex-col sm:flex-row items-center justify-center gap-8 text-blue-100",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"w-5 h-5 mr-2"}),(0,t.jsx)("span",{children:"10,000+ Aktif Kullanıcı"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"w-5 h-5 mr-2"}),(0,t.jsx)("span",{children:"Enterprise G\xfcvenlik"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"w-5 h-5 mr-2"}),(0,t.jsx)("span",{children:"99.9% Uptime"})]})]})]})})]}),(0,t.jsx)("footer",{className:"bg-gray-900",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(m.A,{className:"h-8 w-8 text-blue-400"}),(0,t.jsx)("span",{className:"ml-2 text-xl font-bold text-white",children:"Promptbir"})]}),(0,t.jsx)("p",{className:"text-gray-400 mb-6 max-w-md",children:"AI prompt'larınızı profesyonelce y\xf6netin. Takımınızla paylaşın, organize edin ve verimliliğinizi artırın."}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:[(0,t.jsx)("span",{className:"sr-only",children:"Twitter"}),(0,t.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"})})]}),(0,t.jsxs)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:[(0,t.jsx)("span",{className:"sr-only",children:"GitHub"}),(0,t.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z",clipRule:"evenodd"})})]}),(0,t.jsxs)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:[(0,t.jsx)("span",{className:"sr-only",children:"LinkedIn"}),(0,t.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z",clipRule:"evenodd"})})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-semibold mb-4",children:"\xdcr\xfcn"}),(0,t.jsxs)("ul",{className:"space-y-3",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#features",className:"text-gray-400 hover:text-white transition-colors",children:"\xd6zellikler"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#pricing",className:"text-gray-400 hover:text-white transition-colors",children:"Fiyatlandırma"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/dashboard",className:"text-gray-400 hover:text-white transition-colors",children:"Demo"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"API"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Entegrasyonlar"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-semibold mb-4",children:"Şirket"}),(0,t.jsxs)("ul",{className:"space-y-3",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/about",className:"text-gray-400 hover:text-white transition-colors",children:"Hakkımızda"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/blog",className:"text-gray-400 hover:text-white transition-colors",children:"Blog"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/careers",className:"text-gray-400 hover:text-white transition-colors",children:"Kariyer"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/contact",className:"text-gray-400 hover:text-white transition-colors",children:"İletişim"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/bug-report",className:"text-gray-400 hover:text-white transition-colors",children:"Bug Report"})})]})]})]}),(0,t.jsx)("div",{className:"mt-12 pt-8 border-t border-gray-800",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2024 Promptbir. T\xfcm hakları saklıdır."}),(0,t.jsxs)("div",{className:"mt-4 md:mt-0 flex space-x-6",children:[(0,t.jsx)("a",{href:"/privacy",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Gizlilik Politikası"}),(0,t.jsx)("a",{href:"/terms",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Kullanım Şartları"}),(0,t.jsx)("a",{href:"/cookies",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"\xc7erez Politikası"})]})]})})]})})]})]})}},22100:(e,s,r)=>{"use strict";r.d(s,{Jd:()=>x,Rt:()=>p,a7:()=>h,rU:()=>m,wF:()=>u});var t=r(26715),l=r(32960),a=r(5041),i=r(70478),n=r(12115),o=r(35695),c=r(56671);function d(e,s){let r=e instanceof Error?e.message:String(e);return!!(r.includes("Invalid Refresh Token")||r.includes("Refresh Token Not Found")||r.includes("refresh_token_not_found"))&&(console.warn("Refresh token hatası, oturum temizleniyor:",r),i.L.auth.signOut({scope:"local"}),s.clear(),!0)}function m(){let e=(0,t.jE)(),s=(0,o.useRouter)();(0,n.useEffect)(()=>{console.log("\uD83C\uDFA7 [AUTH_LISTENER] Setting up auth state listener");let{data:{subscription:r}}=i.L.auth.onAuthStateChange((r,t)=>{var l,a;console.log("\uD83D\uDD04 [AUTH_LISTENER] Auth state change: ".concat(r),{userId:null==t||null==(l=t.user)?void 0:l.id,email:null==t||null==(a=t.user)?void 0:a.email,hasSession:!!t}),("SIGNED_OUT"===r||"TOKEN_REFRESHED"===r)&&(console.log("\uD83D\uDD04 [AUTH_LISTENER] Invalidating queries for event: ".concat(r)),e.invalidateQueries({queryKey:["user"]}),e.invalidateQueries({queryKey:["session"]})),"SIGNED_OUT"===r&&(console.log("\uD83D\uDEAA [AUTH_LISTENER] User signed out - clearing cache and redirecting"),e.clear(),localStorage.removeItem("promptflow-app-store"),setTimeout(()=>{console.log("\uD83D\uDEAA [AUTH_LISTENER] Redirecting to /auth"),s.push("/auth"),s.refresh()},100)),"SIGNED_IN"===r&&(console.log("\uD83D\uDD11 [AUTH_LISTENER] User signed in - refreshing queries and redirecting"),e.invalidateQueries({queryKey:["user"]}),e.invalidateQueries({queryKey:["session"]}),setTimeout(()=>{let e=window.location.pathname;("/auth"===e||"/"===e)&&(console.log("\uD83D\uDE80 [AUTH_LISTENER] Redirecting from ".concat(e," to /dashboard")),s.push("/dashboard"))},100))});return()=>{console.log("\uD83C\uDFA7 [AUTH_LISTENER] Cleaning up auth state listener"),r.unsubscribe()}},[e,s])}function x(){let e=(0,t.jE)();return(0,l.I)({queryKey:["user"],queryFn:async()=>{console.log("\uD83D\uDD10 [USE_USER] Getting user...");try{let{data:{session:s},error:r}=await i.L.auth.getSession();if(r)return console.error("❌ [USE_USER] Session error:",r),null;if(!s)return console.log("\uD83D\uDD10 [USE_USER] No session found"),null;console.log("\uD83D\uDD10 [USE_USER] Session found, getting user...");let{data:{user:t},error:l}=await i.L.auth.getUser();if(l){if(console.error("❌ [USE_USER] Error getting user:",l),l.message.includes("Auth session missing"))return console.log("\uD83D\uDD04 [USE_USER] Auth session missing, returning null"),null;if(d(l,e))return console.log("\uD83D\uDD04 [USE_USER] Handled auth error, returning null"),null;throw Error(l.message)}return console.log("✅ [USE_USER] User retrieved:",(null==t?void 0:t.email)||"null"),t}catch(s){if(console.error("\uD83D\uDCA5 [USE_USER] Exception:",s),(s instanceof Error?s.message:String(s)).includes("Auth session missing"))return console.log("\uD83D\uDD04 [USE_USER] Auth session missing exception, returning null"),null;if(d(s,e))return console.log("\uD83D\uDD04 [USE_USER] Handled auth error exception, returning null"),null;throw s}},staleTime:3e5,retry:(e,s)=>{let r=s instanceof Error?s.message:String(s);return(console.log("\uD83D\uDD04 [USE_USER] Retry attempt ".concat(e,", error: ").concat(r)),r.includes("Invalid Refresh Token")||r.includes("Refresh Token Not Found")||r.includes("Auth session missing"))?(console.log("\uD83D\uDEAB [USE_USER] Not retrying auth error: ".concat(r)),!1):e<3}})}function h(){let e=(0,t.jE)();return(0,a.n)({mutationFn:async e=>{var s,r;let{email:t,password:l}=e;console.log("\uD83D\uDD11 [SIGN_IN] Attempting login for: ".concat(t));let{data:a,error:n}=await i.L.auth.signInWithPassword({email:t,password:l});if(n)throw console.error("❌ [SIGN_IN] Login failed:",n),Error(n.message);return console.log("✅ [SIGN_IN] Login successful:",{userId:null==(s=a.user)?void 0:s.id,email:null==(r=a.user)?void 0:r.email,hasSession:!!a.session}),a},onSuccess:()=>{console.log("\uD83C\uDF89 [SIGN_IN] onSuccess triggered, invalidating queries"),e.invalidateQueries({queryKey:["user"]}),e.invalidateQueries({queryKey:["session"]})},onError:e=>{console.error("\uD83D\uDCA5 [SIGN_IN] onError triggered:",e)}})}function u(){let e=(0,t.jE)();return(0,a.n)({mutationFn:async e=>{let{email:s,password:r}=e,{data:t,error:l}=await i.L.auth.signUp({email:s,password:r});if(l)throw Error(l.message);return t},onSuccess:()=>{e.invalidateQueries({queryKey:["user"]}),e.invalidateQueries({queryKey:["session"]})}})}function p(){let e=(0,t.jE)(),s=(0,o.useRouter)();return(0,a.n)({mutationFn:async()=>{console.log("Starting logout process...");let{error:e}=await i.L.auth.signOut({scope:"global"});if(e)throw console.error("Logout error:",e),Error(e.message);console.log("Logout successful")},onSuccess:()=>{console.log("Logout onSuccess triggered"),c.oR.success("Başarıyla \xe7ıkış yapıldı",{description:"Giriş sayfasına y\xf6nlendiriliyorsunuz..."}),e.clear(),localStorage.removeItem("promptflow-app-store"),sessionStorage.clear(),setTimeout(()=>{console.log("Redirecting to auth page..."),s.push("/auth"),s.refresh(),window.location.href="/auth"},1e3)},onError:r=>{console.error("Logout failed:",r),c.oR.error("\xc7ıkış yapılırken hata oluştu",{description:"Yine de oturum temizleniyor..."}),e.clear(),localStorage.removeItem("promptflow-app-store"),sessionStorage.clear(),setTimeout(()=>{s.push("/auth"),s.refresh()},1e3)}})}},30285:(e,s,r)=>{"use strict";r.d(s,{$:()=>o});var t=r(95155);r(12115);var l=r(99708),a=r(74466),i=r(59434);let n=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:s,variant:r,size:a,asChild:o=!1,...c}=e,d=o?l.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,i.cn)(n({variant:r,size:a,className:s})),...c})}},32849:(e,s,r)=>{Promise.resolve().then(r.bind(r,10223))},59434:(e,s,r)=>{"use strict";r.d(s,{cn:()=>a});var t=r(52596),l=r(39688);function a(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,l.QP)((0,t.$)(s))}},70478:(e,s,r)=>{"use strict";r.d(s,{L:()=>t});let t=(0,r(98616).createBrowserClient)("https://iqehopwgrczylqliajww.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlxZWhvcHdncmN6eWxxbGlhand3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2OTQyNzksImV4cCI6MjA2ODI3MDI3OX0.9zrZ8Zot6SvsjxTGMyh3IYFqrr_QXQkKSNU4rJNz0VM",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0,flowType:"pkce",debug:!1,storageKey:"sb-iqehopwgrczylqliajww-auth-token",storage:window.localStorage},db:{schema:"public"},realtime:{params:{eventsPerSecond:10}},global:{headers:{"X-Client-Info":"promptflow-web","X-Client-Version":"1.0.0"}}});t.auth.onAuthStateChange((e,s)=>{var r,t;if(console.log("\uD83D\uDD10 [SUPABASE_BROWSER] Auth state change: ".concat(e),{hasSession:!!s,userId:null==s||null==(r=s.user)?void 0:r.id,email:null==s||null==(t=s.user)?void 0:t.email,expiresAt:(null==s?void 0:s.expires_at)?new Date(1e3*s.expires_at).toISOString():null}),s){let e=Math.round((1e3*s.expires_at-Date.now())/1e3);document.cookie="sb-iqehopwgrczylqliajww-auth-token=".concat(JSON.stringify(s),"; path=/; max-age=").concat(e,"; SameSite=Lax; secure=").concat("https:"===location.protocol),console.log("\uD83C\uDF6A [SUPABASE_BROWSER] Set auth cookie with maxAge: ".concat(e,"s"))}else document.cookie="sb-iqehopwgrczylqliajww-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",console.log("\uD83C\uDF6A [SUPABASE_BROWSER] Cleared auth cookie")})}},e=>{e.O(0,[2123,8611,1024,8779,8650,8523,8405,9420,4939,1486,2662,3240,8669,3189,4696,1277,7979,1899,7098,4450,9744,4495,9592,433,2652,5030,465,2903,2663,9173,408,558,1356,6475,2130,4207,4191,6489,5230,7339,957,5677,6691,1151,7114,5803,3976,3492,2608,5644,2789,9824,4075,9473,7530,6759,0,6325,6077,4409,9184,7650,1595,4532,1911,7358],()=>e(e.s=32849)),_N_E=e.O()}]);