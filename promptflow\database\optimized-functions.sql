-- Optimized Database Functions for PromptFlow
-- These functions improve query performance and reduce client-side processing

-- 1. Get project hashtags with counts (optimized aggregation)
CREATE OR REPLACE FUNCTION get_project_hashtags(project_id UUID)
RETURNS TABLE (
  hashtag TEXT,
  count BIGINT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    unnest(hashtags) as hashtag,
    COUNT(*) as count
  FROM prompts 
  WHERE prompts.project_id = get_project_hashtags.project_id
    AND hashtags IS NOT NULL
    AND array_length(hashtags, 1) > 0
  GROUP BY unnest(hashtags)
  ORDER BY count DESC, hashtag ASC
  LIMIT 50;
END;
$$;

-- 2. Search prompts with full-text search and ranking
CREATE OR REPLACE FUNCTION search_prompts_ranked(
  project_id UUID,
  search_query TEXT,
  limit_count INTEGER DEFAULT 50
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  content TEXT,
  category TEXT,
  hashtags TEXT[],
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  rank REAL
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.title,
    p.content,
    p.category,
    p.hashtags,
    p.created_at,
    p.updated_at,
    (
      ts_rank(
        to_tsvector('english', COALESCE(p.title, '') || ' ' || COALESCE(p.content, '')),
        plainto_tsquery('english', search_query)
      ) +
      CASE 
        WHEN p.title ILIKE '%' || search_query || '%' THEN 0.5
        ELSE 0
      END +
      CASE 
        WHEN search_query = ANY(p.hashtags) THEN 1.0
        ELSE 0
      END
    ) as rank
  FROM prompts p
  WHERE p.project_id = search_prompts_ranked.project_id
    AND (
      to_tsvector('english', COALESCE(p.title, '') || ' ' || COALESCE(p.content, '')) 
      @@ plainto_tsquery('english', search_query)
      OR p.title ILIKE '%' || search_query || '%'
      OR p.content ILIKE '%' || search_query || '%'
      OR search_query = ANY(p.hashtags)
    )
  ORDER BY rank DESC, p.updated_at DESC
  LIMIT limit_count;
END;
$$;

-- 3. Get project statistics (optimized aggregation)
CREATE OR REPLACE FUNCTION get_project_stats(project_id UUID)
RETURNS TABLE (
  total_prompts BIGINT,
  total_categories BIGINT,
  total_hashtags BIGINT,
  last_updated TIMESTAMPTZ,
  avg_content_length NUMERIC
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_prompts,
    COUNT(DISTINCT category) FILTER (WHERE category IS NOT NULL) as total_categories,
    COUNT(DISTINCT unnest(hashtags)) FILTER (WHERE hashtags IS NOT NULL) as total_hashtags,
    MAX(updated_at) as last_updated,
    AVG(LENGTH(content)) as avg_content_length
  FROM prompts 
  WHERE prompts.project_id = get_project_stats.project_id;
END;
$$;

-- 4. Batch update prompts (optimized bulk operations)
CREATE OR REPLACE FUNCTION batch_update_prompts(
  prompt_updates JSONB
)
RETURNS TABLE (
  id UUID,
  updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  update_record JSONB;
BEGIN
  FOR update_record IN SELECT * FROM jsonb_array_elements(prompt_updates)
  LOOP
    UPDATE prompts 
    SET 
      title = COALESCE((update_record->>'title')::TEXT, title),
      content = COALESCE((update_record->>'content')::TEXT, content),
      category = COALESCE((update_record->>'category')::TEXT, category),
      hashtags = COALESCE(
        ARRAY(SELECT jsonb_array_elements_text(update_record->'hashtags')), 
        hashtags
      ),
      updated_at = NOW()
    WHERE prompts.id = (update_record->>'id')::UUID;
  END LOOP;
  
  RETURN QUERY
  SELECT p.id, p.updated_at
  FROM prompts p
  WHERE p.id = ANY(
    ARRAY(
      SELECT (jsonb_array_elements(prompt_updates)->>'id')::UUID
    )
  );
END;
$$;

-- 5. Get popular context templates (with usage tracking)
CREATE OR REPLACE FUNCTION get_popular_contexts(
  category_filter TEXT DEFAULT NULL,
  limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  content TEXT,
  category TEXT,
  tags TEXT[],
  usage_count INTEGER,
  created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ct.id,
    ct.title,
    ct.content,
    ct.category,
    ct.tags,
    ct.usage_count,
    ct.created_at
  FROM context_templates ct
  WHERE (category_filter IS NULL OR ct.category = category_filter)
  ORDER BY ct.usage_count DESC, ct.created_at DESC
  LIMIT limit_count;
END;
$$;

-- 6. Increment context usage (atomic operation)
CREATE OR REPLACE FUNCTION increment_context_usage(context_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE context_templates 
  SET usage_count = usage_count + 1,
      updated_at = NOW()
  WHERE id = context_id;
END;
$$;

-- 7. Get user plan limits with current usage
CREATE OR REPLACE FUNCTION get_user_limits_with_usage(user_id UUID)
RETURNS TABLE (
  plan_name TEXT,
  max_projects INTEGER,
  max_prompts_per_project INTEGER,
  max_contexts INTEGER,
  current_projects BIGINT,
  current_prompts BIGINT,
  current_contexts BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pt.name as plan_name,
    pt.max_projects,
    pt.max_prompts_per_project,
    pt.max_contexts,
    (SELECT COUNT(*) FROM projects WHERE user_id = get_user_limits_with_usage.user_id) as current_projects,
    (SELECT COUNT(*) FROM prompts p 
     JOIN projects pr ON p.project_id = pr.id 
     WHERE pr.user_id = get_user_limits_with_usage.user_id) as current_prompts,
    0::BIGINT as current_contexts -- Placeholder for context usage
  FROM user_plans up
  JOIN plan_types pt ON up.plan_type_id = pt.id
  WHERE up.user_id = get_user_limits_with_usage.user_id
    AND up.is_active = true
  LIMIT 1;
END;
$$;

-- 8. Cleanup old data (maintenance function)
CREATE OR REPLACE FUNCTION cleanup_old_data(days_old INTEGER DEFAULT 90)
RETURNS TABLE (
  deleted_prompts BIGINT,
  deleted_projects BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_prompt_count BIGINT;
  deleted_project_count BIGINT;
BEGIN
  -- Delete old prompts from inactive projects
  WITH deleted_prompts AS (
    DELETE FROM prompts 
    WHERE project_id IN (
      SELECT id FROM projects 
      WHERE updated_at < NOW() - INTERVAL '1 day' * days_old
        AND (SELECT COUNT(*) FROM prompts WHERE project_id = projects.id) = 0
    )
    RETURNING id
  )
  SELECT COUNT(*) INTO deleted_prompt_count FROM deleted_prompts;
  
  -- Delete empty old projects
  WITH deleted_projects AS (
    DELETE FROM projects 
    WHERE updated_at < NOW() - INTERVAL '1 day' * days_old
      AND (SELECT COUNT(*) FROM prompts WHERE project_id = projects.id) = 0
    RETURNING id
  )
  SELECT COUNT(*) INTO deleted_project_count FROM deleted_projects;
  
  RETURN QUERY SELECT deleted_prompt_count, deleted_project_count;
END;
$$;

-- Create indexes for better performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_prompts_project_updated 
ON prompts(project_id, updated_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_prompts_search_text 
ON prompts USING gin(to_tsvector('english', title || ' ' || content));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_prompts_hashtags 
ON prompts USING gin(hashtags);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_prompts_category 
ON prompts(category) WHERE category IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_context_templates_category_usage 
ON context_templates(category, usage_count DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_user_updated 
ON projects(user_id, updated_at DESC);

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_project_hashtags(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION search_prompts_ranked(UUID, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_project_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION batch_update_prompts(JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION get_popular_contexts(TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION increment_context_usage(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_limits_with_usage(UUID) TO authenticated;

-- Only admins can run cleanup
GRANT EXECUTE ON FUNCTION cleanup_old_data(INTEGER) TO service_role;
