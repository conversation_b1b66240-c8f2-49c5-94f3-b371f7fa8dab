// Service Worker for PromptFlow
// Provides offline caching and background sync

const CACHE_NAME = 'promptflow-v1'
const STATIC_CACHE = 'promptflow-static-v1'
const DYNAMIC_CACHE = 'promptflow-dynamic-v1'

// Files to cache immediately
const STATIC_FILES = [
  '/',
  '/dashboard',
  '/auth',
  '/profile',
  '/manifest.json',
  '/favicon.ico',
  // Add critical CSS and JS files
]

// API endpoints to cache
const CACHEABLE_APIS = [
  '/api/projects',
  '/api/prompts',
  '/api/contexts',
  '/api/user/profile',
  '/api/user/plan',
]

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Caching static files')
        return cache.addAll(STATIC_FILES)
      })
      .then(() => {
        return self.skipWaiting()
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...')
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== CACHE_NAME) {
              console.log('Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        return self.clients.claim()
      })
  )
})

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return
  }
  
  // Handle different types of requests
  if (url.pathname.startsWith('/api/')) {
    // API requests - Network First strategy
    event.respondWith(networkFirstStrategy(request))
  } else if (url.pathname.startsWith('/_next/static/')) {
    // Static assets - Cache First strategy
    event.respondWith(cacheFirstStrategy(request))
  } else {
    // Pages - Stale While Revalidate strategy
    event.respondWith(staleWhileRevalidateStrategy(request))
  }
})

// Network First Strategy (for API calls)
async function networkFirstStrategy(request) {
  const cacheName = DYNAMIC_CACHE
  
  try {
    // Try network first
    const networkResponse = await fetch(request)
    
    // If successful, update cache
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    // Network failed, try cache
    console.log('Network failed, trying cache for:', request.url)
    const cachedResponse = await caches.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
      return caches.match('/offline.html') || new Response('Offline', { status: 503 })
    }
    
    throw error
  }
}

// Cache First Strategy (for static assets)
async function cacheFirstStrategy(request) {
  const cacheName = STATIC_CACHE
  
  // Try cache first
  const cachedResponse = await caches.match(request)
  if (cachedResponse) {
    return cachedResponse
  }
  
  // Cache miss, fetch from network
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    console.error('Cache first strategy failed:', error)
    throw error
  }
}

// Stale While Revalidate Strategy (for pages)
async function staleWhileRevalidateStrategy(request) {
  const cacheName = DYNAMIC_CACHE
  
  // Get from cache immediately
  const cachedResponse = await caches.match(request)
  
  // Fetch from network in background
  const networkResponsePromise = fetch(request)
    .then((networkResponse) => {
      if (networkResponse.ok) {
        const cache = caches.open(cacheName)
        cache.then(c => c.put(request, networkResponse.clone()))
      }
      return networkResponse
    })
    .catch((error) => {
      console.log('Background fetch failed:', error)
      return null
    })
  
  // Return cached version immediately, or wait for network
  return cachedResponse || networkResponsePromise
}

// Background Sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Background sync triggered:', event.tag)
  
  if (event.tag === 'background-sync-prompts') {
    event.waitUntil(syncPrompts())
  }
  
  if (event.tag === 'background-sync-projects') {
    event.waitUntil(syncProjects())
  }
})

// Sync functions
async function syncPrompts() {
  try {
    // Get pending prompt actions from IndexedDB
    const pendingActions = await getPendingActions('prompts')
    
    for (const action of pendingActions) {
      try {
        await fetch(action.url, {
          method: action.method,
          headers: action.headers,
          body: action.body
        })
        
        // Remove from pending actions
        await removePendingAction('prompts', action.id)
      } catch (error) {
        console.error('Failed to sync prompt action:', error)
      }
    }
  } catch (error) {
    console.error('Background sync failed:', error)
  }
}

async function syncProjects() {
  try {
    const pendingActions = await getPendingActions('projects')
    
    for (const action of pendingActions) {
      try {
        await fetch(action.url, {
          method: action.method,
          headers: action.headers,
          body: action.body
        })
        
        await removePendingAction('projects', action.id)
      } catch (error) {
        console.error('Failed to sync project action:', error)
      }
    }
  } catch (error) {
    console.error('Background sync failed:', error)
  }
}

// IndexedDB helpers for offline storage
async function getPendingActions(type) {
  // This would integrate with IndexedDB
  // For now, return empty array
  return []
}

async function removePendingAction(type, id) {
  // This would remove from IndexedDB
  console.log('Removing pending action:', type, id)
}

// Push notification handling
self.addEventListener('push', (event) => {
  console.log('Push notification received:', event)
  
  const options = {
    body: event.data ? event.data.text() : 'New update available',
    icon: '/icon-192x192.png',
    badge: '/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Open App',
        icon: '/icon-192x192.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/icon-192x192.png'
      }
    ]
  }
  
  event.waitUntil(
    self.registration.showNotification('PromptFlow', options)
  )
})

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event)
  
  event.notification.close()
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/dashboard')
    )
  }
})

// Message handling from main thread
self.addEventListener('message', (event) => {
  console.log('Service Worker received message:', event.data)
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
  
  if (event.data && event.data.type === 'CACHE_URLS') {
    event.waitUntil(
      caches.open(DYNAMIC_CACHE)
        .then(cache => cache.addAll(event.data.urls))
    )
  }
})
