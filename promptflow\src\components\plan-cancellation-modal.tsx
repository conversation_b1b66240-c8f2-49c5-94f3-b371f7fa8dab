'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'
import { useCancelPlan, useCancellationEligibility, useUserTrialInfo } from '@/hooks/use-plans'
import { toast } from 'sonner'

interface PlanCancellationModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

export function PlanCancellationModal({ isOpen, onClose, onSuccess }: PlanCancellationModalProps) {
  const [cancellationReason, setCancellationReason] = useState('')
  const [requestRefund, setRequestRefund] = useState(false)
  const [confirmCancellation, setConfirmCancellation] = useState(false)

  const { data: eligibility, isLoading: eligibilityLoading } = useCancellationEligibility()
  const { data: trialInfo, isLoading: trialLoading } = useUserTrialInfo()
  const cancelPlanMutation = useCancelPlan()

  const isLoading = eligibilityLoading || trialLoading || cancelPlanMutation.isPending

  const handleCancel = async () => {
    if (!confirmCancellation) {
      toast.error('Lütfen iptal işlemini onaylayın')
      return
    }

    if (!cancellationReason.trim()) {
      toast.error('Lütfen iptal nedeninizi belirtin')
      return
    }

    try {
      const result = await cancelPlanMutation.mutateAsync({
        cancellationReason: cancellationReason.trim(),
        requestRefund: requestRefund && trialInfo?.is_in_trial
      })

      if (result?.success) {
        toast.success(result.message || 'Plan başarıyla iptal edildi')
        onSuccess?.()
        onClose()
        
        // Reset form
        setCancellationReason('')
        setRequestRefund(false)
        setConfirmCancellation(false)
      } else {
        toast.error(result?.message || 'Plan iptal edilemedi')
      }
    } catch (error) {
      console.error('Plan cancellation error:', error)
      toast.error('Plan iptal edilirken bir hata oluştu')
    }
  }

  const handleClose = () => {
    if (cancelPlanMutation.isPending) return
    
    setCancellationReason('')
    setRequestRefund(false)
    setConfirmCancellation(false)
    onClose()
  }

  if (!eligibility?.can_cancel) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-500" />
              Plan İptal Edilemiyor
            </DialogTitle>
            <DialogDescription>
              {eligibility?.reason || 'Bu plan iptal edilemez.'}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={handleClose} variant="outline">
              Kapat
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Plan İptal Et
          </DialogTitle>
          <DialogDescription>
            {eligibility?.plan_display_name} planınızı iptal etmek üzeresiniz. Bu işlem geri alınamaz.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Trial Information */}
          {trialInfo?.is_in_trial && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Deneme süreniz devam ediyor ({trialInfo.days_remaining} gün kaldı). 
                İptal ederseniz tam iade alabilirsiniz.
              </AlertDescription>
            </Alert>
          )}

          {/* Refund Information */}
          {eligibility?.refund_eligible && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Deneme süresi içinde olduğunuz için {eligibility.estimated_refund_amount} TL tam iade alabilirsiniz.
              </AlertDescription>
            </Alert>
          )}

          {/* Cancellation Reason */}
          <div className="space-y-2">
            <Label htmlFor="cancellation-reason">
              İptal Nedeni <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="cancellation-reason"
              placeholder="Planınızı neden iptal etmek istiyorsunuz? Geri bildiriminiz bizim için değerli."
              value={cancellationReason}
              onChange={(e) => setCancellationReason(e.target.value)}
              rows={3}
              maxLength={500}
              disabled={isLoading}
            />
            <p className="text-xs text-muted-foreground">
              {cancellationReason.length}/500 karakter
            </p>
          </div>

          {/* Refund Request */}
          {eligibility?.refund_eligible && (
            <div className="flex items-center space-x-2">
              <Checkbox
                id="request-refund"
                checked={requestRefund}
                onCheckedChange={(checked) => setRequestRefund(checked as boolean)}
                disabled={isLoading}
              />
              <Label htmlFor="request-refund" className="text-sm">
                İade talep ediyorum ({eligibility.estimated_refund_amount} TL)
              </Label>
            </div>
          )}

          {/* Confirmation */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="confirm-cancellation"
              checked={confirmCancellation}
              onCheckedChange={(checked) => setConfirmCancellation(checked as boolean)}
              disabled={isLoading}
            />
            <Label htmlFor="confirm-cancellation" className="text-sm">
              Plan iptal işlemini onaylıyorum ve bu işlemin geri alınamayacağını anlıyorum
            </Label>
          </div>

          {/* Warning */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Plan iptal edildikten sonra ücretsiz plana geçeceksiniz. 
              Mevcut projeleriniz ve promptlarınız korunacak ancak plan limitleri geçerli olacak.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter className="gap-2">
          <Button 
            onClick={handleClose} 
            variant="outline"
            disabled={isLoading}
          >
            Vazgeç
          </Button>
          <Button 
            onClick={handleCancel}
            variant="destructive"
            disabled={isLoading || !confirmCancellation || !cancellationReason.trim()}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Planı İptal Et
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
