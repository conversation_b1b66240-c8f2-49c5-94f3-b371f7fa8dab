module.exports = {
  // TypeScript and JavaScript files
  '*.{ts,tsx,js,jsx}': [
    'eslint --fix',
    'prettier --write',
    'jest --bail --findRelatedTests --passWithNoTests',
  ],
  
  // JSON files
  '*.json': ['prettier --write'],
  
  // CSS and styling files
  '*.{css,scss,sass,less}': [
    'stylelint --fix',
    'prettier --write',
  ],
  
  // Markdown files
  '*.{md,mdx}': [
    'prettier --write',
    'markdownlint --fix',
  ],
  
  // YAML files
  '*.{yml,yaml}': ['prettier --write'],
  
  // Package.json
  'package.json': [
    'prettier --write',
    'npm audit --audit-level moderate',
  ],
}
