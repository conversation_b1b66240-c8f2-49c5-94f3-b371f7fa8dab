{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/app/bug-report/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { \n  ArrowLeft, \n  Sparkles, \n  Bug, \n  AlertTriangle,\n  Info,\n  Zap,\n  Send,\n  CheckCircle,\n  Upload\n} from 'lucide-react'\n\nexport default function BugReportPage() {\n  const [formData, setFormData] = useState({\n    title: '',\n    email: '',\n    priority: '',\n    category: '',\n    description: '',\n    steps: '',\n    expected: '',\n    actual: '',\n    browser: '',\n    os: ''\n  })\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [isSubmitted, setIsSubmitted] = useState(false)\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsSubmitting(true)\n    \n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000))\n    \n    setIsSubmitting(false)\n    setIsSubmitted(true)\n    \n    // Reset form after 3 seconds\n    setTimeout(() => {\n      setIsSubmitted(false)\n      setFormData({\n        title: '', email: '', priority: '', category: '', description: '',\n        steps: '', expected: '', actual: '', browser: '', os: ''\n      })\n    }, 3000)\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value\n    }))\n  }\n\n  const handleSelectChange = (name: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      {/* Header */}\n      <header className=\"border-b border-gray-200 bg-white/80 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <Link \n              href=\"/\" \n              className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <ArrowLeft className=\"h-5 w-5\" />\n              <span>Ana Sayfaya Dön</span>\n            </Link>\n            <div className=\"flex items-center space-x-2\">\n              <Sparkles className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">PromptBir</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Hero Section */}\n        <div className=\"text-center mb-12\">\n          <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n            <Bug className=\"h-8 w-8 text-red-600\" />\n          </div>\n          <h1 className=\"text-4xl sm:text-5xl font-bold text-gray-900 mb-6\">\n            Bug Raporu\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Karşılaştığınız teknik sorunları bildirin. Detaylı bilgi vererek sorunun \n            daha hızlı çözülmesine yardımcı olabilirsiniz.\n          </p>\n        </div>\n\n        {/* Priority Info */}\n        <div className=\"grid md:grid-cols-3 gap-4 mb-12\">\n          <Card className=\"border-red-200 bg-red-50\">\n            <CardContent className=\"p-4 text-center\">\n              <AlertTriangle className=\"h-8 w-8 text-red-600 mx-auto mb-2\" />\n              <h3 className=\"font-semibold text-red-900\">Yüksek Öncelik</h3>\n              <p className=\"text-sm text-red-700\">Sistem çökmesi, veri kaybı</p>\n            </CardContent>\n          </Card>\n          <Card className=\"border-yellow-200 bg-yellow-50\">\n            <CardContent className=\"p-4 text-center\">\n              <Zap className=\"h-8 w-8 text-yellow-600 mx-auto mb-2\" />\n              <h3 className=\"font-semibold text-yellow-900\">Orta Öncelik</h3>\n              <p className=\"text-sm text-yellow-700\">Özellik çalışmıyor</p>\n            </CardContent>\n          </Card>\n          <Card className=\"border-blue-200 bg-blue-50\">\n            <CardContent className=\"p-4 text-center\">\n              <Info className=\"h-8 w-8 text-blue-600 mx-auto mb-2\" />\n              <h3 className=\"font-semibold text-blue-900\">Düşük Öncelik</h3>\n              <p className=\"text-sm text-blue-700\">Görsel sorunlar, öneriler</p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Bug Report Form */}\n        <Card className=\"shadow-lg\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-3 text-2xl\">\n              <Bug className=\"h-6 w-6 text-red-600\" />\n              Bug Raporu Formu\n            </CardTitle>\n            <CardDescription>\n              Lütfen formu mümkün olduğunca detaylı doldurun. Bu, sorunu daha hızlı çözmemize yardımcı olacaktır.\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            {isSubmitted ? (\n              <div className=\"text-center py-8\">\n                <CheckCircle className=\"h-16 w-16 text-green-600 mx-auto mb-4\" />\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                  Bug Raporu Gönderildi!\n                </h3>\n                <p className=\"text-gray-600\">\n                  Raporunuz alındı. En kısa sürede inceleyeceğiz.\n                </p>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                {/* Basic Info */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <Label htmlFor=\"title\">Bug Başlığı *</Label>\n                    <Input\n                      id=\"title\"\n                      name=\"title\"\n                      type=\"text\"\n                      required\n                      value={formData.title}\n                      onChange={handleChange}\n                      className=\"mt-1\"\n                      placeholder=\"Kısa ve açıklayıcı başlık\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"email\">E-posta *</Label>\n                    <Input\n                      id=\"email\"\n                      name=\"email\"\n                      type=\"email\"\n                      required\n                      value={formData.email}\n                      onChange={handleChange}\n                      className=\"mt-1\"\n                      placeholder=\"<EMAIL>\"\n                    />\n                  </div>\n                </div>\n\n                {/* Priority and Category */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <Label htmlFor=\"priority\">Öncelik Seviyesi *</Label>\n                    <Select onValueChange={(value) => handleSelectChange('priority', value)}>\n                      <SelectTrigger className=\"mt-1\">\n                        <SelectValue placeholder=\"Öncelik seviyesi seçin\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"high\">Yüksek - Sistem çökmesi, veri kaybı</SelectItem>\n                        <SelectItem value=\"medium\">Orta - Özellik çalışmıyor</SelectItem>\n                        <SelectItem value=\"low\">Düşük - Görsel sorunlar, öneriler</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                  <div>\n                    <Label htmlFor=\"category\">Kategori *</Label>\n                    <Select onValueChange={(value) => handleSelectChange('category', value)}>\n                      <SelectTrigger className=\"mt-1\">\n                        <SelectValue placeholder=\"Bug kategorisi seçin\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"ui\">Kullanıcı Arayüzü</SelectItem>\n                        <SelectItem value=\"functionality\">İşlevsellik</SelectItem>\n                        <SelectItem value=\"performance\">Performans</SelectItem>\n                        <SelectItem value=\"security\">Güvenlik</SelectItem>\n                        <SelectItem value=\"data\">Veri İşleme</SelectItem>\n                        <SelectItem value=\"auth\">Kimlik Doğrulama</SelectItem>\n                        <SelectItem value=\"other\">Diğer</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                </div>\n\n                {/* Description */}\n                <div>\n                  <Label htmlFor=\"description\">Bug Açıklaması *</Label>\n                  <Textarea\n                    id=\"description\"\n                    name=\"description\"\n                    required\n                    value={formData.description}\n                    onChange={handleChange}\n                    className=\"mt-1 min-h-[100px]\"\n                    placeholder=\"Karşılaştığınız sorunu detaylı olarak açıklayın...\"\n                  />\n                </div>\n\n                {/* Steps to Reproduce */}\n                <div>\n                  <Label htmlFor=\"steps\">Hatayı Tekrarlama Adımları *</Label>\n                  <Textarea\n                    id=\"steps\"\n                    name=\"steps\"\n                    required\n                    value={formData.steps}\n                    onChange={handleChange}\n                    className=\"mt-1 min-h-[100px]\"\n                    placeholder=\"1. İlk adım&#10;2. İkinci adım&#10;3. Üçüncü adım...\"\n                  />\n                </div>\n\n                {/* Expected vs Actual */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <Label htmlFor=\"expected\">Beklenen Sonuç *</Label>\n                    <Textarea\n                      id=\"expected\"\n                      name=\"expected\"\n                      required\n                      value={formData.expected}\n                      onChange={handleChange}\n                      className=\"mt-1 min-h-[80px]\"\n                      placeholder=\"Ne olmasını bekliyordunuz?\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"actual\">Gerçek Sonuç *</Label>\n                    <Textarea\n                      id=\"actual\"\n                      name=\"actual\"\n                      required\n                      value={formData.actual}\n                      onChange={handleChange}\n                      className=\"mt-1 min-h-[80px]\"\n                      placeholder=\"Gerçekte ne oldu?\"\n                    />\n                  </div>\n                </div>\n\n                {/* System Info */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <Label htmlFor=\"browser\">Tarayıcı</Label>\n                    <Input\n                      id=\"browser\"\n                      name=\"browser\"\n                      type=\"text\"\n                      value={formData.browser}\n                      onChange={handleChange}\n                      className=\"mt-1\"\n                      placeholder=\"Chrome 120, Firefox 121, Safari 17...\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"os\">İşletim Sistemi</Label>\n                    <Input\n                      id=\"os\"\n                      name=\"os\"\n                      type=\"text\"\n                      value={formData.os}\n                      onChange={handleChange}\n                      className=\"mt-1\"\n                      placeholder=\"Windows 11, macOS 14, Ubuntu 22.04...\"\n                    />\n                  </div>\n                </div>\n\n                {/* File Upload Info */}\n                <Card className=\"bg-blue-50 border-blue-200\">\n                  <CardContent className=\"p-4\">\n                    <div className=\"flex items-start gap-3\">\n                      <Upload className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n                      <div>\n                        <h4 className=\"font-semibold text-blue-900 mb-1\">Ekran Görüntüsü Ekleyin</h4>\n                        <p className=\"text-sm text-blue-700\">\n                          Sorunu daha iyi anlayabilmemiz için ekran görüntüsü veya video ekleyebilirsiniz. \n                          Dosyaları e-posta ile <strong><EMAIL></strong> adresine gönderebilirsiniz.\n                        </p>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                <Button \n                  type=\"submit\" \n                  className=\"w-full bg-gradient-to-r from-red-600 to-pink-600 text-white hover:from-red-700 hover:to-pink-700\"\n                  disabled={isSubmitting}\n                >\n                  {isSubmitting ? (\n                    <>\n                      <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                      Gönderiliyor...\n                    </>\n                  ) : (\n                    <>\n                      <Send className=\"h-4 w-4 mr-2\" />\n                      Bug Raporu Gönder\n                    </>\n                  )}\n                </Button>\n              </form>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Tips */}\n        <Card className=\"shadow-lg mt-8\">\n          <CardHeader>\n            <CardTitle className=\"text-xl\">İyi Bir Bug Raporu İçin İpuçları</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <ul className=\"space-y-2 text-gray-700\">\n              <li className=\"flex items-start gap-2\">\n                <span className=\"text-blue-600 font-bold\">•</span>\n                <span>Sorunu tekrarlamak için gereken adımları net bir şekilde yazın</span>\n              </li>\n              <li className=\"flex items-start gap-2\">\n                <span className=\"text-blue-600 font-bold\">•</span>\n                <span>Ekran görüntüsü veya video ekleyin</span>\n              </li>\n              <li className=\"flex items-start gap-2\">\n                <span className=\"text-blue-600 font-bold\">•</span>\n                <span>Hata mesajlarını tam olarak kopyalayın</span>\n              </li>\n              <li className=\"flex items-start gap-2\">\n                <span className=\"text-blue-600 font-bold\">•</span>\n                <span>Tarayıcı konsol hatalarını kontrol edin (F12 tuşu)</span>\n              </li>\n              <li className=\"flex items-start gap-2\">\n                <span className=\"text-blue-600 font-bold\">•</span>\n                <span>Sorunun hangi koşullarda ortaya çıktığını belirtin</span>\n              </li>\n            </ul>\n          </CardContent>\n        </Card>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-8 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 PromptBir. Tüm hakları saklıdır.\n          </p>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;;AAsBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,OAAO;QACP,UAAU;QACV,QAAQ;QACR,SAAS;QACT,IAAI;IACN;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gBAAgB;QAChB,eAAe;QAEf,6BAA6B;QAC7B,WAAW;YACT,eAAe;YACf,YAAY;gBACV,OAAO;gBAAI,OAAO;gBAAI,UAAU;gBAAI,UAAU;gBAAI,aAAa;gBAC/D,OAAO;gBAAI,UAAU;gBAAI,QAAQ;gBAAI,SAAS;gBAAI,IAAI;YACxD;QACF,GAAG;IACL;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;YACjC,CAAC;IACH;IAEA,MAAM,qBAAqB,CAAC,MAAc;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAuB;;;;;;;;;;;;;;;;;0CAGxC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,6LAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;;;;;;0CAG3C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAyB;;;;;;;kDAG1C,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACT,4BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;yDAK/B,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDAEtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAQ;;;;;;sEACvB,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAQ;;;;;;sEACvB,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAMlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW;;;;;;sEAC1B,6LAAC,qIAAA,CAAA,SAAM;4DAAC,eAAe,CAAC,QAAU,mBAAmB,YAAY;;8EAC/D,6LAAC,qIAAA,CAAA,gBAAa;oEAAC,WAAU;8EACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAO;;;;;;sFACzB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAS;;;;;;sFAC3B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;;;;;;;;;;;;;;;;;;;8DAI9B,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW;;;;;;sEAC1B,6LAAC,qIAAA,CAAA,SAAM;4DAAC,eAAe,CAAC,QAAU,mBAAmB,YAAY;;8EAC/D,6LAAC,qIAAA,CAAA,gBAAa;oEAAC,WAAU;8EACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAK;;;;;;sFACvB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAgB;;;;;;sFAClC,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAc;;;;;;sFAChC,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAW;;;;;;sFAC7B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAO;;;;;;sFACzB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAO;;;;;;sFACzB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOlC,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,MAAK;oDACL,QAAQ;oDACR,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAKhB,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,MAAK;oDACL,QAAQ;oDACR,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAKhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW;;;;;;sEAC1B,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,QAAQ;4DACxB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAS;;;;;;sEACxB,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,MAAM;4DACtB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAMlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAU;;;;;;sEACzB,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAK;;;;;;sEACpB,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,EAAE;4DAClB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAMlB,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;;wEAAwB;sFAEb,6LAAC;sFAAO;;;;;;wEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOrE,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU;sDAET,6BACC;;kEACE,6LAAC;wDAAI,WAAU;;;;;;oDAAuE;;6EAIxF;;kEACE,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAW/C,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAU;;;;;;;;;;;0CAEjC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAA0B;;;;;;8DAC1C,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAA0B;;;;;;8DAC1C,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAA0B;;;;;;8DAC1C,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAA0B;;;;;;8DAC1C,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAA0B;;;;;;8DAC1C,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAOvC;GA5WwB;KAAA", "debugId": null}}]}