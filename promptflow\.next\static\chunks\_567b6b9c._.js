(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript)");
    });
});
}),
"[project]/src/components/context-gallery.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_b35c1e19._.js",
  "static/chunks/node_modules_288726c2._.js",
  "static/chunks/src_components_context-gallery_tsx_6f96755f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/context-gallery.tsx [app-client] (ecmascript)");
    });
});
}),
"[project]/src/components/prompt-workspace.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_components_enhanced-context-gallery-modal_tsx_c6738cda._.js",
  "static/chunks/src_f950decb._.js",
  "static/chunks/node_modules_f6acbf12._.js",
  "static/chunks/src_components_prompt-workspace_tsx_6f96755f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/prompt-workspace.tsx [app-client] (ecmascript)");
    });
});
}),
"[project]/src/components/context-sidebar.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_9f323a76._.js",
  "static/chunks/src_components_context-sidebar_tsx_6f96755f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/context-sidebar.tsx [app-client] (ecmascript)");
    });
});
}),
"[project]/src/components/project-name-editor.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/components/project-name-editor.tsx [app-client] (ecmascript)");
    });
});
}),
"[project]/src/components/context-creation-modal.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_c567901b._.js",
  "static/chunks/node_modules_9df03a5b._.js",
  "static/chunks/src_components_context-creation-modal_tsx_6f96755f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/context-creation-modal.tsx [app-client] (ecmascript)");
    });
});
}),
"[project]/src/components/context-edit-modal.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_0771e55d._.js",
  "static/chunks/node_modules_69fdee22._.js",
  "static/chunks/src_components_context-edit-modal_tsx_6f96755f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/context-edit-modal.tsx [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/recharts/es6/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_recharts_es6_util_e1c5c651._.js",
  "static/chunks/node_modules_recharts_es6_component_088c8a74._.js",
  "static/chunks/node_modules_recharts_es6_state_215afadf._.js",
  "static/chunks/node_modules_recharts_es6_polar_b5621a9a._.js",
  "static/chunks/node_modules_recharts_es6_cartesian_191da03b._.js",
  "static/chunks/node_modules_recharts_es6_chart_a9886de3._.js",
  "static/chunks/node_modules_recharts_es6_4e76202b._.js",
  "static/chunks/node_modules_008b7bf1._.js",
  "static/chunks/node_modules_recharts_es6_index_289d8cc1.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/recharts/es6/index.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/date-fns/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_date-fns_a1732155._.js",
  "static/chunks/node_modules_date-fns_index_289d8cc1.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/date-fns/index.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/date-fns/locale.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_date-fns_locale_39451c31._.js",
  "static/chunks/node_modules_date-fns_420209d5._.js",
  "static/chunks/node_modules_date-fns_locale_289d8cc1.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/date-fns/locale.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/zod/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_zod_4354ef0d._.js",
  "static/chunks/node_modules_zod_index_289d8cc1.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/zod/index.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/papaparse/papaparse.min.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_papaparse_papaparse_min_590fc416.js",
  "static/chunks/node_modules_papaparse_papaparse_min_6f96755f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/papaparse/papaparse.min.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/react-syntax-highlighter/dist/esm/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_6a448757._.js",
  "static/chunks/node_modules_react-syntax-highlighter_dist_esm_f640543a._.js",
  "static/chunks/node_modules_highlight_js_lib_languages_mathematica_b6fce4e3.js",
  "static/chunks/node_modules_highlight_js_lib_languages_f5da2e43._.js",
  "static/chunks/node_modules_highlight_js_lib_core_af9e08a7.js",
  "static/chunks/node_modules_refractor_f85e00c5._.js",
  "static/chunks/node_modules_eb30a3e2._.js",
  "static/chunks/node_modules_react-syntax-highlighter_dist_esm_index_289d8cc1.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/react-syntax-highlighter/dist/esm/index.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_react-syntax-highlighter_dist_esm_styles_prism_8a838b91._.js",
  "static/chunks/node_modules_react-syntax-highlighter_dist_esm_styles_prism_index_289d8cc1.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/framer-motion/dist/es/index.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_framer-motion_dist_es_c1edb52d._.js",
  "static/chunks/node_modules_motion-dom_dist_es_bf1a9e95._.js",
  "static/chunks/node_modules_motion-utils_dist_es_26636f5e._.js",
  "static/chunks/node_modules_framer-motion_dist_es_index_mjs_289d8cc1._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/framer-motion/dist/es/index.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/src/app/dashboard/page.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/app/dashboard/page.tsx [app-client] (ecmascript)");
    });
});
}),
"[project]/src/app/profile/page.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_f1692e56._.js",
  "static/chunks/node_modules_2205f932._.js",
  "static/chunks/src_app_profile_page_tsx_6f96755f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/app/profile/page.tsx [app-client] (ecmascript)");
    });
});
}),
"[project]/src/app/auth/page.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_824e855d._.js",
  "static/chunks/src_app_auth_page_tsx_6f96755f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/app/auth/page.tsx [app-client] (ecmascript)");
    });
});
}),
}]);