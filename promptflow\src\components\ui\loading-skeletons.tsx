/**
 * Loading Skeleton Components
 * Optimized loading states for better UX
 */

import React, { memo } from 'react'
import { cn } from '@/lib/utils'
import { CheckCircle } from 'lucide-react'

// Base skeleton component
const Skeleton = memo(function Skeleton({ 
  className, 
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-gray-200 dark:bg-gray-800",
        className
      )}
      {...props}
    />
  )
})

// Prompt workspace skeleton
export const PromptWorkspaceSkeleton = memo(function PromptWorkspaceSkeleton() {
  return (
    <div className="flex-1 flex flex-col p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-48" />
        <div className="flex gap-2">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>
      
      {/* Search bar */}
      <Skeleton className="h-12 w-full" />
      
      {/* Prompt list */}
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-8 w-20" />
            </div>
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <div className="flex gap-2">
              <Skeleton className="h-6 w-16" />
              <Skeleton className="h-6 w-20" />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
})

// Context sidebar skeleton
export const ContextSidebarSkeleton = memo(function ContextSidebarSkeleton() {
  return (
    <div className="h-full flex flex-col p-4 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-24" />
        <Skeleton className="h-8 w-8" />
      </div>
      
      {/* Search */}
      <Skeleton className="h-10 w-full" />
      
      {/* Categories */}
      <div className="space-y-2">
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-8 w-full" />
        ))}
      </div>
      
      {/* Context items */}
      <div className="flex-1 space-y-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="border rounded-lg p-3 space-y-2">
            <Skeleton className="h-5 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <div className="flex gap-2">
              <Skeleton className="h-5 w-12" />
              <Skeleton className="h-5 w-16" />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
})

// Context gallery skeleton
export const ContextGallerySkeleton = memo(function ContextGallerySkeleton() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-40" />
        <Skeleton className="h-10 w-32" />
      </div>
      
      {/* Filters */}
      <div className="flex gap-4">
        <Skeleton className="h-10 w-48" />
        <Skeleton className="h-10 w-32" />
        <Skeleton className="h-10 w-28" />
      </div>
      
      {/* Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array.from({ length: 9 }).map((_, i) => (
          <div key={i} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-6 w-6" />
            </div>
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
            <div className="flex gap-2">
              <Skeleton className="h-6 w-16" />
              <Skeleton className="h-6 w-20" />
            </div>
            <Skeleton className="h-8 w-full" />
          </div>
        ))}
      </div>
    </div>
  )
})

// Project sidebar skeleton
export const ProjectSidebarSkeleton = memo(function ProjectSidebarSkeleton() {
  return (
    <div className="h-full flex flex-col p-4 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-20" />
        <Skeleton className="h-8 w-8" />
      </div>
      
      {/* Create button */}
      <Skeleton className="h-10 w-full" />
      
      {/* Project list */}
      <div className="flex-1 space-y-2">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="flex items-center gap-3 p-2">
            <Skeleton className="h-8 w-8 rounded" />
            <div className="flex-1 space-y-1">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-3 w-2/3" />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
})

// Profile page skeleton
export const ProfilePageSkeleton = memo(function ProfilePageSkeleton() {
  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-6">
        <Skeleton className="h-24 w-24 rounded-full" />
        <div className="space-y-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-5 w-64" />
        </div>
      </div>
      
      {/* Tabs */}
      <div className="flex gap-4 border-b">
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-10 w-24" />
        ))}
      </div>
      
      {/* Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <div className="space-y-4">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-12 w-full" />
          </div>
          <div className="space-y-4">
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-12 w-full" />
          </div>
        </div>
        
        <div className="space-y-6">
          <div className="border rounded-lg p-6 space-y-4">
            <Skeleton className="h-6 w-40" />
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex justify-between">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-16" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
})

// Auth page skeleton
export const AuthPageSkeleton = memo(function AuthPageSkeleton() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        <div className="text-center space-y-2">
          <Skeleton className="h-8 w-48 mx-auto" />
          <Skeleton className="h-5 w-64 mx-auto" />
        </div>
        
        <div className="border rounded-lg p-6 space-y-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          
          <div className="flex gap-4">
            <Skeleton className="h-10 flex-1" />
            <Skeleton className="h-10 flex-1" />
          </div>
        </div>
      </div>
    </div>
  )
})

// Generic card skeleton
export const CardSkeleton = memo(function CardSkeleton({ 
  lines = 3 
}: { 
  lines?: number 
}) {
  return (
    <div className="border rounded-lg p-4 space-y-3">
      <Skeleton className="h-6 w-3/4" />
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton 
          key={i} 
          className={`h-4 ${i === lines - 1 ? 'w-1/2' : 'w-full'}`} 
        />
      ))}
    </div>
  )
})

// List skeleton
export const ListSkeleton = memo(function ListSkeleton({ 
  items = 5 
}: { 
  items?: number 
}) {
  return (
    <div className="space-y-2">
      {Array.from({ length: items }).map((_, i) => (
        <div key={i} className="flex items-center gap-3 p-3 border rounded">
          <Skeleton className="h-10 w-10 rounded" />
          <div className="flex-1 space-y-1">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-3 w-2/3" />
          </div>
          <Skeleton className="h-8 w-16" />
        </div>
      ))}
    </div>
  )
})

// Advanced loading states with progress
export const ProgressiveLoader = memo(function ProgressiveLoader({
  stages,
  currentStage,
  className
}: {
  stages: string[]
  currentStage: number
  className?: string
}) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div className="space-y-2">
        {stages.map((stage, index) => (
          <div key={index} className="flex items-center gap-3">
            <div className={cn(
              "w-4 h-4 rounded-full border-2",
              index < currentStage
                ? "bg-green-500 border-green-500"
                : index === currentStage
                  ? "border-blue-500 animate-pulse"
                  : "border-gray-300"
            )} />
            <span className={cn(
              "text-sm",
              index <= currentStage ? "text-gray-900" : "text-gray-500"
            )}>
              {stage}
            </span>
          </div>
        ))}
      </div>
    </div>
  )
})

// Smart loading state that adapts to content
export const SmartLoader = memo(function SmartLoader({
  type = 'default',
  message,
  progress,
  className
}: {
  type?: 'default' | 'data' | 'upload' | 'processing'
  message?: string
  progress?: number
  className?: string
}) {
  const getLoaderConfig = () => {
    switch (type) {
      case 'data':
        return {
          icon: '📊',
          defaultMessage: 'Veriler yükleniyor...',
          color: 'blue'
        }
      case 'upload':
        return {
          icon: '📤',
          defaultMessage: 'Dosya yükleniyor...',
          color: 'green'
        }
      case 'processing':
        return {
          icon: '⚙️',
          defaultMessage: 'İşleniyor...',
          color: 'purple'
        }
      default:
        return {
          icon: '⏳',
          defaultMessage: 'Yükleniyor...',
          color: 'blue'
        }
    }
  }

  const config = getLoaderConfig()

  return (
    <div className={cn("flex flex-col items-center justify-center p-8", className)}>
      <div className="text-4xl mb-4 animate-bounce">
        {config.icon}
      </div>

      <div className={`animate-spin rounded-full h-8 w-8 border-b-2 border-${config.color}-600 mb-4`}></div>

      <p className="text-gray-600 text-center mb-4">
        {message || config.defaultMessage}
      </p>

      {typeof progress === 'number' && (
        <div className="w-full max-w-xs">
          <div className="flex justify-between text-sm text-gray-500 mb-1">
            <span>İlerleme</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`bg-${config.color}-600 h-2 rounded-full transition-all duration-300`}
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      )}
    </div>
  )
})

// Enhanced progress indicators
export const CircularProgress = memo(function CircularProgress({
  progress,
  size = 40,
  strokeWidth = 4,
  className,
  showPercentage = false
}: {
  progress: number
  size?: number
  strokeWidth?: number
  className?: string
  showPercentage?: boolean
}) {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (progress / 100) * circumference

  return (
    <div className={cn("relative inline-flex items-center justify-center", className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          className="text-gray-200"
        />
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="text-blue-600 transition-all duration-300 ease-in-out"
          strokeLinecap="round"
        />
      </svg>
      {showPercentage && (
        <span className="absolute text-xs font-medium">
          {Math.round(progress)}%
        </span>
      )}
    </div>
  )
})

// Linear progress bar
export const LinearProgress = memo(function LinearProgress({
  progress,
  className,
  showPercentage = false,
  animated = true
}: {
  progress: number
  className?: string
  showPercentage?: boolean
  animated?: boolean
}) {
  return (
    <div className={cn("w-full", className)}>
      {showPercentage && (
        <div className="flex justify-between text-sm text-gray-600 mb-1">
          <span>İlerleme</span>
          <span>{Math.round(progress)}%</span>
        </div>
      )}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={cn(
            "bg-blue-600 h-2 rounded-full",
            animated && "transition-all duration-300 ease-out"
          )}
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
          role="progressbar"
          aria-valuenow={progress}
          aria-valuemin={0}
          aria-valuemax={100}
        />
      </div>
    </div>
  )
})

// Step progress indicator
export const StepProgress = memo(function StepProgress({
  steps,
  currentStep,
  className
}: {
  steps: Array<{ label: string; description?: string }>
  currentStep: number
  className?: string
}) {
  return (
    <div className={cn("w-full", className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const stepNumber = index + 1
          const isCompleted = stepNumber < currentStep
          const isCurrent = stepNumber === currentStep
          const isUpcoming = stepNumber > currentStep

          return (
            <React.Fragment key={index}>
              <div className="flex flex-col items-center">
                <div
                  className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium border-2",
                    isCompleted && "bg-green-600 border-green-600 text-white",
                    isCurrent && "bg-blue-600 border-blue-600 text-white",
                    isUpcoming && "bg-white border-gray-300 text-gray-400"
                  )}
                >
                  {isCompleted ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : (
                    stepNumber
                  )}
                </div>
                <div className="mt-2 text-center">
                  <div
                    className={cn(
                      "text-sm font-medium",
                      isCompleted && "text-green-600",
                      isCurrent && "text-blue-600",
                      isUpcoming && "text-gray-400"
                    )}
                  >
                    {step.label}
                  </div>
                  {step.description && (
                    <div className="text-xs text-gray-500 mt-1">
                      {step.description}
                    </div>
                  )}
                </div>
              </div>
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    "flex-1 h-0.5 mx-4",
                    stepNumber < currentStep ? "bg-green-600" : "bg-gray-300"
                  )}
                />
              )}
            </React.Fragment>
          )
        })}
      </div>
    </div>
  )
})

// Pulse loading indicator
export const PulseLoader = memo(function PulseLoader({
  size = 'md',
  className
}: {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}) {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  }

  return (
    <div className={cn("flex space-x-1", className)}>
      {[0, 1, 2].map((index) => (
        <div
          key={index}
          className={cn(
            "bg-blue-600 rounded-full animate-pulse",
            sizeClasses[size]
          )}
          style={{
            animationDelay: `${index * 0.15}s`,
            animationDuration: '0.6s'
          }}
        />
      ))}
    </div>
  )
})

export { Skeleton }
