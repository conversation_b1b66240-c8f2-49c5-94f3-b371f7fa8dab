'use client'

import { useState, useRef, useEffect, useCallback, memo, Suspense, lazy } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Copy, Plus, CheckCircle, Circle, Edit2, Check, X, Square, CheckSquare, MousePointer, GripVertical, Download, Undo, Menu, Settings, Search, Hash, Folder, ChevronDown, ChevronUp } from "lucide-react";
import { useAppStore } from "@/store/app-store";
import { usePrompts, useCreatePrompt, useMarkPromptAsUsed, useUpdatePrompt, useBulkUpdatePrompts } from "@/hooks/use-prompts";
import { useProject } from "@/hooks/use-projects";
import { useAllHashtags, useAllCategories } from "@/hooks/use-hashtags";
import { useUserLimits } from "@/hooks/use-plans";
import { InlineLimitWarning } from "@/components/limit-warning";
import { toast } from "sonner";
import { HashtagInput } from "@/components/hashtag-input";
import { CategorySelector } from "@/components/category-selector";
import { PopularHashtagsSidebar } from "@/components/popular-hashtags-sidebar";
import { parsePromptCategoriesPreserveText, mergeHashtags } from "@/lib/hashtag-utils";

import { Context } from "./context-gallery";
import SmartAutocomplete from "./smart-autocomplete";
import { ContextGallerySkeleton } from "./progressive-loader";
import { SharePromptButton } from "./share-prompt-button";

// Lazy load heavy modal
const EnhancedContextGalleryModal = lazy(() => import("./enhanced-context-gallery-modal").then(m => ({ default: m.EnhancedContextGalleryModal })));

import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

type ExtendedPrompt = {
  id: string;
  project_id: string;
  user_id: string;
  prompt_text: string;
  title: string | null;
  description: string | null;
  category: string | null;
  tags: string[];
  order_index: number;
  is_used: boolean;
  is_favorite: boolean;
  usage_count: number;
  last_used_at: string | null;
  task_code: string | null;
  created_at: string;
  updated_at: string;
};

interface SortablePromptItemProps {
  prompt: ExtendedPrompt;
  isMultiSelectMode: boolean;
  selectedPrompts: Set<string>;
  editingPromptId: string | null;
  editingText: string;
  editTextareaRef: React.RefObject<HTMLTextAreaElement | null>;
  onSelectPrompt: (id: string) => void;
  onEditPrompt: (prompt: ExtendedPrompt) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  onCopyPrompt: (prompt: ExtendedPrompt) => void;
  setEditingText: (text: string) => void;
  onHashtagFilter: (hashtag: string) => void;
}

const SortablePromptItem = memo(function SortablePromptItem({
  prompt,
  isMultiSelectMode,
  selectedPrompts,
  editingPromptId,
  editingText,
  editTextareaRef,
  onSelectPrompt,
  onEditPrompt,
  onSaveEdit,
  onCancelEdit,
  onCopyPrompt,
  setEditingText,
  onHashtagFilter,
}: SortablePromptItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: prompt.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`transition-all duration-200 ${
        prompt.is_used
          ? 'bg-gray-50 border-gray-300'
          : 'bg-white border-gray-200 hover:border-blue-300 hover:shadow-md'
      } ${
        selectedPrompts.has(prompt.id)
          ? 'ring-2 ring-blue-500 border-blue-500'
          : ''
      }`}
      onClick={() => isMultiSelectMode ? onSelectPrompt(prompt.id) : undefined}
    >
      <CardHeader className="pb-3">
        {/* Mobil Layout */}
        <div className="block lg:hidden">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              {!isMultiSelectMode && (
                <div
                  className="flex items-center justify-center w-8 h-8 cursor-grab active:cursor-grabbing"
                  {...attributes}
                  {...listeners}
                >
                  <GripVertical className="h-5 w-5 text-gray-400" />
                </div>
              )}
              {isMultiSelectMode && (
                <div
                  className="flex items-center justify-center w-8 h-8 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    onSelectPrompt(prompt.id);
                  }}
                >
                  {selectedPrompts.has(prompt.id) ? (
                    <CheckSquare className="h-6 w-6 text-blue-600" />
                  ) : (
                    <Square className="h-6 w-6 text-gray-400" />
                  )}
                </div>
              )}
              <div className={`flex items-center justify-center w-10 h-10 rounded-full text-base font-medium ${
                prompt.is_used
                  ? 'bg-green-100 text-green-700'
                  : 'bg-blue-100 text-blue-700'
              }`}>
                {prompt.order_index}
              </div>
              <div className="flex flex-col">
                <span className={`text-sm font-mono px-2 py-1 rounded ${
                  prompt.is_used
                    ? 'bg-gray-100 text-gray-500'
                    : 'bg-blue-50 text-blue-600'
                }`}>
                  {prompt.task_code || `task-${prompt.order_index}`}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {prompt.is_used ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <Circle className="h-5 w-5 text-gray-400" />
              )}
              <span className={`text-sm font-medium ${
                prompt.is_used ? 'text-gray-500' : 'text-gray-900'
              }`}>
                {prompt.is_used ? 'Kullanıldı' : 'Bekliyor'}
              </span>
            </div>
          </div>
          {!isMultiSelectMode && (
            <div className="flex items-center gap-2 justify-end">
              {editingPromptId === prompt.id ? (
                <>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={onSaveEdit}
                    className="bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700 min-h-[44px] px-4"
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Kaydet
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onCancelEdit}
                    className="text-red-600 hover:text-red-700 min-h-[44px] px-4"
                  >
                    <X className="h-4 w-4 mr-2" />
                    İptal
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEditPrompt(prompt)}
                    className="text-gray-600 hover:text-gray-700 min-h-[44px] px-4"
                  >
                    <Edit2 className="h-4 w-4 mr-2" />
                    Düzenle
                  </Button>
                  <SharePromptButton
                    promptId={prompt.id}
                    promptTitle={prompt.title || undefined}
                    promptText={prompt.prompt_text}
                    taskCode={prompt.task_code || undefined}
                    variant="outline"
                    size="sm"
                    className="min-h-[44px] px-4"
                  />
                  <Button
                    variant={prompt.is_used ? "secondary" : "default"}
                    size="sm"
                    onClick={() => onCopyPrompt(prompt)}
                    className={`min-h-[44px] px-4 ${prompt.is_used ? "opacity-60" : ""}`}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    {prompt.is_used ? 'Kopyalandı' : 'Kopyala'}
                  </Button>
                </>
              )}
            </div>
          )}
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:flex items-center justify-between">
          <div className="flex items-center gap-3">
            {!isMultiSelectMode && (
              <div
                className="flex items-center justify-center w-6 h-6 cursor-grab active:cursor-grabbing"
                {...attributes}
                {...listeners}
              >
                <GripVertical className="h-4 w-4 text-gray-400" />
              </div>
            )}
            {isMultiSelectMode && (
              <div
                className="flex items-center justify-center w-6 h-6 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  onSelectPrompt(prompt.id);
                }}
              >
                {selectedPrompts.has(prompt.id) ? (
                  <CheckSquare className="h-5 w-5 text-blue-600" />
                ) : (
                  <Square className="h-5 w-5 text-gray-400" />
                )}
              </div>
            )}
            <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
              prompt.is_used
                ? 'bg-green-100 text-green-700'
                : 'bg-blue-100 text-blue-700'
            }`}>
              {prompt.order_index}
            </div>
            <div className="flex flex-col">
              <span className={`text-xs font-mono px-2 py-1 rounded ${
                prompt.is_used
                  ? 'bg-gray-100 text-gray-500'
                  : 'bg-blue-50 text-blue-600'
              }`}>
                {prompt.task_code || `task-${prompt.order_index}`}
              </span>
            </div>
            <div className="flex items-center gap-2">
              {prompt.is_used ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <Circle className="h-4 w-4 text-gray-400" />
              )}
              <span className={`text-sm font-medium ${
                prompt.is_used ? 'text-gray-500' : 'text-gray-900'
              }`}>
                {prompt.is_used ? 'Kullanıldı' : 'Bekliyor'}
              </span>
            </div>
          </div>
          {!isMultiSelectMode && (
            <div className="flex items-center gap-2">
              {editingPromptId === prompt.id ? (
                <>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={onSaveEdit}
                    className="bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700"
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Kaydet
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onCancelEdit}
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-4 w-4 mr-2" />
                    İptal
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEditPrompt(prompt)}
                    className="text-gray-600 hover:text-gray-700"
                  >
                    <Edit2 className="h-4 w-4 mr-2" />
                    Düzenle
                  </Button>
                  <SharePromptButton
                    promptId={prompt.id}
                    promptTitle={prompt.title || undefined}
                    promptText={prompt.prompt_text}
                    taskCode={prompt.task_code || undefined}
                    variant="outline"
                    size="sm"
                  />
                  <Button
                    variant={prompt.is_used ? "secondary" : "default"}
                    size="sm"
                    onClick={() => onCopyPrompt(prompt)}
                    className={prompt.is_used ? "opacity-60" : ""}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    {prompt.is_used ? 'Kopyalandı' : 'Kopyala'}
                  </Button>
                </>
              )}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {editingPromptId === prompt.id ? (
          <Textarea
            ref={editTextareaRef}
            value={editingText}
            onChange={(e) => setEditingText(e.target.value)}
            className="min-h-[60px] max-h-[200px] resize-none"
            style={{ height: 'auto' }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && e.ctrlKey) {
                onSaveEdit();
              } else if (e.key === 'Escape') {
                onCancelEdit();
              }
            }}
            autoFocus
          />
        ) : (
          <div className="space-y-2">
            <p
              className={`text-sm leading-relaxed cursor-pointer ${
                prompt.is_used ? 'text-gray-500' : 'text-gray-700'
              }`}
              onDoubleClick={() => onEditPrompt(prompt)}
              title="Düzenlemek için çift tıklayın"
            >
              {prompt.prompt_text}
            </p>

            {/* Hashtags and Category Display */}
            {((prompt.tags && prompt.tags.length > 0) || prompt.category) && (
              <div className="flex flex-wrap items-center gap-1 pt-2 sm:gap-1.5">
                {/* Category */}
                {prompt.category && (
                  <Badge
                    variant="outline"
                    className="text-xs sm:text-xs bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100 transition-colors cursor-pointer"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      // TODO: Add category filter functionality
                    }}
                  >
                    <Folder className="w-3 h-3 mr-1 text-gray-500" />
                    {prompt.category}
                  </Badge>
                )}

                {/* Hashtags */}
                {prompt.tags && Array.isArray(prompt.tags) && prompt.tags.map((tag: string, index: number) => {
                  // Ensure tag is a valid string
                  const tagStr = typeof tag === 'string' ? tag : String(tag);
                  if (!tagStr || tagStr.trim() === '') return null;

                  return (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="text-xs sm:text-xs bg-blue-50 text-blue-700 border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onHashtagFilter(tagStr);
                      }}
                    >
                      <Hash className="w-3 h-3 mr-1 text-blue-500" />
                      {tagStr.replace('#', '')}
                    </Badge>
                  );
                })}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
});

interface PromptWorkspaceProps {
  isContextGalleryOpen?: boolean;
  onToggleContextGallery?: () => void;
}

export function PromptWorkspace({
  isContextGalleryOpen = false,
  onToggleContextGallery
}: PromptWorkspaceProps = {}) {
  const [newPromptText, setNewPromptText] = useState("");
  const [editingPromptId, setEditingPromptId] = useState<string | null>(null);
  const [editingText, setEditingText] = useState("");
  const [selectedPrompts, setSelectedPrompts] = useState<Set<string>>(new Set());
  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // New categorization state
  const [newPromptHashtags, setNewPromptHashtags] = useState<string[]>([]);
  const [newPromptCategory, setNewPromptCategory] = useState<string | null>(null);
  const [newPromptTitle, setNewPromptTitle] = useState("");
  const [showAdvancedForm, setShowAdvancedForm] = useState(false);
  const [showHashtagsSidebar, setShowHashtagsSidebar] = useState(true);
  const [filterHashtags, setFilterHashtags] = useState<string[]>([]);
  const [filterCategory, setFilterCategory] = useState<string | null>(null);

  const editTextareaRef = useRef<HTMLTextAreaElement>(null);
  const { activeProjectId, isContextEnabled } = useAppStore();
  const { data: limits } = useUserLimits();
  
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  
  const { data: prompts = [] } = usePrompts(activeProjectId);
  const { data: project } = useProject(activeProjectId);
  const { data: allHashtags = [] } = useAllHashtags(activeProjectId);
  const { data: allCategories = [] } = useAllCategories(activeProjectId);
  const createPromptMutation = useCreatePrompt();
  const updatePromptMutation = useUpdatePrompt();
  const bulkUpdatePromptsMutation = useBulkUpdatePrompts();
  const markAsUsedMutation = useMarkPromptAsUsed();

  // Arama ve kategori filtrelemesi
  const filteredPrompts = prompts.filter((prompt: ExtendedPrompt) => {
    try {
      // Text search filter
      const matchesSearch = searchQuery.trim() === "" ||
        prompt.prompt_text.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (prompt.title && prompt.title.toLowerCase().includes(searchQuery.toLowerCase()));

      // Category filter
      const matchesCategory = !filterCategory || prompt.category === filterCategory;

      // Hashtag filter
      const matchesHashtags = filterHashtags.length === 0 ||
        (prompt.tags && Array.isArray(prompt.tags) &&
         filterHashtags.some(hashtag =>
           prompt.tags.some((tag: string) => {
             const tagStr = typeof tag === 'string' ? tag : String(tag);
             return tagStr.toLowerCase() === hashtag.toLowerCase();
           })
         ));

      return matchesSearch && matchesCategory && matchesHashtags;
    } catch (error) {
      console.error('Error filtering prompt:', error, prompt);
      return true; // Include prompt if filtering fails
    }
  });


  // Otomatik boyutlandırma fonksiyonu (sadece düzenleme için)
  const adjustTextareaHeight = (textarea: HTMLTextAreaElement) => {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
  };

  // Düzenleme textarea'sı için otomatik boyutlandırma
  useEffect(() => {
    if (editTextareaRef.current) {
      adjustTextareaHeight(editTextareaRef.current);
    }
  }, [editingText]);

  const handleAddPrompt = async () => {
    if (newPromptText.trim() && activeProjectId) {
      // Plan limiti kontrol et
      if (limits && !limits.can_create_prompt) {
        toast.error(`Prompt oluşturma limitinize ulaştınız (${limits.current_prompts}/${limits.max_prompts_per_project}). Planınızı yükseltin.`);
        return;
      }

      try {
        // Parse hashtags and folder paths from prompt text while preserving original text
        const { hashtags: extractedHashtags, folderPaths, originalText } = parsePromptCategoriesPreserveText(newPromptText);

        // Merge extracted hashtags with manually added ones
        const allHashtags = mergeHashtags(newPromptHashtags || [], extractedHashtags || []);

        // Use folder path as category if found, otherwise use manually selected category
        const finalCategory = folderPaths && folderPaths.length > 0 ? folderPaths[0] : newPromptCategory;

        await createPromptMutation.mutateAsync({
          project_id: activeProjectId,
          prompt_text: originalText, // Keep original text with hashtags and folders
          title: newPromptTitle?.trim() || undefined,
          category: finalCategory || undefined,
          tags: allHashtags || [],
          order_index: prompts.length + 1,
          is_used: false,
        });

        // Reset form
        setNewPromptText("");
        setNewPromptTitle("");
        setNewPromptHashtags([]);
        setNewPromptCategory(null);
        setShowAdvancedForm(false);
      } catch (error) {
        console.error('Prompt ekleme hatası:', error);
        // Show user-friendly error message
        alert('Prompt eklenirken bir hata oluştu. Lütfen tekrar deneyin.');
      }
    }
  };

  const handleSelectContextFromGallery = (context: Context) => {
    setNewPromptText(context.content);
    onToggleContextGallery?.();
  };

  // Hashtag sidebar handlers
  const handleHashtagFilter = useCallback((hashtag: string) => {
    try {
      // Ensure hashtag is a string
      const hashtagStr = typeof hashtag === 'string' ? hashtag : String(hashtag);

      if (!hashtagStr || hashtagStr.trim() === '') {
        console.warn('Invalid hashtag provided to handleHashtagFilter:', hashtag);
        return;
      }

      if (filterHashtags.includes(hashtagStr)) {
        setFilterHashtags(filterHashtags.filter(h => h !== hashtagStr));
      } else {
        setFilterHashtags([...filterHashtags, hashtagStr]);
      }
    } catch (error) {
      console.error('Error in handleHashtagFilter:', error, hashtag);
    }
  }, [filterHashtags]);

  const handleCategoryFilter = useCallback((category: string) => {
    setFilterCategory(filterCategory === category ? null : category);
  }, [filterCategory]);





  const handleCopyPrompt = useCallback(async (prompt: ExtendedPrompt) => {
    try {
      // Context text'i proje bilgilerinden al ve context enabled durumunu kontrol et
      const contextText = isContextEnabled ? (project?.context_text || "") : "";
      const taskCode = prompt.task_code || `task-${prompt.order_index}`;

      let fullText = "";
      if (contextText) {
        fullText = `${contextText}\n\n${taskCode}\n${prompt.prompt_text}`;
      } else {
        fullText = `${taskCode}\n${prompt.prompt_text}`;
      }

      await navigator.clipboard.writeText(fullText);

      // Prompt'u kullanıldı olarak işaretle
      await markAsUsedMutation.mutateAsync(prompt.id);
    } catch (error) {
      console.error('Kopyalama hatası:', error);
    }
  }, [isContextEnabled, project?.context_text, markAsUsedMutation]);

  const handleUndoLastCopy = async () => {
    if (activeProjectId) {
      try {
        // En son kopyalanan (is_used=true) prompt'u bul
        const lastCopiedPrompt = prompts
          .filter(p => p.is_used)
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];
        
        if (lastCopiedPrompt) {
          // En son kopyalanan prompt'u "Bekliyor" durumuna getir
          await updatePromptMutation.mutateAsync({
            id: lastCopiedPrompt.id,
            is_used: false
          });
        }
      } catch (error) {
        console.error('Geri alma hatası:', error);
      }
    }
  };

  const handleEditPrompt = useCallback((prompt: ExtendedPrompt) => {
    setEditingPromptId(prompt.id);
    setEditingText(prompt.prompt_text);
  }, []);

  const handleSaveEdit = async () => {
    if (editingPromptId && editingText.trim()) {
      try {
        // Parse hashtags and folder paths from edited text while preserving original text
        const { hashtags: extractedHashtags, folderPaths, originalText } = parsePromptCategoriesPreserveText(editingText);

        // Get the current prompt to preserve existing tags and category
        const currentPrompt = prompts.find(p => p.id === editingPromptId);
        const existingTags = currentPrompt?.tags || [];
        const existingCategory = currentPrompt?.category;

        // Merge extracted hashtags with existing ones (avoid duplicates)
        const allHashtags = mergeHashtags(existingTags, extractedHashtags || []);

        // Use folder path as category if found, otherwise keep existing category
        const finalCategory = folderPaths && folderPaths.length > 0 ? folderPaths[0] : existingCategory;

        await updatePromptMutation.mutateAsync({
          id: editingPromptId,
          prompt_text: originalText, // Keep original text with hashtags and folders
          tags: allHashtags,
          category: finalCategory || undefined,
        });

        setEditingPromptId(null);
        setEditingText("");
      } catch (error) {
        console.error('Prompt güncelleme hatası:', error);
        // Show user-friendly error message
        alert('Prompt güncellenirken bir hata oluştu. Lütfen tekrar deneyin.');
      }
    }
  };

  const handleCancelEdit = useCallback(() => {
    setEditingPromptId(null);
    setEditingText("");
  }, []);

  const handleToggleMultiSelect = useCallback(() => {
    setIsMultiSelectMode(!isMultiSelectMode);
    setSelectedPrompts(new Set());
  }, [isMultiSelectMode]);

  const handleSelectPrompt = useCallback((promptId: string) => {
    const newSelected = new Set(selectedPrompts);
    if (newSelected.has(promptId)) {
      newSelected.delete(promptId);
    } else {
      newSelected.add(promptId);
    }
    setSelectedPrompts(newSelected);
  }, [selectedPrompts]);

  const handleCopySelectedPrompts = async () => {
    if (selectedPrompts.size === 0) return;

    try {
      const selectedPromptList = prompts
        .filter(p => selectedPrompts.has(p.id))
        .sort((a, b) => a.order_index - b.order_index);

      const contextText = isContextEnabled ? (project?.context_text || "") : "";
      
      let fullText = contextText ? `${contextText}\n\n` : "";
      
      selectedPromptList.forEach((prompt, index) => {
        const taskCode = prompt.task_code || `task-${prompt.order_index}`;
        fullText += `${index + 1}. ${taskCode}\n${prompt.prompt_text}`;
        if (index < selectedPromptList.length - 1) {
          fullText += "\n\n";
        }
      });

      await navigator.clipboard.writeText(fullText);
      
      // Tüm seçili prompt'ları kullanıldı olarak işaretle
      for (const promptId of selectedPrompts) {
        await markAsUsedMutation.mutateAsync(promptId);
      }
      
      // Seçimi temizle
      setSelectedPrompts(new Set());
      setIsMultiSelectMode(false);
    } catch (error) {
      console.error('Çoklu kopyalama hatası:', error);
    }
  };

  const handleExportUnusedPrompts = () => {
    const unusedPrompts = prompts
      .filter(p => !p.is_used)
      .sort((a, b) => a.order_index - b.order_index);

    if (unusedPrompts.length === 0) {
      alert('Kopyalanmamış prompt bulunamadı!');
      return;
    }

    const contextText = isContextEnabled ? (project?.context_text || "") : "";
    
    let markdownContent = `# ${project?.name || 'Prompt Listesi'}\n\n`;
    
    if (contextText) {
      markdownContent += `## Context\n\n${contextText}\n\n`;
    }
    
    markdownContent += `## Kopyalanmamış Prompt'lar\n\n`;
    
    unusedPrompts.forEach((prompt, index) => {
      const taskCode = prompt.task_code || `task-${prompt.order_index}`;
      markdownContent += `### ${index + 1}. ${taskCode}\n\n`;
      markdownContent += `${prompt.prompt_text}\n\n`;
      markdownContent += `---\n\n`;
    });

    // Markdown dosyasını indir
    const blob = new Blob([markdownContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${project?.name || 'prompts'}-unused.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };



  const handleDragEnd = useCallback(async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    const sortedPrompts = prompts.sort((a, b) => {
      // Önce kullanılmayan promptlar (is_used: false), sonra kullanılanlar (is_used: true)
      if (a.is_used !== b.is_used) {
        return a.is_used ? 1 : -1;
      }
      // Aynı durumda olanları order_index'e göre sırala
      return a.order_index - b.order_index;
    });

    const oldIndex = sortedPrompts.findIndex((prompt) => prompt.id === active.id);
    const newIndex = sortedPrompts.findIndex((prompt) => prompt.id === over.id);

    const newOrder = arrayMove(sortedPrompts, oldIndex, newIndex);

    // Bulk update için değişiklikleri topla
    const updates: Array<{ id: string; order_index: number; task_code: string }> = [];

    for (let i = 0; i < newOrder.length; i++) {
      const prompt = newOrder[i];
      const newOrderIndex = i + 1;
      const newTaskCode = `task-${newOrderIndex}`;

      if (prompt.order_index !== newOrderIndex || prompt.task_code !== newTaskCode) {
        updates.push({
          id: prompt.id,
          order_index: newOrderIndex,
          task_code: newTaskCode,
        });
      }
    }

    // Bulk update ile tüm değişiklikleri tek seferde uygula
    if (updates.length > 0) {
      try {
        await bulkUpdatePromptsMutation.mutateAsync(updates);
      } catch (error) {
        console.error('Sıralama güncelleme hatası:', error);
      }
    }
  }, [prompts, bulkUpdatePromptsMutation]);

  if (!activeProjectId) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Circle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Proje Seçin</h3>
          <p className="text-gray-500">Başlamak için sol panelden bir proje seçin</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Main Content */}
      <div className="flex flex-col flex-1">
        {/* Enhanced Responsive Header */}
        <div className="p-3 sm:p-4 lg:p-6 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <div className="flex items-center gap-2 sm:gap-3">
            {/* Mobile Menu Buttons - Enhanced */}
            <div className="flex md:hidden gap-1 sm:gap-2">
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8 sm:h-10 sm:w-10 p-0 touch-manipulation"
              >
                <Menu className="h-4 w-4 sm:h-5 sm:w-5" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8 sm:h-10 sm:w-10 p-0 touch-manipulation"
              >
                <Settings className="h-4 w-4 sm:h-5 sm:w-5" />
              </Button>
            </div>
            <h2 className="text-base sm:text-lg lg:text-xl font-semibold text-gray-900 truncate">
              {project?.name || 'Prompt Listesi'}
            </h2>
          </div>
          <div className="flex items-center gap-1 sm:gap-2 overflow-x-auto">
            {/* Enhanced Filter indicators for mobile */}
            {(filterHashtags.length > 0 || filterCategory) && (
              <div className="flex items-center gap-1 flex-shrink-0">
                {filterHashtags.slice(0, 2).map(hashtag => (
                  <Badge key={hashtag} variant="outline" className="text-xs whitespace-nowrap">
                    <Hash className="w-3 h-3 mr-1" />
                    <span className="hidden sm:inline">{hashtag.replace('#', '')}</span>
                    <span className="sm:hidden">{hashtag.replace('#', '').slice(0, 3)}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 w-3 h-3 ml-1 hover:bg-transparent touch-manipulation"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleHashtagFilter(hashtag);
                      }}
                    >
                      <X className="w-2 h-2" />
                    </Button>
                  </Badge>
                ))}
                {filterHashtags.length > 2 && (
                  <Badge variant="outline" className="text-xs">
                    +{filterHashtags.length - 2}
                  </Badge>
                )}
                {filterCategory && (
                  <Badge variant="outline" className="text-xs whitespace-nowrap">
                    <Folder className="w-3 h-3 mr-1" />
                    <span className="hidden sm:inline">{filterCategory}</span>
                    <span className="sm:hidden">{filterCategory.slice(0, 3)}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 w-3 h-3 ml-1 hover:bg-transparent"
                      onClick={() => setFilterCategory(null)}
                    >
                      <X className="w-2 h-2" />
                    </Button>
                  </Badge>
                )}
              </div>
            )}

            {/* Hashtags sidebar toggle */}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowHashtagsSidebar(!showHashtagsSidebar)}
              className="hidden lg:flex"
            >
              <Hash className="w-4 h-4 mr-1" />
              AI Tag {showHashtagsSidebar ? 'Sakla' : 'Göster'}
            </Button>

            <Badge variant="secondary" className="text-xs lg:text-sm">
              {searchQuery ? filteredPrompts.filter(p => !p.is_used).length : prompts.filter(p => !p.is_used).length} / {searchQuery ? filteredPrompts.length : prompts.length}
            </Badge>
          </div>
        </div>
        
        {/* Enhanced Responsive Search Box */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Prompt'larda ara..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-10 py-2.5 sm:py-2 border border-gray-300 rounded-lg text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 hover:bg-white transition-colors touch-manipulation"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery("")}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 touch-manipulation p-1"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      {/* Prompts List */}
      <ScrollArea className="flex-1 p-2 sm:p-3 lg:p-6 pb-32 sm:pb-28 lg:pb-24">
        <div className="space-y-2 sm:space-y-3">
          {prompts.length === 0 ? (
            <div className="text-center py-8 sm:py-12">
              <Plus className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4" />
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">Henüz Prompt Yok</h3>
              <p className="text-sm sm:text-base text-gray-500 px-4">Aşağıdaki alandan ilk promptunuzu ekleyin</p>
            </div>
          ) : filteredPrompts.length === 0 ? (
            <div className="text-center py-8 sm:py-12">
              <Search className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4" />
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">Arama Sonucu Bulunamadı</h3>
              <p className="text-sm sm:text-base text-gray-500 px-4">&quot;{searchQuery}&quot; için eşleşen prompt bulunamadı</p>
              <Button
                variant="outline"
                className="mt-3 sm:mt-4 touch-manipulation"
                onClick={() => setSearchQuery("")}
              >
                Aramayı Temizle
              </Button>
            </div>
          ) : (
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={filteredPrompts.map(p => p.id)}
                strategy={verticalListSortingStrategy}
              >
                {filteredPrompts
                  .sort((a, b) => {
                    // Önce kullanılmayan promptlar (is_used: false), sonra kullanılanlar (is_used: true)
                    if (a.is_used !== b.is_used) {
                      return a.is_used ? 1 : -1;
                    }
                    // Aynı durumda olanları order_index'e göre sırala
                    return a.order_index - b.order_index;
                  })
                  .map((prompt) => (
                    <SortablePromptItem
                      key={prompt.id}
                      prompt={prompt}
                      isMultiSelectMode={isMultiSelectMode}
                      selectedPrompts={selectedPrompts}
                      editingPromptId={editingPromptId}
                      editingText={editingText}
                      editTextareaRef={editTextareaRef}
                      onSelectPrompt={handleSelectPrompt}
                      onEditPrompt={handleEditPrompt}
                      onSaveEdit={handleSaveEdit}
                      onCancelEdit={handleCancelEdit}
                      onCopyPrompt={handleCopyPrompt}
                      setEditingText={setEditingText}
                      onHashtagFilter={handleHashtagFilter}
                    />
                  ))}
              </SortableContext>
            </DndContext>
          )}
        </div>
      </ScrollArea>

      {/* Enhanced Context Gallery Modal */}
      <Suspense fallback={
        isContextGalleryOpen ? (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
              <ContextGallerySkeleton />
            </div>
          </div>
        ) : null
      }>
        <EnhancedContextGalleryModal
          open={isContextGalleryOpen}
          onOpenChange={onToggleContextGallery || (() => {})}
          onSelectContext={handleSelectContextFromGallery}
        />
      </Suspense>

      {/* Enhanced Sticky Add New Prompt - Redesigned Interface */}
      <div className="fixed bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white to-white/95 border-t border-gray-200 shadow-xl safe-area-bottom z-[60] backdrop-blur-sm">
        <div className="max-w-6xl mx-auto">
          {/* Mobile: Enhanced two-row layout */}
          <div className="block lg:hidden">
            {/* Top row: Redesigned toolbar with better organization */}
            <div className="p-3 border-b border-gray-100 bg-gray-50/50">
              <div className="flex justify-center mb-2">
                <Button
                  variant={isMultiSelectMode ? "default" : "outline"}
                  size="sm"
                  onClick={handleToggleMultiSelect}
                  className={`min-h-[44px] px-3 transition-all duration-200 ${isMultiSelectMode ? "text-white bg-blue-600 hover:bg-blue-700 shadow-md" : "text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300 hover:bg-blue-50"}`}
                >
                  <MousePointer className="h-4 w-4 mr-1" />
                  <span className="text-xs font-medium">{isMultiSelectMode ? 'Seçim İptal' : 'Çoklu Seç'}</span>
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-2">
                {isMultiSelectMode && selectedPrompts.size > 0 && (
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleCopySelectedPrompts}
                    className="text-white bg-green-600 hover:bg-green-700 min-h-[40px] px-2 shadow-md transition-all duration-200"
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    <span className="text-xs font-medium">Kopyala ({selectedPrompts.size})</span>
                  </Button>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportUnusedPrompts}
                  className="text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[40px] px-2 transition-all duration-200"
                >
                  <Download className="h-4 w-4 mr-1" />
                  <span className="text-xs font-medium">Export</span>
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleUndoLastCopy}
                  className="text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[40px] px-2 transition-all duration-200"
                >
                  <Undo className="h-4 w-4 mr-1" />
                  <span className="text-xs font-medium">Geri Al</span>
                </Button>
              </div>
            </div>
            
            {/* Enhanced Mobile-First Input Section */}
            <div className="p-2 sm:p-3 relative z-[60]">
              {/* Mobile: Stacked layout for small screens */}
              <div className="block sm:hidden space-y-2">
                <div className="relative">
                  <SmartAutocomplete
                    value={newPromptText}
                    onChange={setNewPromptText}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleAddPrompt();
                      }
                    }}
                    placeholder="Prompt yazın... (/ klasör, # etiket)"
                    className="resize-none text-base border-2 focus:border-blue-500 transition-colors duration-200 pr-16 w-full rounded-lg p-3 touch-manipulation"
                    suggestions={{
                      hashtags: allHashtags || [],
                      folders: allCategories || []
                    }}
                    enableDynamicHeight={true}
                    heightConfig={{
                      minHeight: 48,
                      maxHeightFraction: 0.25, // 1/4 for mobile
                      baseHeight: 48
                    }}
                  />

                </div>

                {/* Mobile: Button row */}
                <div className="flex gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleToggleMultiSelect}
                    className={`flex-1 min-h-[44px] transition-all duration-200 touch-manipulation ${isMultiSelectMode ? "text-white bg-blue-600 hover:bg-blue-700" : "text-blue-600 hover:text-blue-700 border-blue-200"}`}
                  >
                    <MousePointer className="h-4 w-4 mr-1" />
                    <span className="text-xs">Seç</span>
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExportUnusedPrompts}
                    className="flex-1 text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[44px] touch-manipulation"
                  >
                    <Download className="h-4 w-4 mr-1" />
                    <span className="text-xs">Export</span>
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleUndoLastCopy}
                    className="flex-1 text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[44px] touch-manipulation"
                  >
                    <Undo className="h-4 w-4 mr-1" />
                    <span className="text-xs">Geri</span>
                  </Button>

                  <Button
                    onClick={handleAddPrompt}
                    disabled={!newPromptText.trim()}
                    size="default"
                    className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white min-h-[44px] shadow-lg hover:shadow-xl transition-all duration-200 font-medium touch-manipulation"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    <span className="text-xs">Ekle</span>
                  </Button>
                </div>
              </div>

              {/* Tablet/Desktop: Side-by-side layout */}
              <div className="hidden sm:flex gap-2 items-end relative z-[60]">
                <div className="flex-1 relative">
                  <SmartAutocomplete
                    value={newPromptText}
                    onChange={setNewPromptText}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleAddPrompt();
                      }
                    }}
                    placeholder="Prompt metninizi buraya yazın... (/ için klasör, # için etiket)"
                    className="resize-none text-base border-2 focus:border-blue-500 transition-colors duration-200 pr-16 w-full rounded-lg p-3"
                    suggestions={{
                      hashtags: allHashtags || [],
                      folders: allCategories || []
                    }}
                    enableDynamicHeight={true}
                    heightConfig={{
                      minHeight: 44,
                      maxHeightFraction: 0.33, // 1/3 for tablet
                      baseHeight: 44
                    }}
                  />

                </div>

                {/* Tablet/Desktop: Compact button row */}
                <div className="flex gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleToggleMultiSelect}
                    className={`min-h-[44px] px-2 transition-all duration-200 ${isMultiSelectMode ? "text-white bg-blue-600 hover:bg-blue-700" : "text-blue-600 hover:text-blue-700 border-blue-200"}`}
                  >
                    <MousePointer className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExportUnusedPrompts}
                    className="text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 hover:bg-purple-50 min-h-[44px] px-2"
                  >
                    <Download className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleUndoLastCopy}
                    className="text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300 hover:bg-orange-50 min-h-[44px] px-2"
                  >
                    <Undo className="h-4 w-4" />
                  </Button>

                  <Button
                    onClick={handleAddPrompt}
                    disabled={!newPromptText.trim()}
                    size="default"
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white px-4 min-h-[44px] shrink-0 shadow-lg hover:shadow-xl transition-all duration-200 font-medium"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Ekle
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Desktop: Optimized compact layout */}
          <div className="hidden lg:block p-4 bg-gradient-to-r from-gray-50 to-white border-t border-gray-100 relative z-[60]">
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 p-4">
              <div className="flex gap-4 items-end">
                {/* Smart Autocomplete Textarea */}
                <div className="flex-1 relative">
                  <SmartAutocomplete
                    value={newPromptText}
                    onChange={setNewPromptText}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleAddPrompt();
                      }
                    }}
                    placeholder="Prompt metninizi buraya yazın... (/ için klasör, # için etiket)"
                    className="resize-none border-0 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg bg-gray-50 hover:bg-white transition-all duration-200 text-base pr-20 w-full p-3"
                    suggestions={{
                      hashtags: allHashtags || [],
                      folders: allCategories || []
                    }}
                    enableDynamicHeight={true}
                    heightConfig={{
                      minHeight: 56,
                      maxHeightFraction: 0.33, // 1/3 for desktop
                      baseHeight: 56
                    }}
                  />


                    {/* Modern Advanced Categorization Fields */}
                    {showAdvancedForm && (
                      <div className="space-y-4 pt-4 border-t border-gray-200 bg-gray-50 rounded-lg p-4 mt-3">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-1 h-4 bg-purple-500 rounded-full"></div>
                          <span className="text-sm font-medium text-gray-700">Kategorilendirme</span>
                        </div>

                        {/* Modern Title Field */}
                        <div>
                          <label className="text-sm font-medium text-gray-700 mb-2 block">
                            Başlık (İsteğe bağlı)
                          </label>
                          <Input
                            type="text"
                            placeholder="Bu prompt için kısa bir başlık..."
                            value={newPromptTitle}
                            onChange={(e) => setNewPromptTitle(e.target.value)}
                            className="border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                          />
                        </div>

                        {/* Modern Hashtags */}
                        <div>
                          <label className="text-sm font-medium text-gray-700 mb-2 block">
                            Etiketler
                          </label>
                          {activeProjectId && (
                            <HashtagInput
                              hashtags={newPromptHashtags}
                              onHashtagsChange={setNewPromptHashtags}
                              suggestions={allHashtags || []}
                              placeholder="Etiket ekleyin... (örn: #frontend, #api)"
                              maxTags={5}
                            />
                          )}
                        </div>

                        {/* Modern Category */}
                        <div>
                          <label className="text-sm font-medium text-gray-700 mb-2 block">
                            Klasör/Kategori
                          </label>
                          {activeProjectId && (
                            <CategorySelector
                              category={newPromptCategory}
                              onCategoryChange={setNewPromptCategory}
                              suggestions={allCategories || []}
                              placeholder="Klasör seçin... (örn: /frontend, /admin)"
                            />
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Prompt Limit Warning */}
                  {limits && (
                    <InlineLimitWarning
                      type="prompt"
                      current={limits.current_prompts}
                      max={limits.max_prompts_per_project}
                      onUpgrade={() => {/* Plan upgrade modal açılacak */}}
                    />
                  )}

                  {/* Add Prompt Button - Moved from bottom */}
                  <Button
                    onClick={handleAddPrompt}
                    disabled={!newPromptText.trim() || (limits && !limits.can_create_prompt)}
                    size="lg"
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-300 disabled:to-gray-400 text-white px-8 py-3 min-h-[56px] shadow-lg hover:shadow-xl transition-all duration-200 font-semibold rounded-lg"
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    Prompt Ekle
                  </Button>
                </div>

                {/* Action buttons row */}
                <div className="space-y-3">

                  {/* Reorganized buttons */}
                  <div className="flex gap-2">
                    <Button
                      variant={isMultiSelectMode ? "default" : "outline"}
                      size="sm"
                      onClick={handleToggleMultiSelect}
                      className={`transition-all duration-200 rounded-lg font-medium ${
                        isMultiSelectMode
                          ? "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md"
                          : "border-blue-200 text-blue-600 hover:text-blue-700 hover:border-blue-300 hover:bg-blue-50"
                      }`}
                    >
                      <MousePointer className="h-4 w-4 mr-2" />
                      Çoklu Seç
                    </Button>

                    {isMultiSelectMode && selectedPrompts.size > 0 && (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={handleCopySelectedPrompts}
                        className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-md transition-all duration-200 rounded-lg font-medium"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Kopyala ({selectedPrompts.size})
                      </Button>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleExportUnusedPrompts}
                      className="border-purple-200 text-purple-600 hover:text-purple-700 hover:border-purple-300 hover:bg-purple-50 transition-all duration-200 rounded-lg font-medium"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleUndoLastCopy}
                      className="border-orange-200 text-orange-600 hover:text-orange-700 hover:border-orange-300 hover:bg-orange-50 transition-all duration-200 rounded-lg font-medium"
                    >
                      <Undo className="h-4 w-4 mr-2" />
                      Geri Al
                    </Button>

                    {/* Advanced Form Toggle - Moved from top */}
                    {!newPromptText.trim() && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setShowAdvancedForm(!showAdvancedForm)}
                        className="border-gray-300 text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition-all duration-200"
                      >
                        {showAdvancedForm ? (
                          <>
                            <ChevronUp className="w-4 h-4 mr-2" />
                            Kategorileri Gizle
                          </>
                        ) : (
                          <>
                            <ChevronDown className="w-4 h-4 mr-2" />
                            Kategori Ekle
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      {/* Hashtags Sidebar */}
      {showHashtagsSidebar && activeProjectId && (
        <PopularHashtagsSidebar
          projectId={activeProjectId}
          onHashtagClick={handleHashtagFilter}
          onCategoryClick={handleCategoryFilter}
          selectedHashtags={filterHashtags}
          selectedCategory={filterCategory}
          className="hidden lg:flex"
        />
      )}
    </div>
  );
}

export default PromptWorkspace;