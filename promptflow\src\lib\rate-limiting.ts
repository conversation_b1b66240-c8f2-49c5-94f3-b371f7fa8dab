/**
 * Advanced Rate Limiting System
 * Comprehensive rate limiting with multiple strategies
 */

import { NextRequest } from 'next/server'

// Rate limiting configurations
export const RATE_LIMITS = {
  // Authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per window
    blockDuration: 30 * 60 * 1000, // 30 minutes block
  },
  
  // API endpoints
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
    blockDuration: 5 * 60 * 1000, // 5 minutes block
  },
  
  // Form submissions
  forms: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 submissions per minute
    blockDuration: 10 * 60 * 1000, // 10 minutes block
  },
  
  // File uploads
  uploads: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // 5 uploads per minute
    blockDuration: 15 * 60 * 1000, // 15 minutes block
  },
  
  // Admin operations
  admin: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50, // 50 requests per minute
    blockDuration: 5 * 60 * 1000, // 5 minutes block
  }
} as const

export type RateLimitType = keyof typeof RATE_LIMITS

interface RateLimitEntry {
  count: number
  windowStart: number
  blocked?: boolean
  blockExpiry?: number
  lastRequest: number
}

// In-memory store (in production, use Redis or database)
const rateLimitStore = new Map<string, RateLimitEntry>()

/**
 * Generate rate limit key
 */
function generateRateLimitKey(
  identifier: string, 
  type: RateLimitType, 
  additionalContext?: string
): string {
  const context = additionalContext ? `-${additionalContext}` : ''
  return `ratelimit:${type}:${identifier}${context}`
}

/**
 * Get client identifier from request
 */
export function getClientIdentifier(request: NextRequest): string {
  // Try to get user ID from auth (if available)
  const userId = request.headers.get('x-user-id')
  if (userId) return `user:${userId}`
  
  // Fallback to IP address
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0].trim() : 
             request.headers.get('x-real-ip') || 
             'unknown'
  
  return `ip:${ip}`
}

/**
 * Check if request should be rate limited
 */
export function checkRateLimit(
  identifier: string,
  type: RateLimitType,
  additionalContext?: string
): {
  allowed: boolean
  remaining: number
  resetTime: number
  blocked?: boolean
  blockExpiry?: number
} {
  const key = generateRateLimitKey(identifier, type, additionalContext)
  const config = RATE_LIMITS[type]
  const now = Date.now()
  
  let entry = rateLimitStore.get(key)
  
  // Check if currently blocked
  if (entry?.blocked && entry.blockExpiry && now < entry.blockExpiry) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: entry.blockExpiry,
      blocked: true,
      blockExpiry: entry.blockExpiry
    }
  }
  
  // Initialize or reset window
  if (!entry || now - entry.windowStart > config.windowMs) {
    entry = {
      count: 0,
      windowStart: now,
      lastRequest: now,
      blocked: false
    }
  }
  
  // Increment counter
  entry.count++
  entry.lastRequest = now
  
  // Check if limit exceeded
  if (entry.count > config.maxRequests) {
    // Block the identifier
    entry.blocked = true
    entry.blockExpiry = now + config.blockDuration
    
    rateLimitStore.set(key, entry)
    
    return {
      allowed: false,
      remaining: 0,
      resetTime: entry.blockExpiry,
      blocked: true,
      blockExpiry: entry.blockExpiry
    }
  }
  
  // Update store
  rateLimitStore.set(key, entry)
  
  const remaining = Math.max(0, config.maxRequests - entry.count)
  const resetTime = entry.windowStart + config.windowMs
  
  return {
    allowed: true,
    remaining,
    resetTime
  }
}

/**
 * Rate limiting middleware
 */
export function rateLimitMiddleware(
  request: NextRequest,
  type: RateLimitType,
  additionalContext?: string
): {
  allowed: boolean
  headers: Record<string, string>
  status?: number
  message?: string
} {
  const identifier = getClientIdentifier(request)
  const result = checkRateLimit(identifier, type, additionalContext)
  
  const headers: Record<string, string> = {
    'X-RateLimit-Limit': RATE_LIMITS[type].maxRequests.toString(),
    'X-RateLimit-Remaining': result.remaining.toString(),
    'X-RateLimit-Reset': Math.ceil(result.resetTime / 1000).toString(),
  }

  if (result.blocked && result.blockExpiry) {
    headers['X-RateLimit-Blocked-Until'] = Math.ceil(result.blockExpiry / 1000).toString()
  }
  
  if (!result.allowed) {
    const message = result.blocked 
      ? `Rate limit exceeded. Blocked until ${new Date(result.blockExpiry!).toISOString()}`
      : 'Rate limit exceeded'
    
    return {
      allowed: false,
      headers,
      status: 429,
      message
    }
  }
  
  return {
    allowed: true,
    headers
  }
}

/**
 * Clean up expired entries (should be called periodically)
 */
export function cleanupRateLimitStore(): void {
  const now = Date.now()
  const maxAge = Math.max(...Object.values(RATE_LIMITS).map(config => 
    config.windowMs + config.blockDuration
  ))
  
  for (const [key, entry] of rateLimitStore.entries()) {
    // Remove entries that are older than the maximum possible age
    if (now - entry.lastRequest > maxAge) {
      rateLimitStore.delete(key)
    }
    
    // Remove entries that are no longer blocked and outside window
    if (entry.blocked && entry.blockExpiry && now > entry.blockExpiry) {
      const windowExpired = now - entry.windowStart > maxAge
      if (windowExpired) {
        rateLimitStore.delete(key)
      } else {
        // Reset block status but keep entry for window tracking
        entry.blocked = false
        delete entry.blockExpiry
        rateLimitStore.set(key, entry)
      }
    }
  }
}

/**
 * Get rate limit status for a client
 */
export function getRateLimitStatus(
  identifier: string,
  type: RateLimitType,
  additionalContext?: string
): {
  isBlocked: boolean
  remaining: number
  resetTime: number
  blockExpiry?: number
} {
  const key = generateRateLimitKey(identifier, type, additionalContext)
  const entry = rateLimitStore.get(key)
  const config = RATE_LIMITS[type]
  const now = Date.now()
  
  if (!entry) {
    return {
      isBlocked: false,
      remaining: config.maxRequests,
      resetTime: now + config.windowMs
    }
  }
  
  const isBlocked = Boolean(entry.blocked && entry.blockExpiry && now < entry.blockExpiry)
  const remaining = Math.max(0, config.maxRequests - entry.count)
  const resetTime = entry.windowStart + config.windowMs
  
  return {
    isBlocked,
    remaining,
    resetTime,
    blockExpiry: entry.blockExpiry
  }
}

/**
 * React hook for client-side rate limit awareness
 */
export function useRateLimit(type: RateLimitType) {
  const checkClientRateLimit = (): boolean => {
    if (typeof window === 'undefined') return true
    
    const key = `client-rate-limit-${type}`
    const stored = localStorage.getItem(key)
    const now = Date.now()
    const config = RATE_LIMITS[type]
    
    if (!stored) {
      localStorage.setItem(key, JSON.stringify({
        count: 1,
        windowStart: now
      }))
      return true
    }
    
    try {
      const data = JSON.parse(stored)
      
      // Check if window expired
      if (now - data.windowStart > config.windowMs) {
        localStorage.setItem(key, JSON.stringify({
          count: 1,
          windowStart: now
        }))
        return true
      }
      
      // Check limit
      if (data.count >= config.maxRequests) {
        return false
      }
      
      // Increment and store
      data.count++
      localStorage.setItem(key, JSON.stringify(data))
      return true
      
    } catch (error) {
      console.warn('Rate limit check failed:', error)
      return true // Fail open
    }
  }
  
  return {
    checkClientRateLimit,
    config: RATE_LIMITS[type]
  }
}

// Cleanup interval (run every 5 minutes)
if (typeof window === 'undefined') {
  setInterval(cleanupRateLimitStore, 5 * 60 * 1000)
}
