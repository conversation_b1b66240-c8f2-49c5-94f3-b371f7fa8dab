/**
 * Cross-Device Testing and Validation
 * Comprehensive testing framework for different device and browser combinations
 */

// Device and browser test configurations
export const TEST_DEVICES = {
  mobile: [
    { name: 'iPhone SE', width: 375, height: 667, userAgent: 'iPhone', pixelRatio: 2 },
    { name: 'iPhone 12', width: 390, height: 844, userAgent: 'iPhone', pixelRatio: 3 },
    { name: 'iPhone 14 Pro Max', width: 430, height: 932, userAgent: 'iPhone', pixelRatio: 3 },
    { name: 'Samsung Galaxy S21', width: 360, height: 800, userAgent: 'Android', pixelRatio: 3 },
    { name: 'Google Pixel 6', width: 393, height: 851, userAgent: 'Android', pixelRatio: 2.75 },
    { name: 'OnePlus 9', width: 412, height: 915, userAgent: 'Android', pixelRatio: 3.5 }
  ],
  
  tablet: [
    { name: 'iPad', width: 768, height: 1024, userAgent: 'iPad', pixelRatio: 2 },
    { name: 'iPad Pro 11"', width: 834, height: 1194, userAgent: 'iPad', pixelRatio: 2 },
    { name: 'iPad Pro 12.9"', width: 1024, height: 1366, userAgent: 'iPad', pixelRatio: 2 },
    { name: 'Samsung Galaxy Tab S7', width: 753, height: 1037, userAgent: 'Android', pixelRatio: 2.4 },
    { name: 'Surface Pro 8', width: 880, height: 1240, userAgent: 'Windows', pixelRatio: 1.5 }
  ],
  
  desktop: [
    { name: 'MacBook Air', width: 1440, height: 900, userAgent: 'Macintosh', pixelRatio: 2 },
    { name: 'MacBook Pro 16"', width: 1728, height: 1117, userAgent: 'Macintosh', pixelRatio: 2 },
    { name: 'Windows Laptop', width: 1366, height: 768, userAgent: 'Windows', pixelRatio: 1 },
    { name: 'Full HD Monitor', width: 1920, height: 1080, userAgent: 'Windows', pixelRatio: 1 },
    { name: '4K Monitor', width: 3840, height: 2160, userAgent: 'Windows', pixelRatio: 2 }
  ]
} as const

export const TEST_BROWSERS = {
  chrome: {
    name: 'Chrome',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    features: ['webp', 'avif', 'css-grid', 'flexbox', 'css-variables']
  },
  firefox: {
    name: 'Firefox',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
    features: ['webp', 'css-grid', 'flexbox', 'css-variables']
  },
  safari: {
    name: 'Safari',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    features: ['webp', 'css-grid', 'flexbox', 'css-variables', 'safe-area-insets']
  },
  edge: {
    name: 'Edge',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
    features: ['webp', 'avif', 'css-grid', 'flexbox', 'css-variables']
  }
} as const

// Test result types
interface TestResult {
  device: string
  browser: string
  viewport: { width: number; height: number }
  passed: boolean
  issues: Array<{
    type: 'layout' | 'performance' | 'accessibility' | 'functionality'
    severity: 'critical' | 'major' | 'minor'
    description: string
    element?: string
  }>
  metrics: {
    loadTime: number
    renderTime: number
    layoutShifts: number
    touchTargetIssues: number
    accessibilityScore: number
  }
}

// Cross-device testing framework
export class CrossDeviceTester {
  private results: TestResult[] = []
  private currentTest: Partial<TestResult> = {}

  // Run comprehensive cross-device tests
  static async runFullTestSuite(): Promise<TestResult[]> {
    const tester = new CrossDeviceTester()
    
    // Test all device categories
    for (const category of ['mobile', 'tablet', 'desktop'] as const) {
      const devices = TEST_DEVICES[category]
      
      for (const device of devices) {
        for (const browser of Object.values(TEST_BROWSERS)) {
          await tester.testDeviceBrowserCombination(device, browser)
        }
      }
    }
    
    return tester.results
  }

  // Test specific device-browser combination
  private async testDeviceBrowserCombination(
    device: any,
    browser: any
  ) {
    console.log(`🧪 Testing ${device.name} with ${browser.name}`)
    
    this.currentTest = {
      device: device.name,
      browser: browser.name,
      viewport: { width: device.width, height: device.height },
      issues: [],
      metrics: {
        loadTime: 0,
        renderTime: 0,
        layoutShifts: 0,
        touchTargetIssues: 0,
        accessibilityScore: 0
      }
    }

    // Simulate device viewport
    await this.simulateDevice(device)
    
    // Simulate browser environment
    await this.simulateBrowser(browser)
    
    // Run tests
    await this.runLayoutTests()
    await this.runPerformanceTests()
    await this.runAccessibilityTests()
    await this.runFunctionalityTests()
    
    // Calculate overall pass/fail
    const criticalIssues = this.currentTest.issues?.filter(i => i.severity === 'critical').length || 0
    this.currentTest.passed = criticalIssues === 0
    
    this.results.push(this.currentTest as TestResult)
  }

  // Simulate device viewport and characteristics
  private async simulateDevice(device: typeof TEST_DEVICES.mobile[0]) {
    // Set viewport size
    Object.defineProperty(window, 'innerWidth', { value: device.width, writable: true })
    Object.defineProperty(window, 'innerHeight', { value: device.height, writable: true })
    Object.defineProperty(window, 'devicePixelRatio', { value: device.pixelRatio, writable: true })
    
    // Set screen properties
    Object.defineProperty(window.screen, 'width', { value: device.width * device.pixelRatio })
    Object.defineProperty(window.screen, 'height', { value: device.height * device.pixelRatio })
    
    // Trigger resize event
    window.dispatchEvent(new Event('resize'))
    
    // Wait for layout to settle
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  // Simulate browser environment
  private async simulateBrowser(browser: any) {
    // Set user agent
    Object.defineProperty(navigator, 'userAgent', { value: browser.userAgent, writable: true })
    
    // Simulate browser feature support
    browser.features.forEach((feature: any) => {
      switch (feature) {
        case 'webp':
          // Mock WebP support
          break
        case 'avif':
          // Mock AVIF support
          break
        case 'css-grid':
          // Mock CSS Grid support
          break
        case 'safe-area-insets':
          // Mock safe area support
          document.documentElement.style.setProperty('--safe-area-inset-top', '44px')
          break
      }
    })
  }

  // Run layout tests
  private async runLayoutTests() {
    const startTime = performance.now()
    
    // Check for horizontal scrolling
    if (document.body.scrollWidth > window.innerWidth) {
      this.addIssue({
        type: 'layout',
        severity: 'critical',
        description: 'Horizontal scrolling detected',
        element: 'body'
      })
    }
    
    // Check for elements outside viewport
    const elements = document.querySelectorAll('*')
    elements.forEach(element => {
      const rect = element.getBoundingClientRect()
      if (rect.right > window.innerWidth + 10) { // 10px tolerance
        this.addIssue({
          type: 'layout',
          severity: 'major',
          description: 'Element extends beyond viewport',
          element: this.getElementSelector(element as HTMLElement)
        })
      }
    })
    
    // Check for proper responsive breakpoints
    const computedStyle = getComputedStyle(document.body)
    const fontSize = parseFloat(computedStyle.fontSize)
    
    if (window.innerWidth < 768 && fontSize < 16) {
      this.addIssue({
        type: 'layout',
        severity: 'minor',
        description: 'Font size too small for mobile',
        element: 'body'
      })
    }
    
    this.currentTest.metrics!.renderTime = performance.now() - startTime
  }

  // Run performance tests
  private async runPerformanceTests() {
    const startTime = performance.now()
    
    // Check image optimization
    const images = document.querySelectorAll('img')
    images.forEach(img => {
      if (!img.hasAttribute('loading')) {
        this.addIssue({
          type: 'performance',
          severity: 'minor',
          description: 'Image missing lazy loading',
          element: this.getElementSelector(img)
        })
      }
      
      if (!img.hasAttribute('srcset') && img.naturalWidth > window.innerWidth * 2) {
        this.addIssue({
          type: 'performance',
          severity: 'major',
          description: 'Image not optimized for device resolution',
          element: this.getElementSelector(img)
        })
      }
    })
    
    // Check for large bundle sizes (simulated)
    const scripts = document.querySelectorAll('script[src]')
    if (scripts.length > 10) {
      this.addIssue({
        type: 'performance',
        severity: 'minor',
        description: 'Too many script files',
        element: 'head'
      })
    }
    
    this.currentTest.metrics!.loadTime = performance.now() - startTime
  }

  // Run accessibility tests
  private async runAccessibilityTests() {
    let score = 100
    
    // Check touch targets
    const interactiveElements = document.querySelectorAll('button, a, input, select, textarea')
    let touchTargetIssues = 0
    
    interactiveElements.forEach(element => {
      const rect = element.getBoundingClientRect()
      const minSize = window.innerWidth < 768 ? 44 : 40 // Smaller requirement for desktop
      
      if (rect.width < minSize || rect.height < minSize) {
        touchTargetIssues++
        this.addIssue({
          type: 'accessibility',
          severity: 'major',
          description: `Touch target too small: ${Math.round(rect.width)}x${Math.round(rect.height)}px`,
          element: this.getElementSelector(element as HTMLElement)
        })
        score -= 5
      }
    })
    
    // Check for alt text on images
    const images = document.querySelectorAll('img')
    images.forEach(img => {
      if (!img.hasAttribute('alt')) {
        this.addIssue({
          type: 'accessibility',
          severity: 'critical',
          description: 'Image missing alt text',
          element: this.getElementSelector(img)
        })
        score -= 10
      }
    })
    
    // Check for proper heading structure
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
    let previousLevel = 0
    
    headings.forEach(heading => {
      const level = parseInt(heading.tagName.charAt(1))
      if (level > previousLevel + 1) {
        this.addIssue({
          type: 'accessibility',
          severity: 'minor',
          description: 'Heading level skipped',
          element: this.getElementSelector(heading as HTMLElement)
        })
        score -= 2
      }
      previousLevel = level
    })
    
    this.currentTest.metrics!.touchTargetIssues = touchTargetIssues
    this.currentTest.metrics!.accessibilityScore = Math.max(0, score)
  }

  // Run functionality tests
  private async runFunctionalityTests() {
    // Test form functionality
    const forms = document.querySelectorAll('form')
    forms.forEach(form => {
      const inputs = form.querySelectorAll('input, select, textarea')
      inputs.forEach(input => {
        // Check for proper labels
        const hasLabel = input.hasAttribute('aria-label') || 
                         input.hasAttribute('aria-labelledby') ||
                         form.querySelector(`label[for="${input.id}"]`)
        
        if (!hasLabel) {
          this.addIssue({
            type: 'functionality',
            severity: 'major',
            description: 'Form input missing label',
            element: this.getElementSelector(input as HTMLElement)
          })
        }
      })
    })
    
    // Test navigation functionality
    const navLinks = document.querySelectorAll('nav a, [role="navigation"] a')
    navLinks.forEach(link => {
      if (!link.hasAttribute('href') || link.getAttribute('href') === '#') {
        this.addIssue({
          type: 'functionality',
          severity: 'minor',
          description: 'Navigation link missing proper href',
          element: this.getElementSelector(link as HTMLElement)
        })
      }
    })
  }

  // Helper methods
  private addIssue(issue: TestResult['issues'][0]) {
    this.currentTest.issues = this.currentTest.issues || []
    this.currentTest.issues.push(issue)
  }

  private getElementSelector(element: HTMLElement): string {
    if (element.id) return `#${element.id}`
    if (element.className) return `.${element.className.split(' ')[0]}`
    return element.tagName.toLowerCase()
  }
}

// Test report generator
export class TestReportGenerator {
  static generateReport(results: TestResult[]): string {
    const totalTests = results.length
    const passedTests = results.filter(r => r.passed).length
    const failedTests = totalTests - passedTests
    
    const criticalIssues = results.reduce((sum, r) => 
      sum + r.issues.filter(i => i.severity === 'critical').length, 0)
    const majorIssues = results.reduce((sum, r) => 
      sum + r.issues.filter(i => i.severity === 'major').length, 0)
    const minorIssues = results.reduce((sum, r) => 
      sum + r.issues.filter(i => i.severity === 'minor').length, 0)
    
    return `
# Cross-Device Testing Report

## Summary
- **Total Tests**: ${totalTests}
- **Passed**: ${passedTests} (${Math.round(passedTests/totalTests*100)}%)
- **Failed**: ${failedTests} (${Math.round(failedTests/totalTests*100)}%)

## Issues
- **Critical**: ${criticalIssues}
- **Major**: ${majorIssues}
- **Minor**: ${minorIssues}

## Device Categories
${this.generateCategoryReport(results, 'mobile')}
${this.generateCategoryReport(results, 'tablet')}
${this.generateCategoryReport(results, 'desktop')}

## Detailed Results
${results.map(result => this.generateDetailedResult(result)).join('\n')}
    `.trim()
  }

  private static generateCategoryReport(results: TestResult[], category: string): string {
    const categoryDevices = TEST_DEVICES[category as keyof typeof TEST_DEVICES]
    const deviceNames = categoryDevices.map(d => d.name)
    const categoryResults = results.filter(r => deviceNames.includes(r.device as any))
    
    const passed = categoryResults.filter(r => r.passed).length
    const total = categoryResults.length
    
    return `
### ${category.charAt(0).toUpperCase() + category.slice(1)} Devices
- **Pass Rate**: ${total > 0 ? Math.round(passed/total*100) : 0}% (${passed}/${total})
    `.trim()
  }

  private static generateDetailedResult(result: TestResult): string {
    const status = result.passed ? '✅' : '❌'
    const issueCount = result.issues.length
    
    return `
### ${status} ${result.device} - ${result.browser}
- **Viewport**: ${result.viewport.width}x${result.viewport.height}
- **Issues**: ${issueCount}
- **Load Time**: ${result.metrics.loadTime.toFixed(2)}ms
- **Accessibility Score**: ${result.metrics.accessibilityScore}/100
    `.trim()
  }
}

// Auto-run tests in development
export function enableAutoTesting() {
  if (process.env.NODE_ENV !== 'development') return
  
  console.log('🧪 Cross-device testing enabled')
  
  // Run tests on page load
  window.addEventListener('load', async () => {
    console.log('🚀 Running cross-device tests...')
    const results = await CrossDeviceTester.runFullTestSuite()
    const report = TestReportGenerator.generateReport(results)
    
    console.group('📊 Cross-Device Test Results')
    console.log(report)
    console.groupEnd()
  })
}
