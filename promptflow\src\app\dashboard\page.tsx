'use client'

import { useState, Suspense, useEffect, memo } from "react";
import { ProjectSidebar } from "@/components/project-sidebar";
import { AuthGuard } from "@/components/auth-guard";
import { ErrorBoundary } from "@/components/error-boundary";
import { useResponsive } from "@/hooks/use-responsive";
import { Button } from "@/components/ui/button";
import { <PERSON>u, Settings } from "lucide-react";
import { useAppStore } from "@/store/app-store";

// Optimized lazy loading with caching
import {
  LazyPromptWorkspace,
  LazyContextSidebar,
  preloadPageImports
} from "@/lib/dynamic-imports";

const Dashboard = memo(function Dashboard() {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [isMobileContextOpen, setIsMobileContextOpen] = useState(false);
  const [isContextGalleryOpen, setIsContextGalleryOpen] = useState(false);

  // Responsive state
  const { isMobile, orientation } = useResponsive();

  // Zustand store for sidebar collapse states
  const {
    isProjectSidebarCollapsed,
    setIsProjectSidebarCollapsed,
    isContextSidebarCollapsed,
    setIsContextSidebarCollapsed
  } = useAppStore();

  // Preload critical imports on component mount
  useEffect(() => {
    try {
      preloadPageImports('dashboard')
    } catch (error) {
      console.warn('Failed to preload dashboard imports:', error)
    }
  }, [])

  // Auto-close mobile sidebars on orientation change
  useEffect(() => {
    if (isMobile) {
      setIsMobileSidebarOpen(false);
      setIsMobileContextOpen(false);
    }
  }, [orientation, isMobile]);

  return (
    <AuthGuard>
      <ErrorBoundary level="page" showDetails={process.env.NODE_ENV === 'development'}>
      <div className={`
        flex full-height-mobile bg-gray-50 relative
        ${isMobile ? 'flex-col' : 'flex-row'}
      `}>
        {/* Mobile Overlay */}
        {(isMobileSidebarOpen || isMobileContextOpen) && (
          <div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden mobile-transition"
            onClick={() => {
              setIsMobileSidebarOpen(false);
              setIsMobileContextOpen(false);
            }}
          />
        )}

        {/* Sol Sütun - Proje Listesi */}
        <div className={`
          fixed lg:relative inset-y-0 left-0 z-50
          w-[85vw] sm:w-80
          ${isProjectSidebarCollapsed ? 'lg:w-16' : 'lg:w-80'}
          transform transition-all duration-300 ease-in-out
          lg:transform-none lg:translate-x-0
          border-r border-gray-200 bg-white
          ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          ${isMobileSidebarOpen ? 'block' : 'hidden lg:block'}
        `}>
          <ProjectSidebar
            onClose={() => setIsMobileSidebarOpen(false)}
            isCollapsed={isProjectSidebarCollapsed}
            onToggleCollapse={() => setIsProjectSidebarCollapsed(!isProjectSidebarCollapsed)}
          />
        </div>

        {/* Orta Sütun - Prompt Workspace */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* Mobile Header */}
          <div className="lg:hidden flex items-center justify-between p-4 bg-white border-b border-gray-200 safe-area-top">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileSidebarOpen(true)}
              className="flex items-center gap-2 touch-target focus-visible-enhanced"
            >
              <Menu className="h-5 w-5" />
              <span className="font-medium mobile-text-base">Projeler</span>
            </Button>
            
            <h1 className="text-lg font-semibold text-gray-900 mobile-text-base">Promptbir</h1>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileContextOpen(true)}
              className="flex items-center gap-2 touch-target focus-visible-enhanced"
            >
              <Settings className="h-5 w-5" />
              <span className="font-medium mobile-text-base">Ayarlar</span>
            </Button>
          </div>

          <Suspense fallback={
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          }>
            <LazyPromptWorkspace
              {...{
                isContextGalleryOpen,
                onToggleContextGallery: () => setIsContextGalleryOpen(!isContextGalleryOpen)
              } as any}
            />
          </Suspense>
        </div>

        {/* Sağ Sütun - Context Sidebar */}
        <div className={`
          fixed xl:relative inset-y-0 right-0 z-50
          w-[90vw] sm:w-96
          ${isContextSidebarCollapsed ? 'xl:w-16' : 'xl:w-96'}
          transform transition-all duration-300 ease-in-out
          xl:transform-none xl:translate-x-0
          border-l border-gray-200 bg-white
          ${isMobileContextOpen ? 'translate-x-0' : 'translate-x-full xl:translate-x-0'}
          ${isMobileContextOpen ? 'block' : 'hidden xl:block'}
        `}>
          <Suspense fallback={
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          }>
            <LazyContextSidebar
              {...{
                onClose: () => setIsMobileContextOpen(false),
                isCollapsed: isContextSidebarCollapsed,
                onToggleCollapse: () => setIsContextSidebarCollapsed(!isContextSidebarCollapsed),
                isContextGalleryOpen,
                onToggleContextGallery: () => setIsContextGalleryOpen(!isContextGalleryOpen)
              } as any}
            />
          </Suspense>
        </div>
      </div>
      </ErrorBoundary>
    </AuthGuard>
  );
})

export default Dashboard
