/**
 * Core Web Vitals Optimization
 * LCP, FID, CLS optimization and Google PageSpeed Insights score improvement
 */

import { useState, useEffect } from 'react'

// Core Web Vitals thresholds (Google recommendations)
export const WEB_VITALS_THRESHOLDS = {
  LCP: { good: 2500, needsImprovement: 4000 }, // Largest Contentful Paint (ms)
  FID: { good: 100, needsImprovement: 300 },   // First Input Delay (ms)
  CLS: { good: 0.1, needsImprovement: 0.25 },  // Cumulative Layout Shift
  FCP: { good: 1800, needsImprovement: 3000 }, // First Contentful Paint (ms)
  TTFB: { good: 800, needsImprovement: 1800 }  // Time to First Byte (ms)
} as const

// Web Vitals measurement and optimization
export class WebVitalsOptimizer {
  private static measurements: Map<string, number[]> = new Map()
  private static observers: Map<string, PerformanceObserver> = new Map()

  // Initialize Core Web Vitals monitoring
  static initialize() {
    if (typeof window === 'undefined') return

    this.measureLCP()
    this.measureFID()
    this.measureCLS()
    this.measureFCP()
    this.measureTTFB()
    this.optimizeForWebVitals()
  }

  // Measure Largest Contentful Paint (LCP)
  private static measureLCP() {
    if (!('PerformanceObserver' in window)) return

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1] as PerformanceEntry & { startTime: number }
      
      if (lastEntry) {
        this.recordMeasurement('LCP', lastEntry.startTime)
        this.evaluateMetric('LCP', lastEntry.startTime)
      }
    })

    try {
      observer.observe({ type: 'largest-contentful-paint', buffered: true })
      this.observers.set('LCP', observer)
    } catch (e) {
      console.warn('LCP measurement not supported')
    }
  }

  // Measure First Input Delay (FID)
  private static measureFID() {
    if (!('PerformanceObserver' in window)) return

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        if (entry.processingStart && entry.startTime) {
          const fid = entry.processingStart - entry.startTime
          this.recordMeasurement('FID', fid)
          this.evaluateMetric('FID', fid)
        }
      })
    })

    try {
      observer.observe({ type: 'first-input', buffered: true })
      this.observers.set('FID', observer)
    } catch (e) {
      console.warn('FID measurement not supported')
    }
  }

  // Measure Cumulative Layout Shift (CLS)
  private static measureCLS() {
    if (!('PerformanceObserver' in window)) return

    let clsValue = 0
    let sessionValue = 0
    let sessionEntries: any[] = []

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          const firstSessionEntry = sessionEntries[0]
          const lastSessionEntry = sessionEntries[sessionEntries.length - 1]

          if (sessionValue && 
              entry.startTime - lastSessionEntry.startTime < 1000 &&
              entry.startTime - firstSessionEntry.startTime < 5000) {
            sessionValue += entry.value
            sessionEntries.push(entry)
          } else {
            sessionValue = entry.value
            sessionEntries = [entry]
          }

          if (sessionValue > clsValue) {
            clsValue = sessionValue
            this.recordMeasurement('CLS', clsValue)
            this.evaluateMetric('CLS', clsValue)
          }
        }
      })
    })

    try {
      observer.observe({ type: 'layout-shift', buffered: true })
      this.observers.set('CLS', observer)
    } catch (e) {
      console.warn('CLS measurement not supported')
    }
  }

  // Measure First Contentful Paint (FCP)
  private static measureFCP() {
    if (!('PerformanceObserver' in window)) return

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        if (entry.name === 'first-contentful-paint') {
          this.recordMeasurement('FCP', entry.startTime)
          this.evaluateMetric('FCP', entry.startTime)
        }
      })
    })

    try {
      observer.observe({ type: 'paint', buffered: true })
      this.observers.set('FCP', observer)
    } catch (e) {
      console.warn('FCP measurement not supported')
    }
  }

  // Measure Time to First Byte (TTFB)
  private static measureTTFB() {
    if (!('PerformanceObserver' in window)) return

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        if (entry.responseStart && entry.requestStart) {
          const ttfb = entry.responseStart - entry.requestStart
          this.recordMeasurement('TTFB', ttfb)
          this.evaluateMetric('TTFB', ttfb)
        }
      })
    })

    try {
      observer.observe({ type: 'navigation', buffered: true })
      this.observers.set('TTFB', observer)
    } catch (e) {
      console.warn('TTFB measurement not supported')
    }
  }

  // Record measurement
  private static recordMeasurement(metric: string, value: number) {
    if (!this.measurements.has(metric)) {
      this.measurements.set(metric, [])
    }
    this.measurements.get(metric)!.push(value)
  }

  // Evaluate metric against thresholds
  private static evaluateMetric(metric: string, value: number) {
    const thresholds = WEB_VITALS_THRESHOLDS[metric as keyof typeof WEB_VITALS_THRESHOLDS]
    if (!thresholds) return

    let rating: 'good' | 'needs-improvement' | 'poor'
    if (value <= thresholds.good) {
      rating = 'good'
    } else if (value <= thresholds.needsImprovement) {
      rating = 'needs-improvement'
    } else {
      rating = 'poor'
    }

    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('webVitalsMeasured', {
      detail: { metric, value, rating }
    }))

    if (process.env.NODE_ENV === 'development') {
      const emoji = rating === 'good' ? '✅' : rating === 'needs-improvement' ? '⚠️' : '❌'
      console.log(`${emoji} ${metric}: ${Math.round(value)}${metric === 'CLS' ? '' : 'ms'} (${rating})`)
    }
  }

  // Optimize for Web Vitals
  private static optimizeForWebVitals() {
    this.optimizeLCP()
    this.optimizeFID()
    this.optimizeCLS()
  }

  // LCP Optimization
  private static optimizeLCP() {
    // Preload critical resources
    this.preloadCriticalResources()
    
    // Optimize images
    this.optimizeImages()
    
    // Remove render-blocking resources
    this.removeRenderBlockingResources()
  }

  // FID Optimization
  private static optimizeFID() {
    // Break up long tasks
    this.breakUpLongTasks()
    
    // Optimize JavaScript execution
    this.optimizeJavaScriptExecution()
    
    // Use web workers for heavy computations
    this.setupWebWorkers()
  }

  // CLS Optimization
  private static optimizeCLS() {
    // Set dimensions for images and videos
    this.setMediaDimensions()
    
    // Reserve space for dynamic content
    this.reserveSpaceForDynamicContent()
    
    // Avoid inserting content above existing content
    this.avoidContentInsertion()
  }

  // Preload critical resources
  private static preloadCriticalResources() {
    const criticalResources = [
      { href: '/fonts/inter-var.woff2', as: 'font', type: 'font/woff2' },
      { href: '/images/logo.png', as: 'image' }
    ]

    criticalResources.forEach(resource => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = resource.href
      link.as = resource.as
      if (resource.type) link.type = resource.type
      if (resource.as === 'font') link.crossOrigin = 'anonymous'
      
      document.head.appendChild(link)
    })
  }

  // Optimize images for LCP
  private static optimizeImages() {
    const images = document.querySelectorAll('img')
    
    images.forEach((img, index) => {
      // Prioritize above-the-fold images
      if (index < 3) {
        img.loading = 'eager'
        img.fetchPriority = 'high' as any
      } else {
        img.loading = 'lazy'
      }

      // Add responsive images if not present
      if (!img.srcset && img.src) {
        const baseSrc = img.src.split('.').slice(0, -1).join('.')
        const ext = img.src.split('.').pop()
        
        img.srcset = [
          `${baseSrc}-320w.${ext} 320w`,
          `${baseSrc}-640w.${ext} 640w`,
          `${baseSrc}-1024w.${ext} 1024w`
        ].join(', ')
        
        img.sizes = '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw'
      }
    })
  }

  // Remove render-blocking resources
  private static removeRenderBlockingResources() {
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]')
    
    stylesheets.forEach(linkElement => {
      const link = linkElement as HTMLLinkElement
      // Make non-critical CSS non-blocking
      if (!link.hasAttribute('data-critical')) {
        link.media = 'print'
        link.onload = () => {
          link.media = 'all'
        }
      }
    })
  }

  // Break up long tasks
  private static breakUpLongTasks() {
    // Implement task scheduling
    const scheduler = (callback: () => void) => {
      if ('scheduler' in window && 'postTask' in (window as any).scheduler) {
        (window as any).scheduler.postTask(callback)
      } else if ('requestIdleCallback' in window) {
        requestIdleCallback(callback)
      } else {
        setTimeout(callback, 0)
      }
    }

    // Export scheduler for use in components
    ;(window as any).__scheduler = scheduler
  }

  // Optimize JavaScript execution
  private static optimizeJavaScriptExecution() {
    // Defer non-critical scripts
    const scripts = document.querySelectorAll('script[src]')
    
    scripts.forEach(scriptElement => {
      const script = scriptElement as HTMLScriptElement
      if (!script.hasAttribute('data-critical')) {
        script.defer = true
      }
    })
  }

  // Setup web workers for heavy computations
  private static setupWebWorkers() {
    if ('Worker' in window) {
      // Create a simple worker for heavy computations
      const workerCode = `
        self.onmessage = function(e) {
          const { type, data } = e.data
          
          switch (type) {
            case 'heavyComputation':
              // Simulate heavy computation
              const result = data.map(item => item * 2)
              self.postMessage({ type: 'result', data: result })
              break
          }
        }
      `
      
      const blob = new Blob([workerCode], { type: 'application/javascript' })
      const workerUrl = URL.createObjectURL(blob)
      
      ;(window as any).__webWorker = new Worker(workerUrl)
    }
  }

  // Set dimensions for media elements
  private static setMediaDimensions() {
    const images = document.querySelectorAll('img:not([width]):not([height])')
    const videos = document.querySelectorAll('video:not([width]):not([height])')
    
    ;[...images, ...videos].forEach(element => {
      const mediaElement = element as HTMLElement
      // Set aspect ratio to prevent layout shift
      mediaElement.style.aspectRatio = '16 / 9' // Default aspect ratio
    })
  }

  // Reserve space for dynamic content
  private static reserveSpaceForDynamicContent() {
    const dynamicContainers = document.querySelectorAll('[data-dynamic-content]')
    
    dynamicContainers.forEach(container => {
      const minHeight = container.getAttribute('data-min-height') || '200px'
      ;(container as HTMLElement).style.minHeight = minHeight
    })
  }

  // Avoid content insertion above existing content
  private static avoidContentInsertion() {
    // Monitor for content insertions that might cause layout shift
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          const target = mutation.target as HTMLElement
          const rect = target.getBoundingClientRect()
          
          // If content is added above the fold, it might cause CLS
          if (rect.top < window.innerHeight) {
            console.warn('Content inserted above the fold, potential CLS issue')
          }
        }
      })
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })
  }

  // Get current measurements
  static getMeasurements() {
    const results: Record<string, { values: number[], average: number, latest: number }> = {}
    
    this.measurements.forEach((values, metric) => {
      results[metric] = {
        values: [...values],
        average: values.reduce((sum, val) => sum + val, 0) / values.length,
        latest: values[values.length - 1]
      }
    })
    
    return results
  }

  // Get Web Vitals score
  static getWebVitalsScore() {
    const measurements = this.getMeasurements()
    let totalScore = 0
    let metricCount = 0

    Object.entries(WEB_VITALS_THRESHOLDS).forEach(([metric, thresholds]) => {
      const measurement = measurements[metric]
      if (measurement) {
        const value = measurement.latest
        let score = 100
        
        if (value > thresholds.needsImprovement) {
          score = 0
        } else if (value > thresholds.good) {
          score = 50
        }
        
        totalScore += score
        metricCount++
      }
    })

    return metricCount > 0 ? Math.round(totalScore / metricCount) : 0
  }

  // Cleanup observers
  static cleanup() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers.clear()
    this.measurements.clear()
  }
}

// React hook for Web Vitals
export function useWebVitals() {
  const [vitals, setVitals] = useState<Record<string, any>>({})
  const [score, setScore] = useState(0)

  useEffect(() => {
    WebVitalsOptimizer.initialize()

    const handleWebVitals = (event: CustomEvent) => {
      const { metric, value, rating } = event.detail
      setVitals(prev => ({
        ...prev,
        [metric]: { value, rating }
      }))
      setScore(WebVitalsOptimizer.getWebVitalsScore())
    }

    window.addEventListener('webVitalsMeasured', handleWebVitals as EventListener)

    return () => {
      window.removeEventListener('webVitalsMeasured', handleWebVitals as EventListener)
      WebVitalsOptimizer.cleanup()
    }
  }, [])

  return { vitals, score, measurements: WebVitalsOptimizer.getMeasurements() }
}

// Auto-initialize on module load
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      WebVitalsOptimizer.initialize()
    })
  } else {
    WebVitalsOptimizer.initialize()
  }
}
