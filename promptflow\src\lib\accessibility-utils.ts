/**
 * Accessibility Utilities
 * WCAG 2.1 AA compliance tools and helpers
 */

// ARIA live region manager
export class AriaLiveRegion {
  private static regions = new Map<string, HTMLElement>()

  static announce(message: string, priority: 'polite' | 'assertive' = 'polite') {
    if (typeof window === 'undefined') return

    const regionId = `aria-live-${priority}`
    let region = this.regions.get(regionId)

    if (!region) {
      region = document.createElement('div')
      region.id = regionId
      region.setAttribute('aria-live', priority)
      region.setAttribute('aria-atomic', 'true')
      region.className = 'sr-only'
      region.style.cssText = `
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        padding: 0 !important;
        margin: -1px !important;
        overflow: hidden !important;
        clip: rect(0, 0, 0, 0) !important;
        white-space: nowrap !important;
        border: 0 !important;
      `
      
      document.body.appendChild(region)
      this.regions.set(regionId, region)
    }

    // Clear previous message and set new one
    region.textContent = ''
    setTimeout(() => {
      region!.textContent = message
    }, 100)
  }

  static clear(priority: 'polite' | 'assertive' = 'polite') {
    const regionId = `aria-live-${priority}`
    const region = this.regions.get(regionId)
    if (region) {
      region.textContent = ''
    }
  }
}

// Color contrast checker
export class ColorContrast {
  // Convert hex to RGB
  static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  }

  // Calculate relative luminance
  static getLuminance(r: number, g: number, b: number): number {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
    })
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
  }

  // Calculate contrast ratio
  static getContrastRatio(color1: string, color2: string): number {
    const rgb1 = this.hexToRgb(color1)
    const rgb2 = this.hexToRgb(color2)
    
    if (!rgb1 || !rgb2) return 0

    const lum1 = this.getLuminance(rgb1.r, rgb1.g, rgb1.b)
    const lum2 = this.getLuminance(rgb2.r, rgb2.g, rgb2.b)
    
    const brightest = Math.max(lum1, lum2)
    const darkest = Math.min(lum1, lum2)
    
    return (brightest + 0.05) / (darkest + 0.05)
  }

  // Check WCAG compliance
  static checkWCAGCompliance(foreground: string, background: string): {
    ratio: number
    aa: boolean
    aaa: boolean
    aaLarge: boolean
    aaaLarge: boolean
  } {
    const ratio = this.getContrastRatio(foreground, background)
    
    return {
      ratio,
      aa: ratio >= 4.5,
      aaa: ratio >= 7,
      aaLarge: ratio >= 3,
      aaaLarge: ratio >= 4.5
    }
  }
}

// Keyboard navigation helper
export class KeyboardNavigation {
  private static trapStack: HTMLElement[] = []

  // Focus trap for modals and dialogs
  static trapFocus(element: HTMLElement) {
    this.trapStack.push(element)
    
    const focusableElements = this.getFocusableElements(element)
    if (focusableElements.length === 0) return

    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault()
          lastElement.focus()
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault()
          firstElement.focus()
        }
      }
    }

    element.addEventListener('keydown', handleKeyDown)
    firstElement.focus()

    return () => {
      element.removeEventListener('keydown', handleKeyDown)
      this.trapStack.pop()
    }
  }

  // Get all focusable elements
  static getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ')

    return Array.from(container.querySelectorAll(focusableSelectors))
      .filter(el => this.isVisible(el as HTMLElement)) as HTMLElement[]
  }

  // Check if element is visible
  private static isVisible(element: HTMLElement): boolean {
    const style = window.getComputedStyle(element)
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           style.opacity !== '0'
  }

  // Restore focus to previous element
  static restoreFocus() {
    const lastTrap = this.trapStack[this.trapStack.length - 1]
    if (lastTrap) {
      const focusableElements = this.getFocusableElements(lastTrap)
      if (focusableElements.length > 0) {
        focusableElements[0].focus()
      }
    }
  }
}

// Screen reader utilities
export class ScreenReader {
  // Generate unique IDs for ARIA relationships
  static generateId(prefix: string = 'aria'): string {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`
  }

  // Create describedby relationship
  static createDescribedBy(element: HTMLElement, description: string): string {
    const descId = this.generateId('desc')
    
    // Create description element if it doesn't exist
    let descElement = document.getElementById(descId)
    if (!descElement) {
      descElement = document.createElement('div')
      descElement.id = descId
      descElement.className = 'sr-only'
      descElement.textContent = description
      document.body.appendChild(descElement)
    }

    // Add to aria-describedby
    const existingDescribedBy = element.getAttribute('aria-describedby')
    const newDescribedBy = existingDescribedBy 
      ? `${existingDescribedBy} ${descId}`
      : descId
    
    element.setAttribute('aria-describedby', newDescribedBy)
    
    return descId
  }

  // Create labelledby relationship
  static createLabelledBy(element: HTMLElement, labelText: string): string {
    const labelId = this.generateId('label')
    
    // Create label element if it doesn't exist
    let labelElement = document.getElementById(labelId)
    if (!labelElement) {
      labelElement = document.createElement('div')
      labelElement.id = labelId
      labelElement.className = 'sr-only'
      labelElement.textContent = labelText
      document.body.appendChild(labelElement)
    }

    element.setAttribute('aria-labelledby', labelId)
    
    return labelId
  }

  // Announce status changes
  static announceStatus(message: string, priority: 'polite' | 'assertive' = 'polite') {
    AriaLiveRegion.announce(message, priority)
  }
}

// Form accessibility helpers
export class FormAccessibility {
  // Add error announcement to form field
  static addFieldError(field: HTMLElement, errorMessage: string): string {
    const errorId = ScreenReader.generateId('error')
    
    // Create error element
    const errorElement = document.createElement('div')
    errorElement.id = errorId
    errorElement.className = 'text-red-600 text-sm mt-1'
    errorElement.setAttribute('role', 'alert')
    errorElement.textContent = errorMessage
    
    // Insert after field
    field.parentNode?.insertBefore(errorElement, field.nextSibling)
    
    // Update field attributes
    field.setAttribute('aria-invalid', 'true')
    field.setAttribute('aria-describedby', errorId)
    
    // Announce error
    ScreenReader.announceStatus(`Hata: ${errorMessage}`, 'assertive')
    
    return errorId
  }

  // Remove field error
  static removeFieldError(field: HTMLElement, errorId: string) {
    const errorElement = document.getElementById(errorId)
    if (errorElement) {
      errorElement.remove()
    }
    
    field.removeAttribute('aria-invalid')
    field.removeAttribute('aria-describedby')
  }

  // Add success feedback
  static addFieldSuccess(field: HTMLElement, successMessage: string): string {
    const successId = ScreenReader.generateId('success')
    
    // Create success element
    const successElement = document.createElement('div')
    successElement.id = successId
    successElement.className = 'text-green-600 text-sm mt-1'
    successElement.setAttribute('role', 'status')
    successElement.textContent = successMessage
    
    // Insert after field
    field.parentNode?.insertBefore(successElement, field.nextSibling)
    
    // Update field attributes
    field.setAttribute('aria-describedby', successId)
    
    return successId
  }
}

// Accessibility testing utilities
export class AccessibilityTester {
  // Test color contrast on page
  static testColorContrast(): Array<{
    element: HTMLElement
    foreground: string
    background: string
    ratio: number
    passes: boolean
  }> {
    const results: Array<any> = []
    
    // Get all text elements
    const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, a, button, label')
    
    textElements.forEach(element => {
      const styles = window.getComputedStyle(element as HTMLElement)
      const foreground = styles.color
      const background = styles.backgroundColor
      
      if (foreground && background && background !== 'rgba(0, 0, 0, 0)') {
        // Convert to hex for testing (simplified)
        const ratio = ColorContrast.getContrastRatio(foreground, background)
        const passes = ratio >= 4.5
        
        results.push({
          element: element as HTMLElement,
          foreground,
          background,
          ratio,
          passes
        })
      }
    })
    
    return results
  }

  // Test keyboard navigation
  static testKeyboardNavigation(): {
    focusableElements: number
    tabOrder: HTMLElement[]
    issues: string[]
  } {
    const focusableElements = KeyboardNavigation.getFocusableElements(document.body)
    const issues: string[] = []
    
    // Check for missing focus indicators
    focusableElements.forEach(element => {
      const styles = window.getComputedStyle(element)
      if (!styles.outline && !styles.boxShadow) {
        issues.push(`Element missing focus indicator: ${element.tagName}`)
      }
    })
    
    return {
      focusableElements: focusableElements.length,
      tabOrder: focusableElements,
      issues
    }
  }

  // Generate accessibility report
  static generateReport(): {
    colorContrast: ReturnType<typeof AccessibilityTester.testColorContrast>
    keyboardNavigation: ReturnType<typeof AccessibilityTester.testKeyboardNavigation>
    ariaLabels: number
    headingStructure: Array<{ level: number; text: string }>
  } {
    const colorContrast = this.testColorContrast()
    const keyboardNavigation = this.testKeyboardNavigation()
    
    // Count ARIA labels
    const ariaLabels = document.querySelectorAll('[aria-label], [aria-labelledby]').length
    
    // Check heading structure
    const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))
    const headingStructure = headings.map(heading => ({
      level: parseInt(heading.tagName.charAt(1)),
      text: heading.textContent || ''
    }))
    
    return {
      colorContrast,
      keyboardNavigation,
      ariaLabels,
      headingStructure
    }
  }
}
