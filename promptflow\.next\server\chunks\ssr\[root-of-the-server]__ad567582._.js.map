{"version": 3, "sources": [], "sections": [{"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/supabase-browser.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Single Supabase client instance to prevent multiple GoTrueClient warnings\nexport const supabaseBrowser = createBrowserClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    persistSession: true,\n    autoRefreshToken: true,\n    detectSessionInUrl: true,\n    flowType: 'pkce',\n    debug: process.env.NODE_ENV === 'development',\n    storageKey: 'sb-iqehopwgrczylqliajww-auth-token',\n    storage: typeof window !== 'undefined' ? window.localStorage : undefined,\n  },\n  db: {\n    schema: 'public',\n  },\n  realtime: {\n    params: {\n      eventsPerSecond: 10, // Rate limit realtime events\n    },\n  },\n  global: {\n    headers: {\n      'X-Client-Info': 'promptflow-web',\n      'X-Client-Version': '1.0.0',\n    }\n  }\n})\n\n// Session debug için\nif (typeof window !== 'undefined') {\n  supabaseBrowser.auth.onAuthStateChange((event, session) => {\n    console.log(`🔐 [SUPABASE_BROWSER] Auth state change: ${event}`, {\n      hasSession: !!session,\n      userId: session?.user?.id,\n      email: session?.user?.email,\n      expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null\n    })\n    \n    // Manually set cookies for server-side access\n    if (session) {\n      const maxAge = Math.round((session.expires_at! * 1000 - Date.now()) / 1000)\n      document.cookie = `sb-iqehopwgrczylqliajww-auth-token=${JSON.stringify(session)}; path=/; max-age=${maxAge}; SameSite=Lax; secure=${location.protocol === 'https:'}`\n      console.log(`🍪 [SUPABASE_BROWSER] Set auth cookie with maxAge: ${maxAge}s`)\n    } else {\n      document.cookie = 'sb-iqehopwgrczylqliajww-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'\n      console.log(`🍪 [SUPABASE_BROWSER] Cleared auth cookie`)\n    }\n  })\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,kBAAkB,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,iBAAiB;IAC/E,MAAM;QACJ,gBAAgB;QAChB,kBAAkB;QAClB,oBAAoB;QACpB,UAAU;QACV,OAAO,oDAAyB;QAChC,YAAY;QACZ,SAAS,sCAAgC,0BAAsB;IACjE;IACA,IAAI;QACF,QAAQ;IACV;IACA,UAAU;QACR,QAAQ;YACN,iBAAiB;QACnB;IACF;IACA,QAAQ;QACN,SAAS;YACP,iBAAiB;YACjB,oBAAoB;QACtB;IACF;AACF;AAEA,qBAAqB;AACrB", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/hooks/use-auth.ts"], "sourcesContent": ["'use client'\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'\nimport { supabaseBrowser as supabase } from '@/lib/supabase-browser'\nimport { User } from '@supabase/supabase-js'\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { toast } from 'sonner'\n\n// Auth error handling\nfunction handleAuthError(error: Error | unknown, queryClient: ReturnType<typeof useQueryClient>) {\n  const errorMessage = error instanceof Error ? error.message : String(error)\n  \n  if (errorMessage.includes('Invalid Refresh Token') || \n      errorMessage.includes('Refresh Token Not Found') ||\n      errorMessage.includes('refresh_token_not_found')) {\n    // Refresh token hatası durumunda oturumu temizle\n    console.warn('Refresh token hatası, oturum temizleniyor:', errorMessage)\n    supabase.auth.signOut({ scope: 'local' })\n    queryClient.clear()\n    return true\n  }\n  return false\n}\n\n// Auth state listener with enhanced logout handling\nexport function useAuthStateListener() {\n  const queryClient = useQueryClient()\n  const router = useRouter()\n\n  useEffect(() => {\n    console.log(`🎧 [AUTH_LISTENER] Setting up auth state listener`)\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      (event, session) => {\n        console.log(`🔄 [AUTH_LISTENER] Auth state change: ${event}`, {\n          userId: session?.user?.id,\n          email: session?.user?.email,\n          hasSession: !!session\n        })\n\n        if (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {\n          console.log(`🔄 [AUTH_LISTENER] Invalidating queries for event: ${event}`)\n          queryClient.invalidateQueries({ queryKey: ['user'] })\n          queryClient.invalidateQueries({ queryKey: ['session'] })\n        }\n\n        if (event === 'SIGNED_OUT') {\n          console.log(`🚪 [AUTH_LISTENER] User signed out - clearing cache and redirecting`)\n          // Clear all cache and redirect to login\n          queryClient.clear()\n\n          // Clear any persisted state\n          localStorage.removeItem('promptflow-app-store')\n\n          // Force redirect to auth page\n          setTimeout(() => {\n            console.log(`🚪 [AUTH_LISTENER] Redirecting to /auth`)\n            router.push('/auth')\n            router.refresh()\n          }, 100)\n        }\n\n        if (event === 'SIGNED_IN') {\n          console.log(`🔑 [AUTH_LISTENER] User signed in - refreshing queries and redirecting`)\n          // Refresh queries when user signs in\n          queryClient.invalidateQueries({ queryKey: ['user'] })\n          queryClient.invalidateQueries({ queryKey: ['session'] })\n\n          // Redirect to dashboard after successful login\n          setTimeout(() => {\n            const currentPath = window.location.pathname\n            if (currentPath === '/auth' || currentPath === '/') {\n              console.log(`🚀 [AUTH_LISTENER] Redirecting from ${currentPath} to /dashboard`)\n              router.push('/dashboard')\n            }\n          }, 100)\n        }\n      }\n    )\n\n    return () => {\n      console.log(`🎧 [AUTH_LISTENER] Cleaning up auth state listener`)\n      subscription.unsubscribe()\n    }\n  }, [queryClient, router])\n}\n\n// Kullanıcı bilgilerini getir\nexport function useUser() {\n  const queryClient = useQueryClient()\n\n  return useQuery({\n    queryKey: ['user'],\n    queryFn: async (): Promise<User | null> => {\n      console.log(`🔐 [USE_USER] Getting user...`)\n      try {\n        // Önce session'ı kontrol et\n        const { data: { session }, error: sessionError } = await supabase.auth.getSession()\n\n        if (sessionError) {\n          console.error(`❌ [USE_USER] Session error:`, sessionError)\n          return null\n        }\n\n        if (!session) {\n          console.log(`🔐 [USE_USER] No session found`)\n          return null\n        }\n\n        console.log(`🔐 [USE_USER] Session found, getting user...`)\n        const { data: { user }, error } = await supabase.auth.getUser()\n\n        if (error) {\n          console.error(`❌ [USE_USER] Error getting user:`, error)\n          // Auth session missing error'ını handle et\n          if (error.message.includes('Auth session missing')) {\n            console.log(`🔄 [USE_USER] Auth session missing, returning null`)\n            return null\n          }\n          // Refresh token hatası kontrolü\n          if (handleAuthError(error, queryClient)) {\n            console.log(`🔄 [USE_USER] Handled auth error, returning null`)\n            return null\n          }\n          throw new Error(error.message)\n        }\n\n        console.log(`✅ [USE_USER] User retrieved:`, user?.email || 'null')\n        return user\n      } catch (error: unknown) {\n        console.error(`💥 [USE_USER] Exception:`, error)\n        const errorMessage = error instanceof Error ? error.message : String(error)\n\n        // Auth session missing error'ını handle et\n        if (errorMessage.includes('Auth session missing')) {\n          console.log(`🔄 [USE_USER] Auth session missing exception, returning null`)\n          return null\n        }\n\n        // Refresh token hatası kontrolü\n        if (handleAuthError(error, queryClient)) {\n          console.log(`🔄 [USE_USER] Handled auth error exception, returning null`)\n          return null\n        }\n        throw error\n      }\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n    retry: (failureCount, error: unknown) => {\n      // Refresh token hatalarında ve session missing'de retry yapma\n      const errorMessage = error instanceof Error ? error.message : String(error)\n      console.log(`🔄 [USE_USER] Retry attempt ${failureCount}, error: ${errorMessage}`)\n      if (errorMessage.includes('Invalid Refresh Token') ||\n          errorMessage.includes('Refresh Token Not Found') ||\n          errorMessage.includes('Auth session missing')) {\n        console.log(`🚫 [USE_USER] Not retrying auth error: ${errorMessage}`)\n        return false\n      }\n      return failureCount < 3\n    },\n  })\n}\n\n// Oturum durumunu kontrol et\nexport function useSession() {\n  const queryClient = useQueryClient()\n  \n  return useQuery({\n    queryKey: ['session'],\n    queryFn: async () => {\n      try {\n        const { data: { session }, error } = await supabase.auth.getSession()\n        \n        if (error) {\n          // Refresh token hatası kontrolü\n          if (handleAuthError(error, queryClient)) {\n            return null\n          }\n          throw new Error(error.message)\n        }\n\n        return session\n      } catch (error: unknown) {\n        // Refresh token hatası kontrolü\n        if (handleAuthError(error, queryClient)) {\n          return null\n        }\n        throw error\n      }\n    },\n    staleTime: 5 * 60 * 1000, // 5 dakika\n    retry: (failureCount, error: unknown) => {\n      // Refresh token hatalarında retry yapma\n      const errorMessage = error instanceof Error ? error.message : String(error)\n      if (errorMessage.includes('Invalid Refresh Token') || \n          errorMessage.includes('Refresh Token Not Found')) {\n        return false\n      }\n      return failureCount < 3\n    },\n  })\n}\n\n// Email ile giriş yap\nexport function useSignInWithEmail() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ email, password }: { email: string; password: string }) => {\n      console.log(`🔑 [SIGN_IN] Attempting login for: ${email}`)\n\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) {\n        console.error(`❌ [SIGN_IN] Login failed:`, error)\n        throw new Error(error.message)\n      }\n\n      console.log(`✅ [SIGN_IN] Login successful:`, {\n        userId: data.user?.id,\n        email: data.user?.email,\n        hasSession: !!data.session\n      })\n\n      return data\n    },\n    onSuccess: () => {\n      console.log(`🎉 [SIGN_IN] onSuccess triggered, invalidating queries`)\n      queryClient.invalidateQueries({ queryKey: ['user'] })\n      queryClient.invalidateQueries({ queryKey: ['session'] })\n    },\n    onError: (error) => {\n      console.error(`💥 [SIGN_IN] onError triggered:`, error)\n    }\n  })\n}\n\n// Email ile kayıt ol\nexport function useSignUpWithEmail() {\n  const queryClient = useQueryClient()\n\n  return useMutation({\n    mutationFn: async ({ email, password }: { email: string; password: string }) => {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n      })\n\n      if (error) {\n        throw new Error(error.message)\n      }\n\n      return data\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['user'] })\n      queryClient.invalidateQueries({ queryKey: ['session'] })\n    },\n  })\n}\n\n// Enhanced logout with proper redirect\nexport function useSignOut() {\n  const queryClient = useQueryClient()\n  const router = useRouter()\n\n  return useMutation({\n    mutationFn: async () => {\n      console.log('Starting logout process...')\n\n      // Sign out from Supabase\n      const { error } = await supabase.auth.signOut({ scope: 'global' })\n\n      if (error) {\n        console.error('Logout error:', error)\n        throw new Error(error.message)\n      }\n\n      console.log('Logout successful')\n    },\n    onSuccess: () => {\n      console.log('Logout onSuccess triggered')\n\n      // Show success toast\n      toast.success('Başarıyla çıkış yapıldı', {\n        description: 'Giriş sayfasına yönlendiriliyorsunuz...'\n      })\n\n      // Clear all cache\n      queryClient.clear()\n\n      // Clear persisted state\n      localStorage.removeItem('promptflow-app-store')\n      sessionStorage.clear()\n\n      // Force redirect to auth page\n      setTimeout(() => {\n        console.log('Redirecting to auth page...')\n        router.push('/auth')\n        router.refresh()\n        window.location.href = '/auth' // Fallback for complete page reload\n      }, 1000) // Increased delay to show toast\n    },\n    onError: (error) => {\n      console.error('Logout failed:', error)\n\n      // Show error toast\n      toast.error('Çıkış yapılırken hata oluştu', {\n        description: 'Yine de oturum temizleniyor...'\n      })\n\n      // Even if logout fails, clear local state and redirect\n      queryClient.clear()\n      localStorage.removeItem('promptflow-app-store')\n      sessionStorage.clear()\n\n      setTimeout(() => {\n        router.push('/auth')\n        router.refresh()\n      }, 1000)\n    }\n  })\n}"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAPA;;;;;;AASA,sBAAsB;AACtB,SAAS,gBAAgB,KAAsB,EAAE,WAA8C;IAC7F,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;IAErE,IAAI,aAAa,QAAQ,CAAC,4BACtB,aAAa,QAAQ,CAAC,8BACtB,aAAa,QAAQ,CAAC,4BAA4B;QACpD,iDAAiD;QACjD,QAAQ,IAAI,CAAC,8CAA8C;QAC3D,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO;QAAQ;QACvC,YAAY,KAAK;QACjB,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,CAAC,iDAAiD,CAAC;QAE/D,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,CAAC,OAAO;YACN,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,OAAO,EAAE;gBAC5D,QAAQ,SAAS,MAAM;gBACvB,OAAO,SAAS,MAAM;gBACtB,YAAY,CAAC,CAAC;YAChB;YAEA,IAAI,UAAU,gBAAgB,UAAU,mBAAmB;gBACzD,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,OAAO;gBACzE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAO;gBAAC;gBACnD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAU;gBAAC;YACxD;YAEA,IAAI,UAAU,cAAc;gBAC1B,QAAQ,GAAG,CAAC,CAAC,mEAAmE,CAAC;gBACjF,wCAAwC;gBACxC,YAAY,KAAK;gBAEjB,4BAA4B;gBAC5B,aAAa,UAAU,CAAC;gBAExB,8BAA8B;gBAC9B,WAAW;oBACT,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;oBACrD,OAAO,IAAI,CAAC;oBACZ,OAAO,OAAO;gBAChB,GAAG;YACL;YAEA,IAAI,UAAU,aAAa;gBACzB,QAAQ,GAAG,CAAC,CAAC,sEAAsE,CAAC;gBACpF,qCAAqC;gBACrC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAO;gBAAC;gBACnD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAU;gBAAC;gBAEtD,+CAA+C;gBAC/C,WAAW;oBACT,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;oBAC5C,IAAI,gBAAgB,WAAW,gBAAgB,KAAK;wBAClD,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,YAAY,cAAc,CAAC;wBAC9E,OAAO,IAAI,CAAC;oBACd;gBACF,GAAG;YACL;QACF;QAGF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;YAChE,aAAa,WAAW;QAC1B;IACF,GAAG;QAAC;QAAa;KAAO;AAC1B;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAO;QAClB,SAAS;YACP,QAAQ,GAAG,CAAC,CAAC,6BAA6B,CAAC;YAC3C,IAAI;gBACF,4BAA4B;gBAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;gBAEjF,IAAI,cAAc;oBAChB,QAAQ,KAAK,CAAC,CAAC,2BAA2B,CAAC,EAAE;oBAC7C,OAAO;gBACT;gBAEA,IAAI,CAAC,SAAS;oBACZ,QAAQ,GAAG,CAAC,CAAC,8BAA8B,CAAC;oBAC5C,OAAO;gBACT;gBAEA,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;gBAC1D,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO;gBAE7D,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE;oBAClD,2CAA2C;oBAC3C,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,yBAAyB;wBAClD,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;wBAChE,OAAO;oBACT;oBACA,gCAAgC;oBAChC,IAAI,gBAAgB,OAAO,cAAc;wBACvC,QAAQ,GAAG,CAAC,CAAC,gDAAgD,CAAC;wBAC9D,OAAO;oBACT;oBACA,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,QAAQ,GAAG,CAAC,CAAC,4BAA4B,CAAC,EAAE,MAAM,SAAS;gBAC3D,OAAO;YACT,EAAE,OAAO,OAAgB;gBACvB,QAAQ,KAAK,CAAC,CAAC,wBAAwB,CAAC,EAAE;gBAC1C,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;gBAErE,2CAA2C;gBAC3C,IAAI,aAAa,QAAQ,CAAC,yBAAyB;oBACjD,QAAQ,GAAG,CAAC,CAAC,4DAA4D,CAAC;oBAC1E,OAAO;gBACT;gBAEA,gCAAgC;gBAChC,IAAI,gBAAgB,OAAO,cAAc;oBACvC,QAAQ,GAAG,CAAC,CAAC,0DAA0D,CAAC;oBACxE,OAAO;gBACT;gBACA,MAAM;YACR;QACF;QACA,WAAW,IAAI,KAAK;QACpB,OAAO,CAAC,cAAc;YACpB,8DAA8D;YAC9D,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;YACrE,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,aAAa,SAAS,EAAE,cAAc;YACjF,IAAI,aAAa,QAAQ,CAAC,4BACtB,aAAa,QAAQ,CAAC,8BACtB,aAAa,QAAQ,CAAC,yBAAyB;gBACjD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,cAAc;gBACpE,OAAO;YACT;YACA,OAAO,eAAe;QACxB;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAU;QACrB,SAAS;YACP,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,UAAU;gBAEnE,IAAI,OAAO;oBACT,gCAAgC;oBAChC,IAAI,gBAAgB,OAAO,cAAc;wBACvC,OAAO;oBACT;oBACA,MAAM,IAAI,MAAM,MAAM,OAAO;gBAC/B;gBAEA,OAAO;YACT,EAAE,OAAO,OAAgB;gBACvB,gCAAgC;gBAChC,IAAI,gBAAgB,OAAO,cAAc;oBACvC,OAAO;gBACT;gBACA,MAAM;YACR;QACF;QACA,WAAW,IAAI,KAAK;QACpB,OAAO,CAAC,cAAc;YACpB,wCAAwC;YACxC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;YACrE,IAAI,aAAa,QAAQ,CAAC,4BACtB,aAAa,QAAQ,CAAC,4BAA4B;gBACpD,OAAO;YACT;YACA,OAAO,eAAe;QACxB;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAuC;YACzE,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,OAAO;YAEzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,CAAC,yBAAyB,CAAC,EAAE;gBAC3C,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,CAAC,EAAE;gBAC3C,QAAQ,KAAK,IAAI,EAAE;gBACnB,OAAO,KAAK,IAAI,EAAE;gBAClB,YAAY,CAAC,CAAC,KAAK,OAAO;YAC5B;YAEA,OAAO;QACT;QACA,WAAW;YACT,QAAQ,GAAG,CAAC,CAAC,sDAAsD,CAAC;YACpE,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAO;YAAC;YACnD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;QACxD;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAE;QACnD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAuC;YACzE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAO;YAAC;YACnD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;QACxD;IACF;AACF;AAGO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY;YACV,QAAQ,GAAG,CAAC;YAEZ,yBAAyB;YACzB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,iIAAA,CAAA,kBAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;gBAAE,OAAO;YAAS;YAEhE,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,QAAQ,GAAG,CAAC;QACd;QACA,WAAW;YACT,QAAQ,GAAG,CAAC;YAEZ,qBAAqB;YACrB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,2BAA2B;gBACvC,aAAa;YACf;YAEA,kBAAkB;YAClB,YAAY,KAAK;YAEjB,wBAAwB;YACxB,aAAa,UAAU,CAAC;YACxB,eAAe,KAAK;YAEpB,8BAA8B;YAC9B,WAAW;gBACT,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;gBACd,OAAO,QAAQ,CAAC,IAAI,GAAG,SAAQ,oCAAoC;YACrE,GAAG,OAAM,gCAAgC;QAC3C;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,kBAAkB;YAEhC,mBAAmB;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;gBAC1C,aAAa;YACf;YAEA,uDAAuD;YACvD,YAAY,KAAK;YACjB,aAAa,UAAU,CAAC;YACxB,eAAe,KAAK;YAEpB,WAAW;gBACT,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB,GAAG;QACL;IACF;AACF", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/landing-page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from \"react\";\nimport Link from \"next/link\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ArrowRight, Sparkles, Zap, Shield, Users, Star, CheckCircle, TrendingUp, Menu, X } from \"lucide-react\";\nimport { useUser } from \"@/hooks/use-auth\";\n\nexport function LandingPage() {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const { data: user, isLoading: userLoading } = useUser();\n\n  // Close mobile menu when clicking on links\n  const closeMobileMenu = useCallback(() => {\n    setIsMobileMenuOpen(false);\n  }, []);\n\n  // Close mobile menu on escape key\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        setIsMobileMenuOpen(false);\n      }\n    };\n\n    if (isMobileMenuOpen) {\n      document.addEventListener('keydown', handleEscape);\n      // Prevent body scroll when menu is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isMobileMenuOpen]);\n\n  // Enhanced Structured Data for SEO\n  const softwareApplicationSchema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"SoftwareApplication\",\n    \"name\": \"PromptFlow\",\n    \"alternateName\": \"Promptbir\",\n    \"description\": \"AI destekli prompt yönetim platformu. Yapay zeka prompt&apos;larınızı organize edin, takımınızla paylaşın ve verimliliğinizi 10x artırın.\",\n    \"url\": \"https://promptbir.com\",\n    \"applicationCategory\": \"DeveloperApplication\",\n    \"operatingSystem\": \"Web Browser\",\n    \"browserRequirements\": \"Requires JavaScript. Requires HTML5.\",\n    \"softwareVersion\": \"1.0\",\n    \"datePublished\": \"2024-01-01\",\n    \"dateModified\": new Date().toISOString(),\n    \"offers\": [\n      {\n        \"@type\": \"Offer\",\n        \"name\": \"Ücretsiz Plan\",\n        \"price\": \"0\",\n        \"priceCurrency\": \"TRY\",\n        \"description\": \"5 proje, 100 prompt/proje, temel özellikler\",\n        \"category\": \"Free\"\n      },\n      {\n        \"@type\": \"Offer\",\n        \"name\": \"Profesyonel Plan\",\n        \"price\": \"99\",\n        \"priceCurrency\": \"TRY\",\n        \"description\": \"50 proje, 5000 prompt/proje, API erişimi, gelişmiş analitik\",\n        \"category\": \"Professional\"\n      }\n    ],\n    \"aggregateRating\": {\n      \"@type\": \"AggregateRating\",\n      \"ratingValue\": \"4.9\",\n      \"ratingCount\": \"1250\",\n      \"bestRating\": \"5\",\n      \"worstRating\": \"1\"\n    },\n    \"author\": {\n      \"@type\": \"Organization\",\n      \"name\": \"PromptFlow Team\",\n      \"url\": \"https://promptbir.com\"\n    },\n    \"featureList\": [\n      \"AI destekli prompt yönetimi\",\n      \"Drag & drop organizasyon\",\n      \"Takım çalışması\",\n      \"Context Gallery\",\n      \"API erişimi\",\n      \"Gerçek zamanlı senkronizasyon\",\n      \"Enterprise güvenlik\"\n    ]\n  };\n\n  const organizationSchema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Organization\",\n    \"name\": \"PromptFlow\",\n    \"alternateName\": \"Promptbir\",\n    \"url\": \"https://promptbir.com\",\n    \"logo\": \"https://promptbir.com/logo.png\",\n    \"description\": \"AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformu\",\n    \"foundingDate\": \"2024\",\n    \"contactPoint\": {\n      \"@type\": \"ContactPoint\",\n      \"contactType\": \"customer service\",\n      \"availableLanguage\": [\"Turkish\", \"English\"],\n      \"url\": \"https://promptbir.com\"\n    },\n    \"sameAs\": [\n      \"https://twitter.com/promptbir\",\n      \"https://github.com/promptbir\",\n      \"https://linkedin.com/company/promptbir\"\n    ]\n  };\n\n  const faqSchema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"FAQPage\",\n    \"mainEntity\": [\n      {\n        \"@type\": \"Question\",\n        \"name\": \"PromptFlow nedir?\",\n        \"acceptedAnswer\": {\n          \"@type\": \"Answer\",\n          \"text\": \"PromptFlow, AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformudur. Prompt'larınızı organize edebilir, takımınızla paylaşabilir ve verimliliğinizi artırabilirsiniz.\"\n        }\n      },\n      {\n        \"@type\": \"Question\",\n        \"name\": \"Ücretsiz plan neler içerir?\",\n        \"acceptedAnswer\": {\n          \"@type\": \"Answer\",\n          \"text\": \"Ücretsiz plan 5 proje, proje başına 100 prompt, temel özellikler ve email destek içerir.\"\n        }\n      },\n      {\n        \"@type\": \"Question\",\n        \"name\": \"Profesyonel plan neler içerir?\",\n        \"acceptedAnswer\": {\n          \"@type\": \"Answer\",\n          \"text\": \"Profesyonel plan 50 proje, proje başına 5000 prompt, API erişimi, takım özellikleri, Context Gallery, gelişmiş analitik ve öncelikli destek içerir.\"\n        }\n      }\n    ]\n  };\n\n  return (\n    <>\n      {/* Enhanced Structured Data for SEO */}\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{ __html: JSON.stringify(softwareApplicationSchema) }}\n      />\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}\n      />\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }}\n      />\n\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      {/* Navigation */}\n      <nav className=\"border-b border-gray-200 bg-white/80 backdrop-blur-sm sticky top-0 z-50\" role=\"navigation\" aria-label=\"Ana navigasyon\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0 flex items-center\">\n                <img\n                  src=\"/logo.png\"\n                  alt=\"Promptbir Logo\"\n                  className=\"h-8 w-auto mr-2\"\n                />\n                <span className=\"text-xl font-bold text-gray-900\">Promptbir</span>\n              </div>\n            </div>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden md:block\">\n              <div className=\"ml-10 flex items-baseline space-x-4\">\n                <Link href=\"#features\" className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\">\n                  Özellikler\n                </Link>\n                <Link href=\"#pricing\" className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\">\n                  Fiyatlandırma\n                </Link>\n                <Link href=\"/blog\" className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\">\n                  Blog\n                </Link>\n                <Link href=\"/about\" className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\">\n                  Hakkımızda\n                </Link>\n                <Link href=\"/contact\" className=\"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\">\n                  İletişim\n                </Link>\n              </div>\n            </div>\n\n            {/* Desktop CTA Buttons */}\n            <div className=\"hidden md:flex items-center space-x-4\">\n              {userLoading ? (\n                <div className=\"w-20 h-8 bg-gray-200 animate-pulse rounded\"></div>\n              ) : user ? (\n                <Link href=\"/dashboard\">\n                  <Button size=\"sm\" className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700\">\n                    Dashboard\n                    <ArrowRight className=\"ml-2 h-4 w-4\" />\n                  </Button>\n                </Link>\n              ) : (\n                <>\n                  <Link href=\"/auth\">\n                    <Button variant=\"ghost\" size=\"sm\">\n                      Giriş Yap\n                    </Button>\n                  </Link>\n                  <Link href=\"/auth\">\n                    <Button size=\"sm\" className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700\">\n                      Üye Ol\n                      <ArrowRight className=\"ml-2 h-4 w-4\" />\n                    </Button>\n                  </Link>\n                </>\n              )}\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                className=\"touch-target\"\n              >\n                {isMobileMenuOpen ? (\n                  <X className=\"h-6 w-6\" />\n                ) : (\n                  <Menu className=\"h-6 w-6\" />\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 bg-white shadow-lg\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1\">\n              <Link\n                href=\"#features\"\n                className=\"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target\"\n                onClick={closeMobileMenu}\n              >\n                Özellikler\n              </Link>\n              <Link\n                href=\"#pricing\"\n                className=\"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target\"\n                onClick={closeMobileMenu}\n              >\n                Fiyatlandırma\n              </Link>\n              <Link\n                href=\"/blog\"\n                className=\"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target\"\n                onClick={closeMobileMenu}\n              >\n                Blog\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target\"\n                onClick={closeMobileMenu}\n              >\n                Hakkımızda\n              </Link>\n              <Link\n                href=\"/contact\"\n                className=\"block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors touch-target\"\n                onClick={closeMobileMenu}\n              >\n                İletişim\n              </Link>\n            </div>\n            <div className=\"px-4 py-3 border-t border-gray-200 space-y-2 safe-area-bottom\">\n              {userLoading ? (\n                <div className=\"space-y-2\">\n                  <div className=\"w-full h-10 bg-gray-200 animate-pulse rounded\"></div>\n                  <div className=\"w-full h-10 bg-gray-200 animate-pulse rounded\"></div>\n                </div>\n              ) : user ? (\n                <Link href=\"/dashboard\" className=\"block\" onClick={closeMobileMenu}>\n                  <Button size=\"sm\" className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 justify-center mobile-btn\">\n                    Dashboard\n                    <ArrowRight className=\"ml-2 h-4 w-4\" />\n                  </Button>\n                </Link>\n              ) : (\n                <>\n                  <Link href=\"/auth\" className=\"block\" onClick={closeMobileMenu}>\n                    <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-center mobile-btn\">\n                      Giriş Yap\n                    </Button>\n                  </Link>\n                  <Link href=\"/auth\" className=\"block\" onClick={closeMobileMenu}>\n                    <Button size=\"sm\" className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 justify-center mobile-btn\">\n                      Üye Ol\n                      <ArrowRight className=\"ml-2 h-4 w-4\" />\n                    </Button>\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden min-h-screen flex items-center\" aria-labelledby=\"hero-heading\">\n        {/* Animated Background */}\n        <div className=\"absolute inset-0 -z-10\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50\"></div>\n          <div className=\"absolute top-0 left-1/4 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse\"></div>\n          <div className=\"absolute top-0 right-1/4 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000\"></div>\n          <div className=\"absolute bottom-0 left-1/3 w-72 h-72 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000\"></div>\n        </div>\n\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center\">\n          <div className=\"mx-auto max-w-4xl\">\n            {/* Badge */}\n            <div className=\"promptbir-fade-in-up mb-8\">\n              <span className=\"inline-flex items-center rounded-full bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10\">\n                <Sparkles className=\"w-4 h-4 mr-2\" />\n                AI Destekli Prompt Yönetimi\n              </span>\n            </div>\n\n            {/* Main Heading */}\n            <h1 id=\"hero-heading\" className=\"promptbir-fade-in-up text-3xl sm:text-5xl lg:text-7xl font-bold tracking-tight text-gray-900 mb-6 leading-tight\">\n              <span className=\"block\">AI Prompt&apos;larınızı</span>\n              <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent block mt-2\">\n                Profesyonelce Yönetin\n              </span>\n            </h1>\n\n            {/* Subtitle */}\n            <p className=\"promptbir-fade-in-up-delay mt-6 text-lg sm:text-xl leading-7 sm:leading-8 text-gray-600 max-w-3xl mx-auto px-4 sm:px-0\">\n              Yapay zeka destekli geliştirme sürecinizi hızlandırın. Prompt&apos;larınızı organize edin,\n              takımınızla paylaşın ve verimliliğinizi <span className=\"font-semibold text-blue-600\">10x artırın</span>.\n            </p>\n\n            {/* CTA Buttons */}\n            <div className=\"promptbir-fade-in-up-delay mt-10 flex flex-col sm:flex-row items-center justify-center gap-4 px-4 sm:px-0\">\n              {userLoading ? (\n                <div className=\"flex flex-col sm:flex-row gap-4 w-full sm:w-auto\">\n                  <div className=\"w-full sm:w-48 h-14 bg-gray-200 animate-pulse rounded-xl\"></div>\n                  <div className=\"w-full sm:w-40 h-14 bg-gray-200 animate-pulse rounded-xl\"></div>\n                </div>\n              ) : user ? (\n                <>\n                  <Link href=\"/dashboard\" className=\"w-full sm:w-auto\">\n                    <Button size=\"lg\" className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-xl mobile-btn\">\n                      <Zap className=\"mr-2 h-5 w-5\" />\n                      Dashboard&apos;a Git\n                      <ArrowRight className=\"ml-2 h-5 w-5\" />\n                    </Button>\n                  </Link>\n                  <Button variant=\"outline\" size=\"lg\" className=\"w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-xl border-2 hover:bg-gray-50 mobile-btn\">\n                    <span className=\"mr-2\">🎥</span>\n                    Demo İzle\n                  </Button>\n                </>\n              ) : (\n                <>\n                  <Link href=\"/auth\" className=\"w-full sm:w-auto\">\n                    <Button size=\"lg\" className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-xl mobile-btn\">\n                      <Zap className=\"mr-2 h-5 w-5\" />\n                      Ücretsiz Başla\n                      <ArrowRight className=\"ml-2 h-5 w-5\" />\n                    </Button>\n                  </Link>\n                  <Button variant=\"outline\" size=\"lg\" className=\"w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-xl border-2 hover:bg-gray-50 mobile-btn\">\n                    <span className=\"mr-2\">🎥</span>\n                    Demo İzle\n                  </Button>\n                </>\n              )}\n            </div>\n\n            {/* Stats */}\n            <div className=\"promptbir-fade-in-up-delay mt-16 grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 max-w-2xl mx-auto px-4 sm:px-0\">\n              <div className=\"text-center p-4 sm:p-0\">\n                <div className=\"text-2xl sm:text-3xl font-bold text-gray-900\">10,000+</div>\n                <div className=\"text-sm text-gray-600 mt-1\">Aktif Kullanıcı</div>\n              </div>\n              <div className=\"text-center p-4 sm:p-0\">\n                <div className=\"text-2xl sm:text-3xl font-bold text-gray-900\">1M+</div>\n                <div className=\"text-sm text-gray-600 mt-1\">Yönetilen Prompt</div>\n              </div>\n              <div className=\"text-center p-4 sm:p-0\">\n                <div className=\"text-2xl sm:text-3xl font-bold text-gray-900\">99.9%</div>\n                <div className=\"text-sm text-gray-600 mt-1\">Uptime</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <div className=\"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse\"></div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section id=\"features\" className=\"py-24 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-20\">\n            <h2 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-4\">\n              Güçlü <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">Özellikler</span>\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              AI destekli geliştirme sürecinizi optimize eden modern araçlar\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\">\n            {/* Feature 1 */}\n            <div className=\"group relative\">\n              <div className=\"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200\"></div>\n              <div className=\"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6\">\n                  <Zap className=\"w-6 h-6 text-white\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Hızlı Erişim</h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Prompt&apos;larınıza tek tıkla erişin. Drag & drop ile organize edin.\n                  Çoklu seçim ile toplu işlemler yapın.\n                </p>\n              </div>\n            </div>\n\n            {/* Feature 2 */}\n            <div className=\"group relative\">\n              <div className=\"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200\"></div>\n              <div className=\"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6\">\n                  <Shield className=\"w-6 h-6 text-white\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Güvenli Saklama</h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Enterprise düzeyinde güvenlik. Row Level Security ile verileriniz\n                  tamamen korunur ve sadece size aittir.\n                </p>\n              </div>\n            </div>\n\n            {/* Feature 3 */}\n            <div className=\"group relative\">\n              <div className=\"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200\"></div>\n              <div className=\"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6\">\n                  <Users className=\"w-6 h-6 text-white\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Takım Çalışması</h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Takımınızla prompt&apos;ları paylaşın. Context galeri ile\n                  ortak şablonlar oluşturun ve verimliliği artırın.\n                </p>\n              </div>\n            </div>\n\n            {/* Feature 4 */}\n            <div className=\"group relative\">\n              <div className=\"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200\"></div>\n              <div className=\"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6\">\n                  <Sparkles className=\"w-6 h-6 text-white\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">AI Optimizasyonu</h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Prompt&apos;larınızı AI ile optimize edin. Akıllı kategorileme\n                  ve otomatik etiketleme ile düzen sağlayın.\n                </p>\n              </div>\n            </div>\n\n            {/* Feature 5 */}\n            <div className=\"group relative\">\n              <div className=\"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200\"></div>\n              <div className=\"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6\">\n                  <ArrowRight className=\"w-6 h-6 text-white\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Gerçek Zamanlı</h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Tüm değişiklikler anında senkronize olur. Takım üyeleri\n                  aynı anda çalışabilir, çakışma olmaz.\n                </p>\n              </div>\n            </div>\n\n            {/* Feature 6 */}\n            <div className=\"group relative\">\n              <div className=\"absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-100 transition duration-1000 group-hover:duration-200\"></div>\n              <div className=\"relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mb-6\">\n                  <span className=\"text-white font-bold text-lg\">∞</span>\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Sınırsız Kullanım</h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Sınırsız proje, prompt ve kullanıcı. Büyüdükçe ölçeklenen\n                  altyapı ile hiç endişelenmeyin.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Testimonials Section */}\n      <section id=\"testimonials\" className=\"py-24 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-20\">\n            <h2 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-4\">\n              Kullanıcılarımız <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">Ne Diyor?</span>\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Binlerce geliştirici Promptbir ile verimliliğini artırdı\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {/* Testimonial 1 */}\n            <div className=\"bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-all duration-300\">\n              <div className=\"flex items-center mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-700 mb-6 leading-relaxed\">\n                &quot;Promptbir sayesinde prompt yönetimi çok kolay oldu. Takımımızla paylaştığımız\n                şablonlar sayesinde geliştirme hızımız 10x arttı.&quot;\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4\">\n                  <span className=\"text-blue-600 font-semibold\">AK</span>\n                </div>\n                <div>\n                  <div className=\"font-semibold text-gray-900\">Ahmet Ocak</div>\n                  <div className=\"text-gray-600 text-sm\">Senior Developer, TechCorp</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Testimonial 2 */}\n            <div className=\"bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-all duration-300\">\n              <div className=\"flex items-center mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-700 mb-6 leading-relaxed\">\n                &quot;AI projelerinde prompt versiyonlama çok kritikti. Promptbir ile hem organize\n                hem de güvenli bir şekilde yönetebiliyoruz.&quot;\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4\">\n                  <span className=\"text-purple-600 font-semibold\">EY</span>\n                </div>\n                <div>\n                  <div className=\"font-semibold text-gray-900\">Elif Yılmaz</div>\n                  <div className=\"text-gray-600 text-sm\">AI Engineer, StartupX</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Testimonial 3 */}\n            <div className=\"bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-all duration-300\">\n              <div className=\"flex items-center mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-gray-700 mb-6 leading-relaxed\">\n                &quot;Drag & drop özelliği harika! Prompt&apos;ları organize etmek hiç bu kadar\n                kolay olmamıştı. Kesinlikle tavsiye ederim.&quot;\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4\">\n                  <span className=\"text-green-600 font-semibold\">MÖ</span>\n                </div>\n                <div>\n                  <div className=\"font-semibold text-gray-900\">Mehmet Özkan</div>\n                  <div className=\"text-gray-600 text-sm\">Product Manager, InnovateLab</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Pricing Section */}\n      <section id=\"pricing\" className=\"py-24 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-20\">\n            <h2 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-4\">\n              Basit ve <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">Şeffaf Fiyatlandırma</span>\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              İhtiyacınıza göre seçin, istediğiniz zaman değiştirin\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto\">\n            {/* Free Plan */}\n            <div className=\"bg-white rounded-2xl shadow-lg p-8 border-2 border-gray-200 hover:border-blue-300 transition-all duration-300\">\n              <div className=\"text-center\">\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">Başlangıç</h3>\n                <div className=\"text-4xl font-bold text-gray-900 mb-1\">Ücretsiz</div>\n                <p className=\"text-gray-600 mb-8\">Bireysel kullanım için</p>\n\n                <ul className=\"space-y-4 mb-8 text-left\">\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>5 proje</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>100 prompt/proje</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>Sınırlı Context Gallery</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>Email destek</span>\n                  </li>\n                </ul>\n\n                <Link href=\"/dashboard\">\n                  <Button className=\"w-full\" variant=\"outline\">\n                    Hemen Başla\n                  </Button>\n                </Link>\n              </div>\n            </div>\n\n            {/* Pro Plan */}\n            <div className=\"bg-white rounded-2xl shadow-xl p-8 border-2 border-blue-500 relative transform scale-105 hover:scale-110 transition-all duration-300\">\n              <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                <span className=\"bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold\">\n                  En Popüler\n                </span>\n              </div>\n              <div className=\"text-center\">\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">Profesyonel</h3>\n                <div className=\"text-4xl font-bold text-gray-900 mb-1\">₺99</div>\n                <p className=\"text-gray-600 mb-2\">aylık</p>\n                <p className=\"text-sm text-gray-500 mb-6\">veya ₺990/yıl (%17 indirim)</p>\n\n                <ul className=\"space-y-4 mb-8 text-left\">\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>50 proje</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>5.000 prompt/proje</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>API erişimi</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>Takım özellikleri</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>Context Gallery</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>Gelişmiş analitik</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>Öncelikli destek</span>\n                  </li>\n                </ul>\n\n                <Link href=\"/dashboard\">\n                  <Button className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700\">\n                    Pro&apos;ya Geç\n                  </Button>\n                </Link>\n              </div>\n            </div>\n\n            {/* Enterprise Plan */}\n            <div className=\"bg-white rounded-2xl shadow-lg p-8 border-2 border-gray-200 hover:border-purple-300 transition-all duration-300\">\n              <div className=\"text-center\">\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">Kurumsal</h3>\n                <div className=\"text-4xl font-bold text-gray-900 mb-1\">Özel</div>\n                <p className=\"text-gray-600 mb-8\">Büyük takımlar için</p>\n\n                <ul className=\"space-y-4 mb-8 text-left\">\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>Tüm Pro özellikler</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>SSO entegrasyonu</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>Özel deployment</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <CheckCircle className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <span>24/7 destek</span>\n                  </li>\n                </ul>\n\n                <Button className=\"w-full\" variant=\"outline\">\n                  İletişime Geç\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* FAQ or additional info */}\n          <div className=\"mt-16 text-center\">\n            <p className=\"text-gray-600\">\n              Tüm planlar 14 gün ücretsiz deneme içerir. İstediğiniz zaman iptal edebilirsiniz.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"relative overflow-hidden\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600\"></div>\n        <div className=\"relative max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:py-24 lg:px-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl\">\n              <span className=\"block\">Hazır mısınız?</span>\n              <span className=\"block mt-2 text-blue-100\">Hemen başlayın.</span>\n            </h2>\n            <p className=\"mt-6 text-xl text-blue-100 max-w-2xl mx-auto\">\n              Binlerce geliştirici gibi siz de AI prompt&apos;larınızı profesyonelce yönetin\n            </p>\n            <div className=\"mt-10 flex flex-col sm:flex-row items-center justify-center gap-4\">\n              <Link href=\"/dashboard\">\n                <Button size=\"lg\" variant=\"secondary\" className=\"bg-white text-blue-600 hover:bg-gray-50 px-8 py-4 text-lg font-semibold rounded-xl\">\n                  <CheckCircle className=\"mr-2 h-5 w-5\" />\n                  Ücretsiz Başla\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Button>\n              </Link>\n              <div className=\"flex items-center text-blue-100\">\n                <CheckCircle className=\"w-5 h-5 mr-2\" />\n                <span>Kredi kartı gerektirmez</span>\n              </div>\n            </div>\n\n            {/* Trust indicators */}\n            <div className=\"mt-12 flex flex-col sm:flex-row items-center justify-center gap-8 text-blue-100\">\n              <div className=\"flex items-center\">\n                <TrendingUp className=\"w-5 h-5 mr-2\" />\n                <span>10,000+ Aktif Kullanıcı</span>\n              </div>\n              <div className=\"flex items-center\">\n                <Shield className=\"w-5 h-5 mr-2\" />\n                <span>Enterprise Güvenlik</span>\n              </div>\n              <div className=\"flex items-center\">\n                <Zap className=\"w-5 h-5 mr-2\" />\n                <span>99.9% Uptime</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            {/* Brand */}\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center mb-4\">\n                <Sparkles className=\"h-8 w-8 text-blue-400\" />\n                <span className=\"ml-2 text-xl font-bold text-white\">Promptbir</span>\n              </div>\n              <p className=\"text-gray-400 mb-6 max-w-md\">\n                AI prompt&apos;larınızı profesyonelce yönetin. Takımınızla paylaşın,\n                organize edin ve verimliliğinizi artırın.\n              </p>\n              <div className=\"flex space-x-4\">\n                <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  <span className=\"sr-only\">Twitter</span>\n                  <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  <span className=\"sr-only\">GitHub</span>\n                  <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path fillRule=\"evenodd\" d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\" clipRule=\"evenodd\" />\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                  <span className=\"sr-only\">LinkedIn</span>\n                  <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path fillRule=\"evenodd\" d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\" clipRule=\"evenodd\" />\n                  </svg>\n                </a>\n              </div>\n            </div>\n\n            {/* Product */}\n            <div>\n              <h3 className=\"text-white font-semibold mb-4\">Ürün</h3>\n              <ul className=\"space-y-3\">\n                <li><a href=\"#features\" className=\"text-gray-400 hover:text-white transition-colors\">Özellikler</a></li>\n                <li><a href=\"#pricing\" className=\"text-gray-400 hover:text-white transition-colors\">Fiyatlandırma</a></li>\n                <li><a href=\"/dashboard\" className=\"text-gray-400 hover:text-white transition-colors\">Demo</a></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">API</a></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">Entegrasyonlar</a></li>\n              </ul>\n            </div>\n\n            {/* Company */}\n            <div>\n              <h3 className=\"text-white font-semibold mb-4\">Şirket</h3>\n              <ul className=\"space-y-3\">\n                <li><a href=\"/about\" className=\"text-gray-400 hover:text-white transition-colors\">Hakkımızda</a></li>\n                <li><a href=\"/blog\" className=\"text-gray-400 hover:text-white transition-colors\">Blog</a></li>\n                <li><a href=\"/careers\" className=\"text-gray-400 hover:text-white transition-colors\">Kariyer</a></li>\n                <li><a href=\"/contact\" className=\"text-gray-400 hover:text-white transition-colors\">İletişim</a></li>\n                <li><a href=\"/bug-report\" className=\"text-gray-400 hover:text-white transition-colors\">Bug Report</a></li>\n              </ul>\n            </div>\n          </div>\n\n          {/* Bottom */}\n          <div className=\"mt-12 pt-8 border-t border-gray-800\">\n            <div className=\"flex flex-col md:flex-row justify-between items-center\">\n              <p className=\"text-gray-400 text-sm\">\n                © 2024 Promptbir. Tüm hakları saklıdır.\n              </p>\n              <div className=\"mt-4 md:mt-0 flex space-x-6\">\n                <a href=\"/privacy\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n                  Gizlilik Politikası\n                </a>\n                <a href=\"/terms\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n                  Kullanım Şartları\n                </a>\n                <a href=\"/cookies\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n                  Çerez Politikası\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      </footer>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAErD,2CAA2C;IAC3C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,oBAAoB;IACtB,GAAG,EAAE;IAEL,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,oBAAoB;YACtB;QACF;QAEA,IAAI,kBAAkB;YACpB,SAAS,gBAAgB,CAAC,WAAW;YACrC,wCAAwC;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAiB;IAErB,mCAAmC;IACnC,MAAM,4BAA4B;QAChC,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,iBAAiB;QACjB,eAAe;QACf,OAAO;QACP,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,mBAAmB;QACnB,iBAAiB;QACjB,gBAAgB,IAAI,OAAO,WAAW;QACtC,UAAU;YACR;gBACE,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,iBAAiB;gBACjB,eAAe;gBACf,YAAY;YACd;YACA;gBACE,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,iBAAiB;gBACjB,eAAe;gBACf,YAAY;YACd;SACD;QACD,mBAAmB;YACjB,SAAS;YACT,eAAe;YACf,eAAe;YACf,cAAc;YACd,eAAe;QACjB;QACA,UAAU;YACR,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,qBAAqB;QACzB,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,iBAAiB;QACjB,OAAO;QACP,QAAQ;QACR,eAAe;QACf,gBAAgB;QAChB,gBAAgB;YACd,SAAS;YACT,eAAe;YACf,qBAAqB;gBAAC;gBAAW;aAAU;YAC3C,OAAO;QACT;QACA,UAAU;YACR;YACA;YACA;SACD;IACH;IAEA,MAAM,YAAY;QAChB,YAAY;QACZ,SAAS;QACT,cAAc;YACZ;gBACE,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAChB,SAAS;oBACT,QAAQ;gBACV;YACF;YACA;gBACE,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAChB,SAAS;oBACT,QAAQ;gBACV;YACF;YACA;gBACE,SAAS;gBACT,QAAQ;gBACR,kBAAkB;oBAChB,SAAS;oBACT,QAAQ;gBACV;YACF;SACD;IACH;IAEA,qBACE;;0BAEE,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBAAE,QAAQ,KAAK,SAAS,CAAC;gBAA2B;;;;;;0BAE/E,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBAAE,QAAQ,KAAK,SAAS,CAAC;gBAAoB;;;;;;0BAExE,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBAAE,QAAQ,KAAK,SAAS,CAAC;gBAAW;;;;;;0BAG/D,8OAAC;gBAAI,WAAU;;kCAEf,8OAAC;wBAAI,WAAU;wBAA0E,MAAK;wBAAa,cAAW;;0CACpH,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAkC;;;;;;;;;;;;;;;;;sDAKtD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAA+F;;;;;;kEAGhI,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAA+F;;;;;;kEAG/H,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;kEAA+F;;;;;;kEAG5H,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAA+F;;;;;;kEAG7H,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAA+F;;;;;;;;;;;;;;;;;sDAOnI,8OAAC;4CAAI,WAAU;sDACZ,4BACC,8OAAC;gDAAI,WAAU;;;;;uDACb,qBACF,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,WAAU;;wDAAkG;sEAE5H,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;qEAI1B;;kEACE,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAAK;;;;;;;;;;;kEAIpC,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,WAAU;;gEAAkG;8EAE5H,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAQhC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,oBAAoB,CAAC;gDACpC,WAAU;0DAET,iCACC,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;yEAEb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQzB,kCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS;0DACV;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS;0DACV;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS;0DACV;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS;0DACV;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS;0DACV;;;;;;;;;;;;kDAIH,8OAAC;wCAAI,WAAU;kDACZ,4BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;mDAEf,qBACF,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;4CAAQ,SAAS;sDACjD,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;;oDAAmI;kEAE7J,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;iEAI1B;;8DACE,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;oDAAQ,SAAS;8DAC5C,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAK,WAAU;kEAAmC;;;;;;;;;;;8DAIjF,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;oDAAQ,SAAS;8DAC5C,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,WAAU;;4DAAmI;0EAE7J,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWtC,8OAAC;wBAAQ,WAAU;wBAA0D,mBAAgB;;0CAE3F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;kEACd,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAMzC,8OAAC;4CAAG,IAAG;4CAAe,WAAU;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAQ;;;;;;8DACxB,8OAAC;oDAAK,WAAU;8DAAwF;;;;;;;;;;;;sDAM1G,8OAAC;4CAAE,WAAU;;gDAAyH;8DAE5F,8OAAC;oDAAK,WAAU;8DAA8B;;;;;;gDAAkB;;;;;;;sDAI1G,8OAAC;4CAAI,WAAU;sDACZ,4BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;uDAEf,qBACF;;kEACE,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;kEAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,WAAU;;8EAC1B,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;8EAEhC,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAG1B,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,WAAU;;0EAC5C,8OAAC;gEAAK,WAAU;0EAAO;;;;;;4DAAS;;;;;;;;6EAKpC;;kEACE,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;kEAC3B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,WAAU;;8EAC1B,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;8EAEhC,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAG1B,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,WAAU;;0EAC5C,8OAAC;gEAAK,WAAU;0EAAO;;;;;;4DAAS;;;;;;;;;;;;;;sDAQxC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;sEAC9D,8OAAC;4DAAI,WAAU;sEAA6B;;;;;;;;;;;;8DAE9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;sEAC9D,8OAAC;4DAAI,WAAU;sEAA6B;;;;;;;;;;;;8DAE9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA+C;;;;;;sEAC9D,8OAAC;4DAAI,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOpD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMrB,8OAAC;wBAAQ,IAAG;wBAAW,WAAU;kCAC/B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAmE;8DACzE,8OAAC;oDAAK,WAAU;8DAA6E;;;;;;;;;;;;sDAErG,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;sEAEjB,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAQjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;sEAEpB,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAQjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAQjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAQjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;sEAExB,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAQjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWvD,8OAAC;wBAAQ,IAAG;wBAAe,WAAU;kCACnC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAmE;8DAC9D,8OAAC;oDAAK,WAAU;8DAA6E;;;;;;;;;;;;sDAEhH,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;4DAAS,WAAU;2DAAb;;;;;;;;;;8DAGf,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAIlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA8B;;;;;;;;;;;sEAEhD,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA8B;;;;;;8EAC7C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAM7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;4DAAS,WAAU;2DAAb;;;;;;;;;;8DAGf,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAIlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;sEAElD,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA8B;;;;;;8EAC7C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAM7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;4DAAS,WAAU;2DAAb;;;;;;;;;;8DAGf,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAIlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA8B;;;;;;8EAC7C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASnD,8OAAC;wBAAQ,IAAG;wBAAU,WAAU;kCAC9B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAmE;8DACtE,8OAAC;oDAAK,WAAU;8DAA6E;;;;;;;;;;;;sDAExG,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;kEAAwC;;;;;;kEACvD,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAElC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;kFACvB,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;kFACvB,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;kFACvB,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;kFACvB,8OAAC;kFAAK;;;;;;;;;;;;;;;;;;kEAIV,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAS,SAAQ;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAQnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAsE;;;;;;;;;;;8DAIxF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;sEACvD,8OAAC;4DAAE,WAAU;sEAAqB;;;;;;sEAClC,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAE1C,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;sEAIV,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;0EAAyG;;;;;;;;;;;;;;;;;;;;;;;sDAQjI,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;kEAAwC;;;;;;kEACvD,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAElC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;kFACvB,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;kFACvB,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;kFACvB,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;kFACvB,8OAAC;kFAAK;;;;;;;;;;;;;;;;;;kEAIV,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;wDAAS,SAAQ;kEAAU;;;;;;;;;;;;;;;;;;;;;;;8CAQnD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;kCAQnC,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAQ;;;;;;8DACxB,8OAAC;oDAAK,WAAU;8DAA2B;;;;;;;;;;;;sDAE7C,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;sDAG5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,SAAQ;wDAAY,WAAU;;0EAC9C,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAiB;0EAExC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAG1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;sDAKV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhB,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;;8DAEtD,8OAAC;oDAAE,WAAU;8DAA8B;;;;;;8DAI3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,MAAK;4DAAI,WAAU;;8EACpB,8OAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,8OAAC;oEAAI,WAAU;oEAAU,MAAK;oEAAe,SAAQ;8EACnD,cAAA,8OAAC;wEAAK,GAAE;;;;;;;;;;;;;;;;;sEAGZ,8OAAC;4DAAE,MAAK;4DAAI,WAAU;;8EACpB,8OAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,8OAAC;oEAAI,WAAU;oEAAU,MAAK;oEAAe,SAAQ;8EACnD,cAAA,8OAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAmtB,UAAS;;;;;;;;;;;;;;;;;sEAG3vB,8OAAC;4DAAE,MAAK;4DAAI,WAAU;;8EACpB,8OAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,8OAAC;oEAAI,WAAU;oEAAU,MAAK;oEAAe,SAAQ;8EACnD,cAAA,8OAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAqf,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOjiB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAY,WAAU;0EAAmD;;;;;;;;;;;sEACrF,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAW,WAAU;0EAAmD;;;;;;;;;;;sEACpF,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAa,WAAU;0EAAmD;;;;;;;;;;;sEACtF,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAmD;;;;;;;;;;;sEAC7E,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAmD;;;;;;;;;;;;;;;;;;;;;;;sDAKjF,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAS,WAAU;0EAAmD;;;;;;;;;;;sEAClF,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAQ,WAAU;0EAAmD;;;;;;;;;;;sEACjF,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAW,WAAU;0EAAmD;;;;;;;;;;;sEACpF,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAW,WAAU;0EAAmD;;;;;;;;;;;sEACpF,8OAAC;sEAAG,cAAA,8OAAC;gEAAE,MAAK;gEAAc,WAAU;0EAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM7F,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DAGrC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,MAAK;wDAAW,WAAU;kEAA2D;;;;;;kEAGxF,8OAAC;wDAAE,MAAK;wDAAS,WAAU;kEAA2D;;;;;;kEAGtF,8OAAC;wDAAE,MAAK;wDAAW,WAAU;kEAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxG", "debugId": null}}]}