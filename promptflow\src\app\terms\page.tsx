import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  ArrowLeft, 
  Sparkles, 
  FileText, 
  Scale, 
  AlertTriangle, 
  CheckCircle,
  XCircle,
  DollarSign,
  Shield,
  Users
} from 'lucide-react'

export const metadata: Metadata = {
  title: 'Kullanım Şartları - PromptBir | Hizmet Şartları ve Koşulları',
  description: 'PromptBir kullanım şartları ve hizmet koşulları. Platform kullanımına dair kurallar, haklar ve sorumluluklar hakkında detaylı bilgi.',
  keywords: [
    'kullanım şartları',
    'hizmet koşulları',
    'kullanıcı sözleşmesi',
    'platform kuralları',
    'yasal şartlar',
    'hizmet sözleşmesi'
  ],
  openGraph: {
    title: '<PERSON>llan<PERSON><PERSON> Şartları - PromptBir',
    description: 'Platform kullanımına dair kurallar, haklar ve sorumluluklar',
    type: 'website',
    url: 'https://promptbir.com/terms'
  }
}

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link 
              href="/" 
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Ana Sayfaya Dön</span>
            </Link>
            <div className="flex items-center space-x-2">
              <Sparkles className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">PromptBir</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Scale className="h-8 w-8 text-blue-600" />
          </div>
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
            Kullanım Şartları
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            PromptBir platformunu kullanarak aşağıdaki şart ve koşulları kabul etmiş olursunuz. 
            Lütfen bu belgeyi dikkatlice okuyun.
          </p>
          <p className="text-sm text-gray-500 mt-4">
            Son güncelleme: 1 Ocak 2024 | Yürürlük tarihi: 1 Ocak 2024
          </p>
        </div>

        {/* Quick Summary */}
        <Card className="shadow-lg mb-8 bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
          <CardContent className="p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Özet
            </h2>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>Platform'u yasal amaçlarla kullanın</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>Hesap güvenliğinizden siz sorumlusunuz</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>İçeriklerinizin telif hakkı size aittir</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span>Hizmet kesintileri olabilir</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="space-y-8">
          {/* Acceptance */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <FileText className="h-6 w-6 text-blue-600" />
                1. Şartların Kabulü
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p className="mb-4">
                PromptBir platformunu ("Hizmet") kullanarak, bu Kullanım Şartları'nı ("Şartlar") 
                kabul etmiş olursunuz. Bu şartları kabul etmiyorsanız, lütfen hizmeti kullanmayın.
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Bu şartlar tüm kullanıcılar için geçerlidir</li>
                <li>Şartlar zaman zaman güncellenebilir</li>
                <li>Güncellemeler platform üzerinden duyurulur</li>
                <li>Devam eden kullanım güncellemeleri kabul anlamına gelir</li>
              </ul>
            </CardContent>
          </Card>

          {/* Service Description */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Sparkles className="h-6 w-6 text-purple-600" />
                2. Hizmet Tanımı
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p className="mb-4">PromptBir aşağıdaki hizmetleri sunar:</p>
              <ul className="list-disc pl-6 space-y-2">
                <li><strong>Prompt Yönetimi:</strong> AI prompt'larınızı organize etme</li>
                <li><strong>Proje Yönetimi:</strong> Prompt'ları projeler halinde gruplandırma</li>
                <li><strong>Paylaşım:</strong> Prompt'ları güvenli şekilde paylaşma</li>
                <li><strong>Context Gallery:</strong> Hazır prompt şablonları</li>
                <li><strong>Analitik:</strong> Kullanım istatistikleri</li>
                <li><strong>API Erişimi:</strong> Programatik erişim (ücretli planlarda)</li>
              </ul>
            </CardContent>
          </Card>

          {/* User Accounts */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Users className="h-6 w-6 text-green-600" />
                3. Kullanıcı Hesapları
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <h4 className="font-semibold mb-2">Hesap Oluşturma:</h4>
              <ul className="list-disc pl-6 mb-4 space-y-1">
                <li>18 yaşından büyük olmalısınız</li>
                <li>Doğru ve güncel bilgiler vermelisiniz</li>
                <li>Hesap başına bir kişi sorumludur</li>
                <li>Şirket hesapları yetkili kişi tarafından açılmalıdır</li>
              </ul>

              <h4 className="font-semibold mb-2">Hesap Güvenliği:</h4>
              <ul className="list-disc pl-6 space-y-1">
                <li>Güçlü şifre kullanın</li>
                <li>Hesap bilgilerinizi kimseyle paylaşmayın</li>
                <li>Şüpheli aktiviteleri derhal bildirin</li>
                <li>Hesabınızdan yapılan tüm işlemlerden siz sorumlusunuz</li>
              </ul>
            </CardContent>
          </Card>

          {/* Acceptable Use */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <CheckCircle className="h-6 w-6 text-green-600" />
                4. Kabul Edilebilir Kullanım
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <h4 className="font-semibold mb-2 text-green-700">İzin Verilen Kullanımlar:</h4>
              <ul className="list-disc pl-6 mb-4 space-y-1">
                <li>Yasal AI prompt'larını yönetme</li>
                <li>Takım çalışması ve işbirliği</li>
                <li>Eğitim ve araştırma amaçlı kullanım</li>
                <li>Ticari projeler (plan limitleri dahilinde)</li>
              </ul>

              <h4 className="font-semibold mb-2 text-red-700">Yasak Kullanımlar:</h4>
              <ul className="list-disc pl-6 space-y-1">
                <li>Yasadışı içerik oluşturma veya paylaşma</li>
                <li>Telif hakkı ihlali yapan içerikler</li>
                <li>Zararlı, tehditkar veya taciz edici içerikler</li>
                <li>Spam veya istenmeyen içerik gönderme</li>
                <li>Sistemi hackleme veya zarar verme girişimleri</li>
                <li>Başkalarının hesaplarına yetkisiz erişim</li>
              </ul>
            </CardContent>
          </Card>

          {/* Content Ownership */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Shield className="h-6 w-6 text-orange-600" />
                5. İçerik Sahipliği ve Lisans
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <h4 className="font-semibold mb-2">Sizin İçerikleriniz:</h4>
              <ul className="list-disc pl-6 mb-4 space-y-1">
                <li>Oluşturduğunuz prompt'ların telif hakkı size aittir</li>
                <li>İçeriklerinizi istediğiniz zaman silebilirsiniz</li>
                <li>Paylaştığınız içeriklerden siz sorumlusunuz</li>
                <li>Başkalarının telif haklarını ihlal etmemelisiniz</li>
              </ul>

              <h4 className="font-semibold mb-2">Platform Lisansı:</h4>
              <ul className="list-disc pl-6 space-y-1">
                <li>Hizmeti sunmak için içeriklerinizi işleme hakkımız vardır</li>
                <li>İçeriklerinizi satmayız veya üçüncü taraflarla paylaşmayız</li>
                <li>Yedekleme ve güvenlik amaçlı kopyalama yapabiliriz</li>
                <li>Hesap silindikten sonra içerikler 30 gün içinde silinir</li>
              </ul>
            </CardContent>
          </Card>

          {/* Payment Terms */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <DollarSign className="h-6 w-6 text-green-600" />
                6. Ödeme Şartları
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <h4 className="font-semibold mb-2">Ücretli Planlar:</h4>
              <ul className="list-disc pl-6 mb-4 space-y-1">
                <li>Aylık veya yıllık ödeme seçenekleri mevcuttur</li>
                <li>Ödemeler peşin olarak tahsil edilir</li>
                <li>Fiyatlar KDV dahildir</li>
                <li>Otomatik yenileme varsayılan olarak açıktır</li>
              </ul>

              <h4 className="font-semibold mb-2">İptal ve İade:</h4>
              <ul className="list-disc pl-6 space-y-1">
                <li>Aboneliği istediğiniz zaman iptal edebilirsiniz</li>
                <li>İptal sonrası mevcut dönem sonuna kadar hizmet devam eder</li>
                <li>İade politikası için destek ekibiyle iletişime geçin</li>
                <li>Kötüye kullanım durumunda iade yapılmayabilir</li>
              </ul>
            </CardContent>
          </Card>

          {/* Service Availability */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <AlertTriangle className="h-6 w-6 text-yellow-600" />
                7. Hizmet Kullanılabilirliği
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <ul className="list-disc pl-6 space-y-2">
                <li><strong>Uptime Hedefi:</strong> %99.9 kullanılabilirlik hedefliyoruz</li>
                <li><strong>Bakım:</strong> Planlı bakımlar önceden duyurulur</li>
                <li><strong>Kesintiler:</strong> Beklenmeyen kesintiler olabilir</li>
                <li><strong>Yedekleme:</strong> Düzenli veri yedeklemesi yapılır</li>
                <li><strong>Destek:</strong> Teknik destek iş saatlerinde sağlanır</li>
              </ul>
            </CardContent>
          </Card>

          {/* Termination */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <XCircle className="h-6 w-6 text-red-600" />
                8. Hesap Sonlandırma
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <h4 className="font-semibold mb-2">Kullanıcı Tarafından:</h4>
              <ul className="list-disc pl-6 mb-4 space-y-1">
                <li>Hesabınızı istediğiniz zaman silebilirsiniz</li>
                <li>Silme işlemi geri alınamaz</li>
                <li>Verileriniz 30 gün içinde tamamen silinir</li>
              </ul>

              <h4 className="font-semibold mb-2">Platform Tarafından:</h4>
              <ul className="list-disc pl-6 space-y-1">
                <li>Şartları ihlal eden hesaplar askıya alınabilir</li>
                <li>Yasadışı aktivite durumunda hesap kapatılır</li>
                <li>Uzun süre inaktif hesaplar silinebilir</li>
                <li>Sonlandırma öncesi uyarı gönderilir</li>
              </ul>
            </CardContent>
          </Card>

          {/* Liability */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>9. Sorumluluk Sınırlaması</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <ul className="list-disc pl-6 space-y-2">
                <li>Hizmet "olduğu gibi" sunulur</li>
                <li>Veri kaybından doğan zararlardan sorumlu değiliz</li>
                <li>Üçüncü taraf hizmetlerinden sorumlu değiliz</li>
                <li>Maksimum sorumluluk ödenen ücretle sınırlıdır</li>
                <li>Dolaylı zararlar kapsamında değildir</li>
              </ul>
            </CardContent>
          </Card>

          {/* Governing Law */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>10. Uygulanacak Hukuk</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <ul className="list-disc pl-6 space-y-2">
                <li>Bu şartlar Türkiye Cumhuriyeti hukukuna tabidir</li>
                <li>Uyuşmazlıklar Türkiye mahkemelerinde çözülür</li>
                <li>KVKK ve diğer yerel mevzuat uygulanır</li>
                <li>Uluslararası kullanıcılar için yerel yasalar da geçerlidir</li>
              </ul>
            </CardContent>
          </Card>

          {/* Contact */}
          <Card className="shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="p-8 text-center">
              <h2 className="text-2xl font-bold mb-4">Sorularınız mı var?</h2>
              <p className="mb-6 text-blue-100">
                Kullanım şartları hakkında sorularınız varsa bizimle iletişime geçin.
              </p>
              <div className="space-y-2">
                <p><strong>Hukuki Sorular:</strong> <EMAIL></p>
                <p><strong>Genel Sorular:</strong> <EMAIL></p>
                <p><strong>Adres:</strong> Türkiye</p>
              </div>
            </CardContent>
          </Card>

          {/* Updates */}
          <Card className="shadow-lg border-blue-200 bg-blue-50">
            <CardContent className="p-6">
              <h3 className="font-bold text-blue-900 mb-2">Şart Güncellemeleri</h3>
              <p className="text-blue-800 text-sm">
                Bu kullanım şartları gerektiğinde güncellenebilir. Önemli değişiklikler 
                e-posta ile bildirilecek ve platform üzerinde duyurulacaktır. 
                Güncellemeleri düzenli olarak kontrol etmenizi öneririz.
              </p>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-400">
            © 2024 PromptBir. Tüm hakları saklıdır.
          </p>
        </div>
      </footer>
    </div>
  )
}
