1:"$Sreact.fragment"
2:I[84573,["2123","static/chunks/icons-cf48fd5521a4059b.js","8611","static/chunks/radix-a46c666e17e8e156.js","1024","static/chunks/tanstack-7320d15109fbe6ff.js","7177","static/chunks/app/layout-8cadaad205f0c372.js"],"ErrorBoundary"]
3:I[49534,["2123","static/chunks/icons-cf48fd5521a4059b.js","8611","static/chunks/radix-a46c666e17e8e156.js","1024","static/chunks/tanstack-7320d15109fbe6ff.js","7177","static/chunks/app/layout-8cadaad205f0c372.js"],"QueryProvider"]
4:I[96750,["2123","static/chunks/icons-cf48fd5521a4059b.js","8611","static/chunks/radix-a46c666e17e8e156.js","1024","static/chunks/tanstack-7320d15109fbe6ff.js","7177","static/chunks/app/layout-8cadaad205f0c372.js"],"FormFeedbackProvider"]
5:I[87555,[],""]
6:I[31295,[],""]
7:I[89074,["2123","static/chunks/icons-cf48fd5521a4059b.js","8611","static/chunks/radix-a46c666e17e8e156.js","1024","static/chunks/tanstack-7320d15109fbe6ff.js","7177","static/chunks/app/layout-8cadaad205f0c372.js"],"Toaster"]
8:I[21506,["2123","static/chunks/icons-cf48fd5521a4059b.js","8611","static/chunks/radix-a46c666e17e8e156.js","1024","static/chunks/tanstack-7320d15109fbe6ff.js","7177","static/chunks/app/layout-8cadaad205f0c372.js"],"ServiceWorkerRegistration"]
9:I[94797,["2123","static/chunks/icons-cf48fd5521a4059b.js","8611","static/chunks/radix-a46c666e17e8e156.js","1024","static/chunks/tanstack-7320d15109fbe6ff.js","7177","static/chunks/app/layout-8cadaad205f0c372.js"],"PerformanceMonitorWrapper"]
a:I[6874,["3846","static/chunks/app/careers/page-af89bb4a83f594c8.js"],""]
11:I[28393,[],""]
:HL["/_next/static/css/c3b902a418318ff2.css","style"]
0:{"P":null,"b":"blGO2jkRXUrLNfG5yAlmR","p":"","c":["","careers"],"i":false,"f":[[["",{"children":["careers",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/c3b902a418318ff2.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"tr","children":[["$","head",null,{"children":[["$","link",null,{"rel":"dns-prefetch","href":"//fonts.googleapis.com"}],["$","link",null,{"rel":"dns-prefetch","href":"//fonts.gstatic.com"}],["$","script",null,{"type":"application/ld+json","dangerouslySetInnerHTML":{"__html":"{\"@context\":\"https://schema.org\",\"@type\":\"WebApplication\",\"name\":\"PromptFlow\",\"description\":\"AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformu. Projelerinizi organize edin, prompt'larınızı yönetin ve AI ile daha verimli çalışın.\",\"url\":\"https://promptbir.com\",\"applicationCategory\":\"DeveloperApplication\",\"operatingSystem\":\"Web Browser\",\"offers\":{\"@type\":\"Offer\",\"price\":\"0\",\"priceCurrency\":\"USD\"},\"author\":{\"@type\":\"Organization\",\"name\":\"PromptFlow\"}}"}}],["$","meta",null,{"name":"theme-color","content":"#3b82f6"}],["$","meta",null,{"name":"mobile-web-app-capable","content":"yes"}],["$","meta",null,{"name":"apple-mobile-web-app-capable","content":"yes"}],["$","meta",null,{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","meta",null,{"name":"format-detection","content":"telephone=no"}],["$","meta",null,{"name":"viewport","content":"width=device-width, initial-scale=1, viewport-fit=cover"}]]}],["$","body",null,{"className":"__variable_e8ce0c font-sans antialiased","children":["$","$L2",null,{"level":"critical","showDetails":false,"children":["$","$L3",null,{"children":["$","$L4",null,{"children":[["$","$L5",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}],["$","$L7",null,{}],["$","$L8",null,{}],["$","$L9",null,{}]]}]}]}]}]]}]]}],{"children":["careers",["$","$1","c",{"children":[null,["$","$L5",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50","children":[["$","header",null,{"className":"border-b border-gray-200 bg-white/80 backdrop-blur-sm","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4","children":["$","div",null,{"className":"flex items-center justify-between","children":[["$","$La",null,{"href":"/","className":"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-left h-5 w-5","aria-hidden":"true","children":[["$","path","1l729n",{"d":"m12 19-7-7 7-7"}],["$","path","x3x0zl",{"d":"M19 12H5"}],"$undefined"]}],["$","span",null,{"children":"Ana Sayfaya Dön"}]]}],["$","div",null,{"className":"flex items-center space-x-2","children":["$Lb","$Lc"]}]]}]}]}],"$Ld","$Le"]}],null,"$Lf"]}],{},null,false]},null,false]},null,false],"$L10",false]],"m":"$undefined","G":["$11",[]],"s":false,"S":true}
19:I[59665,[],"OutletBoundary"]
1b:I[74911,[],"AsyncMetadataOutlet"]
1d:I[59665,[],"ViewportBoundary"]
1f:I[59665,[],"MetadataBoundary"]
20:"$Sreact.suspense"
b:["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-sparkles h-8 w-8 text-blue-600","aria-hidden":"true","children":[["$","path","4pj2yx",{"d":"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"}],["$","path","1olli1",{"d":"M20 3v4"}],["$","path","1gvqau",{"d":"M22 5h-4"}],["$","path","vumght",{"d":"M4 17v2"}],["$","path","zchphs",{"d":"M5 18H3"}],"$undefined"]}]
c:["$","span",null,{"className":"text-xl font-bold text-gray-900","children":"PromptBir"}]
d:["$","main",null,{"className":"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","div",null,{"className":"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-users h-8 w-8 text-purple-600","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","path","16gr8j",{"d":"M16 3.128a4 4 0 0 1 0 7.744"}],["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],"$undefined"]}]}],["$","h1",null,{"className":"text-4xl sm:text-5xl font-bold text-gray-900 mb-6","children":"Ekibimize Katılın"}],["$","p",null,{"className":"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed","children":"AI teknolojilerinin geleceğini şekillendiren bir ekibin parçası olun. PromptBir'de yenilikçi projeler üzerinde çalışın ve kariyerinizi geliştirin."}]]}],["$","section",null,{"className":"mb-16","children":[["$","h2",null,{"className":"text-3xl font-bold text-gray-900 text-center mb-12","children":"Çalışma Kültürümüz"}],["$","div",null,{"className":"grid md:grid-cols-3 gap-8","children":[["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg text-center","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"className":"mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-zap h-6 w-6 text-blue-600","aria-hidden":"true","children":[["$","path","1xq2db",{"d":"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"}],"$undefined"]}]}],["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"İnovasyon Odaklı"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","p",null,{"className":"text-gray-600","children":"Sürekli öğrenme ve yenilik yapma kültürü ile teknolojinin sınırlarını zorluyoruz."}]}]]}],["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg text-center","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"className":"mx-auto w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-users h-6 w-6 text-green-600","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","path","16gr8j",{"d":"M16 3.128a4 4 0 0 1 0 7.744"}],["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],"$undefined"]}]}],["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"Takım Ruhu"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","p",null,{"className":"text-gray-600","children":"Birlikte başarıya ulaşan, birbirini destekleyen ve güçlü bir takım ruhu olan ekip."}]}]]}],"$L12"]}]]}],"$L13","$L14","$L15","$L16","$L17","$L18"]}]
e:["$","footer",null,{"className":"bg-gray-900 text-white py-8 mt-16","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center","children":["$","p",null,{"className":"text-gray-400","children":"© 2024 PromptBir. Tüm hakları saklıdır."}]}]}]
f:["$","$L19",null,{"children":["$L1a",["$","$L1b",null,{"promise":"$@1c"}]]}]
10:["$","$1","h",{"children":[null,[["$","$L1d",null,{"children":"$L1e"}],null],["$","$L1f",null,{"children":["$","div",null,{"hidden":true,"children":["$","$20",null,{"fallback":null,"children":"$L21"}]}]}]]}]
12:["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg text-center","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"className":"mx-auto w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-heart h-6 w-6 text-purple-600","aria-hidden":"true","children":[["$","path","c3ymky",{"d":"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"}],"$undefined"]}]}],["$","div",null,{"data-slot":"card-title","className":"font-semibold text-lg","children":"İş-Yaşam Dengesi"}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":["$","p",null,{"className":"text-gray-600","children":"Çalışanlarımızın mutluluğunu ve refahını önemseyen, esnek çalışma imkanları sunan ortam."}]}]]}]
13:["$","section",null,{"className":"mb-16","children":[["$","h2",null,{"className":"text-3xl font-bold text-gray-900 text-center mb-12","children":"Çalışan Avantajları"}],["$","div",null,{"className":"grid md:grid-cols-2 lg:grid-cols-3 gap-6","children":[["$","div","0",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg","children":["$","div",null,{"data-slot":"card-content","className":"p-6","children":["$","div",null,{"className":"flex items-start gap-4","children":[["$","div",null,{"className":"flex-shrink-0","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-dollar-sign h-6 w-6 text-green-600","aria-hidden":"true","children":[["$","line","7eqyqh",{"x1":"12","x2":"12","y1":"2","y2":"22"}],["$","path","1b0p4s",{"d":"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"}],"$undefined"]}]}],["$","div",null,{"children":[["$","h3",null,{"className":"font-semibold text-gray-900 mb-2","children":"Rekabetçi Maaş"}],["$","p",null,{"className":"text-gray-600 text-sm","children":"Sektör standartlarının üzerinde maaş ve performans bonusu"}]]}]]}]}]}],["$","div","1",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg","children":["$","div",null,{"data-slot":"card-content","className":"p-6","children":["$","div",null,{"className":"flex items-start gap-4","children":[["$","div",null,{"className":"flex-shrink-0","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-map-pin h-6 w-6 text-blue-600","aria-hidden":"true","children":[["$","path","1r0f0z",{"d":"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"}],["$","circle","ilqhr7",{"cx":"12","cy":"10","r":"3"}],"$undefined"]}]}],["$","div",null,{"children":[["$","h3",null,{"className":"font-semibold text-gray-900 mb-2","children":"Esnek Çalışma"}],["$","p",null,{"className":"text-gray-600 text-sm","children":"Remote çalışma imkanı ve esnek çalışma saatleri"}]]}]]}]}]}],["$","div","2",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg","children":["$","div",null,{"data-slot":"card-content","className":"p-6","children":["$","div",null,{"className":"flex items-start gap-4","children":[["$","div",null,{"className":"flex-shrink-0","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-rocket h-6 w-6 text-purple-600","aria-hidden":"true","children":[["$","path","m3kijz",{"d":"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"}],["$","path","1fmvmk",{"d":"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"}],["$","path","1f8sc4",{"d":"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"}],["$","path","qeys4",{"d":"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"}],"$undefined"]}]}],["$","div",null,{"children":[["$","h3",null,{"className":"font-semibold text-gray-900 mb-2","children":"Gelişim Fırsatları"}],["$","p",null,{"className":"text-gray-600 text-sm","children":"Eğitim bütçesi, konferanslar ve sertifikasyon desteği"}]]}]]}]}]}],["$","div","3",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg","children":["$","div",null,{"data-slot":"card-content","className":"p-6","children":["$","div",null,{"className":"flex items-start gap-4","children":[["$","div",null,{"className":"flex-shrink-0","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-heart h-6 w-6 text-red-600","aria-hidden":"true","children":[["$","path","c3ymky",{"d":"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"}],"$undefined"]}]}],"$L22"]}]}]}],"$L23","$L24"]}]]}]
14:["$","section",null,{"className":"mb-16","children":[["$","h2",null,{"className":"text-3xl font-bold text-gray-900 text-center mb-12","children":"Açık Pozisyonlar"}],["$","div",null,{"className":"space-y-6","children":[["$","div","1",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"className":"flex flex-col md:flex-row md:items-center md:justify-between gap-4","children":[["$","div",null,{"children":[["$","div",null,{"data-slot":"card-title","className":"font-semibold text-xl mb-2","children":"Senior Frontend Developer"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-base","children":"Modern web teknolojileri ile kullanıcı deneyimini geliştiren bir frontend developer arıyoruz."}]]}],["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 md:flex-shrink-0","children":"Başvur"}]]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","div",null,{"className":"grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4","children":[["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-users h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","path","16gr8j",{"d":"M16 3.128a4 4 0 0 1 0 7.744"}],["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"Engineering"}]]}],["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-map-pin h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","1r0f0z",{"d":"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"}],["$","circle","ilqhr7",{"cx":"12","cy":"10","r":"3"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"Remote / İstanbul"}]]}],["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"Full-time"}]]}],["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-chart-column h-4 w-4 text-gray-500","aria-hidden":"true","children":["$L25","$L26","$L27","$L28","$undefined"]}],"$L29"]}]]}],"$L2a"]}]]}],"$L2b","$L2c","$L2d"]}]]}]
15:["$","div",null,{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg mb-16 bg-gradient-to-r from-gray-50 to-blue-50 border-blue-200","children":["$","div",null,{"data-slot":"card-content","className":"p-8 text-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-users h-16 w-16 text-blue-600 mx-auto mb-6","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","path","16gr8j",{"d":"M16 3.128a4 4 0 0 1 0 7.744"}],["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],"$undefined"]}],["$","h3",null,{"className":"text-2xl font-bold text-gray-900 mb-4","children":"Aradığınız Pozisyon Yok mu?"}],["$","p",null,{"className":"text-gray-600 mb-6 max-w-2xl mx-auto","children":"Şu anda açık olmayan bir pozisyonda çalışmak istiyorsanız, CV'nizi gönderin. Uygun fırsat çıktığında sizinle iletişime geçelim."}],["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-10 rounded-md px-6 has-[>svg]:px-4 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white","children":"Spontan Başvuru Yap"}]]}]}]
16:["$","section",null,{"className":"mb-16","children":[["$","h2",null,{"className":"text-3xl font-bold text-gray-900 text-center mb-12","children":"Başvuru Süreci"}],["$","div",null,{"className":"grid md:grid-cols-4 gap-8","children":[["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4","children":["$","span",null,{"className":"text-blue-600 font-bold","children":"1"}]}],["$","h3",null,{"className":"font-semibold text-gray-900 mb-2","children":"Başvuru"}],["$","p",null,{"className":"text-gray-600 text-sm","children":"CV ve motivasyon mektubunuzu gönderin"}]]}],["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4","children":["$","span",null,{"className":"text-purple-600 font-bold","children":"2"}]}],["$","h3",null,{"className":"font-semibold text-gray-900 mb-2","children":"İnceleme"}],["$","p",null,{"className":"text-gray-600 text-sm","children":"Başvurunuzu detaylı olarak inceleriz"}]]}],["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4","children":["$","span",null,{"className":"text-green-600 font-bold","children":"3"}]}],["$","h3",null,{"className":"font-semibold text-gray-900 mb-2","children":"Görüşme"}],["$","p",null,{"className":"text-gray-600 text-sm","children":"Teknik ve kültürel uyum görüşmeleri"}]]}],["$","div",null,{"className":"text-center","children":[["$","div",null,{"className":"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4","children":["$","span",null,{"className":"text-orange-600 font-bold","children":"4"}]}],["$","h3",null,{"className":"font-semibold text-gray-900 mb-2","children":"Karar"}],["$","p",null,{"className":"text-gray-600 text-sm","children":"Sonucu 1 hafta içinde bildiririz"}]]}]]}]]}]
17:["$","section",null,{"className":"mb-16","children":["$","div",null,{"data-slot":"card","className":"flex flex-col gap-6 rounded-xl border py-6 shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white","children":["$","div",null,{"data-slot":"card-content","className":"px-6 py-12","children":[["$","h2",null,{"className":"text-3xl font-bold text-center mb-12","children":"Ekibimiz Rakamlarla"}],["$","div",null,{"className":"grid grid-cols-2 md:grid-cols-4 gap-8 text-center","children":[["$","div",null,{"children":[["$","div",null,{"className":"text-3xl font-bold mb-2","children":"15+"}],["$","div",null,{"className":"text-blue-100","children":"Takım Üyesi"}]]}],["$","div",null,{"children":[["$","div",null,{"className":"text-3xl font-bold mb-2","children":"5+"}],["$","div",null,{"className":"text-blue-100","children":"Farklı Uzmanlık"}]]}],["$","div",null,{"children":[["$","div",null,{"className":"text-3xl font-bold mb-2","children":"100%"}],["$","div",null,{"className":"text-blue-100","children":"Remote Uyumlu"}]]}],["$","div",null,{"children":[["$","div",null,{"className":"text-3xl font-bold mb-2","children":"4.8/5"}],["$","div",null,{"className":"text-blue-100","children":"Çalışan Memnuniyeti"}]]}]]}]]}]}]}]
18:["$","section",null,{"className":"text-center","children":["$","div",null,{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg","children":["$","div",null,{"data-slot":"card-content","className":"px-6 py-12","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-headphones h-16 w-16 text-blue-600 mx-auto mb-6","aria-hidden":"true","children":[["$","path","1xhozi",{"d":"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3"}],"$undefined"]}],["$","h2",null,{"className":"text-3xl font-bold text-gray-900 mb-4","children":"Sorularınız mı var?"}],["$","p",null,{"className":"text-gray-600 mb-8 max-w-2xl mx-auto","children":"Kariyer fırsatları, çalışma koşulları veya başvuru süreci hakkında sorularınız varsa bizimle iletişime geçin."}],["$","div",null,{"className":"flex flex-col sm:flex-row gap-4 justify-center","children":[["$","$La",null,{"href":"mailto:<EMAIL>","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs hover:bg-primary/90 h-10 rounded-md px-6 has-[>svg]:px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700","children":"<EMAIL>"}]}],["$","$La",null,{"href":"/contact","children":["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-10 rounded-md px-6 has-[>svg]:px-4","children":"İletişim Formu"}]}]]}]]}]}]}]
22:["$","div",null,{"children":[["$","h3",null,{"className":"font-semibold text-gray-900 mb-2","children":"Sağlık Sigortası"}],["$","p",null,{"className":"text-gray-600 text-sm","children":"Kapsamlı sağlık sigortası ve wellness programları"}]]}]
23:["$","div","4",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg","children":["$","div",null,{"data-slot":"card-content","className":"p-6","children":["$","div",null,{"className":"flex items-start gap-4","children":[["$","div",null,{"className":"flex-shrink-0","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-coffee h-6 w-6 text-orange-600","aria-hidden":"true","children":[["$","path","7u0qdc",{"d":"M10 2v2"}],["$","path","6buw04",{"d":"M14 2v2"}],["$","path","pwadti",{"d":"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1"}],["$","path","colzsn",{"d":"M6 2v2"}],"$undefined"]}]}],["$","div",null,{"children":[["$","h3",null,{"className":"font-semibold text-gray-900 mb-2","children":"Ofis İmkanları"}],["$","p",null,{"className":"text-gray-600 text-sm","children":"Modern ofis, ücretsiz yemek ve içecek"}]]}]]}]}]}]
24:["$","div","5",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg","children":["$","div",null,{"data-slot":"card-content","className":"p-6","children":["$","div",null,{"className":"flex items-start gap-4","children":[["$","div",null,{"className":"flex-shrink-0","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-star h-6 w-6 text-yellow-600","aria-hidden":"true","children":[["$","path","r04s7s",{"d":"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"}],"$undefined"]}]}],["$","div",null,{"children":[["$","h3",null,{"className":"font-semibold text-gray-900 mb-2","children":"Hisse Senedi"}],["$","p",null,{"className":"text-gray-600 text-sm","children":"Şirket büyümesinden pay alma fırsatı"}]]}]]}]}]}]
25:["$","path","c24i48",{"d":"M3 3v16a2 2 0 0 0 2 2h16"}]
26:["$","path","2bz60n",{"d":"M18 17V9"}]
27:["$","path","1frdt8",{"d":"M13 17V5"}]
28:["$","path","17ska0",{"d":"M8 17v-3"}]
29:["$","span",null,{"className":"text-sm text-gray-600","children":"3+ yıl"}]
2a:["$","div",null,{"className":"flex flex-wrap gap-2","children":[["$","span","0",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"React"}],["$","span","1",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"TypeScript"}],["$","span","2",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"Next.js"}],["$","span","3",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"Tailwind CSS"}]]}]
2b:["$","div","2",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"className":"flex flex-col md:flex-row md:items-center md:justify-between gap-4","children":[["$","div",null,{"children":[["$","div",null,{"data-slot":"card-title","className":"font-semibold text-xl mb-2","children":"Backend Developer"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-base","children":"Ölçeklenebilir backend sistemleri geliştiren deneyimli bir developer arıyoruz."}]]}],["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 md:flex-shrink-0","children":"Başvur"}]]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","div",null,{"className":"grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4","children":[["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-users h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","path","16gr8j",{"d":"M16 3.128a4 4 0 0 1 0 7.744"}],["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"Engineering"}]]}],["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-map-pin h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","1r0f0z",{"d":"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"}],["$","circle","ilqhr7",{"cx":"12","cy":"10","r":"3"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"Remote / İstanbul"}]]}],["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"Full-time"}]]}],["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-chart-column h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","c24i48",{"d":"M3 3v16a2 2 0 0 0 2 2h16"}],["$","path","2bz60n",{"d":"M18 17V9"}],["$","path","1frdt8",{"d":"M13 17V5"}],["$","path","17ska0",{"d":"M8 17v-3"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"2+ yıl"}]]}]]}],"$L2e"]}]]}]
2c:["$","div","3",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"className":"flex flex-col md:flex-row md:items-center md:justify-between gap-4","children":[["$","div",null,{"children":[["$","div",null,{"data-slot":"card-title","className":"font-semibold text-xl mb-2","children":"Product Designer"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-base","children":"Kullanıcı odaklı tasarımlar yapan yaratıcı bir product designer arıyoruz."}]]}],["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 md:flex-shrink-0","children":"Başvur"}]]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","div",null,{"className":"grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4","children":[["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-users h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","path","16gr8j",{"d":"M16 3.128a4 4 0 0 1 0 7.744"}],["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"Design"}]]}],["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-map-pin h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","1r0f0z",{"d":"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"}],["$","circle","ilqhr7",{"cx":"12","cy":"10","r":"3"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"Remote / İstanbul"}]]}],["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"Full-time"}]]}],["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-chart-column h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","c24i48",{"d":"M3 3v16a2 2 0 0 0 2 2h16"}],["$","path","2bz60n",{"d":"M18 17V9"}],["$","path","1frdt8",{"d":"M13 17V5"}],["$","path","17ska0",{"d":"M8 17v-3"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"2+ yıl"}]]}]]}],"$L2f"]}]]}]
2d:["$","div","4",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-lg","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":["$","div",null,{"className":"flex flex-col md:flex-row md:items-center md:justify-between gap-4","children":[["$","div",null,{"children":[["$","div",null,{"data-slot":"card-title","className":"font-semibold text-xl mb-2","children":"DevOps Engineer"}],["$","div",null,{"data-slot":"card-description","className":"text-muted-foreground text-base","children":"Altyapı ve deployment süreçlerini optimize eden bir DevOps engineer arıyoruz."}]]}],["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs hover:bg-primary/90 h-9 px-4 py-2 has-[>svg]:px-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 md:flex-shrink-0","children":"Başvur"}]]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","div",null,{"className":"grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4","children":[["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-users h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","path","16gr8j",{"d":"M16 3.128a4 4 0 0 1 0 7.744"}],["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"Engineering"}]]}],["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-map-pin h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","1r0f0z",{"d":"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"}],["$","circle","ilqhr7",{"cx":"12","cy":"10","r":"3"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"Remote / İstanbul"}]]}],["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"Full-time"}]]}],["$","div",null,{"className":"flex items-center gap-2","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-chart-column h-4 w-4 text-gray-500","aria-hidden":"true","children":[["$","path","c24i48",{"d":"M3 3v16a2 2 0 0 0 2 2h16"}],["$","path","2bz60n",{"d":"M18 17V9"}],["$","path","1frdt8",{"d":"M13 17V5"}],["$","path","17ska0",{"d":"M8 17v-3"}],"$undefined"]}],["$","span",null,{"className":"text-sm text-gray-600","children":"3+ yıl"}]]}]]}],"$L30"]}]]}]
2e:["$","div",null,{"className":"flex flex-wrap gap-2","children":[["$","span","0",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"Node.js"}],["$","span","1",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"PostgreSQL"}],["$","span","2",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"Supabase"}],["$","span","3",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"API Design"}]]}]
2f:["$","div",null,{"className":"flex flex-wrap gap-2","children":[["$","span","0",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"Figma"}],["$","span","1",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"UI/UX"}],["$","span","2",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"Design Systems"}],["$","span","3",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"User Research"}]]}]
30:["$","div",null,{"className":"flex flex-wrap gap-2","children":[["$","span","0",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"Docker"}],["$","span","1",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"Kubernetes"}],["$","span","2",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"AWS"}],["$","span","3",{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 text-xs","children":"CI/CD"}]]}]
1e:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"}],["$","meta","2",{"name":"theme-color","media":"(prefers-color-scheme: light)","content":"#ffffff"}],["$","meta","3",{"name":"theme-color","media":"(prefers-color-scheme: dark)","content":"#000000"}]]
1a:null
31:I[38175,[],"IconMark"]
1c:{"metadata":[["$","title","0",{"children":"Kariyer - PromptBir | İş İlanları ve Kariyer Fırsatları"}],["$","meta","1",{"name":"description","content":"PromptBir ekibine katılın! AI teknolojileri alanında kariyer fırsatları, açık pozisyonlar ve çalışma kültürümüz hakkında bilgi edinin."}],["$","meta","2",{"name":"author","content":"PromptFlow Team"}],["$","link","3",{"rel":"manifest","href":"/site.webmanifest","crossOrigin":"$undefined"}],["$","meta","4",{"name":"keywords","content":"PromptBir kariyer,iş ilanları,AI şirketi iş,yazılım geliştirici iş,startup kariyer,teknoloji işleri"}],["$","meta","5",{"name":"creator","content":"PromptFlow Team"}],["$","meta","6",{"name":"publisher","content":"PromptFlow"}],["$","meta","7",{"name":"robots","content":"index, follow"}],["$","meta","8",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","9",{"name":"category","content":"Technology"}],["$","meta","10",{"name":"theme-color","content":"#3b82f6"}],["$","meta","11",{"name":"msapplication-TileColor","content":"#3b82f6"}],["$","meta","12",{"name":"apple-mobile-web-app-capable","content":"yes"}],["$","meta","13",{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","meta","14",{"name":"format-detection","content":"telephone=no"}],["$","link","15",{"rel":"canonical","href":"https://promptbir.com"}],["$","link","16",{"rel":"alternate","hrefLang":"tr-TR","href":"https://promptbir.com"}],["$","link","17",{"rel":"alternate","hrefLang":"en-US","href":"https://promptbir.com/en"}],["$","meta","18",{"name":"google-site-verification","content":"your-google-verification-code"}],["$","meta","19",{"property":"og:title","content":"Kariyer - PromptBir"}],["$","meta","20",{"property":"og:description","content":"AI teknolojileri alanında kariyer fırsatları ve açık pozisyonlar"}],["$","meta","21",{"property":"og:url","content":"https://promptbir.com/careers"}],["$","meta","22",{"property":"og:type","content":"website"}],["$","meta","23",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","24",{"name":"twitter:site","content":"@promptbir"}],["$","meta","25",{"name":"twitter:creator","content":"@promptbir"}],["$","meta","26",{"name":"twitter:title","content":"Promptbir - AI Prompt Yönetim Platformu"}],["$","meta","27",{"name":"twitter:description","content":"Yapay zeka prompt'larınızı profesyonelce yönetin. Takımınızla paylaşın, organize edin ve verimliliğinizi artırın."}],["$","meta","28",{"name":"twitter:image","content":"https://promptbir.com/twitter-image.png"}],["$","link","29",{"rel":"shortcut icon","href":"/favicon.ico"}],["$","link","30",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","link","31",{"rel":"icon","href":"/favicon-16x16.png","sizes":"16x16","type":"image/png"}],["$","link","32",{"rel":"icon","href":"/favicon-32x32.png","sizes":"32x32","type":"image/png"}],["$","link","33",{"rel":"icon","href":"/favicon.ico","sizes":"any"}],["$","link","34",{"rel":"apple-touch-icon","href":"/apple-touch-icon.png"}],["$","$L31","35",{}]],"error":null,"digest":"$undefined"}
21:"$1c:metadata"
