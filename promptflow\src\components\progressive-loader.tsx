'use client'

import { useState, useEffect, ReactNode } from 'react'

interface ProgressiveLoaderProps {
  children: ReactNode
  fallback?: ReactNode
  delay?: number
  className?: string
}

interface LoadingSkeletonProps {
  className?: string
}

export function LoadingSkeleton({ className = '' }: LoadingSkeletonProps) {
  return (
    <div className={`animate-pulse ${className}`}>
      <div className="space-y-4">
        {/* Header skeleton */}
        <div className="h-8 bg-gray-200 rounded-lg w-1/3"></div>
        
        {/* Content skeleton */}
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 rounded w-full"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          <div className="h-4 bg-gray-200 rounded w-4/6"></div>
        </div>
        
        {/* Card skeletons */}
        <div className="grid gap-4 mt-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="border border-gray-200 rounded-lg p-4">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export function ProgressiveLoader({ 
  children, 
  fallback, 
  delay = 200,
  className = '' 
}: ProgressiveLoaderProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [showFallback, setShowFallback] = useState(false)

  useEffect(() => {
    // Delay gösterimi için timer
    const delayTimer = setTimeout(() => {
      setShowFallback(true)
    }, delay)

    // Component yüklendiğinde
    const loadTimer = setTimeout(() => {
      setIsLoaded(true)
    }, delay + 100)

    return () => {
      clearTimeout(delayTimer)
      clearTimeout(loadTimer)
    }
  }, [delay])

  if (!isLoaded) {
    if (!showFallback) {
      return null // İlk delay sırasında hiçbir şey gösterme
    }
    
    return (
      <div className={className}>
        {fallback || <LoadingSkeleton />}
      </div>
    )
  }

  return <>{children}</>
}

// Specialized loaders for different content types
export function PromptListSkeleton() {
  return (
    <div className="space-y-3">
      {[1, 2, 3, 4, 5].map((i) => (
        <div key={i} className="border border-gray-200 rounded-lg p-4 animate-pulse">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-3"></div>
              <div className="flex gap-2">
                <div className="h-5 bg-blue-100 rounded-full w-16"></div>
                <div className="h-5 bg-green-100 rounded-full w-20"></div>
              </div>
            </div>
            <div className="h-8 w-8 bg-gray-200 rounded"></div>
          </div>
        </div>
      ))}
    </div>
  )
}

export function SidebarSkeleton() {
  return (
    <div className="w-full h-full bg-white border-r border-gray-200 animate-pulse">
      <div className="p-4">
        <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="space-y-2">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-10 bg-gray-100 rounded"></div>
          ))}
        </div>
      </div>
    </div>
  )
}

export function ContextGallerySkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 animate-pulse">
      {[1, 2, 3, 4, 5, 6].map((i) => (
        <div key={i} className="border border-gray-200 rounded-lg p-4">
          <div className="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded w-full"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            <div className="h-3 bg-gray-200 rounded w-4/6"></div>
          </div>
          <div className="flex gap-2 mt-4">
            <div className="h-5 bg-blue-100 rounded-full w-16"></div>
            <div className="h-5 bg-purple-100 rounded-full w-20"></div>
          </div>
        </div>
      ))}
    </div>
  )
}

// Hook for progressive loading with intersection observer
export function useProgressiveLoading(threshold = 0.1) {
  const [isVisible, setIsVisible] = useState(false)
  const [ref, setRef] = useState<HTMLElement | null>(null)

  useEffect(() => {
    if (!ref) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold }
    )

    observer.observe(ref)

    return () => observer.disconnect()
  }, [ref, threshold])

  return { isVisible, ref: setRef }
}
