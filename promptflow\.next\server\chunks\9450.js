"use strict";exports.id=9450,exports.ids=[9450],exports.modules={75034:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("pen-line",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},79450:(a,b,c)=>{c.r(b),c.d(b,{ContextEditModal:()=>w});var d=c(60687),e=c(43210),f=c(29523),g=c(89667),h=c(80013),i=c(34729),j=c(96834),k=c(63503),l=c(15079),m=c(75034),n=c(93613),o=c(96474),p=c(37360),q=c(11860),r=c(64021),s=c(11437),t=c(41862),u=c(3178),v=c(52581);function w({open:a,onOpenChange:b,context:c,onSuccess:w}){let[x,y]=(0,e.useState)({title:"",description:"",content:"",category_id:"",is_public:!1,is_template:!1,tags:[]}),[z,A]=(0,e.useState)(""),[B,C]=(0,e.useState)({}),{data:D=[],isLoading:E}=(0,u.RH)(),F=(0,u.sd)(),G=async a=>{if(a.preventDefault(),c){if(!(()=>{let a={};return x.title.trim()||(a.title="Başlık gereklidir"),x.content.trim()||(a.content="İ\xe7erik gereklidir"),x.category_id||(a.category_id="Kategori se\xe7imi gereklidir"),C(a),0===Object.keys(a).length})())return void v.oR.error("L\xfctfen t\xfcm gerekli alanları doldurun");try{await F.mutateAsync({id:c.id,updates:{title:x.title.trim(),description:x.description.trim()||void 0,content:x.content.trim(),category_id:x.category_id,is_public:x.is_public,is_template:x.is_template,tags:x.tags}}),v.oR.success("Context başarıyla g\xfcncellendi!"),w?.(),b(!1)}catch(a){console.error("Context update error:",a),v.oR.error("Context g\xfcncellenirken bir hata oluştu")}}},H=()=>{z.trim()&&!x.tags.includes(z.trim())&&(y(a=>({...a,tags:[...a.tags,z.trim()]})),A(""))};return c?(0,d.jsx)(k.lG,{open:a,onOpenChange:b,children:(0,d.jsxs)(k.Cf,{className:"max-w-2xl max-h-[90vh] overflow-hidden flex flex-col z-[60]",children:[(0,d.jsxs)(k.c7,{className:"flex-shrink-0",children:[(0,d.jsxs)(k.L3,{className:"flex items-center gap-2",children:[(0,d.jsx)(m.A,{className:"h-5 w-5"}),"Context D\xfczenle"]}),(0,d.jsx)(k.rr,{children:"Context bilgilerini d\xfczenleyin. Herkese a\xe7ık contextler admin onayı gerektirir."})]}),(0,d.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,d.jsxs)("form",{onSubmit:G,className:"space-y-6 p-1",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(h.J,{htmlFor:"title",children:["Başlık ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)(g.p,{id:"title",placeholder:"Context başlığını girin...",value:x.title,onChange:a=>y(b=>({...b,title:a.target.value})),className:B.title?"border-red-500":""}),B.title&&(0,d.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,d.jsx)(n.A,{className:"h-3 w-3"}),B.title]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"description",children:"A\xe7ıklama"}),(0,d.jsx)(g.p,{id:"description",placeholder:"Context a\xe7ıklaması (isteğe bağlı)...",value:x.description,onChange:a=>y(b=>({...b,description:a.target.value}))})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(h.J,{htmlFor:"category",children:["Kategori ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsxs)(l.l6,{value:x.category_id,onValueChange:a=>y(b=>({...b,category_id:a})),children:[(0,d.jsx)(l.bq,{className:B.category_id?"border-red-500":"",children:(0,d.jsx)(l.yv,{placeholder:"Kategori se\xe7in..."})}),(0,d.jsx)(l.gC,{children:E?(0,d.jsx)(l.eb,{value:"loading",disabled:!0,children:"Kategoriler y\xfckleniyor..."}):D.map(a=>(0,d.jsx)(l.eb,{value:a.id,children:(0,d.jsxs)("span",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{children:a.icon}),a.name]})},a.id))})]}),B.category_id&&(0,d.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,d.jsx)(n.A,{className:"h-3 w-3"}),B.category_id]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(h.J,{htmlFor:"content",children:["İ\xe7erik ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)(i.T,{id:"content",placeholder:"Context i\xe7eriğini girin...",value:x.content,onChange:a=>y(b=>({...b,content:a.target.value})),className:`min-h-[120px] resize-none ${B.content?"border-red-500":""}`}),B.content&&(0,d.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,d.jsx)(n.A,{className:"h-3 w-3"}),B.content]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.J,{htmlFor:"tags",children:"Etiketler"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(g.p,{id:"tags",placeholder:"Etiket ekle...",value:z,onChange:a=>A(a.target.value),onKeyPress:a=>{"Enter"===a.key&&(a.preventDefault(),H())},className:"flex-1"}),(0,d.jsx)(f.$,{type:"button",variant:"outline",size:"sm",onClick:H,disabled:!z.trim(),children:(0,d.jsx)(o.A,{className:"h-4 w-4"})})]}),x.tags.length>0&&(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:x.tags.map(a=>(0,d.jsxs)(j.E,{variant:"secondary",className:"flex items-center gap-1",children:[(0,d.jsx)(p.A,{className:"h-3 w-3"}),a,(0,d.jsx)("button",{type:"button",onClick:()=>{y(b=>({...b,tags:b.tags.filter(b=>b!==a)}))},className:"ml-1 hover:text-red-500",children:(0,d.jsx)(q.A,{className:"h-3 w-3"})})]},a))})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(h.J,{children:"G\xf6r\xfcn\xfcrl\xfck Ayarları"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,d.jsx)("input",{type:"radio",name:"visibility",checked:!x.is_public,onChange:()=>y(a=>({...a,is_public:!1})),className:"w-4 h-4"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(r.A,{className:"h-4 w-4 text-gray-500"}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"text-sm",children:"\xd6zel"}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"Sadece ben g\xf6rebilirim"})]})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,d.jsx)("input",{type:"radio",name:"visibility",checked:x.is_public,onChange:()=>y(a=>({...a,is_public:!0})),className:"w-4 h-4"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(s.A,{className:"h-4 w-4 text-blue-500"}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"text-sm",children:"Herkese A\xe7ık"}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"T\xfcm kullanıcılar g\xf6rebilir"})]})]})]})]})]}),(0,d.jsx)("div",{className:"space-y-2",children:(0,d.jsxs)("label",{className:"flex items-center gap-3 cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:x.is_template,onChange:a=>y(b=>({...b,is_template:a.target.checked})),className:"w-4 h-4"}),(0,d.jsx)("span",{className:"text-sm",children:"Bu contexti şablon olarak işaretle"})]})}),(0,d.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,d.jsx)(f.$,{type:"button",variant:"outline",onClick:()=>b(!1),className:"flex-1",children:"İptal"}),(0,d.jsxs)(f.$,{type:"submit",disabled:F.isPending,className:"flex-1",children:[F.isPending&&(0,d.jsx)(t.A,{className:"mr-2 h-4 w-4 animate-spin"}),"G\xfcncelle"]})]})]})})]})}):null}}};