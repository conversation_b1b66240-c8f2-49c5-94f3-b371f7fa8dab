#!/usr/bin/env node

/**
 * Performance Monitoring Script
 * Comprehensive performance testing and monitoring setup
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// Performance monitoring configuration
const PERFORMANCE_CONFIG = {
  lighthouse: {
    urls: [
      'http://localhost:4444',
      'http://localhost:4444/auth',
      'http://localhost:4444/dashboard',
      'http://localhost:4444/profile',
      'http://localhost:4444/templates',
    ],
    thresholds: {
      performance: 80,
      accessibility: 90,
      bestPractices: 80,
      seo: 90,
    },
  },
  bundleAnalysis: {
    maxBundleSize: 500 * 1024, // 500KB
    maxChunkSize: 200 * 1024,  // 200KB
    maxAssetSize: 100 * 1024,  // 100KB
  },
  webVitals: {
    lcp: 2500,  // ms
    fid: 100,   // ms
    cls: 0.1,   // score
    fcp: 1800,  // ms
    ttfb: 800,  // ms
  },
}

class PerformanceMonitor {
  constructor() {
    this.results = {
      lighthouse: {},
      bundleAnalysis: {},
      webVitals: {},
      timestamp: new Date().toISOString(),
    }
  }

  // Run comprehensive performance tests
  async runTests() {
    console.log('🚀 Starting performance monitoring...\n')

    try {
      await this.runLighthouseTests()
      await this.runBundleAnalysis()
      await this.generateReport()
      
      console.log('\n✅ Performance monitoring completed!')
      console.log(`📊 Report saved to: ${this.getReportPath()}`)
      
    } catch (error) {
      console.error('❌ Performance monitoring failed:', error.message)
      process.exit(1)
    }
  }

  // Run Lighthouse tests
  async runLighthouseTests() {
    console.log('🔍 Running Lighthouse tests...')
    
    try {
      // Check if Lighthouse CI is installed
      execSync('npx lhci --version', { stdio: 'ignore' })
      
      // Run Lighthouse CI
      const output = execSync('npx lhci autorun --config=lighthouse.config.js', {
        encoding: 'utf8',
        stdio: 'pipe'
      })
      
      this.results.lighthouse = this.parseLighthouseOutput(output)
      console.log('✅ Lighthouse tests completed')
      
    } catch (error) {
      console.warn('⚠️ Lighthouse tests failed:', error.message)
      this.results.lighthouse = { error: error.message }
    }
  }

  // Parse Lighthouse output
  parseLighthouseOutput(output) {
    const results = {}
    
    try {
      // Extract scores from output
      const scoreRegex = /(\w+):\s*(\d+)/g
      let match
      
      while ((match = scoreRegex.exec(output)) !== null) {
        const [, category, score] = match
        results[category] = parseInt(score)
      }
      
      // Check against thresholds
      results.passed = Object.entries(PERFORMANCE_CONFIG.lighthouse.thresholds)
        .every(([category, threshold]) => (results[category] || 0) >= threshold)
      
    } catch (error) {
      results.error = 'Failed to parse Lighthouse output'
    }
    
    return results
  }

  // Run bundle analysis
  async runBundleAnalysis() {
    console.log('📦 Running bundle analysis...')
    
    try {
      // Build the project
      execSync('npm run build', { stdio: 'ignore' })
      
      // Analyze bundle
      const buildDir = path.join(process.cwd(), '.next')
      const stats = this.analyzeBuildDirectory(buildDir)
      
      this.results.bundleAnalysis = {
        ...stats,
        passed: this.checkBundleThresholds(stats)
      }
      
      console.log('✅ Bundle analysis completed')
      
    } catch (error) {
      console.warn('⚠️ Bundle analysis failed:', error.message)
      this.results.bundleAnalysis = { error: error.message }
    }
  }

  // Analyze build directory
  analyzeBuildDirectory(buildDir) {
    const stats = {
      totalSize: 0,
      chunks: [],
      assets: [],
      largestChunk: 0,
      largestAsset: 0,
    }

    try {
      const staticDir = path.join(buildDir, 'static')
      
      if (fs.existsSync(staticDir)) {
        this.analyzeDirectory(staticDir, stats)
      }
      
    } catch (error) {
      stats.error = error.message
    }

    return stats
  }

  // Analyze directory recursively
  analyzeDirectory(dir, stats) {
    const files = fs.readdirSync(dir)
    
    files.forEach(file => {
      const filePath = path.join(dir, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isDirectory()) {
        this.analyzeDirectory(filePath, stats)
      } else {
        const size = stat.size
        stats.totalSize += size
        
        if (file.endsWith('.js')) {
          stats.chunks.push({ name: file, size })
          stats.largestChunk = Math.max(stats.largestChunk, size)
        } else {
          stats.assets.push({ name: file, size })
          stats.largestAsset = Math.max(stats.largestAsset, size)
        }
      }
    })
  }

  // Check bundle against thresholds
  checkBundleThresholds(stats) {
    const { bundleAnalysis } = PERFORMANCE_CONFIG
    
    return (
      stats.largestChunk <= bundleAnalysis.maxChunkSize &&
      stats.largestAsset <= bundleAnalysis.maxAssetSize &&
      stats.totalSize <= bundleAnalysis.maxBundleSize * 10 // Allow 10x for total
    )
  }

  // Generate performance report
  async generateReport() {
    console.log('📝 Generating performance report...')
    
    const report = this.createReport()
    const reportPath = this.getReportPath()
    
    // Ensure reports directory exists
    const reportsDir = path.dirname(reportPath)
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true })
    }
    
    // Write report
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    
    // Generate HTML report
    const htmlReport = this.createHTMLReport(report)
    const htmlPath = reportPath.replace('.json', '.html')
    fs.writeFileSync(htmlPath, htmlReport)
    
    console.log('✅ Performance report generated')
  }

  // Create performance report
  createReport() {
    const { lighthouse, bundleAnalysis } = this.results
    
    return {
      timestamp: this.results.timestamp,
      summary: {
        overallPassed: lighthouse.passed && bundleAnalysis.passed,
        lighthousePassed: lighthouse.passed,
        bundlePassed: bundleAnalysis.passed,
      },
      lighthouse: {
        scores: lighthouse,
        thresholds: PERFORMANCE_CONFIG.lighthouse.thresholds,
      },
      bundle: {
        analysis: bundleAnalysis,
        thresholds: PERFORMANCE_CONFIG.bundleAnalysis,
      },
      recommendations: this.generateRecommendations(),
    }
  }

  // Generate performance recommendations
  generateRecommendations() {
    const recommendations = []
    const { lighthouse, bundleAnalysis } = this.results
    
    // Lighthouse recommendations
    if (lighthouse.performance < 80) {
      recommendations.push({
        category: 'Performance',
        priority: 'high',
        issue: 'Low Lighthouse performance score',
        solution: 'Optimize images, reduce bundle size, implement lazy loading'
      })
    }
    
    if (lighthouse.accessibility < 90) {
      recommendations.push({
        category: 'Accessibility',
        priority: 'high',
        issue: 'Accessibility issues detected',
        solution: 'Add alt text, improve color contrast, fix ARIA labels'
      })
    }
    
    // Bundle recommendations
    if (bundleAnalysis.largestChunk > PERFORMANCE_CONFIG.bundleAnalysis.maxChunkSize) {
      recommendations.push({
        category: 'Bundle Size',
        priority: 'medium',
        issue: 'Large JavaScript chunks detected',
        solution: 'Implement code splitting, remove unused dependencies'
      })
    }
    
    if (bundleAnalysis.largestAsset > PERFORMANCE_CONFIG.bundleAnalysis.maxAssetSize) {
      recommendations.push({
        category: 'Assets',
        priority: 'medium',
        issue: 'Large asset files detected',
        solution: 'Optimize images, use modern formats (WebP, AVIF)'
      })
    }
    
    return recommendations
  }

  // Create HTML report
  createHTMLReport(report) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PromptFlow Performance Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 40px; }
        .status { display: inline-block; padding: 8px 16px; border-radius: 20px; font-weight: bold; }
        .status.passed { background: #d4edda; color: #155724; }
        .status.failed { background: #f8d7da; color: #721c24; }
        .section { margin: 30px 0; }
        .score { display: inline-block; width: 60px; height: 60px; border-radius: 50%; text-align: center; line-height: 60px; font-weight: bold; color: white; margin: 10px; }
        .score.good { background: #28a745; }
        .score.warning { background: #ffc107; color: #212529; }
        .score.poor { background: #dc3545; }
        .recommendations { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        .recommendation { margin: 15px 0; padding: 15px; border-left: 4px solid #007bff; background: white; }
        .priority-high { border-left-color: #dc3545; }
        .priority-medium { border-left-color: #ffc107; }
        .priority-low { border-left-color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PromptFlow Performance Report</h1>
            <p>Generated: ${report.timestamp}</p>
            <div class="status ${report.summary.overallPassed ? 'passed' : 'failed'}">
                ${report.summary.overallPassed ? '✅ PASSED' : '❌ FAILED'}
            </div>
        </div>

        <div class="section">
            <h2>Lighthouse Scores</h2>
            <div>
                ${Object.entries(report.lighthouse.scores).map(([category, score]) => {
                  if (category === 'passed' || category === 'error') return ''
                  const className = score >= 90 ? 'good' : score >= 70 ? 'warning' : 'poor'
                  return `<div class="score ${className}">${score}</div>`
                }).join('')}
            </div>
        </div>

        <div class="section">
            <h2>Bundle Analysis</h2>
            <p><strong>Total Size:</strong> ${Math.round(report.bundle.analysis.totalSize / 1024)}KB</p>
            <p><strong>Largest Chunk:</strong> ${Math.round(report.bundle.analysis.largestChunk / 1024)}KB</p>
            <p><strong>Largest Asset:</strong> ${Math.round(report.bundle.analysis.largestAsset / 1024)}KB</p>
        </div>

        <div class="section recommendations">
            <h2>Recommendations</h2>
            ${report.recommendations.map(rec => `
                <div class="recommendation priority-${rec.priority}">
                    <h4>${rec.category} - ${rec.priority.toUpperCase()} Priority</h4>
                    <p><strong>Issue:</strong> ${rec.issue}</p>
                    <p><strong>Solution:</strong> ${rec.solution}</p>
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>
    `.trim()
  }

  // Get report file path
  getReportPath() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    return path.join(process.cwd(), 'reports', `performance-${timestamp}.json`)
  }
}

// CLI interface
if (require.main === module) {
  const monitor = new PerformanceMonitor()
  monitor.runTests().catch(console.error)
}

module.exports = PerformanceMonitor
