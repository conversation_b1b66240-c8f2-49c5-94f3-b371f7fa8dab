{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Masa%C3%BCst%C3%BC/Promptbir/promptflow/src/app/blog/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { <PERSON>ada<PERSON> } from 'next'\nimport Link from 'next/link'\nimport { notFound } from 'next/navigation'\nimport { Card, CardContent, CardHeader } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { \n  ArrowLeft, \n  Sparkles, \n  Calendar, \n  Clock, \n  User,\n  Share2,\n  BookOpen,\n  ArrowRight\n} from 'lucide-react'\n\n// Blog posts data (in a real app, this would come from a CMS or database)\nconst blogPosts = {\n  'promptbir-ile-ai-verimlilik': {\n    id: 1,\n    title: 'PromptBir ile AI Verimliliğinizi 10 Kat Artırın',\n    slug: 'promptbir-ile-ai-verimlilik',\n    excerpt: 'AI prompt\\'larınızı organize ederek nasıl daha verimli çalışabileceğinizi öğrenin. PromptBir\\'in sunduğu özellikler ile iş akışınızı optimize edin.',\n    author: 'PromptBir Ekibi',\n    publishDate: '2024-01-15',\n    readTime: '8 dakika',\n    category: 'Verim<PERSON><PERSON>',\n    tags: ['AI', 'Verimli<PERSON>', 'Prompt Yönetimi'],\n    featured: true,\n    content: `\n# PromptBir ile AI Verimliliğinizi 10 Kat Artırın\n\nAI teknolojilerinin hızla geliştiği günümüzde, doğru prompt yönetimi kritik önem taşıyor. PromptBir, AI prompt'larınızı organize etmenizi ve verimliliğinizi maksimize etmenizi sağlayan güçlü bir platform.\n\n## Neden Prompt Yönetimi Önemli?\n\nModern iş dünyasında AI araçları günlük rutinimizin ayrılmaz bir parçası haline geldi. ChatGPT, Claude, Gemini gibi AI asistanları ile sürekli etkileşim halindeyiz. Ancak çoğu zaman:\n\n- Daha önce kullandığımız etkili prompt'ları hatırlayamıyoruz\n- Benzer görevler için sürekli yeni prompt'lar yazıyoruz\n- Takım arkadaşlarımızla prompt'ları paylaşamıyoruz\n- Hangi prompt'un hangi durumda daha etkili olduğunu bilmiyoruz\n\n## PromptBir'in Sunduğu Çözümler\n\n### 1. Merkezi Prompt Kütüphanesi\n\nPromptBir ile tüm prompt'larınızı tek bir yerde toplayabilirsiniz:\n\n- **Kategorilere Ayırma**: Prompt'larınızı konularına göre organize edin\n- **Etiketleme Sistemi**: Hızlı arama için etiketler kullanın\n- **Favoriler**: En çok kullandığınız prompt'ları favorilerinize ekleyin\n\n### 2. Takım Çalışması\n\nEkip halinde çalışırken prompt paylaşımı kritik önem taşır:\n\n- **Proje Bazlı Organizasyon**: Her proje için ayrı prompt koleksiyonları\n- **Yetki Yönetimi**: Kimin hangi prompt'lara erişebileceğini belirleyin\n- **Gerçek Zamanlı Senkronizasyon**: Değişiklikler anında tüm ekibe yansır\n\n### 3. Akıllı Öneriler\n\nPromptBir'in AI destekli özellikleri:\n\n- **Benzer Prompt Önerileri**: Yazdığınız prompt'a benzer olanları gösterir\n- **Performans Analizi**: Hangi prompt'ların daha etkili olduğunu analiz eder\n- **Otomatik Kategorilendirme**: Yeni prompt'ları otomatik olarak kategoriler\n\n## Pratik Kullanım Örnekleri\n\n### İçerik Üretimi\n\n\\`\\`\\`\nPrompt: \"Aşağıdaki konuda 500 kelimelik SEO uyumlu bir blog yazısı yaz:\n- Hedef kitle: [HEDEF_KITLE]\n- Ana anahtar kelime: [ANAHTAR_KELIME]\n- Ton: Profesyonel ama samimi\n- CTA: [CALL_TO_ACTION]\"\n\\`\\`\\`\n\n### Kod İncelemesi\n\n\\`\\`\\`\nPrompt: \"Bu kodu incele ve şu açılardan değerlendir:\n1. Performans optimizasyonu\n2. Güvenlik açıkları\n3. Kod kalitesi ve okunabilirlik\n4. Best practice uyumu\nÖnerilerini madde madde listele.\"\n\\`\\`\\`\n\n### E-posta Yazımı\n\n\\`\\`\\`\nPrompt: \"Aşağıdaki bilgileri kullanarak profesyonel bir e-posta yaz:\n- Alıcı: [ALICI_ADI]\n- Konu: [KONU]\n- Amaç: [AMAC]\n- Ton: [TON]\n- Önemli noktalar: [ONEMLI_NOKTALAR]\"\n\\`\\`\\`\n\n## Verimlilik Artırma Stratejileri\n\n### 1. Template Kullanımı\n\nSık kullandığınız prompt türleri için şablonlar oluşturun:\n\n- **Değişken Alanlar**: [DEGISKEN] formatında placeholder'lar kullanın\n- **Standart Yapı**: Her prompt türü için tutarlı format\n- **Hızlı Doldurma**: Değişkenleri tek tıkla doldurun\n\n### 2. Versiyon Kontrolü\n\nPrompt'larınızın farklı versiyonlarını takip edin:\n\n- **Değişiklik Geçmişi**: Her değişikliği kaydedin\n- **A/B Testing**: Farklı versiyonları test edin\n- **Geri Alma**: Önceki versiyonlara kolayca dönün\n\n### 3. Performans Takibi\n\nHangi prompt'ların daha etkili olduğunu ölçün:\n\n- **Kullanım Sıklığı**: En çok kullanılan prompt'ları belirleyin\n- **Başarı Oranı**: Hangi prompt'ların daha iyi sonuç verdiğini takip edin\n- **Zaman Tasarrufu**: Ne kadar zaman kazandığınızı hesaplayın\n\n## Başlangıç İçin İpuçları\n\n### 1. Mevcut Prompt'larınızı Toplayın\n\n- Geçmiş sohbet geçmişlerinizi inceleyin\n- Etkili bulduğunuz prompt'ları kaydedin\n- Kategorilere ayırarak organize edin\n\n### 2. Takımınızla Paylaşın\n\n- En iyi prompt'larınızı ekibinizle paylaşın\n- Ortak bir prompt kütüphanesi oluşturun\n- Düzenli olarak yeni prompt'lar ekleyin\n\n### 3. Sürekli İyileştirin\n\n- Prompt'larınızı düzenli olarak gözden geçirin\n- Yeni AI araçları için uyarlayın\n- Geri bildirimler doğrultusunda güncelleyin\n\n## Sonuç\n\nPromptBir ile AI verimliliğinizi artırmak artık çok kolay. Doğru prompt yönetimi stratejileri ile:\n\n- **%300 Zaman Tasarrufu**: Hazır prompt'lar ile hızlı çalışın\n- **%500 Kalite Artışı**: Test edilmiş prompt'lar ile daha iyi sonuçlar\n- **%200 Takım Verimliliği**: Paylaşılan bilgi ile ekip performansı\n\nHemen bugün PromptBir'i denemeye başlayın ve AI verimliliğinizi bir üst seviyeye taşıyın!\n\n---\n\n*Bu yazı PromptBir ekibi tarafından hazırlanmıştır. Platform hakkında daha fazla bilgi için [PromptBir.com](https://promptbir.com) adresini ziyaret edebilirsiniz.*\n    `\n  },\n  'prompt-engineering-rehber': {\n    id: 2,\n    title: 'Prompt Engineering: Başlangıçtan İleri Seviyeye Rehber',\n    slug: 'prompt-engineering-rehber',\n    excerpt: 'Etkili prompt yazma sanatını öğrenin. Temel prensiplerden ileri tekniklere kadar kapsamlı rehber.',\n    author: 'Dr. Ahmet Yılmaz',\n    publishDate: '2024-01-10',\n    readTime: '12 dakika',\n    category: 'Eğitim',\n    tags: ['Prompt Engineering', 'AI', 'Eğitim'],\n    featured: true,\n    content: `\n# Prompt Engineering: Başlangıçtan İleri Seviyeye Rehber\n\nPrompt engineering, AI ile etkileşimde bulunmanın en önemli becerilerinden biri haline geldi. Bu kapsamlı rehberde, etkili prompt yazma sanatını baştan sona öğreneceksiniz.\n\n## Prompt Engineering Nedir?\n\nPrompt engineering, AI modellerinden istediğiniz sonuçları elde etmek için optimize edilmiş talimatlar yazma sanatıdır. Doğru prompt, AI'dan aldığınız yanıtın kalitesini dramatik şekilde artırabilir.\n\n## Temel Prensipler\n\n### 1. Açık ve Net Olun\n\n**Kötü Örnek:**\n\\`\\`\\`\n\"Bir yazı yaz\"\n\\`\\`\\`\n\n**İyi Örnek:**\n\\`\\`\\`\n\"E-ticaret şirketleri için sosyal medya pazarlama stratejileri hakkında \n500 kelimelik, SEO uyumlu bir blog yazısı yaz. Hedef kitle: KOBİ sahipleri.\"\n\\`\\`\\`\n\n### 2. Bağlam Sağlayın\n\nAI'ya yeterli bağlam vermek kritik önem taşır:\n\n\\`\\`\\`\n\"Sen deneyimli bir pazarlama uzmanısın. 10 yıllık tecriben var.\nAşağıdaki şirket için bir pazarlama stratejisi geliştir:\n- Şirket: Organik gıda e-ticaret\n- Hedef: 25-45 yaş arası sağlık bilincine sahip bireyler\n- Bütçe: Aylık 50.000 TL\n- Süre: 6 ay\"\n\\`\\`\\`\n\n### 3. Örnekler Verin\n\nFew-shot learning tekniği ile daha iyi sonuçlar alın:\n\n\\`\\`\\`\n\"Aşağıdaki örneklere benzer ürün açıklamaları yaz:\n\nÖrnek 1:\nÜrün: Kablosuz Kulaklık\nAçıklama: \"Gürültü önleyici teknoloji ile kristal berraklığında ses deneyimi. \n30 saate kadar kesintisiz müzik keyfi.\"\n\nÖrnek 2:\nÜrün: Akıllı Saat\nAçıklama: \"Sağlığınızı 7/24 takip eden akıllı asistanınız. \nSu geçirmez tasarım ile her ortamda yanınızda.\"\n\nŞimdi bu ürün için açıklama yaz:\nÜrün: Bluetooth Hoparlör\"\n\\`\\`\\`\n\n## İleri Seviye Teknikler\n\n### 1. Chain of Thought (CoT)\n\nAI'ya adım adım düşünmesini söyleyin:\n\n\\`\\`\\`\n\"Bu problemi adım adım çöz:\n\nProblem: Bir şirketin aylık geliri 100.000 TL, giderleri 75.000 TL. \nGelirini %20 artırıp, giderlerini %10 azaltırsa, \nyeni kar marjı ne olur?\n\nAdımlar:\n1. Mevcut kar hesapla\n2. Yeni geliri hesapla\n3. Yeni giderleri hesapla\n4. Yeni karı hesapla\n5. Kar marjını hesapla\"\n\\`\\`\\`\n\n### 2. Role Playing\n\nAI'ya belirli bir rol verin:\n\n\\`\\`\\`\n\"Sen deneyimli bir UX/UI tasarımcısısın. \nAşağıdaki mobil uygulama için kullanıcı deneyimi analizi yap:\n\nUygulama: Yemek sipariş uygulaması\nProblem: Kullanıcılar sipariş tamamlama oranı düşük\nVeri: %60 kullanıcı sepete ürün ekliyor ama sadece %25'i sipariş tamamlıyor\n\nAnaliz et ve çözüm önerileri sun.\"\n\\`\\`\\`\n\n### 3. Constraint-Based Prompting\n\nSınırlamalar belirleyin:\n\n\\`\\`\\`\n\"Aşağıdaki kısıtlamalara uyarak bir blog yazısı yaz:\n- Maksimum 300 kelime\n- En az 3 madde listesi\n- Başlık H1, alt başlıklar H2 formatında\n- Her paragraf maksimum 2 cümle\n- Sonunda CTA bulunsun\n- SEO için 'dijital pazarlama' kelimesi 5 kez geçsin\"\n\\`\\`\\`\n\n## Farklı AI Modelleri İçin Optimizasyon\n\n### ChatGPT İçin\n\n- Uzun ve detaylı prompt'lar tercih edin\n- Bağlam penceresi geniş, önceki konuşmaları referans alabilir\n- Yaratıcı görevlerde çok başarılı\n\n### Claude İçin\n\n- Yapılandırılmış prompt'lar daha etkili\n- Analitik görevlerde güçlü\n- Güvenlik konularında daha dikkatli\n\n### Gemini İçin\n\n- Çok modlu (metin + görsel) prompt'lar destekler\n- Google hizmetleri ile entegrasyon\n- Gerçek zamanlı bilgi erişimi\n\n## Yaygın Hatalar ve Çözümleri\n\n### 1. Belirsiz Talimatlar\n\n**Hata:**\n\\`\\`\\`\n\"İyi bir sunum hazırla\"\n\\`\\`\\`\n\n**Çözüm:**\n\\`\\`\\`\n\"Startup şirketimiz için yatırımcı sunumu hazırla:\n- 10 slayt\n- Problem, çözüm, pazar, iş modeli, takım, finansal projeksiyonlar\n- Her slayt için konuşma notları\n- Görsel öneriler\"\n\\`\\`\\`\n\n### 2. Çok Karmaşık Talimatlar\n\n**Hata:**\n\\`\\`\\`\n\"Bir e-ticaret sitesi için SEO stratejisi geliştir, aynı zamanda sosyal medya \nplanı yap, influencer listesi oluştur, bütçe planla, timeline hazırla...\"\n\\`\\`\\`\n\n**Çözüm:**\nGörevleri parçalara ayırın ve sırayla isteyin.\n\n### 3. Bağlam Eksikliği\n\n**Hata:**\n\\`\\`\\`\n\"Bu kodu optimize et: [kod]\"\n\\`\\`\\`\n\n**Çözüm:**\n\\`\\`\\`\n\"Bu Python kodunu performans açısından optimize et:\n- Hedef: API yanıt süresini %50 azalt\n- Kısıtlar: Mevcut fonksiyonalite değişmemeli\n- Ortam: Django, PostgreSQL\n- Kod: [kod]\"\n\\`\\`\\`\n\n## Prompt Şablonları\n\n### İçerik Üretimi Şablonu\n\n\\`\\`\\`\n\"[ROL] olarak hareket et.\n\nGörev: [GÖREV_TANIMI]\nHedef Kitle: [HEDEF_KITLE]\nTon: [TON]\nUzunluk: [UZUNLUK]\nFormat: [FORMAT]\nAnahtar Kelimeler: [ANAHTAR_KELIMELER]\n\nEk Gereksinimler:\n- [GEREKSINIM_1]\n- [GEREKSINIM_2]\n- [GEREKSINIM_3]\n\nÇıktı formatı:\n[ISTENEN_FORMAT]\"\n\\`\\`\\`\n\n### Problem Çözme Şablonu\n\n\\`\\`\\`\n\"Problem: [PROBLEM_TANIMI]\n\nBağlam:\n- [BAGLAM_1]\n- [BAGLAM_2]\n- [BAGLAM_3]\n\nKısıtlar:\n- [KISIT_1]\n- [KISIT_2]\n\nBeklenen Çıktı:\n1. Problem analizi\n2. Alternatif çözümler (en az 3)\n3. Her çözüm için artı/eksi\n4. Önerilen çözüm ve gerekçesi\n5. Uygulama adımları\"\n\\`\\`\\`\n\n## Performans Ölçümü\n\nPrompt'larınızın etkinliğini ölçmek için:\n\n### 1. A/B Testing\n\nAynı görev için farklı prompt'lar deneyin ve sonuçları karşılaştırın.\n\n### 2. Kalite Metrikleri\n\n- **Doğruluk**: Verilen bilgiler ne kadar doğru?\n- **Relevans**: Cevap soruya ne kadar uygun?\n- **Yaratıcılık**: Özgün ve yaratıcı mı?\n- **Kullanılabilirlik**: Pratik olarak kullanılabilir mi?\n\n### 3. Zaman Tasarrufu\n\n- Prompt yazma süresi\n- İstenen sonucu alma süresi\n- Düzeltme ihtiyacı\n\n## Gelecek Trendleri\n\n### 1. Multimodal Prompting\n\nMetin + görsel + ses kombinasyonları\n\n### 2. Automated Prompt Optimization\n\nAI'ın kendi prompt'larını optimize etmesi\n\n### 3. Domain-Specific Prompting\n\nSektöre özel prompt kütüphaneleri\n\n## Sonuç\n\nPrompt engineering, AI çağında en değerli becerilerden biri. Bu rehberdeki teknikleri uygulayarak:\n\n- Daha etkili AI etkileşimleri\n- Zaman tasarrufu\n- Kaliteli çıktılar\n- Profesyonel avantaj\n\nSürekli pratik yapın ve yeni teknikleri deneyin. AI teknolojisi hızla gelişiyor, prompt engineering becerinizi de sürekli güncel tutun.\n\n---\n\n*Dr. Ahmet Yılmaz, AI ve Makine Öğrenmesi alanında 15 yıllık deneyime sahip akademisyen ve danışmandır.*\n    `\n  },\n  'takim-calismasi-ai-prompts': {\n    id: 3,\n    title: 'Takım Çalışmasında AI Prompt\\'ları: İşbirliği Rehberi',\n    slug: 'takim-calismasi-ai-prompts',\n    excerpt: 'Takım halinde çalışırken AI prompt\\'larını nasıl etkili şekilde paylaşabileceğinizi ve yönetebileceğinizi öğrenin.',\n    author: 'Elif Kaya',\n    publishDate: '2024-01-05',\n    readTime: '10 dakika',\n    category: 'Takım Çalışması',\n    tags: ['Takım Çalışması', 'İşbirliği', 'Prompt Paylaşımı'],\n    featured: false,\n    content: `\n# Takım Çalışmasında AI Prompt'ları: İşbirliği Rehberi\n\nModern iş dünyasında takımlar, AI araçlarını günlük iş akışlarına entegre ediyor. Ancak prompt'ları etkili şekilde paylaşmak ve yönetmek, takım verimliliği için kritik önem taşıyor.\n\n## Takım Prompt Yönetiminin Önemi\n\n### Yaygın Sorunlar\n- Her takım üyesi kendi prompt'larını yazıyor\n- Etkili prompt'lar kaybolup gidiyor\n- Tutarsız sonuçlar alınıyor\n- Zaman kaybı yaşanıyor\n\n### Çözüm: Merkezi Prompt Kütüphanesi\nPromptBir ile takımınız için merkezi bir prompt kütüphanesi oluşturabilirsiniz.\n\n## Takım Prompt Stratejileri\n\n### 1. Rol Bazlı Prompt Organizasyonu\n\n**Pazarlama Takımı:**\n\\`\\`\\`\n\"[ÜRÜN_ADI] için sosyal medya gönderisi oluştur:\n- Platform: [PLATFORM]\n- Hedef kitle: [HEDEF_KİTLE]\n- Ton: [TON]\n- Hashtag sayısı: [SAYI]\n- CTA dahil et\"\n\\`\\`\\`\n\n**Geliştirici Takımı:**\n\\`\\`\\`\n\"[PROGRAMLAMA_DİLİ] ile [AÇIKLAMA] yapan kod yaz:\n- Clean code prensipleri\n- Error handling\n- Unit test örnekleri\n- Dokümantasyon\"\n\\`\\`\\`\n\n**Satış Takımı:**\n\\`\\`\\`\n\"[MÜŞTERİ_PROFİLİ] için kişiselleştirilmiş satış e-postası:\n- Ürün: [ÜRÜN]\n- Müşteri ihtiyacı: [İHTİYAÇ]\n- Ton: profesyonel ama samimi\n- CTA: demo talep et\"\n\\`\\`\\`\n\n### 2. Proje Bazlı Prompt Setleri\n\nHer proje için özel prompt koleksiyonları oluşturun:\n- Proje başlangıç prompt'ları\n- Süreç yönetimi prompt'ları\n- Kalite kontrol prompt'ları\n- Raporlama prompt'ları\n\n### 3. Versiyon Kontrolü\n\nPrompt'larınızı versiyonlayın:\n- v1.0: İlk versiyon\n- v1.1: Küçük iyileştirmeler\n- v2.0: Büyük değişiklikler\n\n## İşbirliği Best Practice'leri\n\n### Prompt Paylaşım Kuralları\n\n1. **Açık İsimlendirme**\n   - \"Email_Template_v2\" ❌\n   - \"Müşteri_Onboarding_Email_Şablonu_v2.1\" ✅\n\n2. **Açıklama Ekleme**\n   - Prompt'un amacı\n   - Kullanım senaryoları\n   - Beklenen sonuçlar\n\n3. **Etiketleme Sistemi**\n   - #pazarlama #email #onboarding\n   - #geliştirme #python #api\n   - #satış #demo #b2b\n\n### Takım Eğitimi\n\n**Haftalık Prompt Review:**\n- En etkili prompt'ları paylaşın\n- Yeni teknikleri tartışın\n- Başarı hikayelerini anlatın\n\n**Prompt Workshop'ları:**\n- Yeni takım üyeleri için eğitim\n- İleri seviye teknikler\n- Sektörel örnekler\n\n## Kalite Kontrol Süreci\n\n### Prompt Değerlendirme Kriterleri\n\n1. **Netlik** (1-5 puan)\n   - Talimatlar açık mı?\n   - Beklentiler net mi?\n\n2. **Tutarlılık** (1-5 puan)\n   - Sonuçlar tutarlı mı?\n   - Farklı kullanıcılarda aynı sonuç mu?\n\n3. **Verimlilik** (1-5 puan)\n   - Zaman tasarrufu sağlıyor mu?\n   - İş akışını hızlandırıyor mu?\n\n### Geri Bildirim Döngüsü\n\n1. Prompt kullanımı\n2. Sonuç değerlendirmesi\n3. Geri bildirim toplama\n4. İyileştirme önerileri\n5. Prompt güncelleme\n\n## Güvenlik ve Gizlilik\n\n### Hassas Bilgi Yönetimi\n\n- Müşteri bilgilerini prompt'larda kullanmayın\n- Placeholder'lar kullanın: [MÜŞTERİ_ADI]\n- Şirket sırlarını koruyun\n\n### Erişim Kontrolü\n\n- Takım bazlı erişim yetkileri\n- Rol bazlı prompt görünürlüğü\n- Düzenleme yetkilerini sınırlayın\n\n## Performans Metrikleri\n\n### Takım Verimliliği KPI'ları\n\n1. **Prompt Kullanım Oranı**\n   - Takım üyelerinin prompt kullanım sıklığı\n   - En çok kullanılan prompt'lar\n\n2. **Zaman Tasarrufu**\n   - Görev tamamlama süreleri\n   - Prompt öncesi vs sonrası karşılaştırma\n\n3. **Kalite Artışı**\n   - Çıktı kalitesi değerlendirmeleri\n   - Müşteri memnuniyeti\n\n### Raporlama\n\nAylık takım raporları:\n- En etkili prompt'lar\n- Kullanım istatistikleri\n- İyileştirme önerileri\n- Başarı hikayeleri\n\n## Gelecek Planlaması\n\n### Prompt Kütüphanesi Büyütme\n\n1. **Sürekli İyileştirme**\n   - Kullanıcı geri bildirimlerini toplayın\n   - A/B test yapın\n   - Yeni teknikleri deneyin\n\n2. **Bilgi Paylaşımı**\n   - Diğer takımlarla işbirliği\n   - Sektör best practice'lerini takip edin\n   - Konferans ve eğitimlere katılın\n\n3. **Teknoloji Entegrasyonu**\n   - API entegrasyonları\n   - Otomasyon araçları\n   - İş akışı optimizasyonu\n\n## Sonuç\n\nTakım halinde AI prompt yönetimi, modern iş dünyasında rekabet avantajı sağlar. PromptBir ile:\n\n- Merkezi prompt kütüphanesi\n- Etkili işbirliği\n- Kalite kontrolü\n- Sürekli iyileştirme\n\nTakımınızın AI verimliliğini artırmak için bugün başlayın!\n\n---\n\n*Elif Kaya, 10 yıllık deneyime sahip proje yöneticisi ve takım liderliği uzmanıdır.*\n    `\n  },\n  'ai-prompt-guvenlik': {\n    id: 4,\n    title: 'AI Prompt Güvenliği: Veri Koruma ve Gizlilik Rehberi',\n    slug: 'ai-prompt-guvenlik',\n    excerpt: 'AI prompt\\'larınızı kullanırken veri güvenliğini nasıl sağlayacağınızı ve gizliliği nasıl koruyacağınızı öğrenin.',\n    author: 'Mehmet Demir',\n    publishDate: '2023-12-28',\n    readTime: '9 dakika',\n    category: 'Güvenlik',\n    tags: ['Güvenlik', 'Gizlilik', 'Veri Koruma'],\n    featured: false,\n    content: `\n# AI Prompt Güvenliği: Veri Koruma ve Gizlilik Rehberi\n\nAI araçlarının yaygınlaşmasıyla birlikte, prompt güvenliği kritik bir konu haline geldi. Bu rehberde, AI prompt'larınızı kullanırken veri güvenliğini nasıl sağlayacağınızı öğreneceksiniz.\n\n## Güvenlik Riskleri\n\n### Yaygın Güvenlik Tehditleri\n\n1. **Veri Sızıntısı**\n   - Hassas bilgilerin prompt'larda kullanılması\n   - Müşteri verilerinin yanlışlıkla paylaşılması\n   - Şirket sırlarının ifşa edilmesi\n\n2. **Prompt Injection Saldırıları**\n   - Kötü niyetli kullanıcıların sistem prompt'larını manipüle etmesi\n   - Yetkisiz erişim girişimleri\n   - Sistem güvenliğinin aşılması\n\n3. **Model Poisoning**\n   - Zararlı verilerle model eğitiminin bozulması\n   - Önyargılı sonuçların üretilmesi\n   - Sistem güvenilirliğinin azalması\n\n## Güvenli Prompt Yazma Prensipleri\n\n### 1. Veri Anonimleştirme\n\n**Yanlış Yaklaşım:**\n\\`\\`\\`\n\"Ahmet Yılmaz (TC: 12345678901) için kredi başvurusu değerlendir\"\n\\`\\`\\`\n\n**Doğru Yaklaşım:**\n\\`\\`\\`\n\"[MÜŞTERİ_ADI] için kredi başvurusu değerlendir:\n- Yaş: [YAŞ]\n- Gelir: [GELİR]\n- Kredi geçmişi: [KREDİ_SKORU]\"\n\\`\\`\\`\n\n### 2. Placeholder Kullanımı\n\nHassas bilgiler yerine placeholder'lar kullanın:\n- [MÜŞTERİ_ADI] → Gerçek isim yerine\n- [ŞIRKET_ADI] → Şirket adı yerine\n- [PROJE_KODU] → Proje detayları yerine\n- [FİNANSAL_VERİ] → Mali bilgiler yerine\n\n### 3. Minimum Veri Prensibi\n\nSadece gerekli bilgileri paylaşın:\n- Görev için gerekli minimum veri\n- Bağlam için yeterli bilgi\n- Gereksiz detaylardan kaçının\n\n## Kurumsal Güvenlik Politikaları\n\n### Veri Sınıflandırması\n\n**Açık Veri (Public)**\n- Genel bilgiler\n- Pazarlama materyalleri\n- Halka açık dökümanlar\n\n**İç Veri (Internal)**\n- Şirket süreçleri\n- İç iletişim\n- Operasyonel bilgiler\n\n**Gizli Veri (Confidential)**\n- Müşteri bilgileri\n- Finansal veriler\n- Stratejik planlar\n\n**Çok Gizli Veri (Restricted)**\n- Ticari sırlar\n- Kişisel veriler\n- Yasal belgeler\n\n### Erişim Kontrolü\n\n1. **Rol Bazlı Erişim**\n   - Departman bazlı yetkilendirme\n   - Görev bazlı sınırlamalar\n   - Proje bazlı erişim\n\n2. **İki Faktörlü Doğrulama**\n   - SMS doğrulama\n   - Authenticator uygulamaları\n   - Biometrik doğrulama\n\n3. **Oturum Yönetimi**\n   - Otomatik oturum sonlandırma\n   - Eşzamanlı oturum sınırları\n   - IP bazlı kısıtlamalar\n\n## KVKK ve GDPR Uyumluluğu\n\n### Kişisel Veri Koruma\n\n**KVKK Gereklilikleri:**\n- Açık rıza alınması\n- Veri işleme amacının belirtilmesi\n- Veri saklama sürelerinin belirlenmesi\n- Veri güvenliği tedbirlerinin alınması\n\n**GDPR Gereklilikleri:**\n- Veri taşınabilirliği hakkı\n- Unutulma hakkı\n- Veri işleme faaliyetlerinin kayıt altına alınması\n- Veri koruma etki değerlendirmesi\n\n### Uygulama Örnekleri\n\n**Müşteri Hizmetleri Prompt'u:**\n\\`\\`\\`\n\"Müşteri şikayeti analizi yap:\n- Şikayet kategorisi: [KATEGORİ]\n- Aciliyet seviyesi: [ACİLİYET]\n- Çözüm önerisi sun\n- Müşteri memnuniyeti için adımlar\n\nNot: Kişisel bilgileri kullanma, sadece şikayet içeriğini analiz et.\"\n\\`\\`\\`\n\n## Teknik Güvenlik Önlemleri\n\n### Şifreleme\n\n1. **Veri Şifreleme**\n   - AES-256 şifreleme\n   - End-to-end encryption\n   - Database şifreleme\n\n2. **İletişim Şifreleme**\n   - HTTPS protokolü\n   - TLS 1.3 kullanımı\n   - Certificate pinning\n\n### Güvenlik Denetimi\n\n**Düzenli Denetimler:**\n- Penetrasyon testleri\n- Güvenlik açığı taramaları\n- Kod güvenlik analizi\n- Erişim log incelemesi\n\n**Olay Müdahale Planı:**\n1. Güvenlik ihlali tespiti\n2. Etki analizi\n3. Müdahale ekibinin devreye girmesi\n4. İyileştirme önlemlerinin alınması\n\n## Güvenli Prompt Şablonları\n\n### Genel Güvenlik Şablonu\n\n\\`\\`\\`\n\"[GÖREV_AÇIKLAMASI] için analiz yap:\n\nGiriş Verileri:\n- [VERİ_TİPİ_1]: [PLACEHOLDER_1]\n- [VERİ_TİPİ_2]: [PLACEHOLDER_2]\n\nGüvenlik Notları:\n- Kişisel bilgileri kullanma\n- Hassas verileri kaydetme\n- Sadece analiz sonuçlarını döndür\n\nBeklenen Çıktı:\n- [ÇIKTI_FORMAT]\n- [ÇIKTI_DETAY]\"\n\\`\\`\\`\n\n### Finansal Veri Analizi Şablonu\n\n\\`\\`\\`\n\"Finansal performans analizi:\n\nVeriler (Anonimleştirilmiş):\n- Gelir: [GELİR_ARALIĞI]\n- Gider: [GİDER_KATEGORİSİ]\n- Dönem: [ZAMAN_ARALIĞI]\n\nAnaliz Kriterleri:\n- Trend analizi\n- Karşılaştırmalı analiz\n- Risk değerlendirmesi\n\nGüvenlik: Gerçek rakamları kullanma, sadece oransal analiz yap.\"\n\\`\\`\\`\n\n## Eğitim ve Farkındalık\n\n### Personel Eğitimi\n\n**Temel Güvenlik Eğitimi:**\n- Güvenli prompt yazma\n- Veri sınıflandırması\n- Olay raporlama\n\n**İleri Seviye Eğitim:**\n- Güvenlik testleri\n- Saldırı simülasyonları\n- Best practice workshop'ları\n\n### Sürekli İyileştirme\n\n1. **Güvenlik Metrikleri**\n   - Güvenlik ihlali sayısı\n   - Eğitim tamamlama oranları\n   - Güvenlik denetim sonuçları\n\n2. **Geri Bildirim Döngüsü**\n   - Kullanıcı raporları\n   - Güvenlik ekibi değerlendirmeleri\n   - Süreç iyileştirmeleri\n\n## Sonuç\n\nAI prompt güvenliği, modern iş dünyasında kritik önem taşır. Bu rehberdeki prensipleri uygulayarak:\n\n- Veri güvenliğini sağlayabilir\n- Yasal gerekliliklere uyum gösterebilir\n- Kurumsal güveni koruyabilir\n- Rekabet avantajı elde edebilirsiniz\n\nGüvenlik, sürekli bir süreçtir. Düzenli eğitim, denetim ve iyileştirme ile güvenlik seviyenizi yüksek tutun.\n\n---\n\n*Mehmet Demir, siber güvenlik alanında 12 yıllık deneyime sahip uzman ve sertifikalı etik hacker'dır.*\n    `\n  },\n  'promptbir-api-rehber': {\n    id: 5,\n    title: 'PromptBir API: Geliştiriciler için Kapsamlı Rehber',\n    slug: 'promptbir-api-rehber',\n    excerpt: 'PromptBir API\\'sini kullanarak kendi uygulamalarınızda prompt yönetimi nasıl entegre edebileceğinizi öğrenin.',\n    author: 'PromptBir Geliştirici Ekibi',\n    publishDate: '2023-12-20',\n    readTime: '15 dakika',\n    category: 'Geliştirici',\n    tags: ['API', 'Geliştirici', 'Entegrasyon'],\n    featured: false,\n    content: `\n# PromptBir API: Geliştiriciler için Kapsamlı Rehber\n\nPromptBir API, geliştiricilerin kendi uygulamalarında prompt yönetimi yapabilmelerini sağlayan güçlü bir RESTful API'dir. Bu rehberde, API'yi nasıl kullanacağınızı adım adım öğreneceksiniz.\n\n## API'ye Başlangıç\n\n### Kimlik Doğrulama\n\nPromptBir API, JWT token tabanlı kimlik doğrulama kullanır:\n\n\\`\\`\\`javascript\n// API anahtarınızı alın\nconst API_KEY = 'your-api-key-here';\nconst BASE_URL = 'https://api.promptbir.com/v1';\n\n// Headers\nconst headers = {\n  'Authorization': \\`Bearer \\${API_KEY}\\`,\n  'Content-Type': 'application/json'\n};\n\\`\\`\\`\n\n### Rate Limiting\n\nAPI kullanımında aşağıdaki limitler geçerlidir:\n- **Free Plan**: 100 istek/saat\n- **Pro Plan**: 1,000 istek/saat\n- **Enterprise Plan**: 10,000 istek/saat\n\n## Temel API Endpoint'leri\n\n### 1. Projeler (Projects)\n\n**Tüm Projeleri Listele**\n\\`\\`\\`javascript\nconst getProjects = async () => {\n  const response = await fetch(\\`\\${BASE_URL}/projects\\`, {\n    method: 'GET',\n    headers: headers\n  });\n\n  const projects = await response.json();\n  return projects;\n};\n\\`\\`\\`\n\n**Yeni Proje Oluştur**\n\\`\\`\\`javascript\nconst createProject = async (projectData) => {\n  const response = await fetch(\\`\\${BASE_URL}/projects\\`, {\n    method: 'POST',\n    headers: headers,\n    body: JSON.stringify({\n      name: projectData.name,\n      description: projectData.description,\n      tags: projectData.tags\n    })\n  });\n\n  const newProject = await response.json();\n  return newProject;\n};\n\\`\\`\\`\n\n### 2. Prompt'lar (Prompts)\n\n**Prompt'ları Listele**\n\\`\\`\\`javascript\nconst getPrompts = async (projectId, filters = {}) => {\n  const queryParams = new URLSearchParams({\n    project_id: projectId,\n    ...filters\n  });\n\n  const response = await fetch(\\`\\${BASE_URL}/prompts?\\${queryParams}\\`, {\n    method: 'GET',\n    headers: headers\n  });\n\n  const prompts = await response.json();\n  return prompts;\n};\n\\`\\`\\`\n\n**Yeni Prompt Oluştur**\n\\`\\`\\`javascript\nconst createPrompt = async (promptData) => {\n  const response = await fetch(\\`\\${BASE_URL}/prompts\\`, {\n    method: 'POST',\n    headers: headers,\n    body: JSON.stringify({\n      project_id: promptData.projectId,\n      title: promptData.title,\n      content: promptData.content,\n      category: promptData.category,\n      hashtags: promptData.hashtags\n    })\n  });\n\n  const newPrompt = await response.json();\n  return newPrompt;\n};\n\\`\\`\\`\n\n**Prompt Güncelle**\n\\`\\`\\`javascript\nconst updatePrompt = async (promptId, updateData) => {\n  const response = await fetch(\\`\\${BASE_URL}/prompts/\\${promptId}\\`, {\n    method: 'PUT',\n    headers: headers,\n    body: JSON.stringify(updateData)\n  });\n\n  const updatedPrompt = await response.json();\n  return updatedPrompt;\n};\n\\`\\`\\`\n\n### 3. Kategoriler (Categories)\n\n**Kategorileri Listele**\n\\`\\`\\`javascript\nconst getCategories = async (projectId) => {\n  const response = await fetch(\\`\\${BASE_URL}/categories?project_id=\\${projectId}\\`, {\n    method: 'GET',\n    headers: headers\n  });\n\n  const categories = await response.json();\n  return categories;\n};\n\\`\\`\\`\n\n## İleri Seviye Özellikler\n\n### Toplu İşlemler (Batch Operations)\n\n**Toplu Prompt Oluşturma**\n\\`\\`\\`javascript\nconst createBatchPrompts = async (prompts) => {\n  const response = await fetch(\\`\\${BASE_URL}/prompts/batch\\`, {\n    method: 'POST',\n    headers: headers,\n    body: JSON.stringify({\n      prompts: prompts\n    })\n  });\n\n  const result = await response.json();\n  return result;\n};\n\\`\\`\\`\n\n### Arama ve Filtreleme\n\n**Gelişmiş Arama**\n\\`\\`\\`javascript\nconst searchPrompts = async (searchParams) => {\n  const response = await fetch(\\`\\${BASE_URL}/prompts/search\\`, {\n    method: 'POST',\n    headers: headers,\n    body: JSON.stringify({\n      query: searchParams.query,\n      filters: {\n        category: searchParams.category,\n        hashtags: searchParams.hashtags,\n        date_range: searchParams.dateRange\n      },\n      sort: searchParams.sort,\n      limit: searchParams.limit,\n      offset: searchParams.offset\n    })\n  });\n\n  const searchResults = await response.json();\n  return searchResults;\n};\n\\`\\`\\`\n\n### Webhook'lar\n\n**Webhook Kurulumu**\n\\`\\`\\`javascript\nconst setupWebhook = async (webhookConfig) => {\n  const response = await fetch(\\`\\${BASE_URL}/webhooks\\`, {\n    method: 'POST',\n    headers: headers,\n    body: JSON.stringify({\n      url: webhookConfig.url,\n      events: webhookConfig.events, // ['prompt.created', 'prompt.updated', 'prompt.deleted']\n      secret: webhookConfig.secret\n    })\n  });\n\n  const webhook = await response.json();\n  return webhook;\n};\n\\`\\`\\`\n\n## SDK'lar ve Kütüphaneler\n\n### JavaScript/Node.js SDK\n\n\\`\\`\\`bash\nnpm install @promptbir/sdk\n\\`\\`\\`\n\n\\`\\`\\`javascript\nimport { PromptBirClient } from '@promptbir/sdk';\n\nconst client = new PromptBirClient({\n  apiKey: 'your-api-key',\n  baseUrl: 'https://api.promptbir.com/v1'\n});\n\n// Kullanım\nconst projects = await client.projects.list();\nconst prompts = await client.prompts.list(projectId);\n\\`\\`\\`\n\n### Python SDK\n\n\\`\\`\\`bash\npip install promptbir-python\n\\`\\`\\`\n\n\\`\\`\\`python\nfrom promptbir import PromptBirClient\n\nclient = PromptBirClient(api_key='your-api-key')\n\n# Kullanım\nprojects = client.projects.list()\nprompts = client.prompts.list(project_id=project_id)\n\\`\\`\\`\n\n## Hata Yönetimi\n\n### HTTP Durum Kodları\n\n- **200**: Başarılı\n- **201**: Oluşturuldu\n- **400**: Hatalı istek\n- **401**: Yetkisiz erişim\n- **403**: Yasak\n- **404**: Bulunamadı\n- **429**: Rate limit aşıldı\n- **500**: Sunucu hatası\n\n### Hata Yanıt Formatı\n\n\\`\\`\\`json\n{\n  \"error\": {\n    \"code\": \"INVALID_REQUEST\",\n    \"message\": \"Gerekli alan eksik: title\",\n    \"details\": {\n      \"field\": \"title\",\n      \"reason\": \"required\"\n    }\n  }\n}\n\\`\\`\\`\n\n### Hata Yönetimi Örneği\n\n\\`\\`\\`javascript\nconst handleApiCall = async (apiFunction) => {\n  try {\n    const result = await apiFunction();\n    return { success: true, data: result };\n  } catch (error) {\n    if (error.status === 429) {\n      // Rate limit aşıldı, bekle ve tekrar dene\n      await new Promise(resolve => setTimeout(resolve, 60000));\n      return handleApiCall(apiFunction);\n    } else if (error.status === 401) {\n      // Token yenile\n      await refreshToken();\n      return handleApiCall(apiFunction);\n    } else {\n      return { success: false, error: error.message };\n    }\n  }\n};\n\\`\\`\\`\n\n## Performans Optimizasyonu\n\n### Önbellekleme (Caching)\n\n\\`\\`\\`javascript\nclass PromptBirCache {\n  constructor() {\n    this.cache = new Map();\n    this.ttl = 5 * 60 * 1000; // 5 dakika\n  }\n\n  get(key) {\n    const item = this.cache.get(key);\n    if (item && Date.now() - item.timestamp < this.ttl) {\n      return item.data;\n    }\n    this.cache.delete(key);\n    return null;\n  }\n\n  set(key, data) {\n    this.cache.set(key, {\n      data: data,\n      timestamp: Date.now()\n    });\n  }\n}\n\nconst cache = new PromptBirCache();\n\\`\\`\\`\n\n### Pagination\n\n\\`\\`\\`javascript\nconst getAllPrompts = async (projectId) => {\n  let allPrompts = [];\n  let offset = 0;\n  const limit = 100;\n\n  while (true) {\n    const response = await getPrompts(projectId, { limit, offset });\n    allPrompts = allPrompts.concat(response.data);\n\n    if (response.data.length < limit) {\n      break; // Son sayfa\n    }\n\n    offset += limit;\n  }\n\n  return allPrompts;\n};\n\\`\\`\\`\n\n## Güvenlik Best Practice'leri\n\n### API Anahtarı Güvenliği\n\n1. **Çevre Değişkenleri Kullanın**\n\\`\\`\\`javascript\nconst API_KEY = process.env.PROMPTBIR_API_KEY;\n\\`\\`\\`\n\n2. **API Anahtarını Asla Frontend'de Kullanmayın**\n\\`\\`\\`javascript\n// ❌ Yanlış - Frontend'de API anahtarı\nconst apiKey = 'pk_live_123456789';\n\n// ✅ Doğru - Backend proxy kullanın\nconst response = await fetch('/api/prompts', {\n  method: 'GET',\n  headers: {\n    'Authorization': \\`Bearer \\${userToken}\\`\n  }\n});\n\\`\\`\\`\n\n3. **HTTPS Kullanın**\nTüm API çağrılarında HTTPS protokolünü kullanın.\n\n## Örnek Uygulamalar\n\n### React Entegrasyonu\n\n\\`\\`\\`jsx\nimport React, { useState, useEffect } from 'react';\nimport { PromptBirClient } from '@promptbir/sdk';\n\nconst PromptList = ({ projectId }) => {\n  const [prompts, setPrompts] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchPrompts = async () => {\n      try {\n        const client = new PromptBirClient({\n          apiKey: process.env.REACT_APP_PROMPTBIR_API_KEY\n        });\n\n        const promptData = await client.prompts.list(projectId);\n        setPrompts(promptData);\n      } catch (error) {\n        console.error('Prompt'lar yüklenemedi:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchPrompts();\n  }, [projectId]);\n\n  if (loading) return <div>Yükleniyor...</div>;\n\n  return (\n    <div>\n      {prompts.map(prompt => (\n        <div key={prompt.id}>\n          <h3>{prompt.title}</h3>\n          <p>{prompt.content}</p>\n        </div>\n      ))}\n    </div>\n  );\n};\n\\`\\`\\`\n\n### Node.js Backend Entegrasyonu\n\n\\`\\`\\`javascript\nconst express = require('express');\nconst { PromptBirClient } = require('@promptbir/sdk');\n\nconst app = express();\nconst client = new PromptBirClient({\n  apiKey: process.env.PROMPTBIR_API_KEY\n});\n\napp.get('/api/prompts/:projectId', async (req, res) => {\n  try {\n    const { projectId } = req.params;\n    const prompts = await client.prompts.list(projectId);\n    res.json(prompts);\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n});\n\napp.listen(3000, () => {\n  console.log('Server 3000 portunda çalışıyor');\n});\n\\`\\`\\`\n\n## Sonuç\n\nPromptBir API, güçlü ve esnek bir prompt yönetim çözümü sunar. Bu rehberdeki örnekleri kullanarak:\n\n- Kendi uygulamalarınızda prompt yönetimi\n- Otomatik prompt senkronizasyonu\n- Gelişmiş arama ve filtreleme\n- Güvenli API entegrasyonu\n\nAPI dokümantasyonunun tamamına [api.promptbir.com](https://api.promptbir.com) adresinden ulaşabilirsiniz.\n\n---\n\n*PromptBir Geliştirici Ekibi, modern web teknolojileri ve API tasarımı konularında uzman geliştiricilerden oluşur.*\n    `\n  }\n}\n\ntype Props = {\n  params: Promise<{ slug: string }>\n}\n\nexport async function generateMetadata({ params }: Props): Promise<Metadata> {\n  const { slug } = await params\n  const post = blogPosts[slug as keyof typeof blogPosts]\n  \n  if (!post) {\n    return {\n      title: 'Blog Yazısı Bulunamadı - PromptBir',\n      description: 'Aradığınız blog yazısı bulunamadı.'\n    }\n  }\n\n  return {\n    title: `${post.title} - PromptBir Blog`,\n    description: post.excerpt,\n    keywords: post.tags,\n    openGraph: {\n      title: post.title,\n      description: post.excerpt,\n      type: 'article',\n      publishedTime: post.publishDate,\n      authors: [post.author],\n      url: `https://promptbir.com/blog/${post.slug}`\n    }\n  }\n}\n\nexport default async function BlogPostPage({ params }: Props) {\n  const { slug } = await params\n  const post = blogPosts[slug as keyof typeof blogPosts]\n  \n  if (!post) {\n    notFound()\n  }\n\n  // Get related posts (excluding current post)\n  const relatedPosts = Object.values(blogPosts)\n    .filter(p => p.id !== post.id && p.tags.some(tag => post.tags.includes(tag)))\n    .slice(0, 2)\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      {/* Header */}\n      <header className=\"border-b border-gray-200 bg-white/80 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <Link \n              href=\"/blog\" \n              className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <ArrowLeft className=\"h-5 w-5\" />\n              <span>Blog'a Dön</span>\n            </Link>\n            <div className=\"flex items-center space-x-2\">\n              <Sparkles className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">PromptBir</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Article Header */}\n        <article>\n          <header className=\"mb-12\">\n            <div className=\"flex items-center gap-2 mb-4\">\n              {post.featured && (\n                <Badge variant=\"secondary\" className=\"bg-orange-100 text-orange-700\">\n                  Öne Çıkan\n                </Badge>\n              )}\n              <Badge variant=\"outline\">\n                {post.category}\n              </Badge>\n            </div>\n            \n            <h1 className=\"text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight\">\n              {post.title}\n            </h1>\n            \n            <p className=\"text-xl text-gray-600 mb-8 leading-relaxed\">\n              {post.excerpt}\n            </p>\n            \n            <div className=\"flex flex-wrap items-center gap-6 text-gray-500 mb-8\">\n              <div className=\"flex items-center gap-2\">\n                <User className=\"h-5 w-5\" />\n                <span>{post.author}</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Calendar className=\"h-5 w-5\" />\n                <span>{new Date(post.publishDate).toLocaleDateString('tr-TR')}</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Clock className=\"h-5 w-5\" />\n                <span>{post.readTime}</span>\n              </div>\n              <Button variant=\"outline\" size=\"sm\" className=\"ml-auto\">\n                <Share2 className=\"h-4 w-4 mr-2\" />\n                Paylaş\n              </Button>\n            </div>\n            \n            <div className=\"flex flex-wrap gap-2\">\n              {post.tags.map((tag, index) => (\n                <Badge key={index} variant=\"secondary\">\n                  {tag}\n                </Badge>\n              ))}\n            </div>\n          </header>\n\n          {/* Article Content */}\n          <div className=\"prose prose-lg prose-gray max-w-none\">\n            <div \n              dangerouslySetInnerHTML={{ \n                __html: post.content.replace(/\\n/g, '<br>').replace(/```([\\s\\S]*?)```/g, '<pre><code>$1</code></pre>') \n              }} \n            />\n          </div>\n        </article>\n\n        {/* Related Posts */}\n        {relatedPosts.length > 0 && (\n          <section className=\"mt-16 pt-16 border-t border-gray-200\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-8 flex items-center gap-3\">\n              <BookOpen className=\"h-8 w-8 text-blue-600\" />\n              İlgili Yazılar\n            </h2>\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              {relatedPosts.map((relatedPost) => (\n                <Card key={relatedPost.id} className=\"shadow-lg hover:shadow-xl transition-shadow group\">\n                  <CardHeader>\n                    <div className=\"flex items-center gap-2 mb-3\">\n                      <Badge variant=\"outline\">\n                        {relatedPost.category}\n                      </Badge>\n                    </div>\n                    <h3 className=\"text-xl font-semibold group-hover:text-blue-600 transition-colors\">\n                      <Link href={`/blog/${relatedPost.slug}`}>\n                        {relatedPost.title}\n                      </Link>\n                    </h3>\n                    <p className=\"text-gray-600 leading-relaxed\">\n                      {relatedPost.excerpt}\n                    </p>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                      <div className=\"flex items-center gap-4\">\n                        <div className=\"flex items-center gap-1\">\n                          <Calendar className=\"h-4 w-4\" />\n                          <span>{new Date(relatedPost.publishDate).toLocaleDateString('tr-TR')}</span>\n                        </div>\n                        <div className=\"flex items-center gap-1\">\n                          <Clock className=\"h-4 w-4\" />\n                          <span>{relatedPost.readTime}</span>\n                        </div>\n                      </div>\n                    </div>\n                    <Link \n                      href={`/blog/${relatedPost.slug}`}\n                      className=\"inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium group-hover:gap-3 transition-all\"\n                    >\n                      Devamını Oku\n                      <ArrowRight className=\"h-4 w-4\" />\n                    </Link>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          </section>\n        )}\n\n        {/* Newsletter CTA */}\n        <section className=\"mt-16\">\n          <Card className=\"shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white\">\n            <CardContent className=\"p-8 text-center\">\n              <h2 className=\"text-2xl font-bold mb-4\">\n                Daha Fazla İçerik İçin Abone Olun\n              </h2>\n              <p className=\"text-blue-100 mb-6\">\n                AI ve prompt engineering konularındaki en güncel yazılarımızı kaçırmayın.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n                <input\n                  type=\"email\"\n                  placeholder=\"E-posta adresiniz\"\n                  className=\"flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500\"\n                />\n                <Button className=\"bg-white text-blue-600 hover:bg-blue-50\">\n                  Abone Ol\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </section>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-8 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 PromptBir. Tüm hakları saklıdır.\n          </p>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AAWA,0EAA0E;AAC1E,MAAM,YAAY;IAChB,+BAA+B;QAC7B,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAM;YAAc;SAAkB;QAC7C,UAAU;QACV,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqIV,CAAC;IACH;IACA,6BAA6B;QAC3B,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAsB;YAAM;SAAS;QAC5C,UAAU;QACV,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2QV,CAAC;IACH;IACA,8BAA8B;QAC5B,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAmB;YAAa;SAAmB;QAC1D,UAAU;QACV,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4LV,CAAC;IACH;IACA,sBAAsB;QACpB,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAY;YAAY;SAAc;QAC7C,UAAU;QACV,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyOV,CAAC;IACH;IACA,wBAAwB;QACtB,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAO;YAAe;SAAc;QAC3C,UAAU;QACV,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAscV,CAAC;IACH;AACF;AAMO,eAAe,iBAAiB,EAAE,MAAM,EAAS;IACtD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,OAAO,SAAS,CAAC,KAA+B;IAEtD,IAAI,CAAC,MAAM;QACT,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;IAEA,OAAO;QACL,OAAO,GAAG,KAAK,KAAK,CAAC,iBAAiB,CAAC;QACvC,aAAa,KAAK,OAAO;QACzB,UAAU,KAAK,IAAI;QACnB,WAAW;YACT,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,OAAO;YACzB,MAAM;YACN,eAAe,KAAK,WAAW;YAC/B,SAAS;gBAAC,KAAK,MAAM;aAAC;YACtB,KAAK,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;QAChD;IACF;AACF;AAEe,eAAe,aAAa,EAAE,MAAM,EAAS;IAC1D,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,OAAO,SAAS,CAAC,KAA+B;IAEtD,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,6CAA6C;IAC7C,MAAM,eAAe,OAAO,MAAM,CAAC,WAChC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,KAAK,IAAI,CAAC,QAAQ,CAAC,OACtE,KAAK,CAAC,GAAG;IAEZ,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;;0CACC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,QAAQ,kBACZ,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAgC;;;;;;0DAIvE,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DACZ,KAAK,QAAQ;;;;;;;;;;;;kDAIlB,8OAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;kDAGb,8OAAC;wCAAE,WAAU;kDACV,KAAK,OAAO;;;;;;kDAGf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAM,KAAK,MAAM;;;;;;;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;kEAAM,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;0DAEvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAM,KAAK,QAAQ;;;;;;;;;;;;0DAEtB,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;;kEAC5C,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKvC,8OAAC;wCAAI,WAAU;kDACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC,iIAAA,CAAA,QAAK;gDAAa,SAAQ;0DACxB;+CADS;;;;;;;;;;;;;;;;0CAQlB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,yBAAyB;wCACvB,QAAQ,KAAK,OAAO,CAAC,OAAO,CAAC,OAAO,QAAQ,OAAO,CAAC,qBAAqB;oCAC3E;;;;;;;;;;;;;;;;;oBAML,aAAa,MAAM,GAAG,mBACrB,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAGhD,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC,gIAAA,CAAA,OAAI;wCAAsB,WAAU;;0DACnC,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEACZ,YAAY,QAAQ;;;;;;;;;;;kEAGzB,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,MAAM,EAAE,YAAY,IAAI,EAAE;sEACpC,YAAY,KAAK;;;;;;;;;;;kEAGtB,8OAAC;wDAAE,WAAU;kEACV,YAAY,OAAO;;;;;;;;;;;;0DAGxB,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,8OAAC;sFAAM,IAAI,KAAK,YAAY,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;8EAE9D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;sFAAM,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;;;;;kEAIjC,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,MAAM,EAAE,YAAY,IAAI,EAAE;wDACjC,WAAU;;4DACX;0EAEC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;uCAlCjB,YAAY,EAAE;;;;;;;;;;;;;;;;kCA4CjC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDAGxC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;0DAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}]}