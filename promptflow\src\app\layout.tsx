import type { <PERSON><PERSON><PERSON>, Viewport } from "next";
import { Inter } from "next/font/google";
import React from "react";
import "./globals.css";
import { QueryProvider } from "@/providers/query-provider";
import { performRuntimeSecurityCheck } from "@/lib/env-security";
import { Toaster } from "@/components/ui/sonner";
import { ServiceWorkerRegistration } from "@/components/service-worker-registration";
import { SEOGenerator, StructuredData, BASE_SEO } from "@/lib/seo-utils";
import { ErrorBoundary } from "@/components/error-boundary";
import { FormFeedbackProvider } from "@/components/ui/form-feedback";
// Performance monitor - Next.js 15 uyumlu Client Component import
import { PerformanceMonitorWrapper } from "@/components/performance-monitor-wrapper";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
  preload: true,
});

// Generate comprehensive SEO metadata
export const metadata: Metadata = {
  ...SEOGenerator.generateMetadata({
    title: "PromptFlow - AI Destekli Prompt Yönetim Platformu",
    description: "AI destekli geliştirme süreçlerini optimize eden minimalist prompt yönetim platformu. Projelerinizi organize edin, prompt'larınızı yönetin ve AI ile daha verimli çalışın.",
    keywords: [
      "prompt yönetimi",
      "AI araçları",
      "yapay zeka",
      "geliştirici araçları",
      "prompt engineering",
      "AI destekli geliştirme",
      "kod optimizasyonu",
      "proje yönetimi",
      "ChatGPT prompts",
      "AI productivity"
    ],
    type: 'webapp'
  }),
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon.ico', sizes: 'any' }
    ],
    shortcut: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "tr_TR",
    url: "https://promptbir.com",
    siteName: "Promptbir",
    title: "Promptbir - AI Prompt Yönetim Platformu",
    description: "Yapay zeka prompt'larınızı profesyonelce yönetin. Takımınızla paylaşın, organize edin ve verimliliğinizi artırın. 10,000+ geliştirici tarafından güvenilir.",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Promptbir - AI Prompt Yönetim Platformu",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@promptbir",
    creator: "@promptbir",
    title: "Promptbir - AI Prompt Yönetim Platformu",
    description: "Yapay zeka prompt'larınızı profesyonelce yönetin. Takımınızla paylaşın, organize edin ve verimliliğinizi artırın.",
    images: ["/twitter-image.png"],
  },
  verification: {
    google: "your-google-verification-code",
  },
  alternates: {
    canonical: "https://promptbir.com",
    languages: {
      'tr-TR': 'https://promptbir.com',
      'en-US': 'https://promptbir.com/en',
    },
  },
  category: "Technology",
  metadataBase: new URL('https://promptbir.com'),
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#000000' },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Run security check on server-side
  if (typeof window === 'undefined') {
    performRuntimeSecurityCheck();
  }

  // Generate structured data for the website
  const websiteStructuredData = SEOGenerator.generateStructuredData({
    type: 'WebApplication',
    data: {
      name: BASE_SEO.siteName,
      description: BASE_SEO.defaultDescription,
      url: BASE_SEO.siteUrl,
      applicationCategory: 'DeveloperApplication',
      operatingSystem: 'Web Browser',
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD'
      },
      author: {
        '@type': 'Organization',
        name: BASE_SEO.siteName
      }
    }
  })

  return (
    <html lang="tr">
      <head>
        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />

        {/* Structured Data */}
        <StructuredData data={websiteStructuredData} />

        {/* Critical resources are automatically preloaded by Next.js 15 */}

        {/* Theme and app configuration */}
        <meta name="theme-color" content="#3b82f6" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="format-detection" content="telephone=no" />

        {/* Viewport meta for responsive design */}
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
      </head>
      <body
        className={`${inter.variable} font-sans antialiased`}
      >
        <ErrorBoundary level="critical" showDetails={process.env.NODE_ENV === 'development'}>
          <QueryProvider>
            <FormFeedbackProvider>
              {children}
              <Toaster />
              <ServiceWorkerRegistration />
              <PerformanceMonitorWrapper />
            </FormFeedbackProvider>
          </QueryProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
